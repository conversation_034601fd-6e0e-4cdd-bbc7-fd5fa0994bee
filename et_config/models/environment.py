"""
Environment configuration models for ETDAG framework.

This module contains data classes for environment-specific configuration
including S3, connections, and environment settings.
"""

from dataclasses import dataclass, field
from typing import Dict, List


@dataclass
class S3Config:
    """S3 configuration for an environment."""

    bucket: str
    prefix: str = ""
    region: str = "us-east-1"

    def get_path(self, dag_id: str, path_type: str, suffix: str = "") -> str:
        """Get S3 path for a specific DAG and path type."""
        if self.prefix and "{dag_id}" in self.prefix:
            # Dev environment with dag_id prefixing
            prefix = self.prefix.format(dag_id=dag_id)
        else:
            prefix = self.prefix

        path_parts = [p for p in [prefix, path_type, suffix] if p]
        path = "/".join(path_parts)
        return f"s3://{self.bucket}/{path}"


@dataclass
class ConnectionConfig:
    """Database/service connection configuration."""

    starburst_conn_id: str
    slack_conn_id: str
    email_conn_id: str = "email_default"
    sftp_conn_id: str = "sftp_default"
    sage_conn_id: str = "sage_default"


@dataclass
class EnvironmentConfig:
    """Complete environment configuration."""

    name: str
    s3: S3Config
    connections: ConnectionConfig
    hive_catalog: str
    iceberg_catalog: str
    allowed_schemas: List[str]
    query_timeout: int = 300
    max_retries: int = 2
    is_production: bool = False
    supported_udfs: Dict[str, bool] = field(default_factory=dict)

    def __post_init__(self):
        # Default UDF availability if not specified
        if not self.supported_udfs:
            self.supported_udfs = {
                "ethash_v1": self.is_production,
                "ethash_v2": self.is_production,
                "geocodeAddress": self.is_production,
                # Add other UDFs here as needed
            }

    @property
    def is_local(self) -> bool:
        return self.name == "local"

    @property
    def is_dev(self) -> bool:
        return self.name == "dev"

    @property
    def is_prod(self) -> bool:
        return self.name == "prod" or self.is_production

    @property
    def trino_catalog(self) -> str:
        """Get the Trino catalog (alias for iceberg_catalog for backward compatibility)."""
        return self.iceberg_catalog

    def supports_udf(self, udf_name: str) -> bool:
        """Check if a specific UDF is supported in this environment."""
        return self.supported_udfs.get(udf_name, False)
