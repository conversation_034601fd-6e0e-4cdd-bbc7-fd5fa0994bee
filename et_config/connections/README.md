# Connection Configurations

This directory contains Airflow connection configurations organized by environment.

## Structure

```
config/connections/
├── local/          # Local development connections (gitignored)
├── dev/            # Development environment connections (gitignored)  
├── templates/      # Template files for reference (tracked in git)
└── README.md       # This file
```

## Environment Organization

### `local/`
Connection files for local development:
- `sage_local.json` - Sage Intacct sandbox connection
- `trino_local.json` - Local Trino/Starburst connection (connects to dev-trino container on port 8080)
- `aws_local.json` - LocalStack S3 connection (connects to dev-localstack container on port 4566)
- `slack_local.json` - Slack webhook for local development notifications

### `dev/`
Connection files for development environment:
- `sage_dev.json` - Sage development connection
- `trino_dev.json` - Development Trino/Starburst connection

### `templates/`
Template files showing the expected structure (tracked in git):
- `sage.template.json` - Template for Sage connections
- `trino.template.json` - Template for Trino connections
- `aws.template.json` - Template for AWS connections

## Security

- **Actual connection files are gitignored** to prevent credential exposure
- **Only templates are tracked** in version control
- **Templates contain placeholder values** like `YOUR_PASSWORD_HERE`

## Usage

### For Local Development
1. Copy templates to the `local/` directory
2. Fill in actual credentials
3. Use with integration tests or local Airflow

### For Integration Tests (Database-Free)
Integration tests automatically load connections from JSON files without requiring an Airflow database:

```python
from tests.utils.connection_loader import load_connection_config

# Load connection config directly from JSON files
conn_config = load_connection_config("trino_local")
```

This approach is perfect for local development environments that don't run a full Airflow instance.

### Setting Up Local Development Connections

1. **Copy templates** to local directory:
```bash
cp config/connections/templates/trino_local.template.json config/connections/local/trino_local.json
cp config/connections/templates/sage_local.template.json config/connections/local/sage_local.json
cp config/connections/templates/aws_local.template.json config/connections/local/aws_local.json
```

2. **Edit files** with real credentials for your local environment

3. **Test connections**:
```bash
# Verify connection files are valid
pytest tests/integration/sage_to_starburst/test_connections.py::TestSageConnectionsIntegration::test_connection_files_exist -v

# Test LocalStack connectivity
pytest tests/integration/sage_to_starburst/test_connections.py::TestSageConnectionsIntegration::test_localstack_s3_with_real_config -v
```

### Creating New Connections
1. Create a template in `templates/` with placeholder values
2. Copy to appropriate environment directory (`local/`, `dev/`)
3. Fill in real credentials
4. Update `.gitignore` if needed
5. Add validation tests in `tests/integration/`

## Connection File Format

All connection files use this JSON structure:
```json
{
  "conn_id": "connection_name",
  "conn_type": "connection_type",
  "description": "Description of the connection",
  "host": "hostname",
  "login": "username", 
  "password": "password",
  "schema": "default_schema",
  "port": 1234,
  "extra": {
    "additional_field": "value",
    "another_field": "value"
  }
}
```

## Integration with Development Tools

The integration test runner and other development tools automatically load connections from the `local/` directory for seamless local development and testing.