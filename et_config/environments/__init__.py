"""
Environment definitions and registry for ETDAG framework.

This package contains environment-specific configurations
and the registry for managing environment lookup and validation.
"""

from et_config.environments.registry import (
    get_current_environment,
    get_environment_config,
    validate_environment_config,
)

__all__ = [
    "get_current_environment",
    "get_environment_config",
    "validate_environment_config",
]
