"""
Production environment configuration for ETDAG framework.

This module defines the configuration for production environment
including production S3 bucket, production connections, and full UDF availability.
"""

from et_config.models.environment import ConnectionConfig, EnvironmentConfig, S3Config

PROD_CONFIG = EnvironmentConfig(
    name="prod",
    s3=S3Config(bucket="et-datalake", prefix="", region="us-east-1"),
    connections=ConnectionConfig(
        starburst_conn_id="starburst_prod",
        slack_conn_id="slack_prod_alerts",
        email_conn_id="email_prod",
        sage_conn_id="sage_prod",
    ),
    hive_catalog="s3",
    iceberg_catalog="olympus",
    allowed_schemas=["analytics", "reporting", "processed", "enriched"],
    query_timeout=900,
    max_retries=3,
    is_production=True,
    supported_udfs={
        "ethash_v1": True,  # Supported in production
        "ethash_v2": True,  # Supported in production
        "geocodeAddress": True,  # Supported in production
    },
)
