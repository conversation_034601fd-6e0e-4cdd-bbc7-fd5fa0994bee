from datetime import datetime, timedelta
from old_dags.xandr_userid_did_rollup.queries import (
    TEMP_TABLE_CREATE,
    TEMP_TABLE_DROP,
    MERGE_TO_ROLLUP,
    OPTIMIZE_ROLLUP,
)
from etdag import ETDAG
import pendulum
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator

"""
    DAG: `xandr_userid_did_rollup`

    Schedule: daily

    Owner:  <PERSON><PERSON><PERSON>

    ## Summary
    Queries all unique userids and dids from the xandr database and rolls them up into a single table 
    including first_seen and last_seen dates

    ## Failures
    This dag is configured to run for specific dates.  It is not sensitive to the sequence of dates.  If a date fails,
    please clear task to rerun.

    ## Escalation
    If rerunning the Dag <NAME_EMAIL>.

    ## Dependencies
    s3.dev_data_engineering.xandr_bid_stream

    ## Results
    Rollup table: olympus.silver_et_xandr.did_userid_rollup
"""

default_args = {
    "owner": "<PERSON><PERSON><PERSON>",
    "retries": 0,
    "retry_delay": timedelta(minutes=1),
}

with ETDAG(
    dag_id="xandr_userid_did_rollup",
    schedule="17 23 * * *",
    default_args=default_args,
    catchup=True,
    description="Updates Xandr user_id and device_id rollup table",
    start_date=datetime(2024, 12, 4),
    tags=["team:DND"],
    et_failure_msg=False,
    max_active_runs=1,
) as dag:

    drop_temp_table = SQLExecuteQueryOperator(
        task_id="drop_temp_table",
        conn_id="trino_conn",
        sql=TEMP_TABLE_DROP,
        retries=3,
        retry_delay=timedelta(minutes=3),
    )

    stage_tmp_table = SQLExecuteQueryOperator(
        task_id="stage_tmp_table",
        conn_id="trino_conn",
        sql=TEMP_TABLE_CREATE,
        retries=3,
        retry_delay=timedelta(minutes=3),
    )

    merge_to_rollup = SQLExecuteQueryOperator(
        task_id="merge_to_rollup",
        conn_id="trino_conn",
        sql=MERGE_TO_ROLLUP,
        retries=3,
        retry_delay=timedelta(minutes=3),
    )

    optimize_rollup = SQLExecuteQueryOperator(
        task_id="optimize_rollup",
        conn_id="trino_conn",
        sql=OPTIMIZE_ROLLUP,
        retries=3,
        retry_delay=timedelta(minutes=3),
    )

    drop_temp_table >> stage_tmp_table >> merge_to_rollup >> optimize_rollup
