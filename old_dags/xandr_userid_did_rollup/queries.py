CREATE_ROLLUP_TABLE = """
    CREATE TABLE IF NOT EXISTS olympus.silver_et_xandr.did_userid_rollup (
        user_id VARCHAR,
        device_id VARCHAR,
        first_seen DATE,
        last_seen DATE
    )
    WITH (
        format = 'PARQUET',
        partitioning = ARRAY[
            'month(last_seen)', 
            'bucket(device_id, 64)'  
        ]
    );
"""

TEMP_TABLE_CREATE = """
    CREATE TABLE olympus.dev_data_analytics.temp_did_userid_rollup AS
    SELECT
        appnexus_did user_id,
        ifa device_id
    FROM
        s3.dev_data_engineering.xandr_bid_stream
    WHERE
        appnexus_did is not null 
        and ifa is not null
        and appnexus_did != ''
        and ifa != ''
        and year = {{ data_interval_end.year }}
        AND month = {{ data_interval_end.month }}
        AND day = {{ data_interval_end.day }}
    GROUP BY
        appnexus_did, ifa
"""

MERGE_TO_ROLLUP = """
    MERGE INTO olympus.silver_et_xandr.did_userid_rollup  AS target
    USING (
        SELECT
            user_id,
            device_id,
            date('{{ data_interval_end.strftime('%Y-%m-%d') }}') todays_date
        FROM olympus.dev_data_analytics.temp_did_userid_rollup
    ) AS source
    ON target.device_id = source.device_id AND target.user_id = source.user_id
    WHEN MATCHED THEN
        UPDATE SET
            first_seen = LEAST(target.first_seen, source.todays_date),
            last_seen = GREATEST(target.last_seen, source.todays_date)
    WHEN NOT MATCHED THEN
        INSERT (user_id, device_id, first_seen, last_seen)
        VALUES (source.user_id, source.device_id, source.todays_date, source.todays_date);
"""


TEMP_TABLE_DROP = """
    DROP TABLE IF EXISTS olympus.dev_data_analytics.temp_did_userid_rollup
"""

OPTIMIZE_ROLLUP = """
    ALTER TABLE olympus.silver_et_xandr.did_userid_rollup EXECUTE OPTIMIZE
"""
