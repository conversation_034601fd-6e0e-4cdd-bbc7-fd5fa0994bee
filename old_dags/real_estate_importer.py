from airflow.decorators import task
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from psycopg2 import sql
from psycopg2.errors import UniqueViolation
import uuid
from io import StringIO
import csv
import pandas as pd
from airflow.models import Variable
from airflow.exceptions import AirflowFailException
from datetime import timed<PERSON>ta
from etdag import ETDAG
import logging

logger = logging.getLogger(__name__)


def psql_upsert_copy(table, conn, keys, data_iter):
    unique = str(uuid.uuid4())[:5]
    dbapi_conn = conn.connection

    buf = StringIO()
    writer = csv.writer(buf)
    writer.writerows(data_iter)
    buf.seek(0)

    if table.schema:
        table_name = sql.SQL("{}.{}").format(
            sql.Identifier(table.schema), sql.Identifier(table.name))
    else:
        table_name = sql.Identifier(table.name)

    tmp_table_name = sql.Identifier(table.name + f"{unique}_staging")
    columns = sql.SQL(', ').join(map(sql.Identifier, keys))

    with dbapi_conn.cursor() as cur:
        # Create the staging table
        stmt = "CREATE TEMPORARY TABLE {} ( LIKE {} ) ON COMMIT DROP"
        stmt = sql.SQL(stmt).format(tmp_table_name, table_name)
        cur.execute(stmt)

        # Populate the staging table
        stmt = "COPY {} ( {} ) FROM STDIN WITH CSV"
        stmt = sql.SQL(stmt).format(tmp_table_name, columns)
        cur.copy_expert(stmt, buf)

        # Upsert from the staging table to the destination. First find
        # out what the primary key columns are.
        stmt = """
               SELECT kcu.column_name
               FROM information_schema.table_constraints tco
               JOIN information_schema.key_column_usage kcu
               ON kcu.constraint_name = tco.constraint_name
               AND kcu.constraint_schema = tco.constraint_schema
               WHERE tco.constraint_type = 'PRIMARY KEY'
               AND tco.table_name = %s
               """
        args = (table.name,)

        if table.schema:
            stmt += "AND tco.table_schema = %s"
            args += (table.schema,)

        cur.execute(stmt, args)
        pk_columns = {row[0] for row in cur.fetchall()}
        # Separate "data" columns from (primary) key columns
        data_columns = [k for k in keys if k not in pk_columns]
        # Build conflict_target
        pk_columns = sql.SQL(", ").join(map(sql.Identifier, pk_columns))
        set_ = sql.SQL(", ").join([
            sql.SQL("{} = EXCLUDED.{}").format(k, k)
            for k in map(sql.Identifier, data_columns)])

        stmt = """
               INSERT INTO {} ( {} )
               SELECT {}
               FROM {}
               ON CONFLICT ( {} )
               """

        if set_ != sql.Composed([]):
            stmt += """
            DO UPDATE SET {}
            """
        else:
            stmt += """
            DO NOTHING {}
            """

        stmt = sql.SQL(stmt).format(
            table_name, columns, columns, tmp_table_name, pk_columns, set_)
        cur.execute(stmt)

with ETDAG(
    dag_id="realestate_observation_importer",
    catchup=False,
    default_args={
        "owner": "Panama",
        "retries": 3,  # Add retries for database operations
        "retry_delay": timedelta(minutes=5),  # Wait before retrying
        "retry_exponential_backoff": True,  # Handle transient DB issues
        "max_retry_delay": timedelta(minutes=20),  # Cap maximum delay
    },
    description="upserts files to hestia realestate tables",
    is_paused_upon_creation=True,
) as dag:
    def upsert_from_s3_to_psql(
        s3_key: str,
        psql_table: str,
        s3_bucket: str="onspot-eltoro-vr-prod",
        psql_conn_id: str="hestia_psql",
        aws_conn_id: str="aws_default"
    ):
        """
            This function uploads all the files in an s3 directory to postgres,
            and then moves the files over to an s3 DONE directory.

            Enhanced with UniqueViolation handling - problematic files are moved to ERROR directory.

            :param s3_key: target s3 directory for uploading
            :param psql_table: psql table destination for file data
            :param s3_bucket: target s3 bucket for uploading
            :param psql_conn_id: airflow postgres connection to use
            :param aws_conn_id: airflow aws connection to use

        """
        psql_engine = PostgresHook(
            psql_conn_id
        ).get_sqlalchemy_engine(
            {"pool_pre_ping": True}
        )
        s3 = S3Hook(aws_conn_id)

        processed_files = 0
        error_files = 0

        for key in s3.list_keys(s3_bucket, s3_key):
            logger.info(f"Processing file: {key}")

            try:
                df = pd.read_csv(f"s3://{s3_bucket}/{key}")
                if env == "prod":
                    schema="realestate"
                else:
                    schema="realestate_dev"

                # Attempt to upsert the data
                df.to_sql(
                    psql_table,
                    psql_engine,
                    method=psql_upsert_copy,
                    index=False,
                    if_exists="append",
                    schema=schema
                )

                # If successful, move to DONE directory
                if env=="prod":
                    dest_key = f"/inbound/realestate/DONE/{psql_table}/{key.split('/')[-1]}"
                    s3.copy_object(
                        source_bucket_name=s3_bucket,
                        source_bucket_key=key,
                        dest_bucket_name=s3_bucket,
                        dest_bucket_key=dest_key
                    )
                    s3.delete_objects(bucket=s3_bucket, keys=[key])

                processed_files += 1
                logger.info(f"Successfully processed file: {key}")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error processing file {key}: {error_msg}")

                # Check if it's a UniqueViolation error
                if "duplicate key value violates unique constraint" in error_msg or "UniqueViolation" in error_msg:
                    logger.warning(f"UniqueViolation detected for file {key}. Moving to ERROR directory.")

                    # Move problematic file to ERROR directory instead of failing the task
                    if env=="prod":
                        error_dest_key = f"/inbound/realestate/ERROR/{psql_table}/{key.split('/')[-1]}"
                        try:
                            s3.copy_object(
                                source_bucket_name=s3_bucket,
                                source_bucket_key=key,
                                dest_bucket_name=s3_bucket,
                                dest_bucket_key=error_dest_key
                            )
                            s3.delete_objects(bucket=s3_bucket, keys=[key])
                            error_files += 1
                            logger.info(f"Moved problematic file to ERROR directory: {error_dest_key}")
                        except Exception as move_error:
                            logger.error(f"Failed to move error file {key}: {move_error}")
                            # Re-raise the original error if we can't even move the file
                            raise e
                    else:
                        # In dev, just log and continue
                        error_files += 1
                        logger.warning(f"UniqueViolation in dev environment for file {key}. Skipping.")
                else:
                    # For non-UniqueViolation errors, re-raise to trigger retries
                    logger.error(f"Non-UniqueViolation error for file {key}. Will retry.")
                    raise e

        logger.info(f"Processing complete. Processed: {processed_files}, Errors: {error_files}")

        # If all files had errors, that might indicate a bigger issue
        if processed_files == 0 and error_files > 0:
            logger.warning(f"All {error_files} files had UniqueViolation errors. This might indicate a data issue.")

        return {"processed_files": processed_files, "error_files": error_files}


    env = Variable.get("environment", default_var="dev")
    s3_key_base = f"inbound/realestate/{env}"

    task(
        task_id="upsert_address",
        retries=4,  # Higher retries for database operations
        retry_delay=timedelta(minutes=3),  # Shorter delay for address data
    )(upsert_from_s3_to_psql)(
        s3_key=f"{s3_key_base}/address",
        psql_table="address",
    )

    task(
        task_id="upsert_device_ethash",
        retries=4,  # Higher retries for database operations
        retry_delay=timedelta(minutes=3),  # Shorter delay for device_ethash data
    )(upsert_from_s3_to_psql)(
        s3_key=f"{s3_key_base}/device_ethash",
        psql_table="device_ethash"
    )
