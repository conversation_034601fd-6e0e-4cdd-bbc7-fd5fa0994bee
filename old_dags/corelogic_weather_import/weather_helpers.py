import os
import json
from airflow.models import Variable
import os
import json
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import tempfile


class Weather:

    def __init__(self):
        self.url = f"https://api-prod.corelogic.com/wvs-api/api/"
        self.headers = {"content-type": "application/xml"}
        self.s3_bucket = "corelogic-weather"
        self.env = Variable.get("environment")
        self.DEFINED_WEATHER_TYPES = {
            "hail": {
                "name": "hail",
                "available_map_function": "GetAvailableMaps",
                "xml_selector": "HailMap",
                "get_map_function": "GetMap",
                "label_name": "label",
            },
            "tornado": {
                "name": "tornado",
                "available_map_function": "GetAvailableTornadoMaps",
                "xml_selector": "TornadoMap",
                "get_map_function": "GetTornadoMap",
                "label_name": "value",
            },
            "wind": {
                "name": "wind",
                "available_map_function": "GetAvailableWindMaps",
                "xml_selector": "WindMap",
                "get_map_function": "GetWindMap",
                "label_name": "label",
            },
        }
        self.DATE_FORMAT = "%Y-%m-%d"
        self.CLIENT_ID = "2343197274"

    def set_weather_type(self, token, start_date, weather_type):
        print(weather_type)
        print(type(weather_type))
        self.name = self.DEFINED_WEATHER_TYPES[weather_type]["name"]
        self.headers = {"Authorization": f"Bearer {token}"}
        self.key = f"dev/raw_maps/{self.name}/{start_date}.json"
        self.available_map_function = self.DEFINED_WEATHER_TYPES[weather_type][
            "available_map_function"
        ]
        self.xml_selector = self.DEFINED_WEATHER_TYPES[weather_type]["xml_selector"]
        self.get_map_function = self.DEFINED_WEATHER_TYPES[weather_type][
            "get_map_function"
        ]
        self.label_name = self.DEFINED_WEATHER_TYPES[weather_type]["label_name"]


def process_file(data, label_name) -> dict:
    print(len(data["features"]))
    totalFeatures = data["features"]
    total_labels = set()
    feature_labelled = {}
    for feature in totalFeatures:
        label = feature["properties"][label_name]
        label = label.replace('"', "")
        label = label.replace(" ", "_")
        if label in total_labels:
            if label in feature_labelled:
                val = feature_labelled.get(label)
                val["features"].append(feature)
        else:
            print("Adding data for : ", label)
            initial_geojson = {
                "type": "FeatureCollection",
                "name": "sql_statement",
                "features": [],
            }
            initial_geojson["features"].append(feature)
            feature_labelled[label] = initial_geojson
        total_labels.add(label)
    return feature_labelled


def generate_files(processed_data, date, year, weather):
    s3_hook = S3Hook(aws_conn_id="s3_conn")
    prefix_name = "US_" + str(date) + "_"
    s3_key_prefix = f"{weather.env}/{weather.name}/{year}/{date}"

    for label in processed_data:
        print("Generating file for:", label)
        file_name = f"{prefix_name}{label}.json"
        s3key = f"{s3_key_prefix}/{label}/{file_name}"

        # Create a new temporary file for each label
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as temp_file:
            # Write JSON data to the temporary file
            json.dump(processed_data[label], temp_file)
            temp_file_path = temp_file.name

        print("Uploading to S3:", s3key)
        s3_hook.load_file(
            filename=temp_file_path,
            key=s3key,
            bucket_name="corelogic-weather",
            replace=True,
        )

        try:
            os.remove(temp_file_path)
        except OSError as e:
            print(f"Error removing temporary file {temp_file_path}: {e}")
