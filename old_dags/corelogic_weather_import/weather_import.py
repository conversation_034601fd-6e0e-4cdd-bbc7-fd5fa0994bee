from etdag import ETDAG
from airflow.decorators import task
from datetime import datetime
import requests
from airflow.models import Variable
import json
from old_dags.corelogic_weather_import.weather_helpers import *


@task
def get_token():
    weather_creds = json.loads(Variable.get(f"corelogic_weather"))
    key = weather_creds["CONSUMERKEY"]
    secret = weather_creds["CONSUMERSECRET"]

    r = requests.post(
        "https://prod.corelogicapi.com/oauth/token?grant_type=client_credentials",
        auth=(f"{key}", f"{secret}"),
    )
    creds = r.json()
    return creds["access_token"]


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    pip_install_options=["--upgrade"],
)
def get_weather_geojson(token, weather_event, logical_date):
    from corelogic_weather_import.weather_helpers import (
        Weather,
        process_file,
        generate_files,
    )
    import geojson
    import json
    from airflow.providers.amazon.aws.hooks.s3 import S3Hook
    from datetime import datetime, timedelta
    from bs4 import FeatureNotFound
    import requests
    import tempfile
    from airflow.exceptions import AirflowSkipException

    start_date = (logical_date - timedelta(days=4)).strftime("%Y-%m-%d")
    # start_date = "2025-02-16"
    weather = Weather()
    print(start_date)
    weather.set_weather_type(token, start_date, weather_event)

    def raw_file_processing(date, data, weather):
        s3_hook = S3Hook(aws_conn_id="s3_conn")

        raw_s3key = f"{weather.env}/raw/{weather.name}/US_{date}.json"
        print("S3 Key:", raw_s3key)

        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".json"
        ) as temp_file:
            geojson.dump(data, temp_file)
            temp_file_path = temp_file.name

        # Upload the file to S3
        s3_hook.load_file(
            filename=temp_file_path,
            key=raw_s3key,
            bucket_name=weather.s3_bucket,
            replace=True,
        )
        print(f"File successfully uploaded to S3: {raw_s3key}")

    try:

        start_date_obj = datetime.strptime(start_date, weather.DATE_FORMAT)
        processing_date = start_date_obj.date().strftime("%Y%m%d")
        processing_year = start_date_obj.year
        print("Getting data for: ", processing_date)
        print("Getting data for: ", processing_year)
        convective_date = start_date_obj.date()
        print("Getting data for: ", convective_date)

        params = {
            "client-id": weather.CLIENT_ID,
            "convective-date": convective_date,
        }
        map_response = requests.get(
            f"{weather.url}{weather.name}/map",
            headers=weather.headers,
            params=params,
        )
        if map_response.status_code != 200:
            error_data = json.loads(map_response.text)
            if (
                error_data.get("status") == "400"
                and error_data.get("message") == "No Data Found for Given Parameters"
            ):
                print("No data found. Skipping this run.")
                raise AirflowSkipException("No Data Found for Given Parameters")
        else:
            print("Response from getMap: ", map_response)
            data = json.loads(map_response.text)

        raw_file_processing(str(processing_date), data, weather)
        processed_data = process_file(data, weather.label_name)
        generate_files(processed_data, processing_date, processing_year, weather)
    except FeatureNotFound:
        print("No maps available.")
    except Exception as err:
        print("Something went wrong!", err)


with ETDAG(
    dag_id="corelogic_weather_import",
    catchup=False,
    default_args={"owner": "BP", "retries": 0},
    schedule_interval="0 15 * * *",
    start_date=datetime(2025, 1, 12),
    max_active_runs=1,
    tags=["weateher", "corelogic"],
    params={"weather_events": ["wind", "hail", "tornado"]},
) as dag:

    token = get_token()
    geojson = get_weather_geojson.partial(token=token).expand(
        weather_event=dag.params["weather_events"]
    )
    token >> geojson
