from airflow.decorators import task
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from etdag import ETDAG
import pandas as pd
from datetime import datetime, timedelta


default_args = {
    "owner": "<PERSON>ur",
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(seconds=30),
}


def get_dataset_from_trino() -> pd.DataFrame:
    query = "select * from s3.dev_prototyping.citizens_precisionmkt_results"
    tr = TrinoHook(trino_conn_id="starburst")
    results_df = tr.get_pandas_df(sql=query, dtype=str)
    return results_df


with ETDAG(
    dag_id="citizens_enrichment",
    description="Provides enrichment weekly, queried from starburst, delivered to sftp",
    start_date=datetime(2025, 4, 20),
    default_args=default_args,
    schedule_interval="0 10 * * 2", #6AM est every tuesday
    catchup=False,
    tags=["application:citizens", "team:DND"],
) as dag:
    BUCKET_NAME = "vr-timestamp"
    today = datetime.today()
    date_st = today.date().strftime("%Y%m%d")
    date_st_hyphen = today.date().strftime("%Y-%m-%d")
    filename = f"{date_st}_citizens_precision_marketing_output.csv"
    S3_PATH = f"bi_sources/realestate/citizens/enrichment/transfer_date={date_st_hyphen}/{filename}"

    @task
    def get_enrichment():
        df = get_dataset_from_trino()
        df.to_csv(f"s3://{BUCKET_NAME}/{S3_PATH}", index=False)

    deliver_dataset = get_enrichment()

    s3_to_sftp_cl = S3ToSFTPOperator(
        task_id="s3_to_sftp_cl",
        s3_bucket=BUCKET_NAME,
        s3_key=S3_PATH,
        sftp_path=f"/Outbound/{filename}",
        sftp_conn_id="citizens_sftp",
        aws_conn_id="s3_conn",
    )

    deliver_dataset >> s3_to_sftp_cl
