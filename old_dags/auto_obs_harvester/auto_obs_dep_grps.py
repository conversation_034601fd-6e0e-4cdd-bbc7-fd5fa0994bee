dep_views_g0 = [
    "s3.gold_auto_intender.location_tags",
    "s3.gold_auto_intender.locations_new_and_used",
    "s3.gold_auto_enrichment.fry_toyota_intender_locations",
    "s3.bronze_auto_intender.auto_intent_daily_4eyes"
]

dep_views_g1 = [
    "s3.gold_auto_intender.distinct_obs_90",
    "s3.gold_auto_intender.observations_legacy_toyota"
    #"s3.gold_auto_intender.recreation_obs_90" 
]

dep_views_g2 = [
    "s3.gold_auto_intender.base",
    "s3.gold_auto_intender.visitor_agg_generic"
    #s3.gold_auto_intender.recreation_visitor_agg_generic
]

dep_views_g3 = [
    "\"s3\".\"gold_auto_enrichment\".\"90_day_agg_v1\"",
    "\"s3\".\"gold_auto_enrichment\".\"recent_agg_v1\""
]

dep_views_g4 = [
    "\"s3\".\"gold_auto_enrichment\".\"90_day_auto_prospects\"",
    "\"s3\".\"gold_auto_enrichment\".\"recent_auto_prospects\"",
    "\"s3\".\"bronze_auto_intender\".\"auto_health_check\""
]

dep_views = [dep_views_g0, dep_views_g1, dep_views_g2, dep_views_g3, dep_views_g4]

'''
health_check_queries = {
    "auto_intent_daily_ethashes" : 
        "select cast(avg(daily_count) as int) from (SELECT count(distinct ethash_v1) as daily_count, event_date FROM s3.bronze_auto_intender.auto_intent_daily_4eyes group by event_date);",
    "location_tags_distinct_locations" : 
        "SELECT count(distinct buckloc_id) FROM s3.gold_auto_intender.location_tags;",
    "distinct_obs_90_daily_ethashes" : 
        "select cast(avg(daily_count) as int) from (SELECT count(distinct visitor_ethash) as daily_count, date FROM s3.gold_auto_intender.distinct_obs_90 group by date) LIMIT 10; --has many device_id w/o ethash",
    "base_daily_ethashes" : 
        "select cast(avg(daily_count) as int) from (SELECT count(distinct parcel_ethash) as daily_count, date FROM s3.gold_auto_intender.base group by date) LIMIT 10; --uses parcel_ethash",
}
'''

health_check_queries = [
        '''SELECT COUNT(DISTINCT buckloc_id) FROM
        "s3"."gold_auto_intender"."location_tags"''',

        '''SELECT COUNT(DISTINCT visitor_ethash) daily_count
        FROM "s3"."gold_auto_intender"."distinct_obs_90"
        where date = cast('{{ (execution_date - macros.timedelta(days=4)).strftime('%Y-%m-%d') }}' as date)''',
        
        '''SELECT COUNT(DISTINCT parcel_ethash) daily_count
        FROM "s3"."gold_auto_intender"."base"
        where date = cast('{{ (execution_date - macros.timedelta(days=4)).strftime('%Y-%m-%d') }}' as date)'''
]