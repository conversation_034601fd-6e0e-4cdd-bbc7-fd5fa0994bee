from io import BytesIO
import logging
import boto3
import requests
import polars as pl
import pandas as pd
import base64
from etdag import ETDAG
from airflow.decorators import task
from airflow.utils.dates import days_ago
from airflow.hooks.base import BaseHook
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from json import loads


gong_conn_id = "gong_api"

s3_bucket = "et-datalake-bi-external"

s3_user_folder = "gong/users/date="
s3_call_folder = "gong/calls/date="
s3_stats_folder = "gong/usersAggregateActivityStats/date="
s3_transcript_folder = "gong/transcripts/date="
s3_transcript_parse_transcript_folder = "gong/parse_transcripts/date="


BASE_URL = "https://us-42832.api.gong.io"
# Different Endpoint. Including Example of URI
USERS = "/v2/users"
# https://app.gong.io/settings/api/documentation#get-/v2/users
CALLS = "/v2/calls"
# https://app.gong.io/settings/api/documentation#get-/v2/calls
STATS = "/v2/stats/activity/aggregate"
# https://app.gong.io/settings/api/documentation#post-/v2/stats/activity/aggregate
TRANSCRIPT = "/v2/calls/transcript"
# https://app.gong.io/settings/api/documentation#post-/v2/calls/transcript

docs = """
# Overview

NOTES: FOR THE GONG TRANSCRIPT DATA, THE ONLY THING WE DID WAS EXTRACT THE DATA FROM THE GONG API AND STORE IT IN OUR AWS S3 ENVIRONMENT. \n
THE TRANSCRIPT COLUMN IS ARRAY LIST INSIDE OF ANOTHER ARRAY LIST, SO IT WILL BE TIME A GOOD CHALLENGE TO PARSE THE DATA OUT IN THE FUTURE. \n -- This was completed on August 23. 2023 (Dev: Tim Hoang)

The Gong Reality Platform capture and store what’s happening in customer conversations between clients and sales reps.


Connect to Gong API. Which allow the developers to 
receive the following information from Gong: 
1. Your company's calls in Gong
2. Your company's users in Gong
3. Your company's user stats in Gong

Base URL for all API is https://us-42832.api.gong.io

Endpoint for the following information:
1. USERS = "/v2/users" 
\n https://app.gong.io/settings/api/documentation#get-/v2/users
2. CALLS = "/v2/calls"\n
https://app.gong.io/settings/api/documentation#get-/v2/calls
3. STATS = "/v2/stats/activity/aggregate" \n https://app.gong.io/settings/api/documentation#post-/v2/stats/activity/aggregate
4. Transcript = 'https://app.gong.io/settings/api/documentation#post-/v2/calls/transcript'

### AWS
1. S3 Bucket: et-datalake-bi-external \n
    user folder - gong/users/date= \n
    call folder - gong/calls/date= \n
    stats folder - gong/userAggregateActivityStats/date= \n
    transcripts folder - gong/transcripts/date=
"""


def get_api_info(conn_id: str):
    conn = BaseHook.get_connection(conn_id)
    return (conn.login, conn.password)


def get_Gong_Request(endpoint, cursor):
    login, password = get_api_info(gong_conn_id)
    token = str(login) + ":" + str(password)
    token = token.encode("utf-8")
    BEARER_TOKEN = base64.standard_b64encode(token).decode()
    try:
        response = requests.get(
            endpoint + cursor,
            headers={"Authorization": "Basic " + BEARER_TOKEN},
            data={},
        )
    except Exception as e:
        print(f"Failed to get {endpoint} response. Possible error: " + str(e))
        logging.error(f"Failed to get {endpoint} response. Possible error: " + str(e))
    else:
        return response.json()


def post_Gong_Request(endpoint, data):
    login, password = get_api_info(gong_conn_id)
    token = str(login) + ":" + str(password)
    token = token.encode("utf-8")
    BEARER_TOKEN = base64.standard_b64encode(token).decode()
    try:
        response = requests.post(
            endpoint, headers={"Authorization": "Basic " + BEARER_TOKEN}, json=data
        )
    except Exception as e:
        print(f"Failed to post {endpoint}. Possible error: " + str(e))
        logging.error(f"Failed to post {endpoint}. Possible error: " + str(e))
    else:
        return response.json()


def dataframe_to_s3(input_dataframe, s3_folder, yesterday_file_name):
    s3_resource = boto3.resource("s3")
    buffer = BytesIO()

    input_dataframe.write_parquet(buffer, use_pyarrow=True)
    s3_resource.Object(s3_bucket, s3_folder + yesterday_file_name + ".parquet").put(
        Body=buffer.getvalue()
    )


def extract_columns_frm_gong_df(gong_dataframe: pl.DataFrame, yesterday):
        # transcript = gong_dataFrame['callTranscripts']
        # print(gong_dataFrame)
        # file_name = f'parse_sentences_from_gong_transcript_{today_date}'
        df = gong_dataframe['transcript'].to_pandas()
        # print(df)

        values = loads(df.to_json(orient="split"))
        speakerId = []
        topic = []
        start = []
        end = []
        text = []
        
        for index, value in enumerate(values['data']):
            for x in range(len(value)):
                speakerId.append(value[x]['speakerId'])
                topic.append(value[x]['topic'])
                start.append(value[x]['sentences'][0]['start'])
                end.append(value[x]['sentences'][0]['end'])
                text.append(value[x]['sentences'][0]['text'])

                
        dict = {'speakerId': speakerId, 'topic': topic, 'start': start, 'end': end, 'text': text}

        sentences_df = pl.DataFrame(dict)
        dataframe_to_s3(
            input_dataframe=sentences_df,
            s3_folder=s3_transcript_parse_transcript_folder + yesterday + "/",
            yesterday_file_name=yesterday + "-parse_txt_from_transcript",
        )


# ----------------------
# GONG API CORE PROCESS
# ----------------------
def get_Gong_users(kwargs_start_date, kwargs_end_date):
    cursor = ""
    query = "?cursor="
    # yesterday = (kwargs_start_date - timedelta(days=offset)).strftime("%Y-%m-%d")
    yesterday = kwargs_start_date.strftime("%Y-%m-%d")
    user_response = get_Gong_Request(BASE_URL + USERS + query, cursor)

    if "users" not in user_response:
        return ""
    if "cursor" in user_response["records"]:
        cursor = user_response["records"]["cursor"]
    else:
        cursor = ""

    user_list = None
    if cursor == "":
        user_list = user_response["users"]
    else:
        user_list = user_response["users"]
        while cursor != "":
            user_response = get_Gong_Request(BASE_URL + USERS + query, cursor)
            if "cursor" in user_response["records"]:
                cursor = user_response["records"]["cursor"]
            user_list = user_list + user_response["user"]
    df = pd.json_normalize(user_list, max_level=1)
    df = pl.DataFrame(df)
    # print(df.dtypes)
    # Drop Column
    df = df.drop(columns=["emailAliases", "personalMeetingUrls", "spokenLanguages"])
    # print(df.head(10))
    logging.info(df.head(10))

    # Save dataframe directly to s3
    dataframe_to_s3(
        input_dataframe=df,
        s3_folder=s3_user_folder + yesterday + "/",
        yesterday_file_name=yesterday + "-users",
    )


# Get Gong call
def get_Gong_call(kwargs_start_date, kwargs_end_date):
    # yesterday = (kwargs_date - timedelta(days=offset)).strftime("%Y-%m-%d")
    yesterday = kwargs_start_date.strftime("%Y-%m-%d")
    cursor = ""
    query = (
        "?fromDateTime="
        + yesterday
        + "T00:00:00Z&toDateTime="
        + yesterday
        + "T23:59:59Z&cursor="
    )

    call_response = get_Gong_Request(BASE_URL + CALLS + query, cursor)

    if "records" not in call_response:
        return ""
    if "cursor" in call_response["records"]:
        cursor = call_response["records"]["cursor"]
    else:
        cursor = ""

    call_list = None
    if cursor == "":
        call_list = call_response["calls"]
        while cursor != "":
            call_response = get_Gong_Request(BASE_URL + CALLS + query, cursor)
    df = pd.json_normalize(call_list, max_level=1)
    df = pl.DataFrame(df)
    print(df.dtypes)
    dataframe_to_s3(
        input_dataframe=df,
        s3_folder=s3_call_folder + yesterday + "/",
        yesterday_file_name=yesterday + "-calls",
    )


def get_Gong_stats(kwargs_start_date, kwargs_end_date):
    # yesterday = (kwargs_date - timedelta(days=offset)).strftime("%Y-%m-%d")
    # today = (kwargs_date - timedelta(days=offset) + timedelta(days=1)).strftime(
    #     "%Y-%m-%d"
    # )

    yesterday = kwargs_start_date.strftime("%Y-%m-%d")
    today = kwargs_end_date.strftime("%Y-%m-%d")

    cursor = None
    data = {
        "cursor": cursor,
        "filter": {
            "fromDate": yesterday,
            "toDate": today,
            "createdFromDateTime": yesterday + "T00:00:00Z",
            "createdToDateTime": yesterday + "T23:59:59.999999Z",
        },
    }

    stats_response = post_Gong_Request(BASE_URL + STATS, data)

    if "records" not in stats_response:
        return ""
    if "cursor" in stats_response["records"]:
        cursor = stats_response["records"]["cursor"]
    else:
        cursor = ""

    call_list = None
    if cursor == "":
        call_list = stats_response["usersAggregateActivityStats"]
    else:
        call_list = stats_response["usersAggregateActivityStats"]
        while cursor != "":
            data = {
                "cursor": cursor,
                "filter": {
                    "fromDate": yesterday + "T00:00:00Z",
                    "toDate": yesterday + "T23:59:59.999999Z",
                },
            }
            stats_response = post_Gong_Request(BASE_URL + STATS, data)
            if "cursor" in stats_response["records"]:
                cursor = stats_response["records"]["cursor"]
            call_list = call_list + stats_response["usersAggregateActivityStats"]
    df = pd.json_normalize(call_list, max_level=1)
    df = pl.DataFrame(df)
    print(df.dtypes)

    # Save dataframe directly to s3
    dataframe_to_s3(
        input_dataframe=df,
        s3_folder=s3_stats_folder + yesterday + "/",
        yesterday_file_name=yesterday + "-usersAggregateActivityStats",
    )


def get_Gong_transcripts(kwargs_start_date, kwargs_end_date):
    yesterday = kwargs_start_date.strftime("%Y-%m-%d")
    today = kwargs_end_date.strftime("%Y-%m-%d")

    # yesterday = kwargs_start_date.strftime("%Y-%m-%d")
    cursor = ""
    data = {
        "cursor": cursor,
        "filter": {
            "fromDateTime": yesterday + "T00:00:00Z",
            "toDateTime": yesterday + "T23:59:59.999999Z",
        },
    }

    transcript_response = post_Gong_Request(BASE_URL + TRANSCRIPT, data)
    # print(transcript_response)

    if "records" not in transcript_response:
        return ""
    if "cursor" in transcript_response["records"]:
        cursor = transcript_response["records"]["cursor"]
    else:
        cursor = ""

    transcript_list = None
    if cursor == "":
        transcript_list = transcript_response["callTranscripts"]
    else:
        transcript_list = transcript_response["callTranscripts"]
        while cursor != "":
            data = {
                "cursor": cursor,
                "filter": {
                    "fromDate": yesterday,
                    "toDate": today,
                    "createdFromDateTime": yesterday + "T00:00:00Z",
                    "createdToDateTime": yesterday + "T23:59:59.999999Z",
                },
            }
            transcript_response = post_Gong_Request(BASE_URL + STATS, data)
            if "cursor" in transcript_response["records"]:
                cursor = transcript_response["records"]["cursor"]
            transcript_list = transcript_list + transcript_response["callTranscripts"]
    df = pd.json_normalize(transcript_list, max_level=1)
    # df["transcript"] = str(df["transcript"][0])
    # conv_date = datetime.strptime(yesterday, '%Y-%m-%d').date()
    # df['date'] = yesterday
    df = pl.DataFrame(df)
    print(df.head(10))

    dataframe_to_s3(
        input_dataframe=df,
        s3_folder=s3_transcript_folder + yesterday + "/",
        yesterday_file_name=yesterday + "-transcripts",
    )

    # Parse the transcript - (Tim Hoang - 08/23/23)
    extract_columns_frm_gong_df(df, yesterday)


with ETDAG(
    dag_id="gong_data_to_starburst",
    description="GONG API Data Extraction",
    schedule_interval="0 6 * * *",
    start_date=days_ago(2),
    catchup=False,
    tags=["startburst", "gong"],
    concurrency=1,
) as dag:
    dag.doc_md = docs

    update_starburst_users = SQLExecuteQueryOperator(
        task_id="update_users_table",
        conn_id="starburst",
        sql="CALL s3.system.sync_partition_metadata('bronze_sales_tools', 'gong_users', 'ADD')",
        handler=list,
    )

    update_starburst_calls = SQLExecuteQueryOperator(
        task_id="update_calls_table",
        conn_id="starburst",
        sql="CALL s3.system.sync_partition_metadata('bronze_sales_tools', 'gong_calls', 'ADD')",
        handler=list,
    )

    update_starburst_stats = SQLExecuteQueryOperator(
        task_id="update_stats_table",
        conn_id="starburst",
        sql="CALL s3.system.sync_partition_metadata('bronze_sales_tools', 'gong_usersAggregateActivityStats', 'ADD')",
        handler=list,
    )

    update_starburst_transcripts = SQLExecuteQueryOperator(
        task_id="update_transcript_table",
        conn_id="starburst",
        sql="CALL s3.system.sync_partition_metadata('bronze_sales_tools', 'gong_transcripts', 'ADD')",
        handler=list,
    )

    @task()
    def init_Gong_users(**kwargs):
        get_Gong_users(kwargs["data_interval_start"], kwargs["data_interval_end"])

    @task()
    def init_Gong_calls(**kwargs):
        get_Gong_call(kwargs["data_interval_start"], kwargs["data_interval_end"])

    @task()
    def init_Gong_Stats(**kwargs):
        get_Gong_stats(kwargs["data_interval_start"], kwargs["data_interval_end"])

    @task()
    def init_Gong_Transcrips(**kwargs):
        get_Gong_transcripts(kwargs["data_interval_start"], kwargs["data_interval_end"])

    init_Gong_users() >> update_starburst_users
    init_Gong_calls() >> update_starburst_calls
    init_Gong_Stats() >> update_starburst_stats
    init_Gong_Transcrips()
