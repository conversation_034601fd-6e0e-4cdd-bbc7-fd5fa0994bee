find_ol_ids = """
  SELECT order_line_id
    FROM "s3"."silver_platform_services"."{table}"
    WHERE order_line_name LIKE '%{region}_{seg}_{month}%'
    and  org_id = '{org_id}' and campaign_id != '';
"""

find_next_month_ol_ids = """
  SELECT order_line_id
    FROM "s3"."silver_platform_services"."{table}"
    WHERE order_line_name LIKE '%{region}_{seg}_{next_month}%'
    and  org_id = '{org_id}' and campaign_id != '';
"""


filter_audince = """
    SELECT streetaddress, zipcode
    FROM "s3"."bronze_auto_intender"."{env}_toyota_daily_audience" 
    where region_abbr = '{region}' AND segment_group = '{seg}' AND UPPER(is_test_group) = 'T' AND UPPER(target) = 'T' AND CAST(date AS VARCHAR) = '{date}';
"""

############# need to update query for target value as well  #############
