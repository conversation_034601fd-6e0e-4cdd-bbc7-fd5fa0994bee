from airflow.decorators import task, task_group
from airflow.exceptions import (
    AirflowFailException,
    AirflowSkipException,
    AirflowException,
)
from airflow.models import Variable, TaskInstance
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON>ook
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import json
import logging
from pygene.audience_api import NextGenAudienceAPI, AudienceType
from pygene.campaign_api import NextGenCampaignAPI
from pygene.target_api import NextGenTargetAPI, TargetFileType
import tempfile
from old_dags.toyota_automation.query import (
    find_ol_ids,
    filter_audince,
    find_next_month_ol_ids,
)

logger = logging.getLogger(__name__)

# Environment variables, default_args & credentials
env = Variable.get("environment", default_var="dev")
pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
client_id = pygene_creds["client_id"]
client_secret = pygene_creds["client_secret"]

if env == "prod":
    org_id = "L6sCSZvFbve54NpLg"
    table = "order_lines"

elif env == "dev":
    org_id = "7GJnjokWuRgTPuDTW"
    table = "dev_order_lines"


# Initiate task group
@task_group(group_id="create_and_upload_audience")
def toyota_task_group(item: dict):

    # Task 1 - Query and upload segment data
    @task()
    def query_upload_segment_data(item: dict, **kwargs):
        passed_values = kwargs["dag_run"].conf or {}

        # Get the logical date
        logical_date_str = passed_values.get("date") or kwargs[
            "data_interval_end"
        ].strftime("%Y-%m-%d")
        print(f"Logical Date: {logical_date_str}")
        logical_date = datetime.strptime(logical_date_str, "%Y-%m-%d")
        audience_date = datetime.strftime(logical_date, "%Y-%m-%d")

        # Get month abbreviation directly from audience_date
        month = logical_date.strftime("%b")
        next_month = (logical_date + relativedelta(months=1)).strftime("%b")

        logger.info(f"Date: {audience_date}, Month: {month}, Next Month: {next_month}")
        key, value = item["key"], item["value"]
        logger.info(f"Querying {key} -> {value}")

        # Environment specific processing
        try:
            trino_hook = TrinoHook(trino_conn_id="trino_conn")

            # Querying order line IDs for the current month
            query = find_ol_ids.format(
                table=table, region=key, seg=value, month=month, org_id=org_id
            )
            order_line_ids = trino_hook.get_records(sql=query)

            # Querying order line IDs for the next month
            query = find_next_month_ol_ids.format(
                table=table, region=key, seg=value, next_month=next_month, org_id=org_id
            )
            order_line_ids.extend(trino_hook.get_records(sql=query))

            order_line_ids = [row[0] for row in order_line_ids]

            # Raise exception if no order line IDs are found
            if len(order_line_ids) == 0:
                logger.warning("No OLs found in trino. Skipping upload.")
                raise AirflowSkipException(f"No data found")

            # Initialize the campaign API
            campaign_api = NextGenCampaignAPI(
                client_id=client_id,
                client_secret=client_secret,
                org_id=org_id,
                env=env,
            )

            audience_name_to_check = f"toyota_{key}_{value}_{audience_date}"

            # Check if audience is already attached to order line to prevent unnecessary uploads and processing
            order_lines_to_process = []
            for order_line_id in order_line_ids:
                order_line = campaign_api.get_order_line(order_line_id)
                audience_names = [audience.name for audience in order_line.audiences]

                if audience_name_to_check in audience_names:
                    logger.info(
                        f"Audience {audience_name_to_check} already attached to order line {order_line_id}. Skipping upload."
                    )
                    continue
                else:
                    order_lines_to_process.append(order_line_id)

            # Return None if no order lines to process - this will skip the task
            if order_lines_to_process == []:
                logger.warning("No data found. Skipping upload.")

                dict_result = {
                    "key": key,
                    "value": value,
                    "order_line_ids": order_line_ids,
                    "audience_date": audience_date,
                }

                return dict_result

            # Query segment data from Trino
            trino_hook = TrinoHook(trino_conn_id="trino_conn")
            query = filter_audince.format(
                env=env, region=key, seg=value, date=audience_date
            )
            df = trino_hook.get_pandas_df(sql=query)

            # Raise exception if no data is found
            if df.empty:
                logger.warning("No data found in trino. Skipping upload.")
                raise AirflowSkipException(f"No data found")

            # Processing using temporary file for upload into NextGen
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".csv", delete=True
            ) as temp_file:
                df.to_csv(temp_file.name, index=False)

                target_api = NextGenTargetAPI(
                    client_id=client_id,
                    client_secret=client_secret,
                    org_id=org_id,
                    env=env,
                )

                header_columns = [
                    {
                        "index": 0,
                        "value": "streetaddress",
                        "type": "address1",
                    },
                    {
                        "index": 1,
                        "value": "zipcode",
                        "type": "zip",
                    },
                ]

                target = target_api.upload_targets(
                    temp_file.name,
                    header_columns,
                    TargetFileType.ADDRESS,
                )

                target_id = target.id
                logger.info(f"Successfully uploaded target: {target_id}")
                temp_file.flush()

                dict_result = {
                    "key": key,
                    "value": value,
                    "target_id": target_id,
                    "order_line_ids": order_lines_to_process,
                    "audience_date": audience_date,
                }

                return dict_result

        # Handle exceptions
        except AirflowSkipException as e:
            logger.warning(f"Skipping task: {str(e)}")
            raise

        except Exception as e:
            raise AirflowException(
                f"Error processing {key} -> {value}. Error message: {e}"
            )

    target_data_output = query_upload_segment_data(item=item)

    # Task 2 - Upload audience
    @task()
    def upload_audience(target_data: dict):

        # Return None if no target data is found - this will skip the task
        if "target_id" not in target_data.keys():
            return target_data

        # Get variables from target data
        audience_date = target_data["audience_date"]
        target_id = target_data["target_id"]
        order_line_ids = target_data["order_line_ids"]
        key, value = target_data["key"], target_data["value"]
        logger.info(f"Uploading audience for {key} -> {value}. Target ID: {target_id}")

        try:
            # Initialize the audience API
            audience_api = NextGenAudienceAPI(
                client_id=client_id,
                client_secret=client_secret,
                org_id=org_id,
                env=env,
            )

            # Create the audience
            audience = audience_api.create_audience(
                name=f"toyota_{key}_{value}_{audience_date}",
                audience_type=AudienceType.ADDRESS,
                target_id=target_id,
            )

            # Get audience ID and name. Print success message.
            audience_id = audience.id
            audience_name = audience.name
            logger.info(f"Successfully uploaded audience: {audience_id}")

            # Return results as a dictionary to be passed into the next step.
            dict_result = {
                "key": key,
                "value": value,
                "audience_id": audience_id,
                "audience_name": audience_name,
                "order_line_ids": order_line_ids,
            }

            return dict_result

        # Handle exceptions
        except Exception as e:
            raise AirflowException(
                f"Error processing {key} -> {value}. Error message: {e}"
            )

    audience_data_output = upload_audience(target_data=target_data_output)

    # Task 3 - Query order line IDs and attach audience
    @task.virtualenv(
            requirements=[
                "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
                "pygene==1.0.52"
            ],
            system_site_packages=True,
            venv_cache_path="/tmp/airflow_venvs",
            retries=3,
            retry_delay=timedelta(seconds=30),
        )
    def get_ol_attach_audience(audience_data: dict):
        from pygene.campaign_api import NextGenCampaignAPI
        import logging
        from airflow.exceptions import (
            AirflowSkipException,
            AirflowException,
        )
        from airflow.models import Variable
        import json

        env = Variable.get("environment")
        pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
        client_id = pygene_creds["client_id"]
        client_secret = pygene_creds["client_secret"]

        if env == "prod":
            org_id = "L6sCSZvFbve54NpLg"

        elif env == "dev":
            org_id = "7GJnjokWuRgTPuDTW"

        logger = logging.getLogger(__name__)

        # Return None if no audience data is found - this will skip the task
        if "audience_id" not in audience_data.keys():
            return audience_data

        # Get variables from audience data
        audience_id = audience_data["audience_id"]
        order_line_ids = audience_data["order_line_ids"]
        key, value = audience_data["key"], audience_data["value"]
        logger.info(f"Querying order line IDs for {key} -> {value}")

        try:
            # Raise exception if no order line IDs are found
            if len(order_line_ids) == 0:
                logger.warning("No data found. Skipping upload.")
                raise AirflowSkipException(f"No data found")

            # Initialize the campaign API
            campaign_api = NextGenCampaignAPI(
                client_id=client_id,
                client_secret=client_secret,
                org_id=org_id,
                env=env,
            )

            # Add audience to order lines. Check if audience is already attached to order line to prevent unnecessary uploads and processing.
            for order_line_id in order_line_ids:
                order_line = campaign_api.get_order_line(order_line_id)

                order_line_audiences_ids = [
                    audience.id for audience in order_line.audiences
                ]

                if audience_id in order_line_audiences_ids:
                    logger.info(
                        f"Audience {audience_id} already attached to order line {order_line_id}. Skipping attach."
                    )
                    continue
                else:
                # Check for lock status
                    lock_status = False
                    if order_line.locked:
                        logger.warning(
                            f"Order line {order_line_id} is locked. Unlocking ."
                        )
                        lock_status = True
                        campaign_api.unlock_ol(order_line_id)
                        logger.info(
                            f"Order line {order_line_id} unlocked successfully."
                        )

                    logger.info(
                        f"Attaching audience {audience_id} to order line {order_line_id}"
                    )
                    campaign_api.batch_add_audiences(order_line_id, [audience_id])

                    if lock_status:
                        logger.info(
                            f"Locking order line {order_line_id} after attaching audience."
                        )
                        campaign_api.lock_ol(order_line_id)
                        logger.info(
                            f"Order line {order_line_id} locked successfully."
                        )

            logger.info(
                f"Successfully attached audience to order lines: {order_line_ids}"
            )

            return audience_data

        # Handle exceptions
        except AirflowSkipException as e:
            logger.warning(f"Skipping task: {str(e)}")
            raise

        except Exception as e:
            # this *will* be retried up to 3×
            raise AirflowException(f"Error processing {key}->{value}: {e}")

    attach_audience_output = get_ol_attach_audience(audience_data=audience_data_output)

    # Task 4 - Deatch audience that is more than 30 days old
    @task.virtualenv(
            requirements=[
                "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
                "pygene==1.0.52"
            ],
            system_site_packages=True,
            venv_cache_path="/tmp/airflow_venvs",
            retries=3,
            retry_delay=timedelta(seconds=30),
        )
    def get_ol_detach_audience(audience_data: dict):
        from pygene.campaign_api import NextGenCampaignAPI
        import logging
        from airflow.exceptions import (
            AirflowException,
        )
        from airflow.models import Variable
        import json
        from datetime import datetime, timedelta

        env = Variable.get("environment")
        pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
        client_id = pygene_creds["client_id"]
        client_secret = pygene_creds["client_secret"]

        if env == "prod":
            org_id = "L6sCSZvFbve54NpLg"
        
        elif env == "dev":
            org_id = "7GJnjokWuRgTPuDTW"

        logger = logging.getLogger(__name__)

        # Return None if no audience data is found - this will skip the task
        if "audience_id" not in audience_data.keys():
            return audience_data

        # Get variables from audience data
        order_line_ids = audience_data["order_line_ids"]
        key, value = audience_data["key"], audience_data["value"]

        # Not going to check for order line IDs length because previous task skips if no data is found anyways
        try:
            # Initialize the campaign API
            campaign_api = NextGenCampaignAPI(
                client_id=client_id,
                client_secret=client_secret,
                org_id=org_id,
                env=env,
            )

            # Sort and detach audiences by order line that are more than 30 days old. If less than 30 audiences, skip detach.
            for order_line_id in order_line_ids:
                order_line = campaign_api.get_order_line(order_line_id)
                order_line_audiences = [
                    (audience.id, audience.name) for audience in order_line.audiences
                ]

                sorted_audiences = sorted(
                    order_line_audiences,
                    key=lambda x: datetime.strptime(x[1][-10:], "%Y-%m-%d"),
                    reverse=True,
                )

                if len(sorted_audiences) <= 30:
                    logger.info(
                        f"Order line {order_line_id} has less than 30 audiences. Skipping detach."
                    )
                    continue
                else:
                    # Check for lock status
                    lock_status = False
                    if order_line.locked:
                        logger.warning(
                            f"Order line {order_line_id} is locked. Unlocking ."
                        )
                        lock_status = True
                        campaign_api.unlock_ol(order_line_id)
                        logger.info(
                            f"Order line {order_line_id} unlocked successfully."
                        )

                    audiences_to_detach = list(
                        list(zip(*sorted_audiences))[0][30:]
                    )  # This is returning a tuple, so we need to convert it to a list
                    logger.info(
                        f"Detaching {audiences_to_detach} from order line {order_line_id}"
                    )

                    campaign_api.batch_remove_audiences(
                        order_line_id, audiences_to_detach
                    )

                    if lock_status:
                        logger.info(
                            f"Locking order line {order_line_id} after detaching audience."
                        )
                        campaign_api.lock_ol(order_line_id)
                        logger.info(
                            f"Order line {order_line_id} locked successfully."
                        )


                    logger.info(
                        f"Successfully detached audiences from order lines: {order_line_ids}"
                    )

            return audience_data

        # Handle exceptions
        except Exception as e:
            raise AirflowException(
                f"Error processing {key} -> {value}. Error message: {e}"
            )

    detach_audience_output = get_ol_detach_audience(
        audience_data=attach_audience_output
    )

    @task()
    def push_to_xcom(detach_audience_output: dict, **kwargs):
            
        # Push variables to XCom
            ti = kwargs["ti"]
            ti.xcom_push(key="return_value", value=detach_audience_output)

    push_to_xcom_output = push_to_xcom(detach_audience_output=detach_audience_output)

    (
        target_data_output
        >> audience_data_output
        >> attach_audience_output
        >> detach_audience_output
        >> push_to_xcom_output
    )
