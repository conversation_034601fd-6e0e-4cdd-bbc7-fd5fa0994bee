from airflow.decorators import task
from airflow.exceptions import AirflowFailException, AirflowSkipException
from airflow.models import Variable, TaskInstance
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.providers.amazon.aws.transfers.sftp_to_s3 import SFTPToS3Operator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from datetime import datetime, timedelta
from etdag import ETDAG
import logging
from old_dags.toyota_automation.toyota_task_group import toyota_task_group

logger = logging.getLogger(__name__)

docs = """
# Toyota Automation - Workflow DAG

## Overview
This DAG is responsible for executing the **Toyota Automation job workflow**, which performs the transfer of SFTP files, 
syncs audience data, and generates audience segments that get attached to order lines. If needed, it performs detaching of audiences.
This DAG follows a structured approach to ensure data integrity and efficient processing. 

## Execution & Triggering
- This DAG **runs 9 AM or 10 AM every day** depending on daylight savings time.
- The DAG can be triggered manually with specific parameters for **region**, **segment_group**, and **date**.
- If no parameters are provided, the DAG defaults to a set of predefined segments: current date, all regions, and all segments.
- Only a single string can be passed for each parameter.
- A specific **region** parameter will run all segment groups for that region.
- A **segment_group** parameter will require a **region** parameter for that segment group.


## Concurrency & Parallelism
- The task group runs in chunks of 10, allowing for parallel processing of segments.
- The tasks within the group will have some concurrency. The first 10 chunk will make it to the next task for processing while the next 10 will begin the previous task.

## Stages of Execution
1. **Get Segment Mapping:** Segment mapping provided by the client helps in retrieving relevant data from the SFTP and Trino.
2. **Get SFTP Path:** Retrieves SFTP path and connection details based on the environment.
3. **Transfer Files:** Checks for the existence of the file in the SFTP directory and transfers it to S3.
4. **Sync Audience File:** Syncs partition metadata for the observations table in Trino.
5. **Toyota Task Group:** Executes the task group for querying, generating, attaching and detaching audiences.
6. **Collect All IDs:** Collects all IDs from the task group and pushes them back to XCom.

## Retry & Failure Handling
- Each stage is **safe to retry** independently, allowing partial reprocessing without impacting overall data integrity.
- A strict logic is applied to prevent duplicate or unnecessary processing.
- Always investigate the error message.
- A **single retry** is allowed for each task with a delay of 1 minute.

## Owners
- **BP** & **Dani**

"""

# Environment variables, default_args & credentials
env = Variable.get("environment", default_var="dev")
default_args = {
    "owner": "bp",
    "max_active_tis_per_dag": 10,
    "retries": 3,
    "retry_delay": timedelta(seconds=30),
}


@task()
def get_segment_mapping(**context):
    """Retrieves segment mapping based on passed values or default list."""
    passed_values = context["dag_run"].conf or {}

    if not passed_values.get("region") and not passed_values.get("segment_group"):
        # Default segment mapping
        segment_map = {
            "BOS": ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"],
            "CIN": ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"],
            "DEN": ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"],
            "LA": ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"],
            "POR": ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"],
        }
        segments = [
            {"key": k, "value": v} for k, values in segment_map.items() for v in values
        ]
    elif "region" in passed_values and passed_values.get("segment_group") == None:
        selected_region = passed_values["region"]
        default_segments = ["1a", "1b", "2a", "2b", "2c", "2d", "3a", "3b", "4a", "4b", "5c"]
        segments = [{"key": selected_region, "value": v} for v in default_segments]

    else:
        # If values are passed, use them directly
        segments = [
            {"key": passed_values["region"], "value": passed_values["segment_group"]}
        ]
    print(f"Segments: {segments}")
    return segments


@task()
def get_sftp_path(**kwargs):
    passed_values = kwargs["dag_run"].conf or {}

    # Determine the logical date
    logical_date = (
        passed_values.get("date") or kwargs["data_interval_end"].date().isoformat()
    )

    if env == "dev":
        sftp_path = f"/incoming/eltoro_toyota/ad-request-{logical_date}.csv"
        sftp_conn_id = "eltoro_sftp_id"
    else:
        sftp_path = f"/et-pickup/ad-request-{logical_date}.csv"
        sftp_conn_id = "groupg-reporting"

    return {
        "sftp_path": sftp_path,
        "sftp_dir": "/".join(sftp_path.split("/")[:-1]),
        "file_name": sftp_path.split("/")[-1],
        "logical_date": logical_date,
        "sftp_conn_id": sftp_conn_id,
    }


@task()
def transfer_files(paths):
    sftp_hook = SFTPHook(ssh_conn_id=paths["sftp_conn_id"])
    sftp_dir = paths["sftp_dir"]

    files_in_dir = sftp_hook.list_directory(sftp_dir)

    file_to_check = paths["file_name"]
    if file_to_check in files_in_dir:
        print(f"File {file_to_check} exists in {sftp_dir}!")
    else:
        print(f"File {file_to_check} NOT found in {sftp_dir}.")
        raise AirflowSkipException(f"File {file_to_check} not found in {sftp_dir}.")
    try:
        sftp_to_s3_job = SFTPToS3Operator(
            task_id="sftp_to_s3_job",
            sftp_conn_id=paths["sftp_conn_id"],
            sftp_path=paths["sftp_path"],
            s3_conn_id="s3_conn",
            s3_bucket="vr-timestamp",
            s3_key=f"bi_sources/toyota_2024/{env}/targeted_audience/date={paths['logical_date']}/ad-request-{paths['logical_date']}.csv",
        ).execute(context={})
    except Exception as e:
        raise AirflowFailException(e)

    # Need to check for weekly mail files
    s3_hook = S3Hook(aws_conn_id="aws_default")
    s3_bucket = "vr-timestamp"
    prefix = f"bi_sources/toyota_2024/{env}/weekly_mail/"

    list_of_files = s3_hook.list_keys(bucket_name=s3_bucket, prefix=prefix)
    list_of_files.remove(prefix)
    list_of_files = [f.split("/")[-1] for f in list_of_files]
    logger.info(f"List of files in S3: {list_of_files}")

    weekly_mail_files = [f for f in files_in_dir if "weekly-mail" in f]
    weekly_mail_new = set(weekly_mail_files) - set(list_of_files)
    if len(weekly_mail_new) == 0:
        return "No new weekly mail files to transfer."
    else:
        logger.info(f"New weekly mail files to transfer: {weekly_mail_new}")
    try:
        for file in weekly_mail_new:
            sftp_to_s3_job = SFTPToS3Operator(
                task_id=f"sftp_to_s3_job_weekly",
                sftp_conn_id=paths["sftp_conn_id"],
                sftp_path=f"{sftp_dir}/{file}",
                s3_conn_id="aws_default",
                s3_bucket=s3_bucket,
                s3_key=prefix + file,
            ).execute(context={})
            logger.info(f"Transferred {file} to S3.")
    except Exception as e:
        raise AirflowFailException(e)


@task()
def weekly_mail_to_csv(**kwargs):
    """Query weekly mail files from Trino and drop CSV in S3 path."""

    ti: TaskInstance = kwargs["ti"]
    if (
        ti.xcom_pull(task_ids="transfer_files", key="return_value")
        == "No new weekly mail files to transfer."
    ):
        raise AirflowSkipException(
            "No new weekly mail files to process. Skipping CSV generation."
        )

    else:
        try:
            logger.info(f"Weekly mail files to CSV")

            trino_hook = TrinoHook(trino_conn_id="trino_conn")
            select_query = (
                f"""SELECT * FROM s3.external_auto_intender.{env}_weekly_mail"""
            )

            df = trino_hook.get_pandas_df(sql=select_query)
            df.to_csv(
                path_or_buf=f"s3://vr-timestamp/bi_sources/toyota_dashboard_datasets/weekly_mail_{env}.csv.gz",
                index=False,
                header=True,
                compression="gzip",
                encoding="utf-8",
            )

            logger.info(f"Weekly mail files to CSV successful")

        except Exception as e:
            error_message = f"Failed to query weekly mail files, Error: {e}"
            logger.error(error_message)
            raise AirflowFailException(f"Query failed: {e}")


@task(trigger_rule="none_failed")
def sync_audience_file():
    """Sync partition metadata for observations table in Trino."""
    try:
        logger.info(f"Syncing table")

        trino_hook = TrinoHook(trino_conn_id="trino_conn")
        sync_query = f"CALL s3.system.sync_partition_metadata('bronze_auto_intender', '{env}_toyota_daily_audience', 'ADD')"

        trino_hook.run(sync_query)
        logger.info(f"Sync successful ")

    except Exception as e:
        error_message = f"Sync failed for '{env}_toyota_daily_audience',Error: {e}"
        logger.error(error_message)
        raise AirflowFailException(f"Sync failed: {e}")


@task(trigger_rule="none_failed")
def collect_all_ids(item: dict, **kwargs):
    ti: TaskInstance = kwargs["ti"]

    # Pull XCom values and force evaluation into a list
    try:
        results = list(
            ti.xcom_pull(
                task_ids="create_and_upload_audience.push_to_xcom",
                key="return_value",
            )
        )

        processed_ids = []
        processed_regions = []
        if results:
            for result in results:
                if (
                    result
                    and isinstance(result, dict)
                    and all(k in result for k in ["order_line_ids", "key", "value"])
                ):
                    processed_ids.extend(result["order_line_ids"])
                    processed_regions.append(f"{result['key']}_{result['value']}")
                else:
                    logger.warning(f"Skipped task result or invalid format: {result}")

        all_regions = [segment["key"] + "_" + segment["value"] for segment in item]
        unprocessed_segments = set(all_regions) - set(processed_regions)

        logger.info(f"Processed IDs: {processed_ids}")
        logger.info(f"Processed Regions: {processed_regions}")
        logger.info(f"Unprocessed Segments: {unprocessed_segments}")

        return processed_ids  # Push back to XCom
    except TypeError as e:
        raise AirflowSkipException(f"No IDs found: {e}")


@task()
def toyota_deliverable_metrics():
    """Drop Deliverable metrics to CSV."""

    try:
        logger.info(f"Deliverable metrics to CSV")

        trino_hook = TrinoHook(trino_conn_id="trino_conn")
        select_query = (
            f"SELECT * FROM s3.bronze_auto_intender.prod_toyota_deliverable_metrics"
        )

        df = trino_hook.get_pandas_df(sql=select_query)
        df.to_csv(
            path_or_buf=f"s3://vr-timestamp/bi_sources/toyota_deliverable_metrics/toyota_deliverable_metrics.csv",
            index=False,
            header=True,
        )

        logger.info(f"Deliverable metrics files to CSV successful")

    except Exception as e:
        error_message = f"Failed to query Deliverable metrics files, Error: {e}"
        logger.error(error_message)
        raise AirflowFailException(f"Query failed: {e}")


with ETDAG(
    dag_id="toyota_automation",
    schedule="0 15 * * *",
    default_args=default_args,
    catchup=False,
    description="Toyota Automation DAG - performs SFTP transfer and audience changes",
    start_date=datetime(2025, 3, 18),
    et_failure_msg=True,
    tags=["toyota", "automation", "DND", "Prod"],
    params={
        "region": "",
        "segment_group": "",
        "date": "",
    },
) as dag:

    segments = get_segment_mapping()
    paths = get_sftp_path()
    path_task = transfer_files(paths)
    weekly_task = weekly_mail_to_csv()
    sync = sync_audience_file()
    toy = toyota_task_group.expand(item=segments)
    id_collection = collect_all_ids(item=segments)
    toyota_deliverable_metrics_task = toyota_deliverable_metrics()

    segments >> paths >> path_task >> weekly_task >> sync >> toy >> id_collection
    toyota_deliverable_metrics_task
