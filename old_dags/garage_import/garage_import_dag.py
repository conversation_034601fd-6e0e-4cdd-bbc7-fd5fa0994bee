import gzip
import pandas as pd
from io import BytesIO
from etdag import ETDAG
from airflow.operators.python_operator import Branch<PERSON>ythonOperator, ShortCircuitOperator
from airflow.operators.python_operator import PythonOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from datetime import datetime, timedelta
from old_dags.alert_if_skipped_operator import AlertIfSkippedOperator
import pyarrow as pa
import pyarrow.parquet as pq
from old_dags.garage_import.config import data_sets
from starburst_geocoder_operator import StarburstGeocoderOperator

docs = """
DAG for Extracting and Loading SP Global Garage Data

This Airflow DAG, `garage_data_import`, is designed to extract and process data from SP Global's Garage dataset and 
load it into an external schema for further processing. The DAG performs the following tasks:

1. **Data Extraction and Processing**:
   - **Find Matching Files**: Identifies gzipped files from an S3 bucket that match a filename template for the 
     current data interval.
   - **Process and Stage Files**: Reads, processes, and stages the data files into Parquet format, and uploads them 
     to S3. Creates necessary Hive tables if they do not exist.

2. **Data Synchronization**:
   - **Check PII Files Processed**: Verifies if PII files were processed for the current data interval.
   - **Run Starburst Geocoder**: Updates the garage PII bridge table using Starburst geocoding operations.

3. **Alerting**:
   - **AlertIfSkippedOperator**: Sends an alert to Slack if the `update_garage_pii_bridge_task` did not run in the past 
     specified number of days.

4. **Task Dependencies**:
   - **Dataset Processing Tasks**: Executes tasks for processing each dataset, dependent on the successful completion 
     of the previous task.
   - **Check PII Files Processed Task**: Determines if new files have arrived.
   - **Branch Task**: Decides whether to run the geocoder task or send an alert based on the presence of new files.
   - **Update Garage PII Bridge Task**: Executes the Starburst geocoder if new files are detected.
   - **Alert Task**: Sends an alert if no new files are detected.

### Configuration:
- **`BUCKET_NAME`**: S3 bucket where data files are stored.
- **`STAGING_PATH`**: Path in S3 where processed data files are staged.
- **`EXTERNAL_SCHEMA`**: External schema name for Trino table creation.
- **`EXPECTED_DELIVERY_WINDOW`**: The expected window of time for data delivery.

### Notes:
- Dag is dependent on data_interval_start.  Please clear failed tasks.  Don't just start a new one.
- Slack alerts are configured to notify only if it is suspected that we have not gotten new files from the data provider.
"""


default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'start_date': datetime(2024, 8, 25),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
}

BUCKET_NAME = "eltoro-sp-global"
STAGING_PATH = "staging"
EXTERNAL_SCHEMA = "external_garage"
EXPECTED_DELIVERY_WINDOW = 60 #days
CHUNK_SIZE = 1_500_000
s3_hook = S3Hook(aws_conn_id="aws_conn")
trino_hook = TrinoHook(trino_conn_id="starburst")


def hive_table_create_statement(dataset, df):
    columns_sql = ",\n".join([f'"{col}" VARCHAR' for col in df.columns])

    create_table_statement = f"""
    CREATE TABLE IF NOT EXISTS s3.{EXTERNAL_SCHEMA}.{dataset}(
        {columns_sql},
        transfer_date date
    )
    WITH(
    partitioned_by=ARRAY['transfer_date'],
    format='PARQUET',
    external_location='s3://{BUCKET_NAME}/{STAGING_PATH}/{dataset}/'
    )
    """
    trino_hook.run(create_table_statement)
    return


def find_matching_files(filename_template, data_interval_start):
    matching_files = []
    s3_client = s3_hook.get_conn()
    paginator = s3_client.get_paginator("list_objects_v2")
    
    date_str = data_interval_start.strftime("%Y%m%d")
    for page in paginator.paginate(Bucket=BUCKET_NAME, Prefix=filename_template):
        if "Contents" in page:
            for obj in page["Contents"]:
                key = obj["Key"]
                last_modified = obj["LastModified"].strftime("%Y%m%d")

                if last_modified == date_str and key.startswith(filename_template):
                    matching_files.append(key)
                    
    print(matching_files)
    return matching_files


def process_and_stage_file(dataset, key, data_interval_start):
    s3_client = s3_hook.get_conn()
    s3_object = s3_client.get_object(Bucket=BUCKET_NAME, Key=key)
    gzipped_content = s3_object['Body'].read()

    with gzip.GzipFile(fileobj=BytesIO(gzipped_content)) as gzipped_file:
        chunk_iter = pd.read_csv(gzipped_file, delimiter='|', dtype=str, chunksize=CHUNK_SIZE)

        for chunk_idx, chunk in enumerate(chunk_iter):
            if chunk_idx == 0:
                hive_table_create_statement(dataset, chunk)
            table = pa.Table.from_pandas(chunk)
            parquet_buffer = BytesIO()

            pq.write_table(table, parquet_buffer)
            parquet_buffer.seek(0)

            output_path = f"{STAGING_PATH}/{dataset}/transfer_date={data_interval_start.strftime('%Y-%m-%d')}"
            s3_client.put_object(
                Bucket=BUCKET_NAME,
                Key=f"{output_path}/{key.split('/')[-1].replace('.txt.gz', f'_chunk_{chunk_idx}.parquet')}",
                Body=parquet_buffer.getvalue()
            )

            parquet_buffer.close()


def process_dataset(dataset, filename_template, **kwargs):
    data_interval_start = kwargs['data_interval_start']
    matching_files = find_matching_files(filename_template, data_interval_start)

    processed_files = 0
    for file_key in matching_files:
        process_and_stage_file(dataset, file_key, data_interval_start)
        processed_files += 1

    if processed_files > 0:
        trino_hook.run(f"CALL s3.system.sync_partition_metadata('{EXTERNAL_SCHEMA}', '{dataset}', 'ADD')")
    else:
        print(f"No new files for {dataset} of data_interval_start of {data_interval_start}")
    return processed_files > 0


def check_pii_table_files_processed(**kwargs):
    filename_template = data_sets["sp_audience_pii"]['filename_template']
    data_interval_start = kwargs['data_interval_start']
    matching_files = find_matching_files(filename_template, data_interval_start)
    print(matching_files)
    return len(matching_files) > 0


def run_starburst_geocoder(data_interval_start, **kwargs):
    update_garage_pii_bridge = StarburstGeocoderOperator(
        task_id="update_garage_pii_bridge",
        source_table_name='"s3"."external_garage"."sp_audience_pii"',
        source_row_identifier_column_name="polkid",
        address1_column_name="addr_line_1",
        zipcode_column_name="zip_cd",
        bridge_table_name='"olympus"."bronze_garage"."sp_garage_address_bridge_tbl"',
        source_where_clause=f"transfer_date = DATE('{data_interval_start[0: 10]}')",
        geocoder_columns=[
            "etHashV1",
            "etHashV2",
            "addressLine",
            "city",
            "state",
            "zipcode",
            "rdi",
            "matchCode",
        ]
    )
    update_garage_pii_bridge.execute(context={})


def if_new_files_arrived(**kwargs):
    ti = kwargs['ti']
    task_result = ti.xcom_pull(task_ids='check_if_pii_files_processed_today')
    if task_result:
        return 'update_garage_pii_bridge_task'
    else:
        return 'alert_if_task_not_run'


with ETDAG(
    dag_id="garage_data_import",
    description="Extract and load SP Global Garage Data",
    schedule_interval="@daily",
    catchup=True,
    tags=["startburst", "team:DND"],
    default_args=default_args,
    concurrency=1,
    max_active_tasks = 1
) as dag:
    dag.doc_md = docs

    dataset_processing_tasks = []
    for dataset, config in data_sets.items():
        filename_template = config['filename_template']

        process_dataset_task = PythonOperator(
            task_id=f"process_{dataset}",
            python_callable=process_dataset,
            op_kwargs={
                "dataset": dataset,
                "filename_template": filename_template
            },
        )
        dataset_processing_tasks.append(process_dataset_task)

    check_pii_table_files_processed_task = PythonOperator(
        task_id='check_if_pii_files_processed_today',
        python_callable=check_pii_table_files_processed,
        provide_context=True,
        do_xcom_push=True
    )

    branch_task = BranchPythonOperator(
        task_id='if_new_files_arrived',
        python_callable=if_new_files_arrived,
        provide_context=True
    )

    update_garage_pii_bridge_task = PythonOperator(
        task_id="update_garage_pii_bridge_task",
        python_callable=run_starburst_geocoder,
        op_kwargs={
            "data_interval_start": '{{ data_interval_start }}'
        },
        provide_context=True
    )

    alert_task = AlertIfSkippedOperator(
        task_id='alert_if_task_not_run',
        inspected_task_id='update_garage_pii_bridge_task',
        days=60,
        msg=f""" New data has not been imported from this provider for the last {EXPECTED_DELIVERY_WINDOW} days (contract is every 6 weeks) Review dag, previous runs and contents of bucket `{BUCKET_NAME}`"""
    )

    for task in dataset_processing_tasks:
        task >> check_pii_table_files_processed_task

    check_pii_table_files_processed_task >> branch_task

    branch_task >> update_garage_pii_bridge_task
    branch_task >> alert_task
