from pandera import Column, DataFrameSchema
from pandera.engines.pandas_engine import Date, Category, BOOL, DateTime

# https://pandera.readthedocs.io/en/stable/reference/dtypes.html#pandas-dtypes

# column names
ETHASH = "ethash"
RDI = "rdi"
REF_ID = "ref_id"
REQUEST_ID = "request_id"
REPORT_DATE = "report_date"
HOUSEHOLD_DATA_AVAILABLE = "household_data_available"
HOUSEHOLD_MATCHED = "household_matched"
COUNT_UNIQ_IFAS = "count_uniq_ifas"
COUNT_UNIQ_IFAS_SEEN_AT_NIGHT = "count_uniq_ifas_seen_at_night"
SUM_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "SUM(devices.uniq_day_count_last_14_days)"
SUM_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "SUM(devices.uniq_day_count_last_21_days)"
SUM_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "SUM(devices.uniq_day_count_last_30_days)"
SUM_DEVICES_UNIQ_HR_COUNT_14_DAYS = "SUM(devices.uniq_hr_count_14_days)"
SUM_DEVICES_UNIQ_HR_COUNT_21_DAYS = "SUM(devices.uniq_hr_count_21_days)"
SUM_DEVICES_UNIQ_HR_COUNT_30_DAYS = "SUM(devices.uniq_hr_count_30_days)"
MIN_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "MIN(devices.uniq_day_count_last_14_days)"
MIN_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "MIN(devices.uniq_day_count_last_21_days)"
MIN_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "MIN(devices.uniq_day_count_last_30_days)"
MIN_DEVICES_UNIQ_HR_COUNT_14_DAYS = "MIN(devices.uniq_hr_count_14_days)"
MIN_DEVICES_UNIQ_HR_COUNT_21_DAYS = "MIN(devices.uniq_hr_count_21_days)"
MIN_DEVICES_UNIQ_HR_COUNT_30_DAYS = "MIN(devices.uniq_hr_count_30_days)"
MAX_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "MAX(devices.uniq_day_count_last_14_days)"
MAX_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "MAX(devices.uniq_day_count_last_21_days)"
MAX_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "MAX(devices.uniq_day_count_last_30_days)"
MAX_DEVICES_UNIQ_HR_COUNT_14_DAYS = "MAX(devices.uniq_hr_count_14_days)"
MAX_DEVICES_UNIQ_HR_COUNT_21_DAYS = "MAX(devices.uniq_hr_count_21_days)"
MAX_DEVICES_UNIQ_HR_COUNT_30_DAYS = "MAX(devices.uniq_hr_count_30_days)"
MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "MEAN(devices.uniq_day_count_last_14_days)"
MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "MEAN(devices.uniq_day_count_last_21_days)"
MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "MEAN(devices.uniq_day_count_last_30_days)"
MEAN_DEVICES_UNIQ_HR_COUNT_14_DAYS = "MEAN(devices.uniq_hr_count_14_days)"
MEAN_DEVICES_UNIQ_HR_COUNT_21_DAYS = "MEAN(devices.uniq_hr_count_21_days)"
MEAN_DEVICES_UNIQ_HR_COUNT_30_DAYS = "MEAN(devices.uniq_hr_count_30_days)"
STD_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "STD(devices.uniq_day_count_last_14_days)"
STD_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "STD(devices.uniq_day_count_last_21_days)"
STD_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "STD(devices.uniq_day_count_last_30_days)"
STD_DEVICES_UNIQ_HR_COUNT_14_DAYS = "STD(devices.uniq_hr_count_14_days)"
STD_DEVICES_UNIQ_HR_COUNT_21_DAYS = "STD(devices.uniq_hr_count_21_days)"
STD_DEVICES_UNIQ_HR_COUNT_30_DAYS = "STD(devices.uniq_hr_count_30_days)"
SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS = "SKEW(devices.uniq_day_count_last_14_days)"
SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS = "SKEW(devices.uniq_day_count_last_21_days)"
SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS = "SKEW(devices.uniq_day_count_last_30_days)"
SKEW_DEVICES_UNIQ_HR_COUNT_14_DAYS = "SKEW(devices.uniq_hr_count_14_days)"
SKEW_DEVICES_UNIQ_HR_COUNT_21_DAYS = "SKEW(devices.uniq_hr_count_21_days)"
SKEW_DEVICES_UNIQ_HR_COUNT_30_DAYS = "SKEW(devices.uniq_hr_count_30_days)"
CENTROID_DISTANCE_MAX = "centroid_distance_max"
CENTROID_DISTANCE_MIN = "centroid_distance_min"
CENTROID_DISTANCE_MEAN = "centroid_distance_mean"
CENTROID_DISTANCE_STD = "centroid_distance_std"
CENTROID_DISTANCE_MEDIAN = "centroid_distance_median"
OCCUPANCY_PROB = "occupancy_prob"
OCCUPANCY_PRED = "occupancy_pred"

occupancy_30d_schema = DataFrameSchema(
    columns={
        REQUEST_ID: Column(str, required=True),
        ETHASH: Column(str, nullable=True),
        REF_ID: Column(str),
        RDI: Column(Category, nullable=True),
        REPORT_DATE: Column(Date),
        HOUSEHOLD_DATA_AVAILABLE: Column(BOOL, nullable=True),
        HOUSEHOLD_MATCHED :Column(BOOL, nullable=True),
        COUNT_UNIQ_IFAS: Column(int),
        COUNT_UNIQ_IFAS_SEEN_AT_NIGHT: Column(int),
        SUM_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(int),
        SUM_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(int),
        SUM_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(int),
        SUM_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(int),
        SUM_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(int),
        SUM_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(int),
        MIN_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(int),
        MIN_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(int),
        MIN_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(int),
        MIN_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(int),
        MIN_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(int),
        MIN_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(int),
        MAX_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(int),
        MAX_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(int),
        MAX_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(int),
        MAX_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(int),
        MAX_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(int),
        MAX_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(int),
        MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(float),
        MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(float),
        MEAN_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(float),
        MEAN_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(float),
        MEAN_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(float),
        MEAN_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(float),
        STD_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(float),
        STD_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(float),
        STD_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(float),
        STD_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(float),
        STD_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(float),
        STD_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_14_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_21_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_DAY_COUNT_LAST_30_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_HR_COUNT_14_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_HR_COUNT_21_DAYS: Column(float),
        SKEW_DEVICES_UNIQ_HR_COUNT_30_DAYS: Column(float),
        CENTROID_DISTANCE_MAX: Column(float),
        CENTROID_DISTANCE_MIN: Column(float),
        CENTROID_DISTANCE_MEAN: Column(float),
        CENTROID_DISTANCE_STD: Column(float),
        CENTROID_DISTANCE_MEDIAN: Column(float),
        OCCUPANCY_PROB: Column(float, nullable=True),
        OCCUPANCY_PRED: Column(str, nullable=True)
    },
    add_missing_columns=True,
    coerce=True,
    strict="filter",
)
