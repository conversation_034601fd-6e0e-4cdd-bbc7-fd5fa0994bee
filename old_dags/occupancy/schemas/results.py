from pandera import Column, DataFrameSchema
from pandera.engines.pandas_engine import Date, Category, BOOL, DateTime


# column names
ADDRESS1 = "address1"
ZIP = "zip"
ETHASH = "ethash"
REF_ID = "ref_id"
REQUEST_ID = "request_id"
REPORT_DATE = "report_date"
OCCUPANCY_PROB_0_TO_14 = "0_to_14_occupancy_prob"
OCCUPANCY_PROB_0_TO_30 = "0_to_30_occupancy_prob"
OCCUPANCY_PROB_30_TO_60 = "30_to_60_occupancy_prob"
OCCUPANCY_PROB_60_TO_90 = "60_to_90_occupancy_prob"
OCCUPANCY_PRED_0_TO_14 = "0_to_14_occupancy_pred"
OCCUPANCY_PRED_0_TO_30 = "0_to_30_occupancy_pred"
OCCUPANCY_PRED_30_TO_60 = "30_to_60_occupancy_pred"
OCCUPANCY_PRED_60_TO_90 = "60_to_90_occupancy_pred"
VISITED_0_TO_14 = "0_to_14_visited"
VISITED_0_TO_30 = "0_to_30_visited"
VISITED_30_TO_60 = "30_to_60_visited"
VISITED_60_TO_90 = "60_to_90_visited"
OCCUPANCY_CODE = "occupancy_code"


result_columns=[
    OCCUPANCY_PROB_0_TO_14,
    OCCUPANCY_PROB_0_TO_30,
    OCCUPANCY_PROB_30_TO_60,
    OCCUPANCY_PROB_60_TO_90,
    OCCUPANCY_PRED_0_TO_14,
    OCCUPANCY_PRED_0_TO_30,
    OCCUPANCY_PRED_30_TO_60,
    OCCUPANCY_PRED_60_TO_90,
    VISITED_0_TO_14,
    VISITED_0_TO_30,
    VISITED_30_TO_60,
    VISITED_60_TO_90,
    OCCUPANCY_CODE
]

# the pandas list type seems to be bugged and the pyarrow list is absolutely bugged
# https://github.com/pandas-dev/pandas/issues/57411
result_schema = DataFrameSchema(
    columns={
        REQUEST_ID: Column(str, required=True),
        ADDRESS1 : Column(str),
        ZIP : Column(str),
        ETHASH: Column(str, nullable=True),
        REF_ID: Column(str),
        REPORT_DATE: Column(Date),
        OCCUPANCY_PROB_0_TO_14: Column(float, nullable=True),
        OCCUPANCY_PROB_0_TO_30: Column(float, nullable=True),
        OCCUPANCY_PROB_30_TO_60: Column(float, nullable=True),
        OCCUPANCY_PROB_60_TO_90: Column(float, nullable=True),
        OCCUPANCY_PRED_0_TO_14: Column(str, nullable=True),
        OCCUPANCY_PRED_0_TO_30: Column(str, nullable=True),
        OCCUPANCY_PRED_30_TO_60: Column(str, nullable=True),
        OCCUPANCY_PRED_60_TO_90: Column(str, nullable=True),
        VISITED_0_TO_14: Column(str, nullable=True),
        VISITED_0_TO_30: Column(str, nullable=True),
        VISITED_30_TO_60: Column(str, nullable=True),
        VISITED_60_TO_90: Column(str, nullable=True),
        OCCUPANCY_CODE: Column(str, nullable=True),
    },
    add_missing_columns=True,
    coerce=True,
    strict="filter",
)
