from datascience_implement_cache import DIC
from pandera import Column, DataFrameSchema
from pandera.engines.pandas_engine import Date, Category, BOOL, DateTime
from pandera.engines.numpy_engine import Object
from enum import Enum
# https://pandera.readthedocs.io/en/stable/reference/dtypes.html#pandas-dtypes

# column names
ETHASH = "ethash"
REF_ID = "ref_id"
REQUEST_ID = "request_id"
REPORT_DATE = "report_date"
IFA = "ifa"
IFA_TIMESTAMP = "timestamp"
LATITUDE = "latitude"
LONGITUDE = "longitude"
DISTANCE_TO_CENTROID = "distance_to_centroid"
IFA_TIMESTAMP_LOCAL = "timestamp_local"
SEEN_AT_NIGHT = "seen_at_night"
PARCEL_MATCHED = "parcel_matched"
HOUSEHOLD_MATCHED = "household_matched"
OFFSET_MATCHED = "offset_matched"

did_schema = DataFrameSchema(
    columns={
        REQUEST_ID: Column(str, required=True),
        REF_ID :Column(str, nullable=True),
        ETHASH :Column(str),
        REPORT_DATE :Column(Date),
        IFA :Column(str),
        IFA_TIMESTAMP :Column(DateTime), # device observations are at the hour level
        LATITUDE :Column(float),
        LONGITUDE :Column(float),
        DISTANCE_TO_CENTROID :Column(float, nullable=True),
        IFA_TIMESTAMP_LOCAL :Column(DateTime, nullable=True),
        SEEN_AT_NIGHT :Column(BOOL, nullable=True),
        PARCEL_MATCHED :Column(BOOL, nullable=True),
        HOUSEHOLD_MATCHED :Column(BOOL, nullable=True),
        OFFSET_MATCHED :Column(BOOL, nullable=True),
    },
    add_missing_columns=True,
    coerce=True,
    strict=True
)
