from pandera import Column, DataFrameSchema
from pandera.engines.pandas_engine import Date, Category, BOOL
from pandera.engines.numpy_engine import Object
# https://pandera.readthedocs.io/en/stable/reference/dtypes.html#pandas-dtypes

# column names
ADDRESS1 = "address1"
ZIP = "zip"
REF_ID = "ref_id"
REQUEST_ID = "request_id"
REPORT_DATE = "report_date"
ETHASH = "ethash"
WKB = "wkb"
OFFSET_WKB = "offset_wkb"
RDI = "rdi"
LATITUDE = "latitude"
LONGITUDE = "longitude"
TIMEZONE = "timezone"
HOUSEHOLD_WKBS = "household_wkbs"
HOUSEHOLD_WKB_MATCHES = "household_wkb_matches"
IS_WKB_POINT = "is_wkb_point"
HOUSEHOLD_DATA_AVAILABLE = "household_data_available"
DEADZONE = "deadzone"

# the pandas list type seems to be bugged and the pyarrow list is absolutely bugged
# https://github.com/pandas-dev/pandas/issues/57411
parcel_schema = DataFrameSchema(
    columns={
        REQUEST_ID: Column(str, required=True),
        ADDRESS1 : Column(str, required=True),
        ZIP : Column(str, required=True),
        REF_ID : Column(str, nullable=True),
        REPORT_DATE: Column(Date, nullable=True),
        ETHASH : Column(str, nullable=True),
        WKB : Column(str, nullable=True),
        OFFSET_WKB : Column(str, nullable=True),
        RDI : Column(Category, nullable=True),
        LATITUDE : Column(float, nullable=True),
        LONGITUDE : Column(float, nullable=True),
        TIMEZONE : Column(str, nullable=True),
        HOUSEHOLD_WKBS : Column(Object, nullable=True), #these are np.ndarray objects
        HOUSEHOLD_WKB_MATCHES : Column(Object, nullable=True), #these are np.ndarray objects
        IS_WKB_POINT : Column(BOOL, nullable=True),
        HOUSEHOLD_DATA_AVAILABLE : Column(BOOL, nullable=True),
        DEADZONE : Column(BOOL, nullable=True),
    },
    add_missing_columns=True,
    coerce=True,
    strict=True
)
