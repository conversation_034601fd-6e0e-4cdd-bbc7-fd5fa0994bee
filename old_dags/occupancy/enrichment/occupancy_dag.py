from etdag import ETDAG
from airflow.decorators import task_group
from airflow.models.param import Param
from airflow.operators.empty import EmptyOperator
from datetime import timedelta
from old_dags.occupancy.enrichment.tasks.geocode_parcels import geocode_target_parcels
from old_dags.occupancy.enrichment.tasks.generate_dids import generate_dids
from old_dags.occupancy.enrichment.tasks.generate_extra_features import generate_extra_features
from old_dags.occupancy.enrichment.tasks.generate_occ_sets import generate_occupancy_set
from old_dags.occupancy.enrichment.tasks.predict_occ_set import predict_occupancy_set
from old_dags.occupancy.enrichment.tasks.generate_results import generate_results
from old_dags.occupancy.enrichment.tasks import utils

with ETDAG(
    dag_id="occupancy_enrichment",
    default_args={
        "owner": "Panama",
        "retries": 0,
        "retry_delay": timedelta(seconds=300),
    },
    is_paused_upon_creation=True,
    schedule_interval=None,
    max_active_runs=1,
    catchup=False,
    params={
        "adr_idx": Param(
            default=None,
            type=["null", "integer"],
            description="The index of the address column in the target file",
        ),
        "zip_idx": Param(
            default=None,
            type=["null", "integer"],
            description="The index of the zip column in the target file",
        ),
         "ref_idx": Param(
            default=None,
            type=["null", "integer"],
            description="The index of the reference column in the target file",
        ),
          "target_s3_url": Param(
            default=None,
            type=["null", "string"],
            description="The target file in s3 as a parquet that includes 2 columns, addr1 and zip",
        ),
        "target_s3_url": Param(
            default=None,
            type=["null", "string"],
            description="The target file in s3 as a parquet that includes 2 columns, addr1 and zip",
        ),
        "request_id": Param(
            default=None,
            type=["null", "string"],
            description="An id to save the results under",
        ),
        "report_date": Param(
            default=None,
            type=["null", "string"],
            format="date",
            description="The ending date of the occupancy prediction range",
        ),
    },
    render_template_as_native_obj=True,
    tags=["occupancy"],
    et_failure_msg=False,
) as dag:
    target_s3_url = "{{ params.target_s3_url }}"  # dag.params["target_s3_url"] doesn't work ¯\_(ツ)_/¯
    request_id = "{{ params.request_id }}"
    # adr_idx = "{{ params.adr_idx }}"
    # zip_idx = "{{ params.zip_idx }}"
    # ref_idx = "{{ params.ref_idx }}"

    report_date = "{{ params.report_date }}"

    geocode_parcels_instance = geocode_target_parcels(
        request_id,
        target_s3_url,
        report_date,
        "{{ params.adr_idx }}",
        "{{ params.zip_idx }}",
        "{{ params.ref_idx }}",
        offset_multiplier=2,  # TODO look into different offset_multiplier
    )
    # TODO currently there is a bug in here in which the onspot results go over the report_date
    # TODO separate this, one for sending the request and a sensor that waits for it to finish
    generate_dids_instance = generate_dids(
        geocode_parcels_instance, request_id, report_date
    )
    generate_extra_features_instance = generate_extra_features(
        geocode_parcels_instance,
        generate_dids_instance,
    )

    @task_group
    def run_date_range(date_range: str):
        generate_occ_set_instance = generate_occupancy_set(
            generate_extra_features_instance["geocoded_parcels_s3_url"],
            generate_extra_features_instance["dids_s3_url"],
            request_id,
            report_date,
            date_range,
        )
        run_predictions_on_occupancy_set = predict_occupancy_set(
            generate_occ_set_instance,
            date_range
        )
        generate_occ_set_instance >> run_predictions_on_occupancy_set
        return run_predictions_on_occupancy_set

    date_range_instance_0_to_14 = run_date_range.override(
        group_id="date_range_0_to_14"
    )(utils.OccSetDateRanges.RANGE_0_TO_14.value)
    date_range_instance_0_to_30 = run_date_range.override(
        group_id="date_range_0_to_30"
    )(utils.OccSetDateRanges.RANGE_0_TO_30.value)
    date_range_instance_30_to_60 = run_date_range.override(
        group_id="date_range_30_to_60"
    )(utils.OccSetDateRanges.RANGE_30_TO_60.value)
    date_range_instance_60_to_90 = run_date_range.override(
        group_id="date_range_60_to_90"
    )(utils.OccSetDateRanges.RANGE_60_TO_90.value)

    generate_results_instance = generate_results(
        request_id,
        report_date,
        geocode_parcels_instance,
        date_range_instance_0_to_14,
        date_range_instance_0_to_30,
        date_range_instance_30_to_60,
        date_range_instance_60_to_90,
    )

    (
        geocode_parcels_instance
        >> generate_dids_instance
        >> generate_extra_features_instance
        >> [
            date_range_instance_0_to_14,
            date_range_instance_0_to_30,
            date_range_instance_30_to_60,
            date_range_instance_60_to_90,
        ]
        >> generate_results_instance
    )
