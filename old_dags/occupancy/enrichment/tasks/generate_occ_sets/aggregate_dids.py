import pandas as pd
from datetime import date, datetime, timedelta
import numpy as np
import old_dags.occupancy.schemas.dids as did_schema

def __count_non_zero(observations):
    return (observations != 0).sum()

def __concat_lists(x):
    result = []
    for lst in x:
        result += lst
    return result

# TODO check these counts again, specifically the uniq_hr_count_night
def __generate_hourly_count_cols(df: pd.DataFrame, end_date: datetime, days_out=30):
    # all hr count condition
    cond = (df["date"] >= end_date - timedelta(days=days_out)) & (
        df["date"] <= end_date
    )
    df[f"uniq_hr_count_last_{days_out}_days"] = (
        df[cond].groupby(["ifa", "ethash"])["date"].transform("count")
    )

    df[f"uniq_hr_count_night_last_{days_out}_days"] = (
        df[cond].groupby(["ifa", "ethash"])["seen_at_night"].transform(__count_non_zero)
    )

    # Fill NaN values with 0
    df[f"uniq_hr_count_last_{days_out}_days"] = df[
        f"uniq_hr_count_last_{days_out}_days"
    ].fillna(0)
    df[f"uniq_hr_count_night_last_{days_out}_days"] = df[
        f"uniq_hr_count_night_last_{days_out}_days"
    ].fillna(0)

    return df



def __generate_daily_count_cols(
    df: pd.DataFrame, end_date: datetime, days_out=30, lag_tag=False
):
    if lag_tag:
        column_name_days = f"uniq_day_count_last_{days_out}_days_lag"
        column_name_nights = f"uniq_day_count_night_last_{days_out}_days_lag"
    else:
        column_name_days = f"uniq_day_count_last_{days_out}_days"
        column_name_nights = f"uniq_day_count_night_last_{days_out}_days"

    # all hr count condition
    cond = (df["date"] >= end_date - timedelta(days=days_out)) & (
        df["date"] <= end_date
    )
    df[column_name_days] = (
        df[cond].groupby(["ifa", "ethash"])["date"].transform("count")
    )

    # night hr count condition
    cond = (df["date"] >= end_date - timedelta(days=days_out)) & (
        df["date"] <= end_date
    )
    df[column_name_nights] = (
        df[cond]
        .groupby(["ifa", "ethash"])["uniq_hr_obs_night"]
        .transform(__count_non_zero)
    )

    # Fill NaN values with 0
    df[column_name_days] = df[column_name_days].fillna(0)
    df[column_name_nights] = df[column_name_nights].fillna(0)

    return df



def did_aggregate_to_hour_level(
    did_df: pd.DataFrame,
    end_date: datetime,
) -> pd.DataFrame:

    df_hour_agg = did_df.groupby(
        ["ifa", "ethash", "date", "hour", "seen_at_night", "household_matched"], as_index=False
    ).agg(
        distances_to_centroid=pd.NamedAgg(column="distance_to_centroid", aggfunc=list),
    )
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=30)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=21)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=14)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=10)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=7)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=5)
    df_hour_agg = __generate_hourly_count_cols(df_hour_agg, end_date, days_out=3)
    # df_hour_agg = df_hour_agg[df_hour_agg["date"] <= end_date]
    df_hour_agg.reset_index(drop=True, inplace=True)
    return df_hour_agg



def did_aggregate_to_day_level(
    df_hour_agg: pd.DataFrame, end_date: datetime
) -> pd.DataFrame:
        df_day_agg = df_hour_agg.groupby(
            [
                "ifa",
                "ethash",
                "date",
                "household_matched"
            ],
            as_index=False,
        ).agg(
            uniq_hr_obs=pd.NamedAgg(column="hour", aggfunc="count"),
            uniq_hr_obs_night=pd.NamedAgg(column="seen_at_night", aggfunc="sum"),
            distances_to_centroid=pd.NamedAgg(
                column="distances_to_centroid", aggfunc=__concat_lists
            ),
            seen_at_night=pd.NamedAgg(column="seen_at_night", aggfunc="max"),
            uniq_hr_count_30_days=pd.NamedAgg(
                column="uniq_hr_count_last_30_days", aggfunc="max"
            ),
            uniq_hr_count_night_30_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_30_days", aggfunc="max"
            ),
            uniq_hr_count_21_days=pd.NamedAgg(
                column="uniq_hr_count_last_21_days", aggfunc="max"
            ),
            uniq_hr_count_night_21_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_21_days", aggfunc="max"
            ),
            uniq_hr_count_14_days=pd.NamedAgg(
                column="uniq_hr_count_last_14_days", aggfunc="max"
            ),
            uniq_hr_count_night_14_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_14_days", aggfunc="max"
            ),
            uniq_hr_count_10_days=pd.NamedAgg(
                column="uniq_hr_count_last_10_days", aggfunc="max"
            ),
            uniq_hr_count_night_10_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_10_days", aggfunc="max"
            ),
            uniq_hr_count_7_days=pd.NamedAgg(
                column="uniq_hr_count_last_7_days", aggfunc="max"
            ),
            uniq_hr_count_night_7_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_7_days", aggfunc="max"
            ),
            uniq_hr_count_5_days=pd.NamedAgg(
                column="uniq_hr_count_last_5_days", aggfunc="max"
            ),
            uniq_hr_count_night_5_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_5_days", aggfunc="max"
            ),
            uniq_hr_count_3_days=pd.NamedAgg(
                column="uniq_hr_count_last_3_days", aggfunc="max"
            ),
            uniq_hr_count_night_3_days=pd.NamedAgg(
                column="uniq_hr_count_night_last_3_days", aggfunc="max"
            ),
        )
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=30)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=21)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=14)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=10)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=7)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=5)
        df_day_agg = __generate_daily_count_cols(df_day_agg, end_date, days_out=3)
        return df_day_agg

def did_aggregate_to_did_level(df_day_agg) -> pd.DataFrame:
    df_did_agg = df_day_agg.groupby(by=["ethash", "ifa", "household_matched"], as_index=False).agg(
        distances_to_centroid=pd.NamedAgg(
            column="distances_to_centroid", aggfunc=__concat_lists
        ),
        uniq_hr_obs=pd.NamedAgg("uniq_hr_obs", "sum"),
        uniq_hr_obs_night=pd.NamedAgg("uniq_hr_obs_night", "sum"),
        seen_at_night=pd.NamedAgg(column="seen_at_night", aggfunc="max"),
        
        # aggregations the hour counts
        uniq_hr_count_30_days=pd.NamedAgg("uniq_hr_count_30_days", "max"),
        uniq_hr_count_night_30_days=pd.NamedAgg("uniq_hr_count_night_30_days", "max"),
        uniq_hr_count_21_days=pd.NamedAgg("uniq_hr_count_21_days", "max"),
        uniq_hr_count_night_21_days=pd.NamedAgg("uniq_hr_count_night_21_days", "max"),
        uniq_hr_count_14_days=pd.NamedAgg("uniq_hr_count_14_days", "max"),
        uniq_hr_count_night_14_days=pd.NamedAgg("uniq_hr_count_night_14_days", "max"),
        uniq_hr_count_10_days=pd.NamedAgg("uniq_hr_count_10_days", "max"),
        uniq_hr_count_night_10_days=pd.NamedAgg("uniq_hr_count_night_10_days", "max"),
        uniq_hr_count_7_days=pd.NamedAgg("uniq_hr_count_7_days", "max"),
        uniq_hr_count_night_7_days=pd.NamedAgg("uniq_hr_count_night_7_days", "max"),
        uniq_hr_count_5_days=pd.NamedAgg("uniq_hr_count_5_days", "max"),
        uniq_hr_count_night_5_days=pd.NamedAgg("uniq_hr_count_night_5_days", "max"),
        uniq_hr_count_3_days=pd.NamedAgg("uniq_hr_count_3_days", "max"),
        uniq_hr_count_night_3_days=pd.NamedAgg("uniq_hr_count_night_3_days", "max"),
        
        # aggregations for the day counts
        uniq_day_count_last_30_days=pd.NamedAgg("uniq_day_count_last_30_days", "max"),
        uniq_day_count_night_last_30_days=pd.NamedAgg("uniq_day_count_night_last_30_days", "max"),
        uniq_day_count_last_21_days=pd.NamedAgg("uniq_day_count_last_21_days", "max"),
        uniq_day_count_night_last_21_days=pd.NamedAgg("uniq_day_count_night_last_21_days", "max"),
        uniq_day_count_last_14_days=pd.NamedAgg("uniq_day_count_last_14_days", "max"),
        uniq_day_count_night_last_14_days=pd.NamedAgg("uniq_day_count_night_last_14_days", "max"),
        uniq_day_count_last_10_days=pd.NamedAgg("uniq_day_count_last_10_days", "max"),
        uniq_day_count_night_last_10_days=pd.NamedAgg("uniq_day_count_night_last_10_days", "max"),
        uniq_day_count_last_7_days=pd.NamedAgg("uniq_day_count_last_7_days", "max"),
        uniq_day_count_night_last_7_days=pd.NamedAgg("uniq_day_count_night_last_7_days", "max"),
        uniq_day_count_last_5_days=pd.NamedAgg("uniq_day_count_last_5_days", "max"),
        uniq_day_count_night_last_5_days=pd.NamedAgg("uniq_day_count_night_last_5_days", "max"),
        uniq_day_count_last_3_days=pd.NamedAgg("uniq_day_count_last_3_days", "max"),
        uniq_day_count_night_last_3_days=pd.NamedAgg("uniq_day_count_night_last_3_days", "max"),
    )
    for col in df_did_agg.columns:
        if pd.api.types.is_integer_dtype(
            df_did_agg[col]
        ) or pd.api.types.is_float_dtype(df_did_agg[col]):
            df_did_agg[col] = df_did_agg[col].fillna(0)
            df_did_agg[col] = df_did_agg[col].astype(np.int64)
    return df_did_agg



def did_aggregate_to_parcel_level(
    did_lvl_df: pd.DataFrame
) -> pd.DataFrame:
    did_parcel_lvl_df = did_lvl_df.groupby(["ethash", "household_matched"], as_index=False).agg(
        **{
            "distances_to_centroid": pd.NamedAgg(
                column="distances_to_centroid", aggfunc=__concat_lists
            ),
            "count_uniq_ifas": pd.NamedAgg(column="ifa", aggfunc="count"),
            "count_uniq_ifas_seen_at_night": pd.NamedAgg(
                column="seen_at_night", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_3_days)": pd.NamedAgg(
                column="uniq_day_count_last_3_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_3_days)": pd.NamedAgg(
                column="uniq_hr_count_3_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_10_days)": pd.NamedAgg(
                column="uniq_hr_count_10_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="sum"
            ),
            "SUM(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="sum"
            ),
            "MIN(devices.uniq_day_count_last_3_days)": pd.NamedAgg(
                column="uniq_day_count_last_3_days", aggfunc="min"
            ),
            "MIN(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="min"
            ),
            "MIN(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="min"
            ),
            "MIN(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="min"
            ),
            "MIN(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="min"
            ),
            "MIN(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_3_days)": pd.NamedAgg(
                column="uniq_hr_count_3_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_10_days)": pd.NamedAgg(
                column="uniq_hr_count_10_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="min"
            ),
            "MIN(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="min"
            ),
            "MAX(devices.uniq_day_count_last_3_days)": pd.NamedAgg(
                column="uniq_day_count_last_3_days", aggfunc="max"
            ),
            "MAX(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="max"
            ),
            "MAX(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="max"
            ),
            "MAX(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="max"
            ),
            "MAX(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="max"
            ),
            "MAX(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_3_days)": pd.NamedAgg(
                column="uniq_hr_count_3_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_10_days)": pd.NamedAgg(
                column="uniq_hr_count_10_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="max"
            ),
            "MAX(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="max"
            ),
            "MEAN(devices.uniq_day_count_last_3_days)": pd.NamedAgg(
                column="uniq_day_count_last_3_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_3_days)": pd.NamedAgg(
                column="uniq_hr_count_3_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_7_days)": pd.NamedAgg(
                column="uniq_hr_count_7_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="mean"
            ),
            "MEAN(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="mean"
            ),
            "STD(devices.uniq_day_count_last_3_days)": pd.NamedAgg(
                column="uniq_day_count_last_3_days", aggfunc="std"
            ),
            "STD(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="std"
            ),
            "STD(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="std"
            ),
            "STD(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="std"
            ),
            "STD(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="std"
            ),
            "STD(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_3_days)": pd.NamedAgg(
                column="uniq_hr_count_3_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_10_days)": pd.NamedAgg(
                column="uniq_hr_count_10_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="std"
            ),
            "STD(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="std"
            ),
            "SKEW(devices.uniq_day_count_last_5_days)": pd.NamedAgg(
                column="uniq_day_count_last_5_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_day_count_last_7_days)": pd.NamedAgg(
                column="uniq_day_count_last_7_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_day_count_last_10_days)": pd.NamedAgg(
                column="uniq_day_count_last_10_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_day_count_last_14_days)": pd.NamedAgg(
                column="uniq_day_count_last_14_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_day_count_last_21_days)": pd.NamedAgg(
                column="uniq_day_count_last_21_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_day_count_last_30_days)": pd.NamedAgg(
                column="uniq_day_count_last_30_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_5_days)": pd.NamedAgg(
                column="uniq_hr_count_5_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_7_days)": pd.NamedAgg(
                column="uniq_hr_count_7_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_10_days)": pd.NamedAgg(
                column="uniq_hr_count_10_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_14_days)": pd.NamedAgg(
                column="uniq_hr_count_14_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_21_days)": pd.NamedAgg(
                column="uniq_hr_count_21_days", aggfunc="skew"
            ),
            "SKEW(devices.uniq_hr_count_30_days)": pd.NamedAgg(
                column="uniq_hr_count_30_days", aggfunc="skew"
            ),
        }
    )
    if did_parcel_lvl_df.empty:
        did_parcel_lvl_df = did_parcel_lvl_df.assign(
            centroid_distance_max=pd.Series(dtype="float"),
            centroid_distance_min=pd.Series(dtype="float"),
            centroid_distance_mean=pd.Series(dtype="float"),
            centroid_distance_std=pd.Series(dtype="float"),
            centroid_distance_median=pd.Series(dtype="float"),
        )
    else:
        def __process_centroid(centroid_list: list):
            try:
                return pd.Series([
                    max(centroid_list),
                    min(centroid_list),
                    np.mean(centroid_list),
                    np.std(centroid_list),
                    np.median(centroid_list),
                ])
            except:
                return pd.Series([
                    None,
                    None,
                    None,
                    None,
                    None,
                ])
        did_parcel_lvl_df[
            [
                "centroid_distance_max",
                "centroid_distance_min",
                "centroid_distance_mean",
                "centroid_distance_std",
                "centroid_distance_median",
            ]
        ] = did_parcel_lvl_df["distances_to_centroid"].apply(__process_centroid)

    did_parcel_lvl_df.drop(columns=["distances_to_centroid"], inplace=True)

    return did_parcel_lvl_df



def aggregate_dids(
    target_dids_df: pd.DataFrame,
    start_date: date,
    end_date: date,
) -> pd.DataFrame:
    parcel_dids=target_dids_df[target_dids_df["parcel_matched"] == True]
    end_date_str = end_date.strftime("%Y-%m-%d")
    start_date_str = start_date.strftime("%Y-%m-%d")
    parcel_dids = parcel_dids.query(
        f"{did_schema.IFA_TIMESTAMP} >= '{start_date_str}' and {did_schema.IFA_TIMESTAMP} <= '{end_date_str}'"
    )
    parcel_dids["date"] = parcel_dids[did_schema.IFA_TIMESTAMP].dt.date
    parcel_dids["hour"] = parcel_dids[did_schema.IFA_TIMESTAMP].dt.hour
    household_dids = parcel_dids[parcel_dids["household_matched"] == True]
    household_ethash_matches = list(household_dids["ethash"].unique())
    # The set of dids is for either the household or the parcel (if the household is not available)
    parcel_dids = parcel_dids[~parcel_dids['ethash'].isin(household_ethash_matches)]
    filtered_dids = pd.concat([household_dids, parcel_dids])
    del parcel_dids
    del household_dids
    filtered_dids = did_aggregate_to_hour_level(
        did_df=filtered_dids,
        end_date=end_date,
    )
    filtered_dids = did_aggregate_to_day_level(
        filtered_dids,
        end_date=end_date,
    )
    filtered_dids = did_aggregate_to_did_level(
        filtered_dids,
    )
    filtered_dids = did_aggregate_to_parcel_level(
        filtered_dids
    )
    return filtered_dids