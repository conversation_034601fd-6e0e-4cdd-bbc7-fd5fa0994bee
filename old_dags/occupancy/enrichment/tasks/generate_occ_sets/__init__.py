# https://github.com/apache/airflow/issues/39092 <-look into this
from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def generate_occupancy_set(
    geocoded_parcels_s3_url: str,
    dids_s3_url: str,
    request_id: str,
    report_date: str,
    date_range: str,
):
    import pandas as pd
    from old_dags.occupancy.enrichment.tasks.generate_occ_sets.aggregate_dids import aggregate_dids
    from old_dags.occupancy.enrichment.tasks.utils import OccSetDateRanges
    from datetime import datetime, timedelta
    from old_dags.occupancy.schemas.occupancy_14d import occupancy_14d_schema
    from old_dags.occupancy.schemas.occupancy_30d import occupancy_30d_schema
    import numpy as np

    parcel_df = pd.read_parquet(geocoded_parcels_s3_url)
    did_df = pd.read_parquet(dids_s3_url)
    report_date_dt = pd.to_datetime(report_date).date()
    start_date: datetime
    end_date: datetime
    occ_set_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/occupancy_set/date_range={date_range}/{request_id}_{report_date}.pq"
    print(date_range)
    match date_range:
        case OccSetDateRanges.RANGE_0_TO_14.value:
            start_date = report_date_dt - timedelta(14)
            end_date = report_date_dt
            ml_schema = occupancy_14d_schema
        case OccSetDateRanges.RANGE_0_TO_30.value:
            start_date = report_date_dt - timedelta(30)
            end_date = report_date_dt
            ml_schema = occupancy_30d_schema
        case OccSetDateRanges.RANGE_30_TO_60.value:
            start_date = report_date_dt - timedelta(60)
            end_date = report_date_dt - timedelta(30)
            ml_schema = occupancy_30d_schema
        case OccSetDateRanges.RANGE_60_TO_90.value:
            start_date = report_date_dt - timedelta(90)
            end_date = report_date_dt - timedelta(60)
            ml_schema = occupancy_30d_schema
    agg_dids = aggregate_dids(did_df, start_date, end_date)
    ml_input_df = pd.merge(
        left=parcel_df,
        right=agg_dids,
        left_on="ethash",
        right_on="ethash",
        how="left",
    )
    for col in ml_input_df.columns:
        if pd.api.types.is_float_dtype(ml_input_df[col]):
            ml_input_df[col] = ml_input_df[col].fillna(value=0)
        if pd.api.types.is_integer_dtype(ml_input_df[col]):
            ml_input_df[col] = ml_input_df[col].fillna(0)
    ml_input_df.info()
    ml_input_df = ml_schema.validate(ml_input_df)
    ml_input_df.to_parquet(
        occ_set_s3_url,
        index=False,
    )
    return occ_set_s3_url
