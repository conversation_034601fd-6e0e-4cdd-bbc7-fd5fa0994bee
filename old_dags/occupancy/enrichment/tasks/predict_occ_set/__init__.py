from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def predict_occupancy_set(
    occ_set_url: str,
    date_range: str,
):
    import pandas as pd
    import mlflow
    from old_dags.occupancy.enrichment.tasks.utils import OccSetDateRanges
    from old_dags.occupancy.schemas.occupancy_30d import occupancy_30d_schema
    from old_dags.occupancy.schemas.occupancy_14d import occupancy_14d_schema

    mlflow.set_tracking_uri(uri="https://mlflow.k8s.eltoro.com")
    occupancy_set = pd.read_parquet(occ_set_url)
    match date_range:
        case OccSetDateRanges.RANGE_0_TO_14.value:
            occupancy_model = mlflow.xgboost.load_model(model_uri=f"models:/occupancy_14_day@prod")
            occupancy_schema = occupancy_14d_schema
        case dr if dr in (OccSetDateRanges.RANGE_0_TO_30.value, OccSetDateRanges.RANGE_30_TO_60.value, OccSetDateRanges.RANGE_60_TO_90.value):
            occupancy_model = mlflow.xgboost.load_model(model_uri=f"models:/occupancy_30_day@prod")
            occupancy_schema = occupancy_30d_schema
            
    
    occupancy_features = occupancy_model.get_booster().feature_names
    occupancy_prob = occupancy_model.predict_proba(
        occupancy_set[occupancy_features]
    )[:, 1]

    occupancy_pred = occupancy_model.predict(
        occupancy_set[occupancy_features]
    )
    occupancy_set["occupancy_prob"] = occupancy_prob
    occupancy_set["occupancy_pred"] = occupancy_pred
    occupancy_set = occupancy_schema.validate(occupancy_set)
    occupancy_set.to_parquet(
        occ_set_url,
        index=False
    )

    return occ_set_url