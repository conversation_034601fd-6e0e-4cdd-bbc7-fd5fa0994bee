from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def geocode_target_parcels(
    request_id: str,
    target_s3_url: str,
    report_date: str,
    adr_idx: int,
    zip_idx: int,
    ref_idx: int,
    n_workers=4,
    offset_multiplier=2
) -> str:
    from datascience_implement_cache import DIC
    from datascience_implement_cache.geocoder import Geocoder
    import pandas as pd
    import old_dags.occupancy.schemas.parcels as schema
    from old_dags.occupancy.enrichment.tasks import utils
    from datetime import datetime, timedelta
    import old_dags.occupancy.enrichment.tasks.geocode_parcels.helper as helper

    output_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/target_parcels/{request_id}_{report_date}.pq"
    print(target_s3_url)
    parsed_report_date = datetime.strptime(report_date, "%Y-%m-%d").date()
    cutoff = datetime.now().date() - timedelta(days=4)
    if parsed_report_date > cutoff:
        raise ValueError("report_date is too recent, has to be older than 4 days from now")
    target_parcels = pd.read_parquet(target_s3_url)
    target_parcels[schema.REQUEST_ID] = request_id
    target_parcels[schema.ZIP] = target_parcels[schema.ZIP].str.zfill(5)
    target_parcels[schema.REPORT_DATE] = report_date
    if ref_idx is None or ref_idx < 0:
        target_parcels = target_parcels.reset_index(names=schema.REF_ID)
        adr_idx+=1
        zip_idx+=1
    else:
        target_parcels = target_parcels.rename(columns={target_parcels.columns[ref_idx]: schema.REF_ID})
    # initial geocoding of target parcels
    print("geocoding target parcels")
    dic = DIC("occupancy", "ivan.trost")
    geocoder = Geocoder(dic)
    target_parcels = geocoder.geocode(
        target_parcels,
        adr1_idx=adr_idx,
        adr2_idx=None,
        zip_idx=zip_idx,
        option_list=[
            "etHashV1",
            "geometryWKB",
            schema.RDI,
            schema.LATITUDE,
            schema.LONGITUDE,
        ],
    ).rename(columns={"etHashV1": schema.ETHASH, "geometryWKB": schema.WKB})
    target_parcels = target_parcels.drop(
        columns=["dic_address_string", "dataset", "matchCode"]
    )
    target_parcels[schema.ETHASH] = target_parcels[schema.ETHASH].replace("", None)

    target_parcels[schema.TIMEZONE] = target_parcels.apply(
        lambda df: helper.add_timezone(
            longitude=df[schema.LONGITUDE], latitude=df[schema.LATITUDE]
        ),
        axis=1,
    )
    print(target_parcels.info())
    # find household wkbs
    print("using pinpoint to find potential wkb households")
    target_parcels[schema.HOUSEHOLD_WKBS] = utils.apply_parallel(
        df=target_parcels,
        func=helper.find_house_wkbs,
        columns=[schema.LATITUDE, schema.LONGITUDE],
        n_jobs=n_workers,
    )

    # match said household wkbs with the parcel
    print("matching household wkbs to parcels")
    target_parcels[schema.HOUSEHOLD_WKB_MATCHES] = utils.apply_parallel(
        target_parcels,
        func=helper.find_household_wkb_matches,
        columns=[schema.WKB, schema.HOUSEHOLD_WKBS],
        n_jobs=10,
    )

    # tag all wkb that are a points
    print("tagging invalid parcel wkbs")
    target_parcels[schema.IS_WKB_POINT] = target_parcels[schema.WKB].apply(
        helper.wkb_point_finder
    )
    # tag wherever the household matches occur
    print("tagging parcels in which household polygons were found")
    target_parcels[schema.HOUSEHOLD_DATA_AVAILABLE] = target_parcels[
        schema.HOUSEHOLD_WKB_MATCHES
    ].apply(helper.used_household)
    print("typecasting rdi as a categorical value")
    target_parcels[schema.RDI] = target_parcels[schema.RDI].fillna("")
    target_parcels[schema.RDI] = pd.Categorical(
        target_parcels[schema.RDI], categories=target_parcels[schema.RDI].unique()
    )
    print(f"generating offset wkbs by a factor of {offset_multiplier}")
    target_parcels[schema.OFFSET_WKB] = target_parcels[schema.WKB].apply(
        lambda x: helper.scale_geometry(x, scale_factor=offset_multiplier)
    )
    print(f"validating parcel results")
    target_parcels = schema.parcel_schema.validate(target_parcels)
    print(
        "finished processing, the columns of type 'list' that appear as 'object' is intended"
    )
    target_parcels.info()
    target_parcels.to_parquet(output_s3_url, index=False)
    return output_s3_url