import backoff
import requests
import pandas as pd
from shapely.errors import GEOSException
from shapely import wkb, affinity
from base64 import b64decode, b64encode
from math import isnan
from timezonefinder import TimezoneFinder

url = "https://atlas.k8s.eltoro.com/"
req_headers = {"accept-encoding": "gzip"}
geo_backoff = backoff.on_exception(
    backoff.expo,
    (
        requests.ConnectionError,
        requests.ConnectTimeout,
        requests.Timeout,
        requests.exceptions.ChunkedEncodingError,
    ),
    max_tries=4,
    max_time=30,
    raise_on_giveup=False,
    factor=2,
)


@geo_backoff
def find_house_wkbs(latitude, longitude):
    if pd.isna(latitude) or pd.isna(longitude):
        return None
    response = requests.post(
        url,
        timeout=30,
        headers=req_headers,
        json={
            "query": f"""{{
            polygonsAtPoint(coords: {{lat: {latitude}, lon: {longitude}}}, zoomLevel: 20, refId: "occupancy")
            }}"""
        },
    )
    if response.status_code != 200:
        return None
    json_response = response.json()
    if not json_response["data"]:
        return None
    return json_response["data"]["polygonsAtPoint"]


def find_household_wkb_matches(target_wkb, household_wkbs, match_buffer=0.00003):
    if pd.isna(target_wkb):
        return None
    target_wkb = scale_geometry(target_wkb, 1.2)
    try:
        parcel_geom = wkb.loads(b64decode(target_wkb), hex=True).buffer(match_buffer)
    except GEOSException:
        return None
    wkb_matches = list()
    if not household_wkbs:
        return None
    for household_wkb in household_wkbs:
        try:
            household_geom = wkb.loads(b64decode(household_wkb), hex=True)
        except GEOSException:
            continue
        if household_geom.within(parcel_geom):
            wkb_matches.append(household_wkb)
    return wkb_matches


def scale_geometry(b64_wkb: str, scale_factor: float):
    # decode base64 wkb to bytes
    try:
        wkb_bytes = b64decode(b64_wkb)
    except TypeError:
        return None
    # load geometry from wkb
    geom = wkb.loads(wkb_bytes)

    # scale geometry
    scaled_geom_planar = affinity.scale(
        geom, xfact=scale_factor, yfact=scale_factor, origin="center"
    )

    # dump geometry to wkb
    scaled_wkb_bytes = wkb.dumps(scaled_geom_planar)

    # encode wkb to base64
    scaled_b64_wkb = b64encode(scaled_wkb_bytes).decode()

    return scaled_b64_wkb


def add_timezone(longitude, latitude) -> str | None:
    if not isnan(longitude) and not isnan(latitude):
        timezone_str = TimezoneFinder().timezone_at(lng=longitude, lat=latitude)
    else:
        timezone_str = None
    return timezone_str


def wkb_point_finder(parcel_wkb: str, threshold=1e-15) -> bool:
    if pd.isna(parcel_wkb):
        return False
    if wkb.loads(b64decode(parcel_wkb), hex=True).area > threshold:
        return False
    return True


def used_household(household_wkb_matches: list) -> bool:
    if not household_wkb_matches:
        return False
    if len(household_wkb_matches) > 0:
        return True
    return False