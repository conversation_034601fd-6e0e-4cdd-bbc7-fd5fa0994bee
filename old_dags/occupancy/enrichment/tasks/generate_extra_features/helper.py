import pandas as pd
from datetime import datetime
from pytz import timezone
from geopy.distance import geodesic


def __find_time_local(timezone_str: str, timestamp: datetime):
    local_tz = timezone(timezone_str)
    local_datetime = (
        timestamp.replace(tzinfo=timezone("UTC")).astimezone(local_tz)
    )
    return pd.to_datetime(local_datetime)


def __find_distance_to_centroid(geom, latitude, longitude):
    if geom.geom_type != "Polygon":
        print("The geometry is not a Polygon.")
    distance = geodesic(
        (latitude, longitude), (geom.centroid.y, geom.centroid.x)
    ).meters
    return distance


def __find_if_seen_at_night(local_datetime, night_time_start, night_time_end):
    if night_time_start <= local_datetime.hour or local_datetime.hour < night_time_end:
        return True
    else:
        return False

def process_did_extra_cols(ifa_timestamp, geom, latitude, longitude, parcel_timezone, night_time_start, night_time_end):
    try:
        timestamp_local = __find_time_local(
            parcel_timezone, 
            ifa_timestamp
            )
        
        return pd.Series(
            [
                __find_distance_to_centroid(geom, latitude, longitude),
                timestamp_local,
                __find_if_seen_at_night(timestamp_local, night_time_start, night_time_end),
            ]
        )
    except:
        return pd.Series(
            [
                None,
                None,
                None,
            ]
        )