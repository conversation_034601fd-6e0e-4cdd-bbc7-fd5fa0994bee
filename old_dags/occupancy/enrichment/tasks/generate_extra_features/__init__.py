from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    multiple_outputs=True,
    # python_version="3.10"
)
def generate_extra_features(
    geocoded_parcels_s3_url: str,
    dids_s3_url: str,
    night_time_start=21,  # 10pm
    night_time_end=5,  # 5am
):
    from datascience_implement_cache import DIC
    import pandas as pd
    import old_dags.occupancy.schemas.dids as did_schema
    import old_dags.occupancy.schemas.parcels as parcel_schema
    from old_dags.occupancy.enrichment.tasks import utils
    from shapely import wkb
    import base64
    import pandas as pd
    import old_dags.occupancy.enrichment.tasks.generate_extra_features.helper as helper
    import numpy as np
    from shapely.geometry import Point
    import geopandas as gpd

    parcel_df = pd.read_parquet(geocoded_parcels_s3_url)
    did_df = pd.read_parquet(dids_s3_url)
    did_df = did_df.drop(columns=[did_schema.REF_ID])
    # create temp columns
    parcel_df["parcel_geom"] = parcel_df[parcel_schema.WKB].apply(
        lambda x: wkb.loads(base64.b64decode(x)) if pd.notnull(x) else None
    )
    parcel_df["offset_geom"] = parcel_df[parcel_schema.OFFSET_WKB].apply(
        lambda x: wkb.loads(base64.b64decode(x)) if pd.notnull(x) else None
    )

    parcel_df["household_geoms"] = parcel_df[parcel_schema.HOUSEHOLD_WKB_MATCHES].apply(
        lambda arr: (
            [wkb.loads(base64.b64decode(wkb_str)) for wkb_str in arr]
            if isinstance(arr, np.ndarray)
            else []
        )
    )

    did_df["loc_point_geom"] = did_df.apply(
        lambda row: Point(row[did_schema.LONGITUDE], row[did_schema.LATITUDE]), axis=1
    )
    did_df["night_time_start"] = night_time_start
    did_df["night_time_end"] = night_time_end

    did_df = pd.merge(
        left=parcel_df[["parcel_geom", parcel_schema.TIMEZONE, parcel_schema.ETHASH, parcel_schema.REF_ID]],
        right=did_df,
        left_on=parcel_schema.ETHASH,
        right_on=did_schema.ETHASH,
        how="right",
    )
    print("adding extra features to dids")
    did_df[
        [
            did_schema.DISTANCE_TO_CENTROID,
            did_schema.IFA_TIMESTAMP_LOCAL,
            did_schema.SEEN_AT_NIGHT,
        ]
    ] = utils.apply_parallel(
        df=did_df,
        func=helper.process_did_extra_cols,
        columns=[
            did_schema.IFA_TIMESTAMP,
            "parcel_geom",
            did_schema.LATITUDE,
            did_schema.LONGITUDE,
            parcel_schema.TIMEZONE,
            "night_time_start",
            "night_time_end",
        ],
        n_jobs=20,
    )
    print("matching dids to parcel wkbs")
    parcels_gdf = gpd.GeoDataFrame(parcel_df, geometry="parcel_geom")
    parcels_gdf[parcel_schema.DEADZONE] = False

    dids_gdf = gpd.GeoDataFrame(did_df, geometry="loc_point_geom")
    dids_gdf[did_schema.PARCEL_MATCHED] = False
    dids_gdf[did_schema.HOUSEHOLD_MATCHED] = False
    dids_gdf[did_schema.OFFSET_MATCHED] = False
    for parcel_i, target_parcel in parcels_gdf.iterrows():
        ethash_dids = dids_gdf[
            dids_gdf[did_schema.ETHASH] == target_parcel[parcel_schema.ETHASH]
        ]

        # if no dids were found for the parcel then it is a deadzone
        if ethash_dids.empty:
            parcels_gdf.at[parcel_i, parcel_schema.DEADZONE] = True
            continue

        # Check household geometries
        household_geometries = gpd.GeoSeries(target_parcel["household_geoms"])
        if not household_geometries.empty:
            dids_in_households = ethash_dids[
                ethash_dids.geometry.apply(
                    lambda x: household_geometries.contains(x).any()
                )
            ]
            # TODO figure a way to add ethash as the index while also keeping a unique index for each ifa
            dids_gdf.loc[dids_in_households.index, did_schema.HOUSEHOLD_MATCHED] = True

        # Check parcel geometry
        dids_in_parcel = ethash_dids[
            ethash_dids.geometry.within(target_parcel["parcel_geom"])
        ]
        dids_gdf.loc[dids_in_parcel.index, did_schema.PARCEL_MATCHED] = True

        # Decode and check offset geometry
        dids_in_offset = ethash_dids[
            ethash_dids.geometry.within(target_parcel["offset_geom"])
        ]
        dids_gdf.loc[dids_in_offset.index, "offset_matched"] = True

    # Drop the geometry columns and convert back to DataFrame
    parcel_df = pd.DataFrame(parcels_gdf)
    did_df = pd.DataFrame(dids_gdf)
    # get rid of temp columns
    did_df.drop(
        columns=[
            "parcel_geom",
            "loc_point_geom",
            "night_time_start",
            "night_time_end",
            parcel_schema.TIMEZONE,
        ],
        inplace=True,
    )
    parcel_df.drop(
        columns=[
            "household_geoms", 
            "parcel_geom", 
            "offset_geom"
        ], 
        inplace=True
    )
    did_df.info()
    parcel_df.info()

    parcel_df.to_parquet(geocoded_parcels_s3_url, index=False)
    did_df.to_parquet(dids_s3_url, index=False)
    
    return {"geocoded_parcels_s3_url": geocoded_parcels_s3_url, "dids_s3_url": dids_s3_url}