from airflow.decorators import task

@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def generate_dids(
    geocoded_parcels_s3_url: str, request_id: str, report_date: str, trino_conn_id="trino_conn"
) -> str:
    import pandas as pd
    import old_dags.occupancy.schemas.dids as schema
    from old_dags.occupancy.schemas.dids import ETHASH, REQUEST_ID, REPORT_DATE, \
        IFA, IFA_TIMESTAMP, LATITUDE, LONGITUDE
    from operators.timestamp_report_operator import TimestampReportOperator
    from airflow.providers.trino.hooks.trino import <PERSON>noHook
    from datetime import timedelta
    from airflow.models import Variable
    
    output_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/dids/{request_id}_{report_date}.pq"
    
    parcels_df = pd.read_parquet(geocoded_parcels_s3_url)
    parcels_df = parcels_df.query(
        "`wkb`.notnull() and `is_wkb_point` == False"
    )
    end_date = pd.to_datetime(report_date).date()
    onspot_input_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/target_parcels_to_onspot/{request_id}_{report_date}.csv"
    df2 = parcels_df.copy(deep=True)
    df2 = df2[["address1", "ethash", "latitude", "longitude", "zip", "offset_wkb"]]
    df2["formatted_address"] = None
    df2["city"] = None
    df2["state"] = None

    df2 = df2.rename(columns={
        "address1": "address",
        "ethash": "etHashV1",
        "offset_wkb": "wkb"
    })

    df2.to_csv(
        onspot_input_s3_url,
        index=False
    )
    response_data = TimestampReportOperator(
        task_id="onspot_ts_request",
        bucket_id=onspot_input_s3_url,
        start=(end_date - timedelta(90)).strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d"),
        wait_for_job_to_finish=True,
        operation="create",
        reduce_to_date=True, #this doesn't matter since the results are extracted from starburst
        request_types=[
            TimestampReportOperator.REQ_TYPE_OBSERVATIONS,
        ]
    ).execute(None)
    
    print(response_data)
    env = Variable.get("environment")
    query = f"""
        SELECT
            location AS {ETHASH},
            DATE_PARSE(date || ' ' || SUBSTR(time, 1, 2) || ':00:00', '%Y-%m-%d %H:%i:%s') AS {IFA_TIMESTAMP},
            deviceid AS {IFA},
            AVG(cast(lat as decimal(18,15))) as {LATITUDE},
            AVG(cast(lng as decimal(18,15))) as {LONGITUDE}
        FROM
            "s3"."bronze_ts_reports"."{env}_os_response_observations"
        WHERE request_id = '{response_data['id']}'
        GROUP BY
            location,
            deviceid,
            DATE_PARSE(date || ' ' || SUBSTR(time, 1, 2) || ':00:00', '%Y-%m-%d %H:%i:%s')
        
    """
    print(query)
    target_dids_df = TrinoHook(trino_conn_id=trino_conn_id).get_pandas_df(query)

    target_dids_df[REQUEST_ID] = request_id
    target_dids_df[REPORT_DATE] = report_date
    target_dids_df.info()
    target_dids_df = schema.did_schema.validate(target_dids_df)
    target_dids_df.to_parquet(output_s3_url, index=False)
    return output_s3_url