from airflow.providers.trino.hooks.trino import <PERSON>noHook


def __replace_zip9_with_0(ethash: str):
    return ethash[:6] + '0' * (10 - 6) + ethash[10:]


def get_occupancy_codes(ethashes: list[str], trino_conn_id: str) -> list[str]:
    trino_hook = TrinoHook(
        trino_conn_id=trino_conn_id,
    )
    processed_ethashes = list()
    for ethash in ethashes:
        if not ethash:
            continue
        processed_ethashes.append(__replace_zip9_with_0(ethash))
    processed_ethashes_sql_str = ', '.join(f"'{item}'" for item in processed_ethashes)
    df = trino_hook.get_pandas_df(
        f"""
        SELECT ethash, owner_occupancy_code FROM olympus.silver_corelogic.owner_occupied
        WHERE ethash IN ({processed_ethashes_sql_str})
        """,
    )
    occ_codes_dict = df.set_index('ethash')['owner_occupancy_code'].to_dict()
    results = list()
    for ethash in ethashes:
        if not ethash:
            results.append(None)
            continue
        ethash = __replace_zip9_with_0(ethash=ethash)
        results.append(occ_codes_dict.get(ethash, None))
    return results


