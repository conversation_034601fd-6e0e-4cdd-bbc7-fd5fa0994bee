from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def generate_results(
    request_id: str,
    report_date: str,
    target_parcels_s3url: str,
    occ_set_0_to_14_s3url: str,
    occ_set_0_to_30_s3url: str,
    occ_set_30_to_60_s3url: str,
    occ_set_60_to_90_s3url: str,
    trino_conn_id="trino_conn"
):
    import pandas as pd
    import old_dags.occupancy.schemas.occupancy_14d as occ_schm
    import old_dags.occupancy.schemas.parcels as parcel_schm
    import old_dags.occupancy.schemas.results as result_schm
    from old_dags.occupancy.enrichment.tasks.generate_results import corelogic_occupancy as crlgc_occ

    def merge_occ_set(results_df: pd.DataFrame, occ_set_s3url: str) -> pd.DataFrame:
        # extract date_range name
        start = occ_set_s3url.find('date_range=') + len('date_range=')
        end = occ_set_s3url.find('/', start)
        date_range = occ_set_s3url[start:end]
        occ_df = pd.read_parquet(occ_set_s3url).dropna(subset=[occ_schm.ETHASH])
        
        # add visited column with 1 wherever a device was seen
        occ_df.loc[(occ_df[occ_schm.COUNT_UNIQ_IFAS]==0), f"{date_range}_visited"] = 0
        occ_df.loc[(occ_df[occ_schm.COUNT_UNIQ_IFAS]>0), f"{date_range}_visited"] = 1
        
        # change results wherever the count of ifas is 0, can't be occupied if no one was ever seen
        occ_df.loc[(occ_df[occ_schm.COUNT_UNIQ_IFAS]==0), [occ_schm.OCCUPANCY_PRED, occ_schm.OCCUPANCY_PROB]] = 0
        
        occ_df = occ_df.rename(
            columns= {
                "count_uniq_ifas": f"{date_range}_count_uniq_ifas",
                "count_uniq_ifas_seen_at_night": f"{date_range}_count_uniq_ifas_seen_at_night",
                "household_matched": f"{date_range}_household_matched",
                "occupancy_prob": f"{date_range}_occupancy_prob",
                "occupancy_pred": f"{date_range}_occupancy_pred",
            }
        )
        occ_df = occ_df[[col for col in result_schm.result_columns + [occ_schm.ETHASH] if col in occ_df.columns]]
        return pd.merge(
            left=results_df, right=occ_df,
            left_on=parcel_schm.ETHASH, right_on=occ_schm.ETHASH,
            how="left"
        )
    results_url = f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/results/{request_id}_{report_date}.pq"
    results_df = pd.read_parquet(target_parcels_s3url)
    results_df = merge_occ_set(results_df, occ_set_0_to_14_s3url)
    results_df = merge_occ_set(results_df, occ_set_0_to_30_s3url)
    results_df = merge_occ_set(results_df, occ_set_30_to_60_s3url)
    results_df = merge_occ_set(results_df, occ_set_60_to_90_s3url)
    
    results_df[result_schm.OCCUPANCY_CODE]=crlgc_occ.get_occupancy_codes(
        list(results_df[parcel_schm.ETHASH]),
        trino_conn_id
    )
    results_df.loc[
        (results_df[parcel_schm.WKB].isnull()) | (results_df[parcel_schm.DEADZONE]==True),
        result_schm.result_columns
    ] = None
    results_df = result_schm.result_schema.validate(results_df)
    results_df.to_parquet(results_url, index=False)
    return results_url
