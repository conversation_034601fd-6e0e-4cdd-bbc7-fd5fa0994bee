import pandas as pd
from concurrent.futures import ProcessPoolExecutor
from typing import Callable
from enum import Enum

# Define a separate function that applies func to a DataFrame chunk
def apply_func_to_chunk(chunk, func):
    return chunk.apply(lambda row: func(*row), axis=1)

def apply_parallel(df: pd.DataFrame, func: Callable, columns: list[str], n_jobs=4):
    # Select only the columns of interest
    df = df[columns]
    
    # Ensure n_jobs is not greater than the number of rows
    n_jobs = min(n_jobs, len(df))
    
    if n_jobs > 1:
        # Split the DataFrame into chunks
        chunk_size = len(df) // n_jobs
        chunks = [df[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
        
        # Create a ProcessPoolExecutor
        with ProcessPoolExecutor(max_workers=n_jobs) as executor:
            # Apply the function to each chunk in parallel
            futures = [executor.submit(apply_func_to_chunk, chunk, func) for chunk in chunks]
            results = [future.result() for future in futures]
        
        # Combine the results into a single Series
        result_series = pd.concat(results)
    else:
        result_series = df.apply(lambda row: func(*row), axis=1)
    
    return result_series


class OccSetDateRanges(Enum):
    RANGE_0_TO_14 = "0_to_14"
    RANGE_0_TO_30 = "0_to_30"
    RANGE_30_TO_60 = "30_to_60"
    RANGE_60_TO_90 = "60_to_90"