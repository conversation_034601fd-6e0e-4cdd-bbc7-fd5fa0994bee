from etdag import ETDAG
from airflow.decorators import task
from airflow.models.param import Param
from datetime import timedelta
from old_dags.occupancy.feedback.tasks.generate_feedback_set import (
    generate_feedback_occupancy_set,
)
from old_dags.occupancy.feedback.tasks.split_feedback_set import split_feedback_set
from old_dags.occupancy.feedback.tasks.train_model import train_model

with ETDAG(
    dag_id="occupancy_feedback",
    default_args={
        "owner": "Panama",
        "retries": 0,
        "retry_delay": timedelta(seconds=300),
    },
    is_paused_upon_creation=True,
    schedule_interval=None,
    max_active_runs=1,
    catchup=False,
    tags=["occupancy"],
    et_failure_msg=False,
) as dag:
    date_ranges = ["0_to_14", "0_to_30"]
    for date_range in date_ranges:
        feedback_set_instance = generate_feedback_occupancy_set.override(
            task_id=f"generate_feedback_occupancy_set_{date_range}"
        )(date_range)

        split_feedback_instance = split_feedback_set.override(
            task_id=f"split_feedback_instance_{date_range}"
        )(feedback_set_instance, date_range=date_range)

        train_model_instance = train_model.override(
            task_id=f"train_model_instance_{date_range}"
        )(
            split_feedback_instance["train_s3_url"],
            split_feedback_instance["test_s3_url"],
            split_feedback_instance["validation_s3_url"],
            date_range,
        )
        feedback_set_instance >> split_feedback_instance >> train_model_instance
