from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    multiple_outputs=True,
    # python_version="3.10"
)
def split_feedback_set(
    ml_set_s3_url: str,
    date_range: str,
    test_size=0.2,
    validation_size=0.2,
    random_state=777,
    minority_upsampler_multiplier=1.3,
    majority_unbalancer_multiplier=1.1,
):
    """
    Split the ml occupancy set into train, test, validation sets
    only the train set gets upsampled, the test and validation remain intact
    the minority (vacant) gets upsampled. vacant_upsampled_len = (vacant_len * minority_upsampler_multiplier)
    the majority (occupied) gets downsampled.  occupancy_downsampled_len = vacant_upsampled_len * majority_unbalancer_multiplier

    """
    from sklearn.model_selection import train_test_split
    from sklearn.utils import resample
    import pandas as pd
    import math

    target_var = "occupancy_pred"

    df = pd.read_parquet(ml_set_s3_url)
    # Split the dataset into train and test sets
    train_df, test_df = train_test_split(
        df, test_size=test_size, stratify=df[target_var], random_state=random_state
    )

    # Split the train set into train and validation sets
    train_df, validation_df = train_test_split(
        train_df,
        test_size=validation_size,
        stratify=train_df[target_var],
        random_state=random_state,
    )

    # Separate majority and minority classes
    majority = train_df[train_df[target_var] == train_df[target_var].mode()[0]]
    minority = train_df[train_df[target_var] != train_df[target_var].mode()[0]]

    minority_upsampled_len = math.floor(len(minority) * minority_upsampler_multiplier)

    # Downsample majority class
    majority_downsampled = resample(
        majority,
        replace=True,  # sample with replacement
        n_samples=math.floor(
            minority_upsampled_len * majority_unbalancer_multiplier
        ),  # to match minority upsampled class
        random_state=random_state,
    )

    # Upsample minority class
    minority_upsampled = resample(
        minority,
        replace=True,  # sample with replacement
        n_samples=minority_upsampled_len,  # to match majority class
        random_state=random_state,
    )

    # Combine majority class with upsampled minority class
    train_df_minority_upsampled = pd.concat([minority_upsampled, majority_downsampled])

    train_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/feedback/training_sets/train_set_{date_range}.pq"
    test_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/feedback/training_sets/test_set_{date_range}.pq"
    validation_s3_url = f"s3://vr-timestamp/bi_sources/occupancy_model/feedback/training_sets/validation_set_{date_range}.pq"
    train_df_minority_upsampled.to_parquet(train_s3_url, index=False)
    test_df.to_parquet(test_s3_url, index=False)
    validation_df.to_parquet(validation_s3_url, index=False)

    return {
        "train_s3_url": train_s3_url,
        "test_s3_url": test_s3_url,
        "validation_s3_url": validation_s3_url,
    }
