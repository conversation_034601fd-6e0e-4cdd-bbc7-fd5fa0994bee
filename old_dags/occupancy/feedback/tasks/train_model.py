from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def train_model(
    train_s3_url: str,
    test_s3_url: str,
    validation_s3_url: str,
    date_range: str,
):
    import mlflow
    import matplotlib.pyplot as plt
    import optuna
    import os
    import pandas as pd
    import shap
    from sklearn.metrics import roc_auc_score, RocCurveDisplay, log_loss, accuracy_score
    import tempfile
    import xgboost as xgb

    optuna.logging.set_verbosity(optuna.logging.WARNING)

    mlflow.set_tracking_uri(uri="https://mlflow.k8s.eltoro.com")

    train_df = pd.read_parquet(train_s3_url)
    test_df = pd.read_parquet(test_s3_url)
    validation_df = pd.read_parquet(validation_s3_url)
    train_df["occupancy_pred"] = pd.to_numeric(train_df["occupancy_pred"])
    test_df["occupancy_pred"] = pd.to_numeric(test_df["occupancy_pred"])
    validation_df["occupancy_pred"] = pd.to_numeric(validation_df["occupancy_pred"])
    
    train_df = train_df.drop(columns=["rdi", "occupancy_prob", "ref_id", "ethash", "report_date"])
    test_df = test_df.drop(columns=["rdi", "occupancy_prob", "ref_id", "ethash", "report_date"])
    validation_df = validation_df.drop(columns=["rdi", "occupancy_prob", "ref_id", "ethash", "report_date"])

    y_train = train_df["occupancy_pred"]
    X_train = train_df.drop(columns=["occupancy_pred"])
    y_test = test_df["occupancy_pred"]
    X_test = test_df.drop(columns=["occupancy_pred"])
    y_valid = validation_df["occupancy_pred"]
    X_valid = validation_df.drop(columns=["occupancy_pred"])

    def upload_df_to_mlflow(df: pd.DataFrame, dataset_name: str, run_id: str, has_target: bool = False):
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file_path = os.path.join(temp_dir, f"{dataset_name}.pq")
            df.to_parquet(temp_file_path, index=False)
            mlflow.log_artifact(temp_file_path, "datasets/")
        if has_target:
            target="occupancy_pred"
        dataset = mlflow.data.from_pandas(
            df,
            name=dataset_name,
            source=f's3://awseast-prod-mlflow/2/{run_id}/artifacts/datasets/{dataset_name}.pq',
            targets=target
        )
        mlflow.log_input(dataset)
        return dataset

    def objective(trial):
        param = {
            "seed": 777,
            "eval_metric": roc_auc_score,
            # "enable_categorical": True,
            "verbosity": 0,
            "objective": "binary:logistic",
            "n_estimators": trial.suggest_int("n_estimators", 50, 350, step=50),
            "learning_rate": trial.suggest_float("learning_rate", 1e-3, 0.15, log=True),
            "booster": "gbtree", # "dart"
            # L2 regularization weight.
            "lambda": trial.suggest_float("lambda", 1e-8, 0.90, log=True),
            # L1 regularization weight.
            "alpha": trial.suggest_float("alpha", 1e-8, 1.00, log=True),
            # sampling ratio for training data.
            "subsample": trial.suggest_float("subsample", 0.1, 0.80),
            # sampling according to each tree.
            "colsample_bytree": trial.suggest_float("colsample_bytree", 0.2, 1.0),
            # maximum depth of the tree, signifies complexity of the tree.
            "max_depth" : trial.suggest_int("max_depth", 3,15, step=2),
            # minimum child weight, larger the term more conservative the tree.
            "min_child_weight" : trial.suggest_int("min_child_weight", 2, 10),
            "eta" : trial.suggest_float("eta", 1e-8, 1.0, log=True),
            # defines how selective algorithm is.
            "gamma" : trial.suggest_float("gamma", 1e-8, 1.0, log=True),
            "grow_policy" : trial.suggest_categorical("grow_policy", ["depthwise", "lossguide"]),
        }
        # Evaluate model
        model = xgb.XGBClassifier(
            enable_categorical=True,
            **param,
            )

        model.fit(X_train, y_train)
        print(roc_auc_score(y_test, model.predict_proba(X_test)[:,1]))
        return roc_auc_score(y_test, model.predict_proba(X_test)[:,1]) 

    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=500, show_progress_bar=False, n_jobs=-1)
    model = xgb.XGBClassifier(
        seed=777,
        eval_metric=roc_auc_score,
        objective="binary:logistic",
        # enable_categorical=True,
        **study.best_params
    )
    model.fit(X_train, y_train)

    print(f"{date_range}_accuracy: {accuracy_score(y_valid, model.predict(X_valid))}")
    active_run = mlflow.start_run(
        experiment_id=2, 
        run_name=f"feedback_dag_test_{date_range}_unbalanced_rdi_gone"
    )
    mlflow.xgboost.log_model(model, "model", model_format="json") #input_example=X_train ...mlflow dont support categories https://github.com/mlflow/mlflow/issues/3849
    mlflow.log_params(model.get_params())
    mlflow.log_metric(key="log_loss", value=log_loss(y_valid, model.predict_proba(X_valid)))
    mlflow.log_metric(key="roc_auc", value=roc_auc_score(y_valid, model.predict_proba(X_valid)[:,1]))
    upload_df_to_mlflow(train_df, "train_set", active_run.info.run_id, has_target=True)
    validation_set = upload_df_to_mlflow(validation_df, "validation_set", active_run.info.run_id, has_target=True)
    upload_df_to_mlflow(test_df, "test_set", active_run.info.run_id, has_target=True)

    mlflow.evaluate(
        model=mlflow.pyfunc.load_model(f"runs:/{mlflow.active_run().info.run_id}/model"),
        data=validation_set, 
        model_type="classifier", 
        targets=None, #an mlflow dataset is being used so mlflow errors out if the target is specified
        evaluator_config={"default":{"log_model_explainability":False}}
    )
    explainer = shap.TreeExplainer(model)
    shap_values = explainer(X_train)

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_file_path = os.path.join(temp_dir, "roc_auc.png")
        RocCurveDisplay.from_estimator(model, X_valid, y_valid)
        plt.savefig(temp_file_path, bbox_inches='tight')
        mlflow.log_artifact(temp_file_path)
        plt.close()
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_file_path = os.path.join(temp_dir, "beeswarm_plt.png")
        shap.plots.beeswarm(shap_values, show=False)
        plt.savefig(temp_file_path, bbox_inches='tight')
        mlflow.log_artifact(temp_file_path)
        plt.close()
        
    mlflow.end_run()
