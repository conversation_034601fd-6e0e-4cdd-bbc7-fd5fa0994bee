from airflow.decorators import task


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    # python_version="3.10"
)
def generate_feedback_occupancy_set(date_range: str):
    """
    Fetches the feedback sets provided by client (real results from previous runs)
    Fetches the occupancy sets (already processed observations from previous runs)
    Combines both of those sets together
    """
    import pandas as pd
    from datascience_implement_cache import DIC, geocoder
    import old_dags.occupancy.schemas.occupancy_14d as occ_columns
    from old_dags.occupancy.schemas.occupancy_14d import occupancy_14d_schema
    from old_dags.occupancy.schemas.occupancy_30d import occupancy_30d_schema

    def fetch_feedback() -> pd.DataFrame:
        feedback_may_df = pd.read_csv(
            "s3://vr-timestamp/bi_sources/occupancy_model/feedback/FM_CL_results_run_date_05212024_FM_Occ_status.csv",
            dtype={"zip": str},
        )
        feedback_may_df = feedback_may_df[
            ["street_address", "zip", "FM Master Occupancy 5/30"]
        ]
        feedback_may_df = feedback_may_df.rename(
            columns={
                "street_address": "address1",
                "FM Master Occupancy 5/30": occ_columns.OCCUPANCY_PRED,
            }
        )
        feedback_may_df["month"] = "may"

        feedback_june_df = pd.read_csv(
            "s3://vr-timestamp/bi_sources/occupancy_model/feedback/RFI Test Loans_CoreLogic_June_RESPONSE_CL.csv",
            dtype={"zip": str},
        )
        feedback_june_df = feedback_june_df[["address1", "zip", "7/1/24 MASTER"]]
        feedback_june_df = feedback_june_df.rename(
            columns={
                "7/1/24 MASTER": occ_columns.OCCUPANCY_PRED,
            }
        )
        feedback_june_df["month"] = "june"

        feedback_df = pd.concat([feedback_may_df, feedback_june_df])
        feedback_df[occ_columns.OCCUPANCY_PRED] = feedback_df[
            occ_columns.OCCUPANCY_PRED
        ].str.lower()
        feedback_df = feedback_df.query(f"{occ_columns.OCCUPANCY_PRED} != 'unknown'")
        feedback_df[occ_columns.OCCUPANCY_PRED] = feedback_df[
            occ_columns.OCCUPANCY_PRED
        ].map({"occupied": 1, "vacant": 0})
        feedback_df = feedback_df.reset_index(drop=True)
        geo_client = geocoder.Geocoder(DIC("occupancy", "ivan.trost"))
        feedback_df = geo_client.get_ethashes(
            feedback_df, adr1_idx=0, adr2_idx=None, zip_idx=1
        )
        feedback_df = feedback_df.drop_duplicates(subset=["etHashV1", "month"])
        return feedback_df

    def fetch_occupancy_set() -> pd.DataFrame:
        may_results_files = [
            "fannie_mae_request_may_part0_2024-05-16.pq",
            "fannie_mae_request_may_part10_2024-05-16.pq",
            "fannie_mae_request_may_part11_2024-05-16.pq",
            "fannie_mae_request_may_part12_2024-05-16.pq",
            "fannie_mae_request_may_part13_2024-05-16.pq",
            "fannie_mae_request_may_part14_2024-05-16.pq",
            "fannie_mae_request_may_part1_2024-05-16.pq",
            "fannie_mae_request_may_part2_2024-05-16.pq",
            "fannie_mae_request_may_part3_2024-05-16.pq",
            "fannie_mae_request_may_part4_2024-05-16.pq",
            "fannie_mae_request_may_part5_2024-05-16.pq",
            "fannie_mae_request_may_part6_2024-05-16.pq",
            "fannie_mae_request_may_part7_2024-05-16.pq",
            "fannie_mae_request_may_part8_2024-05-16.pq",
            "fannie_mae_request_may_part9_2024-05-16.pq",
        ]
        may_results_df = pd.DataFrame()
        for parquet_file in may_results_files:
            may_results_df = pd.concat(
                [
                    may_results_df,
                    pd.read_parquet(
                        path=f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/occupancy_set/date_range={date_range}/{parquet_file}"
                    ),
                ]
            )
        may_results_df["month"] = "may"

        june_results_files = [
            "fannie_mae_request_june_part0_2024-06-08.pq",
            "fannie_mae_request_june_part1_2024-06-08.pq",
            "fannie_mae_request_june_part2_2024-06-08.pq",
            "fannie_mae_request_june_part3_2024-06-08.pq",
            "fannie_mae_request_june_part4_2024-06-08.pq",
        ]
        june_results_df = pd.DataFrame()
        for parquet_file in june_results_files:
            june_results_df = pd.concat(
                [
                    june_results_df,
                    pd.read_parquet(
                        f"s3://vr-timestamp/bi_sources/occupancy_model/enrichment_results_v2/occupancy_set/date_range={date_range}/{parquet_file}"
                    ),
                ]
            )
        june_results_df["month"] = "june"
        occupancy_set = pd.concat([may_results_df, june_results_df])
        occupancy_set = occupancy_set.reset_index(drop=True)
        occupancy_set = occupancy_set.dropna(subset=[occ_columns.ETHASH])
        occupancy_set = occupancy_set.drop(
            columns=[occ_columns.OCCUPANCY_PRED, occ_columns.OCCUPANCY_PROB]
        )
        return occupancy_set

    feedback_df = fetch_feedback()
    occupancy_set = fetch_occupancy_set()
    merged_df = pd.merge(
        left=occupancy_set,
        right=feedback_df,
        left_on=[occ_columns.ETHASH, "month"],
        right_on=["etHashV1", "month"],
        how="right",
    )
    # TODO train on 0
    merged_df = merged_df.query(f"{occ_columns.COUNT_UNIQ_IFAS}>0")
    merged_df = merged_df.query(
        f"({occ_columns.HOUSEHOLD_DATA_AVAILABLE} == False and {occ_columns.HOUSEHOLD_MATCHED} == False) or ({occ_columns.HOUSEHOLD_DATA_AVAILABLE} == True and {occ_columns.HOUSEHOLD_MATCHED} == True)"
    )
    if date_range == "0_to_14":
        merged_df = occupancy_14d_schema.validate(merged_df)
    else:
        merged_df = occupancy_30d_schema.validate(merged_df)
    s3_output_url = f"s3://vr-timestamp/bi_sources/occupancy_model/feedback/training_sets/feedback_occupancy_set_{date_range}.pq"
    merged_df.to_parquet(s3_output_url, index=False)
    return s3_output_url