### IMPORTS ###
CUSTOMER_query = '''
WITH customer AS (
    SELECT 
        scc.loyaltyid, 
        scc.joindate, 
        scc.loyaltyaddress.street loyalty_street,
        scc.loyaltyaddress.city loyalty_city,
        scc.loyaltyaddress.state loyalty_state,
        scc.loyaltyaddress.postalcode loyalty_postalcode,
        loy.ethashv1 AS loyalty_ethashv1,
        loy.ethashv2 AS loyalty_ethashv2,
        loy.matchcode AS loyalty_matchcode,
        scc.appendaddress.street append_street, 
        scc.appendaddress.city append_city, 
        scc.appendaddress.state append_state, 
        scc.appendaddress.postalcode append_postalcode,
        ap.ethashv1 AS append_ethashv1,
        ap.ethashv2 AS append_ethashv2,
        ap.matchcode AS append_matchcode,
        dt transfer_date
    FROM 
        "s3"."external_shoe_carnival"."customer" scc
    LEFT JOIN "olympus"."bronze_shoe_carnival"."append_ethash_bdg" ap
    ON scc.loyaltyid = ap.loyaltyid
    LEFT JOIN "olympus"."bronze_shoe_carnival"."loyalty_ethash_bdg" loy
    ON scc.loyaltyid = loy.loyaltyid
    WHERE scc.dt = (SELECT MAX(dt) FROM s3.external_shoe_carnival."customer$partitions")
        AND ((loy.ethashv1 IS NOT NULL AND loy.ethashv1 != '')
        OR (ap.ethashv1 IS NOT NULL AND ap.ethashv1 != ''))
), combined_ethash AS (
    SELECT
        loyaltyid,
        joindate,
        CASE WHEN LENGTH(loyalty_ethashv1) > 0
            THEN loyalty_ethashv1 
            ELSE append_ethashv1
            END AS ethashv1,
        CASE WHEN LENGTH(loyalty_ethashv1) > 0
            THEN loyalty_street 
            ELSE append_street
            END AS street,
        CASE WHEN LENGTH(loyalty_ethashv1) > 0
            THEN loyalty_city 
            ELSE append_city
            END AS city,
        CASE WHEN LENGTH(loyalty_ethashv1) > 0
            THEN loyalty_state 
            ELSE append_state
            END AS state,
        CASE WHEN LENGTH(loyalty_ethashv1) > 0
            THEN loyalty_postalcode 
            ELSE append_postalcode
            END AS zip_code
    FROM customer
)
SELECT 
    *,
    CONCAT(
        SUBSTR(ethashv1, 1, 6),
        '0000',
        SUBSTR(ethashv1, 11, 40)
    ) AS ETHashNoUnit,
    CONCAT(
        SUBSTR(ethashv1, 1, 6),
        '0000',
        SUBSTR(ethashv1, 11, 44)
    ) AS ETHash
FROM combined_ethash
'''

GEOGRAPHY_query = '''
SELECT 
    scg.brand, 
    scg.bannerid, 
    scg.region, 
    scg.district, 
    scg.store,
    scg.storetype, 
    scg.latitude, 
    scg.longitude, 
    scg.openingdate, 
    scg.closingdate, 
    a.street, 
    a.city, 
    a.state, 
    a.postalcode,
    dt transfer_date
FROM 
    "s3"."external_shoe_carnival"."geography" scg
CROSS JOIN UNNEST 
    (scg.address) a (street, city, state, postalcode)
WHERE scg.dt = (SELECT MAX(dt) FROM s3.external_shoe_carnival."geography$partitions")
'''

TRANSACTION_query = '''
WITH transact AS (
SELECT 
    CAST(sct.rtsid AS VARCHAR) rtsid, 
    sct.transdate, 
    sct.channel, 
    sct.store, 
    sct.loyaltyid, 
    SUM(d.units) total_units,
    SUM(CAST(d.sales AS DECIMAL(18,2))) AS total_sales,
    dt transfer_date
FROM 
    "s3"."external_shoe_carnival"."transaction" sct
CROSS JOIN UNNEST (sct.details) d (sku, units, sales)
WHERE sct.transdate >= CAST('2024-01-01' AS DATE)
    AND sct.dt = (SELECT MAX(dt) FROM s3.external_shoe_carnival."transaction$partitions")
GROUP BY 1, 2, 3, 4, 5, 8
ORDER BY 1
)
SELECT * 
FROM transact
WHERE total_sales > 0 
    AND LENGTH(loyaltyid) > 0
'''

### CREATE TABLES ### - Add to the DAG if needed!

# GEOGRAPHY_create = f'''
# CREATE TABLE IF NOT EXISTS s3.{SCHEMA_EXT}.{PREFIX.lower()} (
#    brand varchar,
#    bannerid varchar,
#    region varchar,
#    district varchar,
#    store bigint,
#    storetype varchar,
#    latitude double,
#    longitude double,
#    openingdate date,
#    closingdate date,
#    address array(ROW(street varchar, city varchar, state varchar, postalcode varchar)),
#    dt varchar
# )
# WITH (
#    external_location = 's3://et-data-staging/external/shoecarnival/GEOGRAPHY',
#    format = 'PARQUET',
#    partitioned_by = ARRAY['dt']
# )
# '''

# CUSTOMER_create = f'''
# CREATE TABLE IF NOT EXISTS s3.{SCHEMA_EXT}.{PREFIX.lower()} (
#    loyaltyid varchar,
#    emailsha256 varbinary,
#    phonesha256 varbinary,
#    joindate date,
#    firstname varchar,
#    lastname varchar,
#    loyaltyaddress ROW(street varchar, city varchar, state varchar, postalcode varchar),
#    appendaddress ROW(street varchar, city varchar, state varchar, postalcode varchar),
#    dt varchar
# )
# WITH (
#    external_location = 's3://et-data-staging/external/shoecarnival/CUSTOMER',
#    format = 'PARQUET',
#    partitioned_by = ARRAY['dt']
# )
# '''

# TRANSACTION_create = f'''
# CREATE TABLE IF NOT EXISTS s3.{SCHEMA_EXT}.{PREFIX.lower()} (
#    rtsid varchar,
#    transdate date,
#    channel varchar,
#    store bigint,
#    loyaltyid varchar,
#    details array(ROW(sku varchar, units varchar, sales varchar)),
#    dt varchar
# )
# WITH (
#    external_location = 's3://et-data-staging/external/shoecarnival/TRANSACTION',
#    format = 'PARQUET',
#    partitioned_by = ARRAY['dt']
# )
# '''