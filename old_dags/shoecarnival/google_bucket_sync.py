from airflow.decorators import task
from airflow.exceptions import AirflowFailException
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from datetime import datetime
from etdag import ETDAG
from operators.gcs_to_s3_operator import GCSToS3Operator
import os
import shutil
import gzip
from old_dags.shoecarnival.queries import CUSTOMER_query, GEOGRAPHY_query, TRANSACTION_query
from starburst_geocoder_operator import StarburstGeocoderOperator
import tempfile

### VARIABLES ###

# External
CATALOG_EXT = 's3'
SCHEMA_EXT = 'external_shoe_carnival'
BUCKET_EXT = 'et-data-staging'

# Internal
BUCKET_INT = 'vr-timestamp'
KEY_INT = 'bi_sources/shoe_carnival/exports/'

# Bronze
CATALOG_BR = 'olympus'
SCHEMA_BR = 'bronze_shoe_carnival'

# Google Cloud Storage
GCS_BUCKET = "scvl_to_eltoro"
GCS_PREFIX = ["CUSTOMER", "GEOGRAPHY", "TRANSACTION"]

# Logical Date
TRANSFER_DATE = '{{ data_interval_end.strftime("%Y-%m-%d") }}'

### TASKS ###

# Query and Drop CSV in destination path
@task(task_id=f'trino_to_s3_csv_task')
def trino_to_s3_csv_task(prefix: str, bucket_name: str = BUCKET_INT, key: str = KEY_INT):

    if prefix == None:
        raise AirflowFailException("Prefix is None")
    
    # Dynamically create the query using the prefix
    query_name = f"{prefix}_query"
    query_var = globals().get(query_name)

    # Get the data from Trino in chunks
    df_chunks = TrinoHook(trino_conn_id="trino_conn").get_pandas_df_by_chunks(
        sql=query_var, 
        chunksize=1_000_000
    )

    # Write the data to a temporary CSV file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
            temp_file_path = temp_file.name
    for i, chunk in enumerate(df_chunks):
        print(f"writing chunk {i}")
        if i == 0:
            chunk.to_csv(temp_file_path, index=False)
        else:
            chunk.to_csv(temp_file_path, mode="a", header=False, index=False)
    
    # Compress the CSV file if needed
    compression_needed = prefix in ["CUSTOMER", "TRANSACTION"]
    if compression_needed:
        compressed_path = temp_file_path + ".gz"
        with open(temp_file_path, 'rb') as f_in, gzip.open(compressed_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
        
        # Remove the uncompressed file
        os.remove(temp_file_path)
    
    # Generate prefix specific s3 key
    if compression_needed:
        file_name = f"{prefix.lower()}_export.csv.gz"
    else:
        file_name = f"{prefix.lower()}_export.csv"
    s3_key = f"{key}{file_name}"

    # Load CSV file to S3
    s3_hook = S3Hook(aws_conn_id="aws_default")
    s3_hook.load_file(
                filename=compressed_path if compression_needed else temp_file_path,
                key=s3_key,
                bucket_name=bucket_name,
                replace=True,
            )
    
    del df_chunks
    return f"CSV file created successfully at path {bucket_name}/{s3_key}"
    
### DAG ###    
with ETDAG(
    dag_id = 'shoecarnival_gcs_s3_transfer',
    description = 'Transfer data from Shoecarnival GCS Bucket to S3 on the 2nd of every month',
    catchup = False,
    default_args = {
         "owner": "Daniel Sos",
         "email": ["<EMAIL>"],
         "email_on_failure": False,
         "email_on_retry": False
    },
    tags=['data-services', 'shoecarnival','team:DND'],
    start_date=datetime(2025, 4, 1),
    schedule_interval="00 11 2 * *"
) as dag:
    
    for PREFIX in GCS_PREFIX:
        
        # Destination Prefix for GSC import
        dest_prefix_gsc = f"external/shoecarnival/{PREFIX}/dt={TRANSFER_DATE}/"

        # Task 1: Copy from GCS to S3
        copy_gsc_to_s3_task = GCSToS3Operator(
            task_id=f'gcs_to_s3_{PREFIX}',
            gcs_bucket=GCS_BUCKET,
            prefix=PREFIX,
            gcp_conn_id='gcp_shoecarnival',
            dest_s3_key=f"s3://{BUCKET_EXT}/{dest_prefix_gsc}",
            dest_aws_conn_id='aws_default',
            replace=True,
            keep_directory_structure=True
        )

        # Task 2: Sync Table Metadata
        sync_table = SQLExecuteQueryOperator(
            task_id=f"sync_table_{PREFIX}",
            conn_id="trino_conn",
            sql=f"CALL {CATALOG_EXT}.system.sync_partition_metadata('{SCHEMA_EXT}', '{PREFIX.lower()}', 'ADD')",
        )

        if PREFIX == "CUSTOMER":
            # Task 3: Geocode the customer table - Loyalty
            bronze_customertable_1 = StarburstGeocoderOperator(
                task_id='bronze_customer_geocoding_loyalty',
                source_table_name='"s3"."external_shoe_carnival"."customer"',
                source_row_identifier_column_name='loyaltyid',
                address1_column_name='loyaltyaddress.street',
                zipcode_column_name='loyaltyaddress.postalcode',
                bridge_table_name='"olympus"."bronze_shoe_carnival"."loyalty_ethash_bdg"',
                geocoder_columns=['etHashV1', 'etHashV2', 'matchCode'],
                bridge_record_ttl_days=365,
                source_where_clause="dt = '{{ data_interval_end.strftime('%Y-%m-%d') }}'"
            )

            # Task 4: Geocode the customer table - Append
            bronze_customertable_2 = StarburstGeocoderOperator(
                task_id='bronze_customer_geocoding_append',
                source_table_name='"s3"."external_shoe_carnival"."customer"',
                source_row_identifier_column_name='loyaltyid',
                address1_column_name='appendaddress.street',
                zipcode_column_name='appendaddress.postalcode',
                bridge_table_name='"olympus"."bronze_shoe_carnival"."append_ethash_bdg"',
                geocoder_columns=['etHashV1', 'etHashV2', 'matchCode'],
                bridge_record_ttl_days=365,
                source_where_clause="dt = '{{ data_interval_end.strftime('%Y-%m-%d') }}'"
            )

            # Task 5: Run the query and create CSV file
            trino_to_s3_csv = trino_to_s3_csv_task(PREFIX)

            copy_gsc_to_s3_task >> sync_table >> bronze_customertable_1 >> bronze_customertable_2 >> trino_to_s3_csv

        else:
            # Task 3: Run the query and create CSV file
            trino_to_s3_csv = trino_to_s3_csv_task(PREFIX)
    
            copy_gsc_to_s3_task >> sync_table >> trino_to_s3_csv

### END OF DAG ###

### -------------------Create Table if NOT exists - Use this function in the for loop if needed------------------- ###
# def hive_table_create_statement(prefix: str):
#     if prefix == None:
#         raise AirflowFailException("Prefix is None")
#     else:
#         query_name = f"{prefix}_create"
#         create_table_statement = globals().get(query_name)
#     trino_hook.run(create_table_statement.format(PREFIX=prefix, SCHEMA_EXT=SCHEMA_EXT))
#     return

### -------------------Checks items uploaded to GCS bucket - Use this function in the for loop if needed------------------- ###
# from airflow.providers.google.cloud.operators.gcs import GCSListObjectsOperator
# check_gcs_files = GCSListObjectsOperator(
#     task_id=f'check_gcs_files_{PREFIX}',
#     bucket=GCS_BUCKET,
#     prefix=PREFIX,
#     gcp_conn_id='gcp_shoecarnival'
# )