from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.stackadapt_import.queries import silver_table_delete_query, silver_table_insert_query, silver_table_sync_partition_query

def delete_silver_performance_hourly():
    # Create a TrinoHook instance to connect to the Trino database
    trino_hook = TrinoHook(trino_conn_id="starburst")

    # Open a connection to Trino and execute the query to delete data
    with trino_hook.get_conn() as conn:
        cur = conn.cursor()
        try:
            # Execute the DELETE query to remove the last 9 days of data from the silver table
            cur.execute(silver_table_delete_query)
            print("Last 9 days of data deleted from the silver table.")
        
        except Exception as e:
            print(f"An error occurred: {e}")
        
        finally:
            # Ensure the cursor is closed after the query execution
            cur.close()

def sync_silver_performance_hourly():
    # Create a TrinoHook instance to connect to the Trino database
    trino_hook = TrinoHook(trino_conn_id="starburst")

    # Open a connection to Trino and execute the query to sync data
    with trino_hook.get_conn() as conn:
        cur = conn.cursor()
        try:
            # Sync partition metadata for the silver table
            cur.execute(silver_table_sync_partition_query)
            print("Partition metadata synced for the silver table.")
        
        except Exception as e:
            print(f"An error occurred: {e}")
        
        finally:
            # Ensure the cursor is closed after the query execution
            cur.close()

def insert_silver_performance_hourly():
    # Create a TrinoHook instance to connect to the Trino database
    trino_hook = TrinoHook(trino_conn_id="starburst")

    # Open a connection to Trino and execute the query to insert bronze data into silver
    with trino_hook.get_conn() as conn:
        cur = conn.cursor()
        try:
            # Execute the INSERT query to insert the last 9 days of data from the bronze table into the silver table
            cur.execute(silver_table_insert_query)
            print("Last 9 days of data inserted into the silver table.")
        
        except Exception as e:
            print(f"An error occurred: {e}")
        
        finally:
            # Ensure the cursor is closed after the query execution
            cur.close()