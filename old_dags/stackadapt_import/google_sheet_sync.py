def google_sheet_sync():
    import pandas as pd
    from airflow.models import Variable
    from airflow.providers.google.suite.hooks.sheets import GSheetsHook

    # Initiate variables for processing: environment, spreadsheet ID, range, and S3 URI.
    env = Variable.get("environment", "dev")
    spreadsheet_id = "12n1-t8Qk2fL37WyoWDDp3ienK9uEtktywX7K61ocHCM"
    range = f"{env}_creative_id_bridge!A2:E"
    s3_uri = "s3://vr-timestamp/bi_sources/stackadapt/id_bridge.csv"

    # Subfunction that retrieves data from Google Sheets and saves it to S3.
    def transfer_id_bridge_table():
        hook = GSheetsHook(gcp_conn_id='googleserviceaccount')
        values = hook.get_values(spreadsheet_id=spreadsheet_id, range_=range)
        df = pd.DataFrame(values,
                          columns=['et_orderline_id', 'sa_campaign_group_id', 'sa_creative_id', 'sa_creative_name',
                                   'et_creative_id'])
        df.to_csv(s3_uri, index=False)

        ## Convert the DataFrame to JSON format for further processing.
        return df.to_json()

    # Return results of the subfunction.
    json_str = transfer_id_bridge_table()
    return json_str
    
def check_missing_entries_v2():
    """
    Check for missing entries using a specific query object. If no disscrepancies are found for the specific time period, or current date,
    or if there are no differences in creatives based on the the silver table vs the Google Sheet, then skip the task. Otherwise,
    create a zendesk ticket with a custom message listing the missing creatives for each orderline ID.
    
    """

    from airflow.providers.trino.hooks.trino import TrinoHook
    from airflow.providers.zendesk.hooks.zendesk import ZendeskHook
    from airflow.exceptions import AirflowSkipException
    from zenpy.lib.api_objects import Ticket, CustomField
    from old_dags.stackadapt_import.queries import discrepancy_query
    from datetime import date
    import pandas as pd

    # Create Trino and Zendesk hook instances, assign today's date to a variable
    trino_hook = TrinoHook(trino_conn_id="trino_conn")
    zendesk_hook = ZendeskHook(zendesk_conn_id="eltoro_zendesk")
    today = date.today()

    # Open a connection to Trino and execute the query to get discrepancies
    with trino_hook.get_conn() as conn:
        cur = conn.cursor()
        try:
            # Execute the discrepancy query
            cur.execute(discrepancy_query)
            # Get the results of the discrepancy query - rows and columns
            rows = cur.fetchall()
            columns = [desc[0] for desc in cur.description]

            # Check if any discrepancies were found
            if rows:
                print("Discrepancies found.")
            else:
                raise AirflowSkipException("No discrepancies found for the specified time period.")
            
        except AirflowSkipException as e:
            raise  # Skip the task if no discrepancies found
        
        except Exception as e:
            print(f"An error occurred: {e}")
            raise # Raise to mark the task as failed
        
        finally:
            # Ensure the cursor is closed after the query execution
            cur.close()
    
    # Initialize a DataFrame with the results
    results_df = pd.DataFrame(rows, columns=columns)

    # Convert 'day' column to datetime
    results_df["day"] = pd.to_datetime(
        results_df['day']
        ).dt.date
    
    # Filter results for the specified date and ensure 'ols_minus_aib_ids' is not empty
    results_df_filtered = results_df[
        (results_df["day"] == today) &
        (results_df["ols_minus_aib_ids"].astype(str) != '[]')]
    
    # Print the number of filtered results
    print(f"Filtered results: {len(results_df_filtered)} rows")

    # Check if there are any missing creatives - if not, skip the task
    if results_df_filtered.empty:
        raise AirflowSkipException("No missing creatives found for the specified date.")
    
    # If there are missing creatives, create a Zendesk ticket with custom message
    else:
        # Rename columns for clarity
        results_df_filtered.rename(
            columns={
                "et_orderline_id": "OrderlineID",
                "ols_minus_aib_ids": "CreativesMissing"
            },
            inplace=True
        )

        # Extract the 'OrderlineID' and 'CreativesMissing' columns and reset the index
        current_missing = results_df_filtered[['OrderlineID', 'CreativesMissing']]
        current_missing.reset_index(
            drop=True, 
            inplace=True
            )

        # Initialize the response message for Zendesk ticket
        response_message = f"""
        The following creatives are missing from the StackAdapt Google Sheet. Please investigate! \n
        """

        # Ensure nice formatting of the response message
        for index, row in current_missing.iterrows():
            message = f"""
            {index+1}. Orderline ID: {row['OrderlineID']} - Creatives Missing: {", ".join(row['CreativesMissing'])}\n
            """
            response_message += message

        # Initialize Zendesk CC IDs for ticket creation - can add more as needed
        zendesk_cc_ids = {
            "<EMAIL>":7096891138331
        }

        # Create a Zendesk ticket with the response message and custom fields
        zendesk_hook.create_tickets(
            Ticket(
                subject=f"Alert: StackAdapt Missing Entries Detected",
                description=response_message,
                collaborator_ids=list(zendesk_cc_ids.values()),
                custom_fields=[CustomField(id=22549614, value="creative_request")], #custom ticket type in zendesk
                tags=["stackadapt_discrepancy"],
            )
        )
    
    print("Done creating Zendesk ticket for missing creatives.")