
def query_and_upsert_reporting_stats_v2(unique_days):
    import json
    import pandas as pd
    from airflow.providers.trino.hooks.trino import Tri<PERSON>Hook
    from elasticsearch import Elasticsearch, helpers
    from old_dags.stackadapt_import.queries import reporting_api_query, narollupv2

    trino_hook = TrinoHook(trino_conn_id="starburst")

    def build_id_for_narollup2(record):
        newtime = record['day'].replace(" ", "_").replace(":", "-")

        if record['org_id'].endswith("-p"):
            id = record["org_id"]
            id = id[:len(id) - 2]
            record["org_id"] = id

        key = f"{str(newtime)}_{str(record['org_id'])}_{str(record['campaign_id'])}_{str(record['order_line_id'])}_{str(record['creative_id'])}_{str(record['creative_name'])}_0_0_{str(record['bidder_source'])}"
        if str(record['campaign_id']) == "--" or str(record['order_line_id']) == '--':
            return '', False
        else:
            return key, True

    def upsert_narollup2(df):
        es_new = Elasticsearch(
            hosts=[{'host': 'elasticsearch.middleearth.eltoro.com', 'port': 9200, "scheme": "http"}],
            request_timeout=100)
        actions = []
        for record in df[narollupv2['columns']].to_dict(orient='records'):
            action = {
                "_index": "networkanalytics",
                "_type": "networkanalytics",
                "_id": build_id_for_narollup2(record)[0],
                "_source": record,
                "doc_as_upsert": "true"}
            actions.append(action)
        response = helpers.bulk(es_new, actions, index="networkanalytics")
        print(response)

    def upsert_date(day):
        # Most of the data preparation is taken care of in the query.\
        with trino_hook.get_conn() as conn:
            print(f"Upserting stats for {day}")
            where_clause = f"AND day = DATE('{day}')"
            print(conn)
            print(conn.cursor)
            print(vars(conn))
            df = pd.read_sql(reporting_api_query.replace(":where_clause", where_clause), conn)
        upsert_narollup2(df)

    for day in json.loads(unique_days):
        upsert_date(day)
