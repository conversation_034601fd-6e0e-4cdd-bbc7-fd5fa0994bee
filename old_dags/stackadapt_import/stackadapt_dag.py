from etdag import ETDAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonVirtualenv<PERSON><PERSON><PERSON>, PythonOperator
from old_dags.stackadapt_import.elasticsearch_insert import query_and_upsert_reporting_stats_v2
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from old_dags.stackadapt_import.google_sheet_sync import google_sheet_sync, check_missing_entries_v2
from old_dags.stackadapt_import.stage_deliveries_external import stage_deliveries_external
from old_dags.stackadapt_import.silver_table_insert import delete_silver_performance_hourly, sync_silver_performance_hourly, insert_silver_performance_hourly

docs = """
This Airflow DAG is designed to synchronize data from external sources into Elasticsearch. It integrates data handling from Google Sheets and potentially other sources, manages data transformations, and upserts into Elasticsearch based on unique day calculations.

The DAG consists of several tasks that perform data fetching, processing, decision making based on the presence of data, and conditional execution of subsequent tasks.

Tasks:
- `sync_google_sheet`: Pulls data from a Google Sheet using a predefined Python function (`google_sheet_sync`). This task retries once in case of failure, with a 10-minute delay between retries.

- `stage_deliveries_ext`: Executes a Python function (`stage_deliveries_external`) to determine the unique days for which data exists. This is a critical task that affects the flow of subsequent tasks. It retries up to three times with a 10-minute delay between retries.

- `branch_operator`: A `BranchPythonOperator` that decides the flow of tasks based on the output of the `stage_deliveries_ext` task. If unique days are found, it routes to the `query_and_upsert_reporting_stats_v1` task; otherwise, it skips to the `skip_processing_task`.

- `skip_processing_task`: A `DummyOperator` used as a placeholder and endpoint for the DAG when there are no unique days to process, effectively doing nothing.

- `v2_es_insert`: These are `PythonVirtualenvOperator` tasks configured to run Python functions (`query_and_upsert_reporting_stats_v2`) inside a virtual environment. These functions handle the upsert operations into Elasticsearch, processing data based on the unique days identified earlier. Each task is set up with specific Python package requirements and retries three times in case of failures, with a 10-minute delay between retries.

Flow:
1. The `sync_google_sheet` task starts the DAG by fetching data from Google Sheets.
2. The `stage_deliveries_ext` task calculates the unique days from the fetched data and passes this information to the `branch_operator`.
3. Based on the presence of unique days, the `branch_operator` directs the flow either to the data processing tasks (`v2_es_insert`) or to the `skip_processing_task`.
4. The `v2_es_insert` tasks perform data upserts into Elasticsearch for the unique days processed.


Tags:
- `application:reporting`: Indicates that the DAG pertains to reporting applications.
- `team:DND`: Tagged for the Do Not Disturb team, likely indicating ownership or primary responsibility for maintenance.

Dependencies:
This DAG depends on the `stackadapt_import` module for its operations, which should include defined functions for interacting with external sources and Elasticsearch.

Owner: Rorie Lizenby
"""

default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}


def branch_based_on_days(**kwargs):
    ti = kwargs['ti']
    unique_days = ti.xcom_pull(task_ids='stage_deliveries_external')
    if len(unique_days) > 0:
        return 'query_and_upsert_reporting_stats_v2'
    else:
        return 'skip_processing_task'


with ETDAG(
    dag_id="stackadapt_stats_import",
    description="",
    start_date=datetime(2025,5,30),
    default_args=default_args,
    schedule_interval="35 * * * *",
    tags=["application:reporting", "team:DND"],
    catchup=False,
) as dag:
    dag.doc_md = docs

    sync_google_sheet = PythonOperator(
        task_id="sync_google_sheet",
        python_callable=google_sheet_sync,
        retries=1,
        retry_delay=timedelta(minutes=10)
    )

    stage_deliveries_ext = PythonOperator(
        task_id="stage_deliveries_external",
        python_callable=stage_deliveries_external,
        retries=3,
        retry_delay=timedelta(minutes=10)
    )

    branch_operator = BranchPythonOperator(
        task_id='branch_based_on_unique_days',
        python_callable=branch_based_on_days,
        provide_context=True,
        retries=2,
        retry_delay=timedelta(minutes=5),
        dag=dag
    )

    skip_processing_task = DummyOperator(
        task_id='skip_processing_task',
        dag=dag
    )

    v2_es_insert = PythonVirtualenvOperator(
        task_id="query_and_upsert_reporting_stats_v2",
        python_callable=query_and_upsert_reporting_stats_v2,
        requirements=[
            "pandas",
            "trino==0.327.0",
            "elasticsearch==7.13.4",
            "boto3",
        ],
        retries=3,
        op_kwargs={
            "unique_days": "{{ ti.xcom_pull(task_ids='stage_deliveries_external') }}"
        },
        retry_delay=timedelta(minutes=10),
    )

    silver_table_delete = PythonOperator(
        task_id="delete_silver_performance_hourly",
        python_callable=delete_silver_performance_hourly,
        retries=3,
        retry_delay=timedelta(minutes=10)
    )

    silver_table_sync = PythonOperator(
        task_id="sync_silver_performance_hourly",
        python_callable=sync_silver_performance_hourly,
        retries=3,
        retry_delay=timedelta(minutes=10)
    )

    silver_table_insert = PythonOperator(
        task_id="insert_silver_performance_hourly",
        python_callable=insert_silver_performance_hourly,
        retries=3,
        retry_delay=timedelta(minutes=10)
    )
    
    missing_entries = PythonOperator(
        task_id="check_missing_entries",
        python_callable=check_missing_entries_v2,
        retries=1,
        retry_delay=timedelta(minutes=10)
    )

sync_google_sheet >> stage_deliveries_ext
sync_google_sheet >> missing_entries
stage_deliveries_ext >> branch_operator
branch_operator >> v2_es_insert >> silver_table_delete >> silver_table_sync >> silver_table_insert
branch_operator >> skip_processing_task