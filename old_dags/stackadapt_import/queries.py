# Query to get the last 3 days worth of (date, hour) pairs from the bronze table
existing_hours_in_external_query = """
SELECT DISTINCT day, hour from s3.bronze_et_stackadapt.log_data ld WHERE day >= CURRENT_DATE - INTERVAL '3' DAY
"""

# Query for getting the data in a format that can be upserted into ElasticSearch
reporting_api_query = """
select 
REPLACE(CONCAT(SUBSTRING(CAST(from_iso8601_timestamp(REPLACE(CONCAT(SUBSTRING(utc_time, 1, 13), ':00:00'), ' ', 'T')) - interval '5' hour AS VARCHAR), 1, 13), ':00:00'), ' ', 'T') "day", 
ld.advertiser_id org_id_appnexus,
ol.org_id org_id,
ol.org_name org_name,
ld.line_item_id campaign_id_appnexus,
ol.campaign_id campaign_id,
ol.campaign_name campaign_name,
ol.order_line_id order_line_id,
ol.order_line_name order_line_name,
ld.campaign_id order_line_id_appnexus,
c.id creative_id, 
c.name creative_name,
ld.creative_id creative_id_appnexus,
CONCAT(CAST(height AS VARCHAR), 'x', CAST(width AS VARCHAR)) size,
0 ol_target_type,
0 ol_creative_type,
COUNT(impressions) FILTER (WHERE impressions = '1') imps,
COUNT(impressions) FILTER (WHERE impressions = '1') imp_requests,
0 imps_blank,
0 imps_psa,
0 imps_psa_error,
0 imps_default_error,
0 imps_default_bidder,
0 imps_kept,
0 imps_resold, 
COUNT(impressions) FILTER (WHERE impressions = '1') imps_rtb,
0 external_impression,
COUNT(impressions) FILTER (WHERE clicks = '1') clicks,
0 external_click,
SUM(CAST(ld.cost AS DOUBLE PRECISION)) cost,
0 cost_including_fees,
0 revenue,
0 revenue_including_fees,
0 booked_revenue,
0 booked_revenue_adv_curr,
0 booked_revenue_ecpm,
0 reseller_revenue,
0 profit,
0 profit_including_fees,
0 commissions,
0 post_click_convs,
0 post_click_revenue,
0 post_view_convs,
0 post_view_revenue,
COUNT(impressions) FILTER (WHERE conversions = '1') conversions,
COUNT(impressions) FILTER (WHERE in_view = '1') imps_viewed,
0 view_measured_imps,
0 data_costs,
0 media_cost_pub_curr,
0 serving_fees,
COUNT(impressions) FILTER (WHERE video_25 = '1') pcts_25, 
COUNT(impressions) FILTER (WHERE video_50 = '1') pcts_50,
COUNT(impressions) FILTER (WHERE video_75 = '1') pcts_75,
0 skips,
COUNT(impressions) FILTER (WHERE video_starts = '1') starts,
COUNT(impressions) FILTER (WHERE video_completed = '1')  completions,
COUNT(impressions) served,
0 errors,
2 bidder_source
from s3.bronze_et_stackadapt.asset_id_bridge idb
JOIN "s3"."silver_platform_services"."creatives" c ON  idb.et_creative_id = c.id
JOIN "s3"."silver_platform_services"."order_lines" ol ON idb.et_orderline_id = ol.order_line_id 
JOIN s3.bronze_et_stackadapt.log_data ld ON ld.line_item_id = idb.sa_campaign_id AND ld.creative_id = idb.sa_creative_id
WHERE c.deployment_destination = 'DEPLOY_DESTINATION_UNKNOWN' and ol.deployment_destination = 'DEPLOY_DESTINATION_UNKNOWN'
:where_clause and cost != ''
group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16
"""

# Deletes the last 9 days worth of data from the silver table 
silver_table_delete_query = """
    DELETE FROM "s3"."silver_et_stackadapt"."performance_hourly"
    WHERE "day" >= (current_date - INTERVAL '9' DAY)
    """

# Sync partition metadata for the silver table
silver_table_sync_partition_query = """
    CALL s3.system.sync_partition_metadata(
    'silver_et_stackadapt',
    'performance_hourly',
    'FULL')
    """

# Inserts the last 9 days worth of data from the bronze table into the silver table
silver_table_insert_query = """
    INSERT INTO "s3"."silver_et_stackadapt"."performance_hourly"
    WITH 
        missing_hours AS (
            SELECT "day", ARRAY_AGG(DISTINCT hour) hours
                FROM "s3"."bronze_et_stackadapt"."log_data"
                WHERE CONCAT(CAST("day" AS VARCHAR), ' ', "hour") NOT IN (
                    SELECT DISTINCT CONCAT(CAST("day" AS VARCHAR), ' ', CAST("hour" AS VARCHAR)) 
                    FROM "s3"."silver_et_stackadapt"."performance_hourly"
                )
            GROUP BY "day"
        ),
        bronze AS (
            SELECT * FROM "s3"."bronze_et_stackadapt"."log_data" WHERE "day" >= (current_date - INTERVAL '9' DAY)
        )    
    SELECT
        TRY_CAST(advertiser_id AS INTEGER) AS advertiser_id, 
        TRY_CAST(line_item_id AS INTEGER) AS line_item_id, 
        TRY_CAST(campaign_id AS INTEGER) AS campaign_id, 
        supply_inventory_type, 
        TRY_CAST(creative_id AS INTEGER) AS creative_id, 
        bid_type, 
        banner_width, 
        banner_height, 
        SUM(TRY_CAST(cost AS DECIMAL)) AS cost, 
        SUM(TRY_CAST(impressions AS INTEGER)) AS impressions, 
        SUM(TRY_CAST(clicks AS INTEGER)) AS clicks, 
        SUM(TRY_CAST(conversions AS INTEGER)) AS conversions, 
        SUM(TRY_CAST(is_measurable AS INTEGER)) AS is_measurable, 
        SUM(TRY_CAST(in_view AS INTEGER)) AS in_view, 
        SUM(TRY_CAST(video_starts AS INTEGER)) AS video_starts, 
        SUM(TRY_CAST(video_25 AS INTEGER)) AS video_25, 
        SUM(TRY_CAST(video_50 AS INTEGER)) AS video_50, 
        SUM(TRY_CAST(video_75 AS INTEGER)) AS video_75, 
        SUM(TRY_CAST(video_completed AS INTEGER)) AS video_completed, 
        SUM(TRY_CAST(engagements AS INTEGER)) AS engagements, 
        SUM(TRY_CAST(total_time AS INTEGER)) AS total_time, 
        SUM(TRY_CAST(page_starts AS INTEGER)) AS page_starts, 
        TRY_CAST(hour AS INTEGER) AS hour, 
        b."day"
    FROM bronze b
    JOIN missing_hours m ON (m."day" = b."day")
    WHERE m."day" = b."day" AND CONTAINS(m."hours", b."hour")
    GROUP BY advertiser_id, line_item_id, campaign_id, supply_inventory_type, bid_type, banner_width, banner_height, creative_id, b."day", hour
    ORDER BY line_item_id, day, hour"""

# Creative Association Discrepancy Query
discrepancy_query = """
    WITH 
        bt AS (
            SELECT 
                day, 
                line_item_id AS campaign_id, 
                COUNT(DISTINCT creative_id) sa_creative_count, 
                SUM(impressions) sa_imps 
            FROM "s3"."silver_et_stackadapt"."performance_hourly" 
            GROUP BY 1, 2
        ),
        jt AS (
            SELECT
                day,
                ld.line_item_id AS campaign_id,
                COUNT(DISTINCT creative_id) et_creative_count,
                COUNT(impressions) FILTER (WHERE impressions = '1') et_imps
            FROM s3.bronze_et_stackadapt.asset_id_bridge idb
            JOIN "s3"."silver_platform_services"."creatives" c 
                ON  idb.et_creative_id = c.id
            JOIN "s3"."silver_platform_services"."order_lines" ol 
                ON idb.et_orderline_id = ol.order_line_id 
            RIGHT JOIN s3.bronze_et_stackadapt.log_data ld 
                ON ld.line_item_id = idb.sa_campaign_id 
                    AND ld.creative_id = idb.sa_creative_id
            WHERE c.deployment_destination = 'DEPLOY_DESTINATION_UNKNOWN' 
                AND ol.deployment_destination = 'DEPLOY_DESTINATION_UNKNOWN' 
                AND day > DATE('2025-06-01')
            GROUP BY 1, 2 
        ),
        comb AS (
            SELECT 
                bt.day, 
                bt.campaign_id, 
                sa_creative_count, 
                et_creative_count, 
                sa_imps, 
                et_imps b
            FROM bt
            LEFT JOIN jt 
                ON bt.day = jt.day 
                AND CAST(bt.campaign_id as varchar) = CAST(jt.campaign_id as varchar)  
            WHERE sa_imps != et_imps
        ),
        asset_bridge AS (
            SELECT DISTINCT 
                aib.et_orderline_id,
                aib.sa_campaign_id,
                ols_creative_ids,
                ARRAY_AGG(DISTINCT aib.et_creative_id) AS aib_creative_ids
            FROM 
                "s3"."bronze_et_stackadapt"."asset_id_bridge" aib
            INNER JOIN (SELECT order_line_id, ARRAY_AGG(id) AS ols_creative_ids
                        FROM "s3"."silver_platform_services"."order_lines" 
                        CROSS JOIN UNNEST(creatives) AS t(refid, id, type)
                        GROUP BY 1) ols
                ON aib.et_orderline_id = ols.order_line_id
            GROUP BY 
                1,2,3
        )
    SELECT 
        comb.*,
        asset_bridge.et_orderline_id,
        asset_bridge.aib_creative_ids,
        asset_bridge.ols_creative_ids,
        ARRAY_EXCEPT(ols_creative_ids, aib_creative_ids) AS ols_minus_aib_ids
    FROM comb
    LEFT JOIN asset_bridge
        ON comb.campaign_id = CAST(asset_bridge.sa_campaign_id AS INTEGER)
    """

# Gen3 specific columns for the reporting API in ElasticSearch
narollupv2 = {
    "columns": [
        "day",
        "org_id_appnexus",
        "org_id",
        "org_name",
        "campaign_id_appnexus",
        "campaign_id",
        "campaign_name",
        "order_line_id",
        "order_line_name",
        "order_line_id_appnexus",
        "creative_id",
        "creative_name",
        "creative_id_appnexus",
        "size",
        "imps",
        "imp_requests",
        "imps_blank",
        "imps_psa",
        "imps_psa_error",
        "imps_default_error",
        "imps_default_bidder",
        "imps_kept",
        "imps_resold",
        "imps_rtb",
        "external_impression",
        "clicks",
        "external_click",
        "cost",
        "cost_including_fees",
        "revenue",
        "revenue_including_fees",
        "booked_revenue",
        "booked_revenue_adv_curr",
        "booked_revenue_ecpm",
        "reseller_revenue",
        "profit",
        "profit_including_fees",
        "commissions",
        "post_click_convs",
        "post_click_revenue",
        "post_view_convs",
        "post_view_revenue",
        "conversions",
        "media_cost_pub_curr",
        "serving_fees",
        "imps_viewed",
        "view_measured_imps",
        "data_costs",
        "pcts_75",
        "pcts_50",
        "skips",
        "starts",
        "completions",
        "pcts_25",
        "served",
        "errors",
        "bidder_source",
        "ol_target_type",
        "ol_creative_type"
    ]
}