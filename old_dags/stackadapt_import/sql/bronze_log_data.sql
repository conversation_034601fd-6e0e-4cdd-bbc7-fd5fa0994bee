CREATE TABLE s3.bronze_et_stackadapt.log_data (
   auction_id varchar,
   advertiser_id varchar,
   line_item_id varchar,
   campaign_id varchar,
   supply_inventory_type varchar,
   utc_time varchar,
   rx_timestamp varchar,
   creative_id varchar,
   category varchar,
   device_id varchar,
   device_type varchar,
   device_make varchar,
   device_model varchar,
   device_language varchar,
   device_carrier varchar,
   device_connection varchar,
   device_isp varchar,
   device_os_vsersion varchar,
   conv_type varchar,
   conv_from varchar,
   ip_address varchar,
   site_id varchar,
   bid_type varchar,
   network_id varchar,
   domain varchar,
   subdomain varchar,
   user_id varchar,
   geo_zip varchar,
   geo_region varchar,
   geo_metro varchar,
   geo_country varchar,
   geo_city varchar,
   platform_browser varchar,
   device_geo_lat varchar,
   device_geo_long varchar,
   banner_width varchar,
   banner_height varchar,
   all_matched_custom_segment varchar,
   cost varchar,
   impressions varchar,
   clicks varchar,
   conversions varchar,
   is_measurable varchar,
   in_view varchar,
   video_starts varchar,
   video_25 varchar,
   video_50 varchar,
   video_75 varchar,
   video_completed varchar,
   engagements varchar,
   total_time varchar,
   page_starts varchar,
   day date,
   hour varchar
)
WITH (
   external_location = 's3a://eltoro-datasources-stackadapt/partitioned',
   format = 'CSV',
   partitioned_by = ARRAY['day','hour'],
   skip_header_line_count = 1
)