CREATE TABLE s3.silver_et_stackadapt.performance_hourly(
advertiser_id integer, 
line_item_id integer, 
campaign_id integer, 
supply_inventory_type varchar, 
creative_id integer, 
bid_type varchar, 
banner_width varchar, 
banner_height varchar, 
cost decimal, 
impressions integer, 
clicks integer, 
conversions integer, 
is_measurable integer, 
in_view integer, 
video_starts integer, 
video_25 integer, 
video_50 integer, 
video_75 integer, 
video_completed integer, 
engagements integer, 
total_time integer, 
page_starts integer, 
hour integer,
day date
)
WITH(
  format='PARQUET',
  partitioned_by=ARRAY['day']
)