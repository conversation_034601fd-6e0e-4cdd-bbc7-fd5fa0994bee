from airflow.models.xcom_arg import XComArg
import pandas as pd
import uuid
import json
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from datetime import datetime, timedelta
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.stackadapt_import.queries import existing_hours_in_external_query

def stage_deliveries_external()-> XComArg:
    def query_existing_hours():
        trino_hook = TrinoHook(trino_conn_id="starburst")
        df = trino_hook.get_pandas_df(existing_hours_in_external_query)
        df['day'] = df['day'].astype(str)
        print("Existing hours found.", len(df))
        return df

    def check_existing_deliveries():
        """Look in S3 for all deliveries from the last 3 days and extract distinct days and hours from filenames."""
        s3_hook = S3Hook(aws_conn_id="aws_default")
        bucket_name = "eltoro-datasources-stackadapt"  # Make sure to define or pass this variable
        prefix = "from-stackadapt/El-toro/"
        objects = s3_hook.list_keys(bucket_name, prefix=prefix)

        # Calculate date range for the last 3 days including today
        today = datetime.now().date()
        two_days_ago = today - timedelta(days=2)  # Include today and the last two days

        # Define a set to hold extracted day and hour
        day_hour_set = set()

        # Define the expected datetime format in the filename
        datetime_format = "%Y-%m-%d %H:%M:%S"

        for key in objects:
            if key.endswith(".csv"):  # Only process CSV files
                filename = key.split('/')[-1]
                try:
                    start_time_str = filename.split('_')[1]
                    start_time = datetime.strptime(start_time_str, datetime_format)
                    # Check if the date is within the last 3 days
                    if two_days_ago <= start_time.date():
                        date_str = start_time.strftime("%Y-%m-%d")
                        hour_str = start_time.strftime("%H")
                        day_hour_set.add((date_str, hour_str, key))
                except (IndexError, ValueError) as e:
                    print(f"Error processing filename {filename}: {e}")
                    pass
        return pd.DataFrame(list(day_hour_set), columns=['day', 'hour', 'key'])

    def partition_new_logs(day, hour, key):
        bucket = "eltoro-datasources-stackadapt"
        df = pd.read_csv(f"s3://{bucket}/{key}", dtype=str)

        # Assuming we are dropping duplicates based on some unique identifier, if needed
        if 'auction_id' in df.columns:
            df.drop_duplicates(subset='auction_id', inplace=True)

        # Save partitioned data back to S3 in a structured partition format
        target_path = f's3://{bucket}/partitioned/day={day}/hour={hour}/{str(uuid.uuid4())}.csv'
        df.to_csv(target_path, index=False)
        print(f"File saved to {target_path}")

    def update_table_partitions():
        trino_hook = TrinoHook(trino_conn_id="starburst")

        # Open a connection to Trino and execute the query to sync the partition metadata
        with trino_hook.get_conn() as conn:
            cur = conn.cursor()
            try:
                # Execute the CALL statement to synchronize the partition metadata
                cur.execute("CALL s3.system.sync_partition_metadata('bronze_et_stackadapt', 'log_data', 'ADD')")
                print("Partitions metadata synced.")
            finally:
                # Ensure the cursor is closed after the query execution
                cur.close()

    existing_hours = query_existing_hours()
    deliveries_df = check_existing_deliveries()

    if 'day' in deliveries_df.columns and 'hour' in deliveries_df.columns and 'day' in existing_hours.columns and 'hour' in existing_hours.columns:
        merged_df = pd.merge(deliveries_df, existing_hours, how='left', on=['day', 'hour'], indicator=True)
        print("Merge successful. Here are the missing entries:", merged_df[merged_df['_merge'] == 'left_only'])
    else:
        print("Merge failed due to missing columns.")

    # Process each missing hour
    for i, row in merged_df[merged_df['_merge'] == 'left_only'].iterrows():
        partition_new_logs(row['day'], row['hour'], row['key'])

    # Update table partitions once after all new logs are processed
    try:
        update_table_partitions()
    except:
        print("Failed to update table partitions.")

    # Return unique days impacted
    unique_days = list(merged_df[merged_df['_merge'] == 'left_only']['day'].unique())
    return json.dumps(unique_days)