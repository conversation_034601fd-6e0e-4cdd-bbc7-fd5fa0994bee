from etdag import ETDAG
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from airflow.decorators import task
import pandas as pd
from airflow.models import Variable
from datetime import timedelta

# sql table creation
# CREATE TABLE IF NOT EXISTS s3.prod_pixel_audience_external.ethashes (
#    ethash varchar,
#    ref_id varchar,
#    "date" date
# )
# WITH (
#    external_location = 's3://vr-timestamp/bi_sources/pixel_audiences/filtered/',
#    format = 'CSV',
#    skip_header_line_count=1,
#    partitioned_by = ARRAY['ref_id','date']
# )

target_buckets = Variable.get("pixel_ethash_sync_target_buckets", deserialize_json=True)

with ETDAG(
    dag_id="pixel_ethash_sync",
    schedule_interval="0 16 * * *",
    catchup=False,
    default_args={
        "owner": "Panama",
        "retries": 3,  # Add retries for database queries and S3 operations
        "retry_delay": timedelta(minutes=5),  # Wait before retrying
        "retry_exponential_backoff": True,  # Handle database connectivity issues
        "max_retry_delay": timedelta(minutes=20),  # Cap maximum delay
    },
    description="updates starburst pixel ethash table with the latest results. Also reuploads requested ethash results to s3 for R&A",
    is_paused_upon_creation=True,
    tags=["pixel"]
) as dag:
    @task(
        max_active_tis_per_dag=3,
        retries=4,  # Higher retries for database queries and S3 operations
        retry_delay=timedelta(minutes=8),  # Longer delay for large data processing
    )
    def update_s3_ethashes(ref_id: str):
        """
        This function uses Starburst to query 1 year worth of ethash results of the passed pixel ref_id (bucket_id).
        The queried data is then uploaded to S3 for R&A to use in PowerBi. The whole results csv is rewritten every time.

        :param ref_id: The ID of the product to query data.
        """
        env = Variable.get("environment")
        s3_output_url = f"s3://vr-timestamp/bi_sources/{env}/wth_audience/{ref_id}.csv.gz"
        trino_hook = TrinoHook(
            trino_conn_id="trino_conn"
        )
        df_chunks = pd.read_sql(
            f"""
                SELECT DISTINCT ref_id as bucket_id, ethash, date
                FROM s3.bronze_eltoro_audiences.web2home_filtered
                WHERE DATE(date) > date_add('year', -1, current_date)
                AND ref_id = '{ref_id}'
                AND ethash IS NOT NULL
                AND ethash != ''
                ORDER BY date
            """,
            chunksize=5_000_000,
            con=trino_hook.get_conn()
        )
        for i, chunk in enumerate(df_chunks):
            if i == 0:
                chunk.to_csv(s3_output_url, mode="w", compression="gzip", index=False, header=True)
            else:
                chunk.to_csv(s3_output_url, mode="a", compression="gzip", index=False, header=False)

    update_s3_ethashes.expand(ref_id=target_buckets)