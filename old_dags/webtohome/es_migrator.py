from etdag import ETDAG
from airflow.models.param import Param
from airflow.decorators import task
import pandas as pd
import boto3
import requests
from requests.auth import HTTPBasicAuth
from concurrent.futures import ThreadPoolExecutor
import json
from airflow.models import Variable
from io import StringIO

with ETDAG(
    dag_id="wth_es_sync",
    schedule="0 4 * * *",  # Run at 00:00 EDT (4:00 UTC)
    catchup=False,
    default_args={
        "owner": "panama",
        "retries": 3,
    },
    params={
        "report_date": Param(
            default=None,
            type=["string", "null"],
            format="date",
            description="report_date to run",
        ),
    },
    max_active_runs=1,
    is_paused_upon_creation=True,
    et_failure_msg=True,
    tags=["pixel", "wth"],
) as dag:

    @task
    def insert_weblogs_into_es(
        report_date: str, logical_date: str, s3_bucket="pixel--logs"
    ):
        def get_s3_urls(s3_bucket: str, report_date_str: str):
            s3 = boto3.client("s3")
            s3_response = s3.list_objects_v2(
                Bucket=s3_bucket, Prefix=f"EZO4TWF936UJB.{report_date_str}"
            )
            urls = []
            while True:
                for obj in s3_response.get("Contents", []):
                    if report_date_str in obj["Key"]:
                        urls.append(f"s3://{s3_bucket}/{obj['Key']}")
                # Check if there is a next page
                if s3_response.get("IsTruncated"):
                    next_token = s3_response.get("NextContinuationToken")
                    s3_response = s3.list_objects_v2(
                        Bucket=s3_bucket, ContinuationToken=next_token
                    )
                else:
                    break
            return urls

        def process_weblog_file(s3_url):
            print("Processing", s3_url)
            df = pd.read_csv(
                s3_url,
                compression="gzip",
                sep="\t",
                skiprows=2,
                dtype=str,
                names=[
                    "date",
                    "time",
                    "x-edge-location",
                    "sc-bytes",
                    "c-ip",
                    "cs-method",
                    "cs(Host)",
                    "cs-uri-stem",
                    "sc-status",
                    "cs(Referer)",
                    "cs(User-Agent)",
                    "cs-uri-query",
                    "cs(Cookie)",
                    "x-edge-result-type",
                    "x-edge-request-id",
                    "x-host-header",
                    "cs-protocol",
                    "cs-bytes",
                    "time-taken",
                    "x-forwaded-for",
                    "ssl-protocol",
                    "ssl-cipher",
                    "x-edge-response-result-type",
                    "cs-protocol-version",
                    "fle-status",
                    "fle-encrypted-data",
                    "c-port",
                    "time-to-first-byte",
                    "x-edge-detailed-result-type",
                    "sc-content-type",
                    "sc-content-len",
                    "sc-range-start",
                    "sc-range-end",
                ],
            )
            df["date"] = df.apply(lambda row: f"{row['date']}T{row['time']}", axis=1)
            bulk_data = []
            for _, row in df.iterrows():
                # Add action and data pair
                bulk_data.append({"index": {"_index": pixel_index_name}})
                bulk_data.append(row.to_dict())

                # Send in batches of 5_000 documents
                if len(bulk_data) >= 10_000:  # 5_000 documents (2 lines per doc)
                    response = requests.post(
                        f"{base_url}/_bulk",
                        headers=headers,
                        data="\n".join(map(json.dumps, bulk_data)) + "\n",
                        auth=auth,
                        verify=False,
                    )
                    if response.status_code != 200:
                        raise Exception(
                            f"Failed to bulk insert data into ES: {response.text}"
                        )
                    bulk_data = []

            # Send any remaining documents
            if bulk_data:
                response = requests.post(
                    f"{base_url}/_bulk",
                    headers=headers,
                    data="\n".join(map(json.dumps, bulk_data)) + "\n",
                    auth=auth,
                    verify=False,
                )
                if response.status_code != 200:
                    raise Exception(
                        f"Failed to bulk insert data into ES: {response.text}"
                    )

        if report_date == "None":
            report_date = logical_date
        creds = json.loads(Variable.get("wth_es_creds"))

        base_url = creds["base_url"]
        pixel_date = pd.to_datetime(report_date).strftime("%-Y-%-m-%-d")
        pixel_index_name = f"pixel-{pixel_date}"
        s3_urls = get_s3_urls(s3_bucket, pd.to_datetime(report_date).strftime("%Y-%m-%d"))
        headers = {
            "Content-Type": "application/json",
        }

        auth = HTTPBasicAuth(creds["user"], creds["password"])
        response = requests.delete(
            f"{base_url}/{pixel_index_name}",
            headers=headers,
            auth=auth,
            verify=False,
        )
        if response.status_code != 200 and response.status_code != 404:
            raise Exception(f"Failed to delete previous index: {response.text}")

        with ThreadPoolExecutor(max_workers=8) as executor:
            executor.map(process_weblog_file, s3_urls)


    @task
    def purge_elasticsearch():
        creds = json.loads(Variable.get("wth_es_creds"))
        base_url = creds["base_url"]
        auth = HTTPBasicAuth(creds["user"], creds["password"])

        url = f"{base_url}/_cat/indices/pixel-*-*-*?v&s=index:desc"
        # Send the GET request
        response = requests.get(url, auth=auth, verify=False,)
        data = StringIO(response.text)
        df = pd.read_csv(data, delim_whitespace=True)
        df["collection_date"] = df["index"].apply(lambda x: "-".join(x.split("-")[1:]))
        df["collection_date"] = pd.to_datetime(df["collection_date"])
        print(f"current index dates: {df['collection_date'].to_list()}")
        filtered_df = df[
            df["collection_date"] < (pd.Timestamp.today() - pd.Timedelta(days=30))
        ]
        index_values = filtered_df["index"].tolist()
        for index_value in index_values:
            response = requests.request(
                "DELETE",
                f"{base_url}/{index_value}",
                auth=auth,
                verify=False,
            )

    purge_elasticsearch_task = purge_elasticsearch()

    insert_weblogs_into_es_task = insert_weblogs_into_es(
        report_date="{{ params.report_date }}",
        logical_date="{{ logical_date }}",
    )
    insert_weblogs_into_es_task  >> purge_elasticsearch_task
