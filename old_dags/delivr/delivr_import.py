from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
import logging
from typing import Dict
from datetime import timedelta, datetime
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models import Variable
import re

logger = logging.getLogger(__name__)
import botocore.credentials
from etdag import task_slack_error_alert
from rclone_python import rclone

default_args = {
    "owner": "BP",
    "retries": 3,  # Each task retries up to 3 times on failure
    "retry_delay": timedelta(seconds=30),
}

ET_CONN = "s3_conn"
DELIVR_CONN = "delivr_s3_conn"
DELIVR_REGION = "us-west-2"
ET_REGION = "us-east-1"

env = Variable.get("environment")
rclone_args = [
    "--transfers=16",
    "--checkers=32",
    "--s3-chunk-size=32M",
    "--s3-upload-concurrency=16",
    "--buffer-size=64M",
    "--fast-list",
    "--ignore-existing",
]

if env == "dev":
    rclone_args.append("--dry-run")


def s3conn_to_creds(s3_creds: str) -> botocore.credentials.Credentials:
    """
    Fetch S3 credentials for a connection.
    """
    s3_hook = S3Hook(s3_creds)
    session = s3_hook.get_session()
    creds = session.get_credentials()
    return creds


def rclone_conn_str(creds: botocore.credentials.Credentials, region_name: str) -> str:
    """
    Generate an rclone connection string for S3.
    """
    rclone_conn = (
        f"s3,provider=AWS,env_auth=false,region={region_name},"
        f"access_key_id={creds.access_key},secret_access_key={creds.secret_key}"
    )
    if creds.token:
        rclone_conn += f",session_token={creds.token}"
    return f":{rclone_conn}:"


def batch_copy_folder(
    source_path: str, dest_path: str, source_pre: str, dest_pre: str
) -> bool:
    """
    Efficiently copy a folder using rclone with optimized settings for cross-region transfer.
    """
    try:
        src_loc = f"{source_path}/{source_pre}"  # source_pre = dt=**********
        dest_loc = f"{dest_path}/{dest_pre}"

        logger.info(f"Copying from {src_loc} to {dest_loc}")

        rclone.copy(
            src_loc,
            dest_loc,
            args=rclone_args,
            show_progress=True,
        )
        logger.info(f"✅ Successfully copied folder from: {source_pre} to {dest_pre}")

        return True
    except Exception as e:
        logger.error(f"❌ Error copying folder {source_pre}: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        logger.error(f"Error details: {getattr(e, '__dict__', str(e))}")
        return False


@task()
def list_folders(aws_conn: str, bucket: str, prefix: str) -> list[str]:
    logger.info(f"Listing folders in bucket: {bucket} with prefix: {prefix}")
    s3_hook = S3Hook(aws_conn_id=aws_conn)
    s3_client = s3_hook.get_conn()

    paginator = s3_client.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket, Prefix=prefix)

    folders = set()
    for page in pages:
        for obj in page.get("Contents", []):
            key = obj["Key"]
            if key.endswith("/"):
                continue  # skip empty directory markers

            # Remove the prefix and get the first subfolder segment
            relative_key = key[len(prefix) :]  # e.g., "dt=2025-05-25/file.csv"
            parts = relative_key.split("/", 1)
            if parts:
                folders.add(
                    f"{prefix}{parts[0]}/"
                )  # e.g., "delivr/foureyes/dt=2025-05-25/"
    return sorted(folders)


@task
def get_keys_to_copy(source_folders: list[str], dest_folders: list[str]) -> list[dict]:
    keys_to_copy = []

    for folder in source_folders:
        folder_clean = folder.strip("/")

        # Handle dt=YYYYMMDDHH
        dt_match = re.fullmatch(r"dt=(\d{10})", folder_clean)
        if dt_match:
            date_part = dt_match.group(1)[:8]  # Strip hour
            dt_obj = datetime.strptime(date_part, "%Y%m%d").date()
            formatted = dt_obj.isoformat()  # e.g., 2025-05-29
            dest_key = f"delivr/foureyes/dt={formatted}/"
        else:
            # Regular folder like "new_folder"
            dest_key = f"delivr/foureyes/{folder_clean}/"

        if dest_key not in dest_folders:
            keys_to_copy.append(
                {
                    "source_key": folder,  # e.g. "dt=**********/"
                    "dest_key": dest_key,  # e.g. "delivr/foureyes/dt=2025-05-29/"
                }
            )

    return keys_to_copy


@task
def copy_files_task(files_to_copy: dict) -> None:
    bucket = "4eyes-intent-out"
    source_creds = s3conn_to_creds(DELIVR_CONN)
    dest_creds = s3conn_to_creds(ET_CONN)

    src_conn_str = rclone_conn_str(source_creds, DELIVR_REGION)
    dest_conn_str = rclone_conn_str(dest_creds, ET_REGION)

    dest_bucket = "eltoro-data-sources"

    source_path = f"{src_conn_str}{bucket}"
    dest_path = f"{dest_conn_str}{dest_bucket}"

    logger.info(
        f"Copying from: {files_to_copy['source_key']} to {files_to_copy['dest_key']}"
    )
    batch_copy_folder(
        source_path=source_path,
        dest_path=dest_path,
        source_pre=files_to_copy["source_key"],
        dest_pre=files_to_copy["dest_key"],
    )


with ETDAG(
    dag_id="delivr_import",
    description="Syncs delivr data files between S3 buckets",
    start_date=days_ago(2),
    default_args=default_args,
    on_failure_callback=task_slack_error_alert,
    schedule_interval="0 11 * * *",  # Run every day at 7am
    catchup=False,
    et_failure_msg=True,
    tags=["intent", "delivr"],
) as dag:

    source_folders = list_folders(DELIVR_CONN, "4eyes-intent-out", "")
    dest_folders = list_folders(ET_CONN, "eltoro-data-sources", "delivr/foureyes/")

    files_to_copy = get_keys_to_copy(source_folders, dest_folders)

    copy = copy_files_task.expand(files_to_copy=files_to_copy)

    [source_folders, dest_folders] >> files_to_copy >> copy
