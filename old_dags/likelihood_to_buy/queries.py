ltb_query = """
WITH ceb_filtered AS (
    SELECT *
    FROM olympus.silver_corelogic.clip_ethash_bridge
    WHERE ethashv2 IS NOT NULL AND ethashv2 <> ''
)

SELECT
    ceb.clip,
    ceb.ethashv1,
    ceb.ethashv2,
    ceb.zipcode as h_zipcode,
    ceb.state,
    pb.land_use_code,
    ceb.rdi,
    -- HS features
    hs.total_obs,
    hs.distinct_listings,
    hs.repeat_visitor,
    hs.avg_days_after_listing_date,
    hs.min_days_after_listing_date,
    hs.median_search_range,
    hs.average_search_range,
    hs.how_recently_shopping,
    hs.new_homeshopper,
    -- VL features
    vl.homeowner,
    vl.first_position_mortgage_date,
    vl.first_position_mortgage_maturity_date,
    vl.days_since_first_mortgage,
    vl.days_left_on_mortgage,
    vl.first_position_mortgage_amount,
    vl.estimated_value_mktg,
    vl.estimated_value_high_mktg,
    vl.estimated_value_low_mktg,
    vl.estimated_equity,
    vl.confidence_score_mktg,
    vl.situs_core_based_statistical_area_cbsa,
    -- ACS features
    acs.tot_ratio,
    acs.percent_margin_of_error_housing_tenure_occupied_housing_units_renter_occupied,
    acs.percent_housing_tenure_occupied_housing_units_renter_occupied,
    acs.estimate_housing_tenure_occupied_housing_units_average_household_size_of_renter_occupied_unit,
    acs.margin_of_error_housing_occupancy_total_housing_units_rental_vacancy_rate,
    acs.estimate_housing_occupancy_total_housing_units_rental_vacancy_rate,
    acs."2014_2018_estimates_housing_tenure_occupied_housing_units_average_household_size_of_renter_occupied_unit",
    acs."2019_2023_estimates_housing_tenure_occupied_housing_units_renter_occupied",
    acs."2019_2023_estimates_gross_rent_occupied_units_paying_rent_1_000_to_1_499",
    acs."2014_2018_estimates_housing_tenure_occupied_housing_units_renter_occupied",
    acs."2014_2018_estimates_gross_rent_as_a_percentage_of_household_income_grapi_occupied_units_paying_rent_excluding_units_where_grapi_cannot_be_computed_20_0_to_24_9_percent",
    acs."2014_2018_estimates_gross_rent_as_a_percentage_of_household_income_grapi_occupied_units_paying_rent_excluding_units_where_grapi_cannot_be_computed_less_than_15_0_percent",
    acs."2014_2018_estimates_housing_occupancy_total_housing_units_rental_vacancy_rate",
    acs."2019_2023_estimates_gross_rent_occupied_units_paying_rent_2_500_to_2_999",
    acs."2014_2018_estimates_gross_rent_occupied_units_paying_rent_less_than_500"

FROM ceb_filtered ceb
LEFT JOIN olympus.bronze_corelogic.property_basic2 pb
    ON ceb.clip = pb.clip
LEFT JOIN s3.dev_data_science.likelihood_to_buy_hs_prod hs
    ON ceb.ethashv2 = hs.ethashv2
LEFT JOIN s3.dev_data_science.likelihood_to_buy_vol_lien_dedup vl
    ON ceb.ethashv2 = vl.ethashv2
LEFT JOIN s3.dev_data_science.acs_zip_cbsa_housing_rent_estimates2023 acs
    ON ceb.zipcode = acs.zip
WHERE pb.land_use_code BETWEEN '100' AND '199' limit 10
"""

homeshopper_query = """CREATE MATERIALIZED VIEW IF NOT EXISTS s3.dev_data_science.likelihood_to_buy_hs_prod AS
WITH device_listing_agg_filtered AS (
    SELECT
        hh.ethashv2 AS visitor_ethashv2,
        obs.clip,
        obs.deviceid,
        obs.zipcode,
        obs.listing_price,
        obs.square_feet,
        obs.listing_date,
        obs.off_market_date,
        COUNT(obs.timestamp) AS days_seen,
        DATE_DIFF('day', obs.listing_date, MIN(obs.timestamp)) AS days_after_listing_date,
        MIN(obs.timestamp) AS first_seen_date,
        MAX(obs.timestamp) AS last_seen_date
    FROM s3.gold_real_estate_intender.device_obs_w_listings_90_days obs
    INNER JOIN s3.gold_real_estate_intender.device_ethash_latest hh
        ON hh.deviceid = obs.deviceid
    GROUP BY
        hh.ethashv2,
        obs.clip,
        obs.deviceid,
        obs.zipcode,
        obs.listing_price,
        obs.square_feet,
        obs.listing_date,
        obs.off_market_date
),
household_listing_agg_filtered_otm AS (
    SELECT
        visitor_ethashv2,
        obs.clip,
        obs.zipcode,
        listing_price,
        square_feet,
        listing_date,
        off_market_date,
        6371 * 2 * ASIN(
            SQRT(
                POWER(SIN(RADIANS(zip_visitor.latitude - zip_listing.latitude) / 2), 2) +
                COS(RADIANS(zip_visitor.latitude)) * COS(RADIANS(zip_listing.latitude)) *
                POWER(SIN(RADIANS(zip_visitor.longitude - zip_listing.longitude) / 2), 2)
            )
        ) AS distance_km,
        BOOL_OR(days_seen > 1) AS repeat_visitor,
        SUM(days_seen) AS total_obs,
        MIN(days_after_listing_date) AS days_after_listing_date,
        MIN(first_seen_date) AS first_seen,
        MAX(last_seen_date) AS last_seen
    FROM device_listing_agg_filtered obs
    LEFT JOIN s3.silver_corelogic.clip_ethash_bridge zip_visitor
        ON obs.visitor_ethashv2 = zip_visitor.ethashv2
    LEFT JOIN s3.silver_corelogic.clip_ethash_bridge zip_listing
        ON zip_listing.clip = obs.clip
    GROUP BY
        visitor_ethashv2,
        obs.clip,
        obs.zipcode,
        listing_price,
        square_feet,
        listing_date,
        off_market_date,
        zip_visitor.latitude,
        zip_visitor.longitude,
        zip_listing.latitude,
        zip_listing.longitude
),
distinct_household AS (
    SELECT
        dh.visitor_ethashv2,
        COUNT(dh.clip) AS distinct_listings,
        SUM(dh.total_obs) AS total_obs,
        MIN(dh.first_seen) AS first_seen,
        MAX(dh.last_seen) AS last_seen,
        BOOL_OR(dh.repeat_visitor) AS repeat_visitor,
        APPROX_PERCENTILE(distance_km, 0.5) AS median_search_range,
        AVG(distance_km) AS average_search_range,
        MIN(dh.days_after_listing_date) AS min_days_after_listing_date,
        AVG(dh.days_after_listing_date) AS avg_days_after_listing_date
    FROM household_listing_agg_filtered_otm dh
    GROUP BY dh.visitor_ethashv2
    HAVING SUM(dh.total_obs) < 50
),
final_hs_table AS (
    SELECT
        ceb.clip,
        ceb.ethashv1,
        ceb.ethashv2,
        dh.total_obs,
        dh.distinct_listings,
        dh.repeat_visitor,
        dh.avg_days_after_listing_date,
        dh.min_days_after_listing_date,
        dh.median_search_range,
        dh.average_search_range,
        DATE_DIFF('day', dh.last_seen, CURRENT_DATE) AS how_recently_shopping,
        CASE
            WHEN dh.total_obs IS NULL THEN FALSE
            WHEN DATE_DIFF('day', dh.first_seen, CURRENT_DATE) <= 14 THEN TRUE
            ELSE FALSE
        END AS new_homeshopper
    FROM distinct_household dh
    INNER JOIN olympus.silver_corelogic.clip_ethash_bridge ceb
        ON dh.visitor_ethashv2 = ceb.ethashv2
)
SELECT *
FROM final_hs_table"""

vol_lien_query = """CREATE MATERIALIZED VIEW IF NOT EXISTS s3.dev_data_science.likelihood_to_buy_vol_lien_prod AS
WITH clip_state_mapping AS (
    SELECT 
        ceb.clip,
        ceb.state,
        ceb.ethashv1,
        ceb.ethashv2
    FROM olympus.silver_corelogic.clip_ethash_bridge ceb
),
filtered_vol_lien AS (
    SELECT 
        vl.clip,
        vl.first_position_mortgage_date,
        vl.first_position_mortgage_maturity_date,
        vl.first_position_mortgage_amount,
        vl.estimated_value_mktg,
        vl.estimated_value_high_mktg,
        vl.estimated_value_low_mktg,
        vl.estimated_equity,
        vl.confidence_score_mktg,
        vl.situs_core_based_statistical_area_cbsa,
        vl.owner_occupancy_code,
        vl.owner_1_corporate_indicator,
        vl.owner_2_corporate_indicator
    FROM olympus.silver_corelogic.vol_lien_status_m2_dpc vl
)

SELECT 
    ceb.clip,
    ceb.ethashv1,
    ceb.ethashv2,
    ceb.state,

    CASE 
        WHEN vl.clip IS NOT NULL 
         AND (vl.owner_1_corporate_indicator IS NULL OR vl.owner_2_corporate_indicator IS NULL)
         AND (vl.owner_occupancy_code IN ('O', 'S', 'M') OR vl.owner_occupancy_code IS NULL)
        THEN TRUE
        ELSE FALSE
    END AS homeowner,

    vl.first_position_mortgage_date,
    vl.first_position_mortgage_maturity_date,
    DATE_DIFF('day', vl.first_position_mortgage_date, CURRENT_DATE) AS days_since_first_mortgage,
    DATE_DIFF('day', CURRENT_DATE, vl.first_position_mortgage_maturity_date) AS days_left_on_mortgage,

    vl.first_position_mortgage_amount,
    vl.estimated_value_mktg,
    vl.estimated_value_high_mktg,
    vl.estimated_value_low_mktg,
    vl.estimated_equity,
    vl.confidence_score_mktg,
    vl.situs_core_based_statistical_area_cbsa

FROM clip_state_mapping ceb
LEFT JOIN filtered_vol_lien vl ON ceb.clip = vl.clip"""