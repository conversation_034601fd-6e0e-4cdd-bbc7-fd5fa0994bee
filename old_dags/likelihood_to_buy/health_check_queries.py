# may need to address the logical date in the dag aka use **kwargs in the health check part-- for right now just pretending you can call it from dag.logical_date

post_health_check_queries = """
SELECT 
    CASE 
        WHEN 
            -- 1. There is any data
            (SELECT COUNT(DISTINCT ethashv2) 
             FROM s3.gold_real_estate_intender.prod_ltb_predictions
             WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)) > 0

            -- 2. Input table counts should match output table counts
            AND (
                (SELECT COUNT(1) FROM s3.dev_data_science.likelihood_to_buy_prod_version) = 
                (SELECT COUNT(1) FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('2025-06-01' AS DATE))
            )

            -- 3. Not all predictions will be NULL
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)) !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND ltb_180_prob IS NULL)
            )

            -- 4. Not all predictions will be 0
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)) !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND ltb_180_prob = 0)
            )

            -- 5. Not all predictions will be 1
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)) !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND ltb_180_prob = 1)
            )

            -- 6. Not all predictions will be 0 or 1
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)) !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND (ltb_180_prob = 0 OR ltb_180_prob = 1))
            )

            -- 7. Check that days_since_first_mortgage is set to zero in the predictions
            AND (
                (SELECT COUNT(1) FROM (
                    SELECT ethashv2
                    FROM s3.dev_data_science.likelihood_to_buy_prod_version
                    WHERE days_since_first_mortgage <= 180
                      AND days_since_first_mortgage IS NOT NULL 
                      AND homeowner = TRUE
                      AND ethashv2 IS NOT NULL
                      AND ethashv2 <> ''
                    EXCEPT
                    SELECT ethashv2
                    FROM s3.gold_real_estate_intender.prod_ltb_predictions
                    WHERE run_date = CAST('2025-06-01' AS DATE)
                      AND ltb_180_prob = 0
                ) t) = 0
            )

            -- 8. Homeowner predictions are not all zero or one
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND model_version like "%homeowner%") !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND model_version like "%homeowner%"
                   AND (ltb_180_prob = 0 OR ltb_180_prob = 1))
            )

            -- 9. Renter predictions are not all zero or one
            AND (
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND model_version like "%renter%") !=
                (SELECT COUNT(1) 
                 FROM s3.gold_real_estate_intender.prod_ltb_predictions
                 WHERE run_date = CAST('{{ (dag.logical_date).strftime('%Y-%m-%d') }}' AS DATE)
                   AND model_version like "%renter%"
                   AND (ltb_180_prob = 0 OR ltb_180_prob = 1))
            )

        THEN 1 ELSE 0 
    END AS all_checks_passed
"""

pre_health_check_queries = """
SELECT 
    CASE 
        WHEN 
            -- Check if all materialized views are fresh
            (SELECT COUNT(1) FROM (
                SELECT freshness FROM system.metadata.materialized_views
                WHERE catalog_name = 's3'
                    AND schema_name = 'gold_real_estate_intender'
                    AND name IN (
                        'likelihood_to_buy_prod_version',
                        'likelihood_to_buy_hs_prod',
                        'likelihood_to_buy_vol_lien_prod'
                    )
            ) t WHERE freshness = 'FRESH') = 3
            -- Check if distinct ethashv2 counts match between input and supplemented data
            AND (
                (SELECT COUNT(DISTINCT ethashv2) 
                 FROM s3.dev_data_science.likelihood_to_buy_prod_version)
                =
                (SELECT COUNT(DISTINCT ethashv2)
                 FROM olympus.silver_corelogic.clip_ethash_bridge ceb
                 LEFT JOIN olympus.bronze_corelogic.property_basic2 pb
                     ON ceb.clip = pb.clip
                 WHERE ethashv2 IS NOT NULL
                     AND ethashv2 <> ''
                     AND pb.land_use_code BETWEEN '100' AND '199')
            )
        THEN 1 ELSE 0 
    END AS all_checks_passed
"""
