from airflow.decorators import task
from datetime import datetime, timedelta
from etdag import ETDAG
import pendulum

from old_dags.likelihood_to_buy.health_check_queries import (
    pre_health_check_queries,
    post_health_check_queries,
)
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.exceptions import AirflowFailException
from airflow.models import Variable

import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
)
logger = logging.getLogger(__name__)
env = Variable.get("environment")


default_args = {
    "owner": "BP",
    "max_active_tis_per_dag": 5,
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
}


# def load_artifacts(segment: str):
#     import mlflow
#     from mlflow.tracking import MlflowClient

#     mlflow.set_tracking_uri(uri="https://mlflow.k8s.eltoro.com")
#     client = MlflowClient()

#     if segment == "homeowner":
#         model_name = "ltbh_home_owner_prod"
#     elif segment == "renter":
#         model_name = "ltbh_renter_prod"
#     else:
#         raise ValueError("Segment must be either 'homeowner' or 'renter'")

#     # === Get model version from Production stage ===
#     mv = client.get_model_version_by_alias(name=model_name, alias="prod")
#     if not mv:
#         raise ValueError(f"No model version found in Production for segment: {segment}")

#     version = mv.version
#     model_uri = f"models:/{model_name}@prod"
#     model = mlflow.sklearn.load_model(model_uri=model_uri)

#     # === Load artifacts based on segment ===
#     if segment == "homeowner":
#         encoder = mlflow.sklearn.load_model("models:/ltb_encoder_homeowner@prod")
#         scaler = mlflow.sklearn.load_model("models:/ltb_scaler_homeowner@prod")
#         imputer = mlflow.sklearn.load_model("models:/ltb_imputer_homeowner@prod")

#         artifact_dict = {
#             "encoder": encoder,
#             "scaler": scaler,
#             "imputer": imputer,
#             "expected_features": model.feature_names_in_.tolist()
#         }

#     elif segment == "renter":
#         preprocessor = mlflow.sklearn.load_model("models:/ltb_renter_pre@prod")
#         scaler = mlflow.sklearn.load_model("models:/ltb_renter_scaler@prod")

#         artifact_dict = {
#             "preprocessor": preprocessor,
#             "scaler": scaler,
#             "expected_features": model.feature_names_in_.tolist()
#         }

#     return model, artifact_dict, version


@task
def get_chunks():
    from old_dags.likelihood_to_buy.queries import ltb_query
    from airflow.providers.trino.hooks.trino import TrinoHook
    import awswrangler as wr

    hook = TrinoHook(trino_conn_id="trino_conn")
    df_chunks = hook.get_pandas_df_by_chunks(sql=ltb_query, chunksize=1_500_000)

    s3_base_path = "s3://vr-timestamp/bi_sources/ltb_predictions/tmp_files/"
    paths = []
    for i, chunk in enumerate(df_chunks):
        if chunk.empty:
            continue

        s3_path = f"{s3_base_path}chunk_{i}.csv"
        print(f"Writing chunk {i} to {s3_path}")
        wr.s3.to_csv(df=chunk, path=s3_path, index=False)
        paths.append(s3_path)

    return paths


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    max_active_tis_per_dag=5,
)
def process_chunk(chunk_path: str, run_date: str):
    import mlflow
    import pandas as pd
    import awswrangler as wr
    from airflow.models import Variable
    from old_dags.likelihood_to_buy.prepare_data import prepare_data, load_artifacts
    import logging

    env = Variable.get("environment")

    logging.basicConfig(
        filename="processing_times.log",
        level=logging.INFO,
        format="%(asctime)s - %(message)s",
    )

    # Load models + version for each segment
    homeowner_model, homeowner_artifacts, homeowner_version = load_artifacts(
        "homeowner"
    )
    renter_model, renter_artifacts, renter_version = load_artifacts("renter")

    df_chunk = wr.s3.read_csv(
        chunk_path,
        dtype={
            "clip": "object",
            "first_position_mortgage_amount": "object",
            "estimated_value_mktg": "object",
            "estimated_equity": "object",
        },
    )
    df_chunk.set_index(["clip", "ethashv1", "ethashv2"], inplace=True)

    df_homeowner = df_chunk[df_chunk["homeowner"] == True].copy()
    df_renter = df_chunk[df_chunk["homeowner"] == False].copy()

    if not df_homeowner.empty:
        X_home = prepare_data(
            df_homeowner, segment="homeowner", artifacts=homeowner_artifacts
        )
        df_homeowner["ltb_180_prob"] = homeowner_model.predict_proba(X_home)[:, 1]
        df_homeowner["ltb_180_pred"] = df_homeowner["ltb_180_prob"].apply(
            lambda x: 1 if x >= 0.5 else 0
        )
        df_homeowner["model_version"] = f"homeowner_v{homeowner_version}"

    if not df_renter.empty:
        X_renter = prepare_data(df_renter, segment="renter", artifacts=renter_artifacts)
        df_renter["ltb_180_prob"] = renter_model.predict_proba(X_renter)[:, 1]
        df_renter["ltb_180_pred"] = df_renter["ltb_180_prob"].apply(
            lambda x: 1 if x >= 0.5 else 0
        )
        df_renter["model_version"] = f"renter_v{renter_version}"

    df_combined = pd.concat([df_homeowner, df_renter]).reset_index()

    df_combined.loc[
        df_combined["ethashv2"].isnull(), ["ltb_180_prob", "ltb_180_pred"]
    ] = None
    df_combined.loc[
        df_combined["days_since_first_mortgage"].notnull()
        & (df_combined["days_since_first_mortgage"] <= 180),
        ["ltb_180_prob", "ltb_180_pred"],
    ] = 0

    df_combined["run_date"] = run_date

    wr.s3.to_parquet(
        df=df_combined[
            [
                "clip",
                "ethashv1",
                "ethashv2",
                "run_date",
                "ltb_180_prob",
                "ltb_180_pred",
                "model_version",
            ]
        ],
        path=f"s3://vr-timestamp/bi_sources/likelihood_to_buy_predictions/{env}/",
        dataset=True,
        mode="append",
        partition_cols=["run_date"],
    )


@task
def run_health_check(query: str, run_date: str, env: str):
    from airflow.providers.trino.hooks.trino import TrinoHook
    from airflow.exceptions import AirflowFailException

    # Replace placeholders in query with actual values
    formatted_query = query.replace("{{ run_date }}", run_date).replace(
        "{{ env }}", env
    )

    hook = TrinoHook(trino_conn_id="trino_conn")  # Use your connection ID
    result = hook.get_records(formatted_query)
    if result[0][0] == 0:  # unnest query result
        raise AirflowFailException(
            f"Health check query failed: {formatted_query}. Returned 0."
        )
    logger.info(f"Query: {formatted_query}. Result: {result[0][0]}")


with ETDAG(
    dag_id="likelihood_to_buy",
    schedule="0 6 * * *",
    default_args=default_args,
    catchup=False,
    description="Likelihood to Buy DAG - Homeowner and Renter 180-day prediction",
    start_date=datetime(2025, 3, 18),
    et_failure_msg=True,
    tags=["LTB", "DS", "Prod"],
    params={"run_date": pendulum.now().format("YYYY-MM-DD")},
) as dag:

    # Get parameters
    run_date = "{{ params.run_date }}"
    env = Variable.get("environment")

    refresh_hs = SQLExecuteQueryOperator(
        task_id="refresh_hs",
        conn_id="trino_conn",
        # sql='REFRESH MATERIALIZED VIEW  "s3"."gold_real_estate_intender"."likelihood_to_buy_hs"',  # need to update the input table here
        sql="select * from s3.gold_real_estate_intender.likelihood_to_buy_hs limit 1",
    )

    pre_health_check = run_health_check.expand(
        query=pre_health_check_queries,
        run_date=[run_date] * len(pre_health_check_queries),
        env=[env] * len(pre_health_check_queries),
    )

    post_health_check = run_health_check.expand(
        query=post_health_check_queries,
        run_date=[run_date] * len(post_health_check_queries),
        env=[env] * len(post_health_check_queries),
    )

    chunk_paths = get_chunks()
    expanded = process_chunk.expand(chunk_path=chunk_paths).partial(run_date=run_date)

    # Dynamically choose sync table based on environment
    target_table = (
        "prod_ltb_predictions"
        if Variable.get("environment") == "prod"
        else "likelihood_to_buy_predictions_test"
    )

    sync_table = SQLExecuteQueryOperator(
        task_id="sync_table",
        conn_id="trino_conn",
        sql=f"CALL s3.system.sync_partition_metadata('gold_real_estate_intender', 'likelihood_to_buy_predictions_{env}', 'ADD')",
    )

    (
        refresh_hs
        >> pre_health_check
        >> chunk_paths
        >> expanded
        >> sync_table
        >> post_health_check
    )
