import pandas as pd
import logging


def load_artifacts(segment: str):
    import mlflow
    from mlflow.tracking import MlflowClient

    mlflow.set_tracking_uri(uri="https://mlflow.k8s.eltoro.com")
    client = MlflowClient()

    if segment == "homeowner":
        model_name = "ltbh_home_owner_prod"
    elif segment == "renter":
        model_name = "ltbh_renter_prod"
    else:
        raise ValueError("Segment must be either 'homeowner' or 'renter'")

    # === Get model version from Production stage ===
    mv = client.get_model_version_by_alias(name=model_name, alias="prod")
    if not mv:
        raise ValueError(f"No model version found in Production for segment: {segment}")

    version = mv.version
    model_uri = f"models:/{model_name}@prod"
    model = mlflow.sklearn.load_model(model_uri=model_uri)

    # === Load artifacts based on segment ===
    if segment == "homeowner":
        encoder = mlflow.sklearn.load_model("models:/ltb_encoder_homeowner@prod")
        scaler = mlflow.sklearn.load_model("models:/ltb_scaler_homeowner@prod")
        imputer = mlflow.sklearn.load_model("models:/ltb_imputer_homeowner@prod")

        artifact_dict = {
            "encoder": encoder,
            "scaler": scaler,
            "imputer": imputer,
            "expected_features": model.feature_names_in_.tolist(),
        }

    elif segment == "renter":
        preprocessor = mlflow.sklearn.load_model("models:/ltb_renter_pre@prod")
        scaler = mlflow.sklearn.load_model("models:/ltb_renter_scaler@prod")

        artifact_dict = {
            "preprocessor": preprocessor,
            "scaler": scaler,
            "expected_features": model.feature_names_in_.tolist(),
        }

    return model, artifact_dict, version


def prepare_data(df: pd.DataFrame, segment: str, artifacts: dict) -> pd.DataFrame:
    def compare_and_log_features(expected, actual, segment):
        """Print, log, and raise errors for feature mismatches."""
        missing = sorted(set(expected) - set(actual))
        extra = sorted(set(actual) - set(expected))

        print(f"\n [DEBUG] {segment.title()} Model - Expected features from MLflow:")
        print(expected)
        print(f"\n [DEBUG] {segment.title()} Model - Features provided to model:")
        print(actual)

        print(f"\n [DEBUG] Feature mismatch ({segment}):")
        print(" - Missing features:", missing)
        print(" - Extra features:", extra)

        with open("feature_mismatch.log", "a") as log:
            log.write(f"\n=== Segment: {segment.upper()} ===\n")
            log.write(f"Expected features:\n{expected}\n")
            log.write(f"Actual features:\n{actual}\n")
            log.write(f"Missing: {missing}\n")
            log.write(f"Extra: {extra}\n\n")

        if missing:
            raise ValueError(f"[ERROR] Missing features for segment '{segment}': {missing}")

    if segment == "homeowner":
        all_features = [
            'days_since_first_mortgage', 'days_left_on_mortgage', 'estimated_value_mktg',
            'estimated_equity', 'estimated_value_high_mktg', 'estimated_value_low_mktg',
            'total_obs', 'avg_days_after_listing_date', 'how_recently_shopping',
            'distinct_listings', 'confidence_score_mktg',
            'situs_core_based_statistical_area_cbsa', 'h_zipcode',
            'new_homeshopper', 'repeat_visitor'
        ]
        numeric_cols = all_features[:11]
        categorical_cols = ['situs_core_based_statistical_area_cbsa', 'h_zipcode']
        boolean_cols = ['new_homeshopper', 'repeat_visitor']

        df = df[all_features].copy()
        df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
        df[categorical_cols] = artifacts["encoder"].transform(df[categorical_cols])
        df[boolean_cols] = df[boolean_cols].fillna(False).astype(int)

        # Maintain original order before scaling
        df = df[all_features]
        scaled = artifacts["scaler"].transform(df)
        df_scaled = pd.DataFrame(scaled, columns=all_features)

        # Impute numeric columns only
        df_scaled[numeric_cols] = artifacts["imputer"].transform(df_scaled[numeric_cols])

        expected_features = artifacts["expected_features"]
        actual_features = df_scaled.columns.tolist()

        compare_and_log_features(expected_features, actual_features, segment="homeowner")

        # Enforce correct feature order before returning
        df_scaled = df_scaled[expected_features]

        return df_scaled

    elif segment == "renter":
        # Full candidate list (everything available in data)
        FEATURES = [
            'state',
            'rdi',
            'percent_housing_tenure_occupied_housing_units_renter_occupied',
            'estimate_housing_tenure_occupied_housing_units_average_household_size_of_renter_occupied_unit',
            'margin_of_error_housing_occupancy_total_housing_units_rental_vacancy_rate',
            'median_search_range',
            'estimate_housing_occupancy_total_housing_units_rental_vacancy_rate',
            '2014_2018_estimates_housing_tenure_occupied_housing_units_average_household_size_of_renter_occupied_unit',
            'tot_ratio',
            '2019_2023_estimates_housing_tenure_occupied_housing_units_renter_occupied',
            '2019_2023_estimates_gross_rent_occupied_units_paying_rent_1_000_to_1_499',
            '2014_2018_estimates_housing_tenure_occupied_housing_units_renter_occupied',
            'average_search_range',
            '2014_2018_estimates_gross_rent_as_a_percentage_of_household_income_grapi_occupied_units_paying_rent_excluding_units_where_grapi_cannot_be_computed_20_0_to_24_9_percent',
            'percent_margin_of_error_housing_tenure_occupied_housing_units_renter_occupied',
            '2014_2018_estimates_gross_rent_as_a_percentage_of_household_income_grapi_occupied_units_paying_rent_excluding_units_where_grapi_cannot_be_computed_less_than_15_0_percent',
            '2014_2018_estimates_housing_occupancy_total_housing_units_rental_vacancy_rate',
            'min_days_after_listing_date',
            '2019_2023_estimates_gross_rent_occupied_units_paying_rent_2_500_to_2_999',
            '2014_2018_estimates_gross_rent_occupied_units_paying_rent_less_than_500'
        ]

        expected_features = artifacts["expected_features"]

        # Step 1: Filter to only known/valid features
        available_features = [col for col in FEATURES if col in df.columns]
        df = df[available_features].copy()

        # Step 2: Compare and log mismatch
        actual_features = df.columns.tolist()
        compare_and_log_features(expected_features, actual_features, segment="renter")

        # Step 3: Reorder and transform
        df = df[expected_features]
        preprocessed = artifacts["preprocessor"].transform(df)

        df_scaled = pd.DataFrame(
            artifacts["scaler"].transform(preprocessed),
            columns=expected_features
        )

        print("\n[DEBUG] Renter - Expected feature order from MLflow:")
        print(artifacts["expected_features"])

        print("\n[DEBUG] Renter - Actual feature order before prediction:")
        print(df_scaled.columns.tolist())

        return pd.DataFrame(df_scaled.values, columns=expected_features)

    else:
        raise ValueError("Segment must be either 'homeowner' or 'renter'")
