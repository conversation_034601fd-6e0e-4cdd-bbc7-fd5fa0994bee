from airflow.decorators import task
from airflow.utils.dates import days_ago
from etdag import ETDAG
import sh
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from datetime import timedelta


default_args = {
    "owner": "<PERSON>",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 3,
    "retry_delay": timedelta(seconds=30),
}


def airflow_export(item):
    sh.airflow(item, "export", "/tmp/saved_{}.json".format(item))


def s3_upload(item):
    s3_key = f"airflow-backups/saved_{item}.json"
    s3_bucket = "diagonalley"
    filename = f"/tmp/saved_{item}.json"
    s3_hook.load_file(filename, s3_key, s3_bucket, replace=True)


with ETDAG(
    dag_id="airflow_backup",
    description="Backup airflow connections and variables",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="@daily",
    catchup=False,
    tags=["airflow", "core", "backup"],
) as dag:

    s3_hook = S3Hook("s3_conn")
    @task()
    def export_connecions():
        airflow_export("connections")
        s3_upload("connections")

    @task()
    def export_variables():
        airflow_export("variables")
        s3_upload("variables")



    backup_connections = export_connecions()

    backup_variables = export_variables()

    backup_connections >> backup_variables
