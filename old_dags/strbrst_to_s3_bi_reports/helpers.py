import requests
from airflow.exceptions import AirflowFailException
import pendulum
from airflow.models import Variable

if Variable.get("environment") == "prod":
    url = f"https://bi-api.k8s.eltoro.com/api/v1/bi-reports"

else:
    url = f"https://bi-api.k8s.dev.eltoro.com/api/v1/bi-reports"

headers = {"accept": "application/json"}


def query_api_for_configs(report_to_run, logical_time):
    if not report_to_run:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            print("Response received successfully!")
            data = response.json()
            data_list = data.get("data", [])
            configs = pull_relevant_configs_for_time(data_list, logical_time)
            return configs
        else:
            print(f"Failed to fetch data. Status code: {response.status_code}")
            raise AirflowFailException(response.text)
    else:
        response = requests.get(f"{url}/{report_to_run}", headers=headers)
        if response.status_code == 200:
            print("Response received successfully!")
            data = response.json()
            return [data]
        else:
            print(f"Failed to fetch data. Status code: {response.status_code}")
            raise AirflowFailException(response.text)


def pull_relevant_configs_for_time(data_list, logical_time):
    configs = []
    for data in data_list:
        if data["enabled"] is True:
            if should_fire(data, logical_time):
                configs.append(data)

    return configs


def should_fire(data, logical_time: pendulum.DateTime) -> bool:
    schedule_value = data["schedule_value"]
    schedule_type = data["schedule_type"]
    schedule_hour = pendulum.parse(schedule_value, strict=False).hour

    if schedule_hour != logical_time.hour:
        return False

    if schedule_type == "daily":
        return True

    if schedule_type == "weekly" and logical_time.day_of_week == pendulum.SUNDAY:
        return True

    if schedule_type == "monthly" and logical_time.day == 1:
        return True

    if schedule_type == "bi-weekly" and logical_time.day in {1, 15}:
        return True

    return False
