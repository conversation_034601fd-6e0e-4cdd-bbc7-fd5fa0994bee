import time
from etdag import ETDAG
from airflow.decorators import task_group, task
from airflow.models.param import Param
from datetime import timedelta, datetime
from airflow.utils.dates import days_ago
from airflow.operators.python import get_current_context
from typing import TypedDict
from airflow.operators.empty import EmptyOperator
from old_dags.strbrst_to_s3_bi_reports.s3_config_finder import list_files_in_s3
from airflow.providers.trino.hooks.trino import TrinoHook
import tempfile
import gzip
import shutil
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from urllib.parse import urlparse
from airflow.models import Variable
import os
from airflow.exceptions import AirflowSkipException
import pandas as pd
from airflow.utils.trigger_rule import TriggerRule
import pendulum
import json

import requests
from old_dags.strbrst_to_s3_bi_reports.helpers import query_api_for_configs


class Job(TypedDict):
    id: int
    email: str
    report_name: str
    enabled: bool
    schedule_type: str
    schedule_value: str
    trino_query: str
    gzip_compress: bool
    s3_locations: list[dict]
    materialized_view_dependencies: list[str]


with ETDAG(
    dag_id="startburst_to_s3_bi_reports",
    default_args={
        "owner": "BP",
        "retries": 0,
        "retry_delay": timedelta(seconds=300),
    },
    schedule_interval="0 15 * * 0",
    start_date=datetime(202, 1, 7),
    max_active_runs=1,
    catchup=True,
    params={
        "configs": Param(
            default={},
            # type="array",
            description="Provide a list of configurations manually when triggering the DAG.",
        ),
    },
    tags=["bi_reports"],
    et_failure_msg=False,
) as dag:

    @task()
    def get_job_configs(
        **context,
    ) -> list:
        # Retrieve the manually provided configs from dag_run.conf
        config_bucket = "vr-timestamp"
        config = context["dag_run"].conf.get("configs", {})

        if not config:
            configs = list_files_in_s3(config_bucket)

            if not configs:
                # Raise AirflowSkipException if no configs are found
                raise AirflowSkipException(
                    "No configuration files found, skipping task."
                )

            return configs
        else:
            print(f"the configs {config}")
            return config

    # def get_job_configs(
    #     **context,
    # ) -> list[Job]:

    #     report_to_run = context["dag_run"].conf.get("report_to_run", "")
    #     logical_date = context["logical_date"]
    #     interval_end_utc = logical_date.add(hours=1)
    #     est_timezone = pendulum.timezone("America/New_York")
    #     logical_date_est = interval_end_utc.in_tz(est_timezone)

    #     # # Normalize logical time to the hour in EST
    #     logical_time = logical_date_est.replace(minute=0, second=0, microsecond=0)
    #     print(f"Logical Date (UTC): {logical_date}")
    #     print(f"Logical Date (EST): {logical_date_est}")
    #     print(f"Logical Time (hour-normalized, EST): {logical_time}")

    #     configs = query_api_for_configs(report_to_run, logical_time)

    #     if configs:
    #         return configs
    #     else:
    #         raise AirflowSkipException("no configs found, skipping")

    @task(max_active_tis_per_dag=3)
    def refresh_mv_deps(configs: list[Job], trino_conn_id="trino_conn"):
        mv_dependency_set = set()
        if len(configs) == 0:
            raise AirflowSkipException("no configs given, skipping task")
        for config in configs:
            if config["materialized_view_dependencies"]:
                print(config["materialized_view_dependencies"])
                for mv_dependency in config["materialized_view_dependencies"]:
                    mv_dependency_set.add(mv_dependency)
            else:
                raise AirflowSkipException("no mat views given, skipping task")
        for mv_dependency in mv_dependency_set:
            print(f"refreshing materialized view: {mv_dependency}")
            TrinoHook(trino_conn_id=trino_conn_id).run(
                f"""REFRESH MATERIALIZED VIEW {mv_dependency}"""
            )

    @task(map_index_template="{{ my_custom_map_index }}", max_active_tis_per_dag=3)
    def do_job(config: Job, trino_conn_id="trino_conn", aws_conn_id="aws_default"):
        context = get_current_context()
        print(f"the config is {config}")
        context["my_custom_map_index"] = config["report_name"]
        print(f"query from config {config['trino_query']}")

        if Variable.get("environment") == "prod":
            query = config["trino_query"]

        else:
            query = "select * from s3.gold_auto_intender.observations_legacy_toyota limit 10"
        print(query)
        print(config["s3_locations"])

        # use trino_hook to get the requested data and save it in a temp csv locally
        df_chunks = TrinoHook(trino_conn_id=trino_conn_id).get_pandas_df_by_chunks(
            sql=query, chunksize=1_000_000
        )
        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
            temp_file_path = temp_file.name
        for i, chunk in enumerate(df_chunks):
            print(f"writing chunk {i}")
            if i == 0:
                chunk.to_csv(temp_file_path, index=False)
            else:
                chunk.to_csv(temp_file_path, mode="a", header=False, index=False)

        # compress the csv file if it needs to be compressed, also modify the extension of the s3_url if the file is compressed
        if config["gzip_compress"]:
            print("compressing file")
            compressed_file_path = temp_file_path + ".gz"
            with open(temp_file_path, "rb") as f_in:
                with gzip.open(temp_file_path + ".gz", "wb") as f_out:
                    shutil.copyfileobj(f_in, f_out)
            config["s3_locations"] = [
                {
                    **location,
                    "s3_key": (
                        location["s3_key"] + ".gz"
                        if not (
                            location["s3_key"].endswith(".gz")
                            or location["s3_key"].endswith(".gzip")
                        )
                        else location["s3_key"]
                    ),
                }
                for location in config["s3_locations"]
            ]
            file_to_upload = compressed_file_path
        else:
            file_to_upload = temp_file_path

        # upload the file to the s3_output_urls
        s3_hook = S3Hook(aws_conn_id=aws_conn_id)
        for s3_url in config["s3_locations"]:
            print(f"uploading to {s3_url}")
            if Variable.get("environment") == "prod":
                environment = Variable.get("environment")
                print(f"Current environment: {environment}")
                bucket_name = s3_url.get("s3_bucket")
                print(f"prod bucket name is {bucket_name}")
                s3_key = s3_url.get("s3_key")
                print(f"prod s3_key is {s3_key}")
            elif Variable.get("environment") == "dev":
                environment = Variable.get("environment")
                print(f"Current environment: {environment}")
                bucket_name = s3_url.get("s3_bucket")
                s3_key = s3_url.get("s3_key")
                # bucket_name = bucket_name.replace(
                #     "vr-timestamp", "vr-timestamp/test", 1
                # )
                print(f"test_bucket is {bucket_name}")
                print(f"test_s3_key is {s3_key}")

            if "%TODAY%" in s3_key:
                s3_key = s3_key.replace(
                    "%TODAY%",
                    f"{pd.to_datetime(context['logical_date']).strftime('%Y-%m-%d')}",
                )
            print(bucket_name)
            print(s3_key)
            print("uploading file")
            s3_hook.load_file(
                filename=file_to_upload,
                key=s3_key,
                bucket_name=bucket_name,
                replace=True,
            )
        return

    configs = get_job_configs(report_to_run="{{ params.configs }}")
    refresh_mvs = refresh_mv_deps(configs)
    dummy = EmptyOperator(
        task_id="dummy_after_refresh",
        trigger_rule=TriggerRule.ALL_DONE,
    )
    do_jobs = do_job.expand(config=configs)
    configs >> refresh_mvs >> dummy >> do_jobs
