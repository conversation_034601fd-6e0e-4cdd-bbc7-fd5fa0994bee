from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import os
import json
from datetime import datetime
from airflow.models import Variable


###########   pull configs from s3 on cron


def read_config_from_s3(config_path, config_bucket):
    s3_hook = S3Hook("s3_conn")
    file = config_path.split("/")[-1]
    path = os.path.dirname(config_path)
    key = f"{path}/{file}"
    response = s3_hook.read_key(key=key, bucket_name=config_bucket)
    return json.loads(response)


def list_files_in_s3(bucket):
    configs = []
    s3_hook = S3Hook("s3_conn")

    config_dir = f"bi_configs/{Variable.get('environment')}/configs/"
    print(f"bucket {bucket}")
    print(f"config_dir {config_dir}")
    response = s3_hook.list_keys(bucket_name=bucket, prefix=config_dir)
    print(response)
    if response:
        files = [key for key in response if key.endswith(".json")]
    else:
        files = []

    for file in files:
        config_data = read_config_from_s3(file, bucket)

        # Adding the S3 path under 'org_id' in each config
        if type(config_data) == list:
            for config in config_data:
                config["s3_path"] = file
            configs.extend(config_data)
        else:
            config_data["s3_path"] = file
            configs.append(config_data)

    return configs
