from airflow.models.baseoperator import BaseOperator
from airflow.utils.context import Context
from airflow.utils.decorators import apply_defaults
from airflow.providers.trino.hooks.trino import TrinoHook
import pandas as pd
from old_dags.medicx.id_res.operators.fetch_deals.fetch_deals_xandr import Deal
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP

class GenerateOutput(BaseOperator):
    template_fields=["deal", "log_date"]
    @apply_defaults
    def __init__(
        self,
        *,
        deal: Deal,
        log_date: str,
        targeted_audience_table: str = "s3.silver_medicx.id_rez_targeted_audience",
        trino_conn_id: str = "trino_conn",
        psql_conn_id: str = "postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.deal=deal
        self.log_date=log_date
        self.targeted_audience_table=targeted_audience_table
        self.trino_conn_id=trino_conn_id
        self.psql_conn_id=psql_conn_id

    def execute(self, context: Context):
        trino_hook = TrinoHook(
            trino_conn_id=self.trino_conn_id,
        )
        match self.deal["dsp"]:
            case DSP.XANDR.value:
                query = f"""
                    SELECT * 
                    FROM (
                        SELECT DISTINCT 
                            auction_id_64,
                            date_time,
                            user_tz_offset,
                            media_type,
                            event_type,
                            user_id_64,
                            hashed_user_id_64,
                            ip_address,
                            ip_address_trunc,
                            country,
                            region,
                            dma,
                            olympus.bronze_medicx_xandr.xandr_curate_feed.city,
                            postal_code,
                            latitude,
                            latitude_trunc,
                            longitude,
                            longitude_trunc,
                            device_unique_id,
                            device_type,
                            tc_string,
                            curated_deal_id,
                            gross_revenue_dollars,
                            curator_margin,
                            total_tech_fees_dollars,
                            total_cost_dollars,
                            net_media_cost_dollars,
                            seller_memeber_id,
                            publisher_id,
                            site_id,
                            site_domain,
                            tag_id,
                            application_id,
                            mobile_app_instance_id,
                            buyer_member_id,
                            creative_id,
                            brand_id,
                            seller_deal_id,
                            view_result,
                            view_non_measurable_reason,
                            supply_type,
                            creative_width,
                            creative_height,
                            partition_time_millis,
                            operation_system,
                            browser,
                            language,
                            device_id
                        FROM olympus.bronze_medicx_xandr.xandr_curate_feed
                        WHERE DATE(partition_time_millis) = DATE '{self.log_date}'
                        AND CAST(curated_deal_id AS VARCHAR) = '{self.deal['deal_id']}'
                    ) as weblogs
                    LEFT JOIN (
                        SELECT DISTINCT
                            CAST(npi AS VARCHAR) AS npi,
                            CAST(aiq_hhid AS VARCHAR) AS aiq_hhid,
                            tar_aud.ethash as ethash_v1,
                            ip,
                            campaign_id,
                            date
                        FROM s3.silver_medicx.id_rez_targeted_audience as tar_aud
                        LEFT JOIN olympus.silver_medicx.aiqs
                            ON tar_aud.ethash = aiqs.ethashv1
                        LEFT JOIN s3.silver_medicx.npi
                            ON tar_aud.ethash = npi.ethash_v1
                        WHERE tar_aud.date = DATE '{self.log_date}'
                        AND tar_aud.ethash NOT IN (SELECT ethash from s3.silver_optout.ethash)
                        AND tar_aud.campaign_id = '{self.deal['deal_id']}'
                    ) as tar_aud 
                    ON tar_aud.ip = weblogs.ip_address
                """
            case DSP.VIANT.value:
                query = f"""
                    SELECT 
                        session_id,
                        event_type,
                        timestamp,
                        ad_order_id,
                        ad_order_name,
                        weblogs.campaign_id as viant_campaign_id,
                        weblogs.campaign_name as viant_campaign_name,
                        advertiser_id,
                        advertiser_name,
                        creative_id,
                        creative_name,
                        publisher_id,
                        publisher_name,
                        site_id,
                        site_name,
                        weblogs.ip,
                        device_id,
                        device_id_type,
                        os,
                        device_type,
                        device_make,
                        device_model,
                        country,
                        region,
                        metrocode,
                        postal_code,
                        city,
                        lat,
                        lon,
                        timezone_offset,
                        adelphic_internal,
                        year,
                        month,
                        day,
                        hour,
                        npi,
                        aiq_hhid,
                        ethash_v1,
                        date,
                        tar_aud.campaign_id
                    FROM (
                        SELECT DISTINCT *
                        FROM s3.external_optimizerx_viant.viant_win_logs
                        WHERE DATE(CAST(timestamp AS timestamp)) = DATE '{self.log_date}'
                        AND CAST(ad_order_id AS VARCHAR) = '{self.deal['deal_id']}'
                    ) as weblogs
                    LEFT JOIN (
                        SELECT DISTINCT
                            CAST(npi AS VARCHAR) AS npi,
                            CAST(aiq_hhid AS VARCHAR) AS aiq_hhid,
                            tar_aud.ethash as ethash_v1,
                            ip,
                            campaign_id,
                            date
                        FROM s3.silver_medicx.id_rez_targeted_audience as tar_aud
                        LEFT JOIN olympus.silver_medicx.aiqs
                            ON tar_aud.ethash = aiqs.ethashv1
                        LEFT JOIN s3.silver_medicx.npi
                            ON tar_aud.ethash = npi.ethash_v1
                        WHERE tar_aud.date = DATE '{self.log_date}'
                        AND tar_aud.ethash NOT IN (SELECT ethash from s3.silver_optout.ethash) 
                        AND tar_aud.campaign_id = '{self.deal['deal_id']}'
                    ) as tar_aud 
                    ON tar_aud.ip = weblogs.ip
                """
        print(query)
        df = pd.read_sql(
            query,
            con=trino_hook.get_conn(),
            coerce_float=False,
        )
        df = df.astype({
            "npi": str,
            "aiq_hhid": str,
            "ethash_v1": str
        })
        df.to_parquet(
            f"s3://eltoro-data-sources/medicx/id_rez/prod/results_{self.deal['dsp']}/date={self.log_date}/campaign_id={self.deal['deal_id']}/results.pq",
            index=False
        )