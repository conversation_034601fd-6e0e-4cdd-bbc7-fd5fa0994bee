from airflow.models.baseoperator import BaseOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.utils.decorators import apply_defaults
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import pandas as pd
from sqlalchemy import select
import requests
import json
from airflow.models import Variable
from requests.auth import HTTPBasicAuth
import re
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP, Resolution

class FetchDealsViant(BaseOperator):
    template_fields = ["log_date"]
    @apply_defaults
    def __init__(
        self,
        log_date: str,
        trino_conn_id = "trino_conn",
        medicx_db_id = "postgres_medicx",
        *args,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.log_date = log_date
        self.trino_conn_id = trino_conn_id
        self.medicx_db_id = medicx_db_id
        oprx_viant_secrets = json.loads(Variable.get("oprx_viant_creds"))
        self.traffic_user = oprx_viant_secrets["traffic_user"]
        self.traffic_pass = oprx_viant_secrets["traffic_pass"]
        self.data_user = oprx_viant_secrets["data_user"]
        self.data_pass = oprx_viant_secrets["data_pass"]
    
    def __sql_to_dict(self, query: str):
        print(query)
        with self.psql_hook.get_conn() as conn:
            cursor = conn.cursor()
            cursor.execute(query)
            # Fetch the column names
            column_names = [desc[0] for desc in cursor.description]
            
            # Fetch the data
            data = cursor.fetchall()
            
            # Create a list of dictionaries
            result = []
            for row in data:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[column_names[i]] = value
                result.append(row_dict)
            return result
    
    def __get_running_deals(self):
        # need the following, the segment_ids and the segment_names for a given order_id 
        hook = TrinoHook(trino_conn_id=self.trino_conn_id)
        query = f"""
            SELECT
                ad_order_id as deal_id
            FROM s3.external_optimizerx_viant.viant_win_logs
            WHERE DATE(date_format(CAST(timestamp AS timestamp), '%Y-%m-%d')) = DATE('{self.log_date}')
            GROUP by ad_order_id
        """
        records = hook.get_records(query)
        deal_ids = [record[0] for record in records]
        df = pd.DataFrame()
        for deal_id in deal_ids:
            response = requests.get(
                f"https://api.adelphic.com/v1/audience/getAdvancedAudiencesByAdOrderId",
                params={"adorder_id": {deal_id}},
                auth=HTTPBasicAuth(self.traffic_user, self.traffic_pass)
            )
            taxonomy_ids = list()
            pattern = r'provider\.taxonomy:\[(.*?)\]'
            for entity in response.json()["entity"]:
                matches = re.findall(pattern, entity["target"])
                matches = [match.replace('"', '') for match in matches]
                taxonomy_ids.extend(matches)
            taxonomy_ids = list(set(taxonomy_ids))
            segment_names = list()
            segment_ids = list()
            for taxonomy_id in taxonomy_ids:
                response = requests.get(
                    f"https://api.adelphic.com/v1/taxonomy/getTaxonomy",
                    params={"id": taxonomy_id},
                    auth=HTTPBasicAuth(self.data_user, self.data_pass)
                )
                segment = response.json()["entity"]["name"]
                segment_names.append(segment.split("|")[0].strip())
                segment_ids.append(segment.split("|")[1].strip())
            df = pd.concat([df, pd.DataFrame({
                "deal_id": deal_id,
                "segment_names": [segment_names],
                "segment_ids": [segment_ids]
            })])
        df["dsp"] = DSP.VIANT.value
        return df


    def execute(self, context) -> list[Deal]:
        print(f"processing log_date: {self.log_date}")
        self.psql_hook = PostgresHook(postgres_conn_id=self.medicx_db_id)
        deals_df = self.__get_running_deals()
        deals_df["resolution_type"] = None
        deals_df["selected_ips_s3_urls"] = None
        req_sess = requests.session()
        req_sess.headers= {
            "Content-Type": "application/json"
        }
        for deal_index, deal in deals_df.iterrows():
            selected_ips_s3_urls = []
            for segment_id in deal["segment_ids"]:
                print(segment_id)
                query = select(
                    MetaDataLogs.input_type,
                    DeployLogs.deploy_id,
                    QuoteLogs.selected_ips_file_s3_url,
                ).join(
                    MetaDataLogs,
                    MetaDataLogs.metadata_id == DeployLogs.metadata_id
                ).join(
                    QuoteLogs,
                    QuoteLogs.quote_log_id == DeployLogs.quote_log_id
                ).where(
                    DeployLogs.segment_id == segment_id
                ).compile(compile_kwargs={"literal_binds": True})
                postgres_data = self.__sql_to_dict(str(query))
                if postgres_data[0]["input_type"].lower() in ("zip9", "zip"):
                    deals_df["resolution_type"][deal_index]=Resolution.AIQ.value
                elif postgres_data[0]["input_type"].lower() in ("hcp", "npi"):
                    deals_df["resolution_type"][deal_index]=Resolution.NPI.value
                else:
                    raise Exception(f"Resolution Type not found: {postgres_data[0]['input_type'].lower()}")
                selected_ips_s3_urls.append(
                    postgres_data[0]['selected_ips_file_s3_url']
                )
            deals_df["selected_ips_s3_urls"][deal_index]=selected_ips_s3_urls
        return deals_df.to_dict(orient="records")

