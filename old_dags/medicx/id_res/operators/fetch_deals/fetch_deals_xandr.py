from airflow.models.baseoperator import BaseOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.utils.decorators import apply_defaults
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import pandas as pd
from sqlalchemy import select
import requests
import json
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP, Resolution


class FetchDealsXandr(BaseOperator):
    template_fields = ["log_date"]
    @apply_defaults
    def __init__(
        self,
        log_date: str,
        trino_conn_id = "trino_conn",
        medicx_db_id = "postgres_medicx",
        *args,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.log_date=log_date
        self.trino_conn_id=trino_conn_id
        self.medicx_db_id=medicx_db_id
    
    def __sql_to_dict(self, query: str):
        print(query)
        with self.psql_hook.get_conn() as conn:
            cursor = conn.cursor()
            cursor.execute(query)
            # Fetch the column names
            column_names = [desc[0] for desc in cursor.description]
            
            # Fetch the data
            data = cursor.fetchall()
            
            # Create a list of dictionaries
            result = []
            for row in data:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[column_names[i]] = value
                result.append(row_dict)
            return result
    
    def __get_trino_deal_data(self,):
        hook = TrinoHook(trino_conn_id=self.trino_conn_id)
        query = f"""
            SELECT 
                curated_deal_id as deal_id,
                array_agg(segment_id) segment_ids,
                array_agg(segment_name) segment_names 
            FROM s3.bronze_medicx.curator_segment_performance
            WHERE day = DATE('{self.log_date}')
            GROUP by 1
            ORDER by curated_deal_id
            """
        df = pd.read_sql(
            query,
            con=hook.get_conn()
        )
        df["dsp"] = DSP.XANDR.value
        return df
    
    def __process_resolution_type(self, resolution_type: str):
        if resolution_type in ("zip9", "zip"):
            return Resolution.AIQ.value
        elif resolution_type in ("hcp", "npi"):
            return Resolution.NPI.value
        else:
            raise Exception(f"Resolution Type not found: {resolution_type}")

    def execute(self, context) -> list[Deal]:
        print(f"processing log_date: {self.log_date}")
        self.psql_hook = PostgresHook(postgres_conn_id=self.medicx_db_id)
        deals_df = self.__get_trino_deal_data()
        deals_df["resolution_type"] = None
        deals_df["selected_ips_s3_urls"] = None
        req_sess = requests.session()
        req_sess.headers= {
            "Content-Type": "application/json"
        }

        for deal_index, deal in deals_df.iterrows():
            selected_ips_s3_urls = []
            for segment_name, segment_id in zip(deal["segment_names"], deal["segment_ids"]):
                query = select(
                    MetaDataLogs.input_type,
                    DeployLogs.deploy_id,
                    QuoteLogs.selected_ips_file_s3_url,
                ).join(
                    MetaDataLogs,
                    MetaDataLogs.metadata_id == DeployLogs.metadata_id
                ).join(
                    QuoteLogs,
                    QuoteLogs.quote_log_id == DeployLogs.quote_log_id
                ).where(
                    DeployLogs.segment_name == segment_name
                ).compile(compile_kwargs={"literal_binds": True})
                postgres_data = self.__sql_to_dict(str(query))
                if len(postgres_data)==1:
                    deals_df["resolution_type"][deal_index] = self.__process_resolution_type(postgres_data[0]["input_type"].lower())
                    selected_ips_s3_urls.append(
                        f"s3://{postgres_data[0]['selected_ips_file_s3_url']}"
                    )
                elif len(postgres_data)>1:
                    for postgres_entry in postgres_data:
                        response_dict = req_sess.get(f"http://deploy.prod.middleearth.eltoro.com/deployments/{postgres_entry['deploy_id']}").json()
                        config_dict = json.loads(response_dict["config"])
                        if str(config_dict["segment"]["id"]) == segment_id:
                            deals_df["resolution_type"][deal_index]=self.__process_resolution_type(postgres_entry["input_type"].lower())
                            selected_ips_s3_urls.append(
                                f"s3://{postgres_entry['selected_ips_file_s3_url']}"
                            )
                            break
            deals_df["selected_ips_s3_urls"][deal_index]=selected_ips_s3_urls
        return deals_df.to_dict(orient="records")

