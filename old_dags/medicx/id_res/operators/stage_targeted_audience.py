from airflow.models.baseoperator import BaseOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.utils.context import Context
from airflow.utils.decorators import apply_defaults
import pandas as pd
from airflow.models import Variable
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP
from airflow.providers.eltoro.operators.ip_resolution.ipresolution import IPResolution


class StageTargetedAudienceOperator(BaseOperator):
    template_fields=["deal", "log_date"]
    @apply_defaults
    def __init__(
        self,
        *,
        deal: Deal,
        log_date: str,
        audience_table_name: str = "id_rez_selected_ips",
        trino_conn_id: str = "trino_conn",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.deal=deal
        self.log_date=log_date
        self.audience_table_name=audience_table_name
        self.trino_conn_id=trino_conn_id

    def execute(self, context: Context):
        trino_hook = TrinoHook(
            trino_conn_id=self.trino_conn_id,
        )
        match self.deal["dsp"]:
            case DSP.VIANT.value:
                targeted_audience_sql_query = f"""
                    SELECT weblogs.ip AS log_ip, selected.ip AS selected_ip, ethash
                    FROM (
                        SELECT DISTINCT ip
                        from "s3"."external_optimizerx_viant"."viant_win_logs"
                        WHERE ad_order_id = '{self.deal['deal_id']}'
                        AND DATE(CAST(timestamp AS timestamp)) = DATE('{self.log_date}')
                    ) AS weblogs
                    LEFT JOIN (
                        SELECT DISTINCT ip, ethash
                        FROM s3.bronze_medicx.{self.audience_table_name}
                        WHERE campaign_id = '{self.deal['deal_id']}'
                    ) AS selected
                    ON weblogs.ip = selected.ip
                """ 
            case DSP.XANDR.value:
                targeted_audience_sql_query = f"""
                    SELECT weblogs.ip AS log_ip, selected.ip AS selected_ip, ethash
                    FROM (
                        SELECT DISTINCT ip_address as ip
                        from "olympus"."bronze_medicx_xandr"."xandr_curate_feed"
                        WHERE curated_deal_id = {int(self.deal['deal_id'])}
                        AND DATE(partition_time_millis) = DATE('{self.log_date}')
                    ) AS weblogs
                    LEFT JOIN (
                        SELECT DISTINCT ip, ethash
                        FROM s3.bronze_medicx.{self.audience_table_name}
                        WHERE campaign_id = '{self.deal['deal_id']}'
                    ) AS selected
                    ON weblogs.ip = selected.ip
                """ 
        print(targeted_audience_sql_query)
        audience_df = pd.read_sql(
            targeted_audience_sql_query,
            # chunksize=5_000_000,
            con=trino_hook.get_conn(),
        )
        audience_df = audience_df.drop_duplicates(subset=["log_ip"])
        
        # audience_df_chunk["ethash"] = audience_df_chunk["ethash"].str.slice(10, 50)
        unknown_audience_ips = audience_df[audience_df["selected_ip"].isna()][["log_ip"]]
        unknown_audience_ips = unknown_audience_ips.rename(columns={"log_ip": "ip"})
        unknown_audience_ips = unknown_audience_ips.drop_duplicates()
        
        known_audience_ips = audience_df.dropna()[["selected_ip", "ethash"]].rename(columns={"selected_ip": "ip"})
        known_audience_ips["selected_audience"] = True
        print(f"count of unknown audience: {len(unknown_audience_ips.index)}")
        unknown_audience_ips.to_parquet(
            f"s3://eltoro-data-sources/medicx/id_rez/prod/unknown_audience/date={self.log_date}/campaign_id={self.deal['deal_id']}/unknown_audience.parquet",
            index=False,
        )
        print(f"count of known audience: {len(known_audience_ips.index)}")
        known_audience_ips.to_parquet(
            f"s3://eltoro-data-sources/medicx/id_rez/prod/targeted_audience/date={self.log_date}/campaign_id={self.deal['deal_id']}/known_audience.parquet",
            index=False,
        )
        if len(unknown_audience_ips.index) == 0:
            return
        results = IPResolution(
            task_id="ip_resolution",
            ip_log_s3_bucket_input="eltoro-data-sources",
            ip_log_s3_key_input=f"medicx/id_rez/prod/unknown_audience/date={self.log_date}/campaign_id={self.deal['deal_id']}/",
            add_s3_bucket_output="eltoro-data-sources",
            add_s3_key_output=f"medicx/id_rez/prod/targeted_audience/date={self.log_date}/campaign_id={self.deal['deal_id']}/resolved_audience.parquet",
            threaded=False
        ).execute(context)
        print(results)
        s3_output_url = f"s3://eltoro-data-sources/medicx/id_rez/prod/targeted_audience/date={self.log_date}/campaign_id={self.deal['deal_id']}/resolved_audience.parquet"
        ip_rez_results = pd.read_parquet(s3_output_url)
        ip_rez_results["selected_audience"] = False
        ip_rez_results["log_date"]= self.log_date
        ip_rez_results = ip_rez_results.to_parquet(s3_output_url, index=False)