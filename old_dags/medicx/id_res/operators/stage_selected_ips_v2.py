from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
import pandas as pd
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
from old_dags.medicx.id_res.operators.fetch_deals.fetch_deals_xandr import Deal
from airflow.providers.amazon.aws.hooks.s3 import S3Hook

class StageSelectedIPsOperator(BaseOperator):
    @apply_defaults
    def __init__(
        self,
        *,
        deal: Deal,
        aws_conn_id: str = "aws_default",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.deal=deal
        self.aws_conn_id=aws_conn_id

    def execute(self, context):
        upload_bucket = "eltoro-data-sources"
        print(self.deal["selected_ips_s3_urls"])
        for segment_id, selected_ips_s3_url in zip(self.deal["segment_ids"], self.deal["selected_ips_s3_urls"]):
            s3_hook = S3Hook(self.aws_conn_id)
            upload_key = f"medicx/id_rez/prod/audience_selected_ips/campaign_id={self.deal['deal_id']}/{segment_id}_selected_ips.pq"
            s3_url = f"s3://{upload_bucket}/{upload_key}"
            print(f"Checking if the selected ip file was already uploaded to: {s3_url}")
            if s3_hook.check_for_key(upload_key, upload_bucket):
                print(f"Found already uploaded selected ip file: {s3_url}")
                continue
            print(f"No already uploaded selected ip file found for: {s3_url}")
            print(f"Downloading selected ip file: {selected_ips_s3_url}")
            selected_ips_df = pd.read_csv(selected_ips_s3_url, names=['ip', 'etHash'], dtype=str)
            annotated_df = pd.read_csv(selected_ips_s3_url.replace("selected", "annotated"), dtype=str)
            annotated_df = annotated_df[annotated_df['target'] == 't']
            annotated_selected_df = annotated_df.merge(selected_ips_df, on=["etHash"])[["etHash", "ip"]]
            annotated_selected_df = annotated_selected_df.rename(columns={"etHash": "ethash"})
            annotated_selected_df["deal_name"] = segment_id
            annotated_selected_df = annotated_selected_df.drop_duplicates(subset="ip")
            annotated_selected_df = annotated_selected_df.drop_duplicates(subset="ethash")
            annotated_selected_df.to_parquet(
                f"{s3_url}",
                index=False,
            )