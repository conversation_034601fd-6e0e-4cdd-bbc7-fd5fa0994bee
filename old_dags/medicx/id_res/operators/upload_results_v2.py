from airflow.models.baseoperator import BaseOperator
from airflow.providers.trino.hooks.trino import Tri<PERSON>Hook
from airflow.utils.context import Context
from airflow.utils.decorators import apply_defaults
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from old_dags.medicx.id_res.operators.fetch_deals.fetch_deals_xandr import Deal
import pandas as pd
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP, Resolution

class UploadResultsOperator(BaseOperator):
    template_fields = ["deal", "log_date"]

    @apply_defaults
    def __init__(
        self,
        *,
        deal: Deal,
        log_date: str,
        aws_conn_id: str = "medicx_s3",
        trino_conn_id: str = "trino_conn",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.deal=deal
        self.log_date=log_date
        self.aws_conn_id=aws_conn_id
        self.trino_conn_id=trino_conn_id

    def dedupe_results(self, df: pd.DataFrame):
        """
        The deduping process is as follows:
        AIQs need to be one to one with the weblogs
        NPIs however can and will have several results per weblog row, sometimes in the thousands
        viant's unique weblog id is "session_id" and xandr's is "auction_id_64"
        """
        dedupe_subset = list()
        if self.deal["resolution_type"] == Resolution.NPI.value:
            dedupe_subset.append("npi")
        if self.deal["dsp"] == DSP.XANDR.value:
            dedupe_subset.append("auction_id_64")
        elif self.deal["dsp"] == DSP.VIANT.value:
            dedupe_subset.append("session_id")
        else:
            raise Exception(f"Unknown dsp: {self.deal['dsp']}")
        return df.drop_duplicates(subset=dedupe_subset)

    def execute(self, context: Context):
        trino_hook = TrinoHook(
            trino_conn_id=self.trino_conn_id,
        )
        s3_hook = S3Hook(
            aws_conn_id=self.aws_conn_id
        )
        match self.deal["resolution_type"]:
            case Resolution.AIQ.value:
                resolution_column = "aiq_hhid"
                resolution_directory = "dtc"
            case Resolution.NPI.value:
                resolution_column = "npi"
                resolution_directory = "hcp"
        match self.deal["dsp"]:
            case DSP.XANDR.value:
                query = f"""
                    SELECT DISTINCT 
                        auction_id_64,
                        date_time,
                        user_tz_offset,
                        media_type,
                        event_type,
                        country,
                        region,
                        dma,
                        city,
                        postal_code,
                        latitude,
                        latitude_trunc,
                        longitude,
                        longitude_trunc,
                        device_type,
                        tc_string,
                        curated_deal_id,
                        gross_revenue_dollars,
                        curator_margin,
                        total_tech_fees_dollars,
                        total_cost_dollars,
                        net_media_cost_dollars,
                        seller_memeber_id,
                        publisher_id,
                        site_id,
                        site_domain,
                        tag_id,
                        application_id,
                        mobile_app_instance_id,
                        buyer_member_id,
                        creative_id,
                        brand_id,
                        seller_deal_id,
                        view_result,
                        view_non_measurable_reason,
                        supply_type,
                        creative_width,
                        creative_height,
                        partition_time_millis,
                        operation_system,
                        browser,
                        language,
                        ethash_v1,
                        {resolution_column},
                        date
                    FROM s3.silver_medicx.id_rez_results_xandr
                    WHERE campaign_id='{self.deal['deal_id']}'
                    AND date = DATE('{self.log_date}')
                """
            case DSP.VIANT.value:
                query = f"""
                    SELECT DISTINCT 
                        session_id,
                        event_type,
                        timestamp,
                        ad_order_id,
                        ad_order_name,
                        viant_campaign_id as campaign_id,
                        viant_campaign_name as campaign_name,
                        advertiser_id,
                        advertiser_name,
                        creative_id,
                        creative_name,
                        publisher_id,
                        publisher_name,
                        site_id,
                        site_name,
                        device_id_type,
                        os,
                        device_type,
                        device_make,
                        device_model,
                        country,
                        region,
                        metrocode,
                        postal_code,
                        city,
                        lat,
                        lon,
                        timezone_offset,
                        adelphic_internal,
                        ethash_v1,
                        {resolution_column},
                        date
                    FROM s3.silver_medicx.id_rez_results_viant
                    WHERE campaign_id='{self.deal['deal_id']}'
                    AND date = DATE('{self.log_date}')
                """
        print(query)
        df = pd.read_sql(query, trino_hook.get_conn())
        df = self.dedupe_results(df)
        s3_bucket = "claimsteam-mdx-eltoro-transfer-files"
        s3_key = f"id_resolution/logs/{resolution_directory}/{self.deal['dsp']}/{self.deal['deal_id']}/output/{self.log_date}/results.csv" 
        print(f"s3://{s3_bucket}/{s3_key}")
        s3_hook.load_string(
            string_data=df.to_csv(index=False), 
            key=s3_key,
            bucket_name=s3_bucket,
            replace=True
        )