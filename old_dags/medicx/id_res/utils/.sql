-- trino bronze
CREATE TABLE IF NOT EXISTS s3.bronze_medicx.id_rez_selected_ips(
    ip VARCHAR,
    ethash VARCHAR,
    deal_name VARCHAR,
    campaign_id VARCHAR
)
WITH (
    external_location = 's3://eltoro-data-sources/medicx/id_rez/prod/audience_selected_ips',
    format = 'PARQUET',
    partitioned_by = ARRAY['campaign_id']
);

--trino silver
CREATE TABLE IF NOT EXISTS s3.silver_medicx.id_rez_targeted_audience(
    ip VARCHAR,
    ethash VARCHAR,
    selected_audience BOOLEAN,
    date DATE,
    campaign_id VARCHAR
)
WITH (
    external_location = 's3://eltoro-data-sources/medicx/id_rez/prod/targeted_audience',
    format = 'PARQUET',
    partitioned_by = ARRAY['date','campaign_id']
);


CREATE TABLE IF NOT EXISTS s3.silver_medicx.id_rez_results_xandr(
    auction_id_64 bigint,
    date_time bigint,
    user_tz_offset integer,
    media_type integer,
    event_type varchar,
    user_id_64 bigint,
    hashed_user_id_64 varbinary,
    ip_address varchar,
    ip_address_trunc varchar,
    country varchar,
    region varchar,
    dma integer,
    city integer,
    postal_code varchar,
    latitude varchar,
    latitude_trunc varchar,
    longitude varchar,
    longitude_trunc varchar,
    device_unique_id varchar,
    device_type integer,
    tc_string varchar,
    curated_deal_id integer,
    gross_revenue_dollars double,
    curator_margin double,
    total_tech_fees_dollars double,
    total_cost_dollars double,
    net_media_cost_dollars double,
    seller_memeber_id integer,
    publisher_id integer,
    site_id integer,
    site_domain varchar,
    tag_id integer,
    application_id varchar,
    mobile_app_instance_id integer,
    buyer_member_id integer,
    creative_id integer,
    brand_id integer,
    seller_deal_id integer,
    view_result integer,
    view_non_measurable_reason integer,
    supply_type integer,
    creative_width integer,
    creative_height integer,
    partition_time_millis timestamp(6),
    operation_system integer,
    browser integer,
    language integer,
    device_id integer,
    -- extended_ids array(row(id_type integer, id_value varchar)),
    npi double,
    aiq_hhid double,
    ethash_v1 varchar,
    ip varchar,
    date date,
    campaign_id varchar
)
WITH (
    external_location = 's3://eltoro-data-sources/medicx/id_rez/prod/results_xandr',
    format = 'PARQUET',
    partitioned_by = ARRAY['date','campaign_id']
);



CREATE TABLE IF NOT EXISTS s3.silver_medicx.id_rez_results_viant(
    session_id varchar,
    event_type varchar,
    timestamp varchar,
    ad_order_id varchar,
    ad_order_name varchar,
    viant_campaign_id varchar,
    viant_campaign_name varchar,
    advertiser_id varchar,
    advertiser_name varchar,
    creative_id varchar,
    creative_name varchar,
    publisher_id varchar,
    publisher_name varchar,
    site_id varchar,
    site_name varchar,
    ip varchar,
    device_id varchar,
    device_id_type varchar,
    os varchar,
    device_type varchar,
    device_make varchar,
    device_model varchar,
    country varchar,
    region varchar,
    metrocode varchar,
    postal_code varchar,
    city varchar,
    lat varchar,
    lon varchar,
    timezone_offset varchar,
    adelphic_internal varchar,
    year varchar,
    month varchar,
    day varchar,
    hour varchar,
    npi varchar,
    aiq_hhid varchar,
    ethash_v1 varchar,
    date date,
    campaign_id varchar
)
WITH (
    external_location = 's3://eltoro-data-sources/medicx/id_rez/prod/results_viant',
    format = 'PARQUET',
    partitioned_by = ARRAY['date','campaign_id']
);