from old_dags.medicx.id_res.operators.fetch_deals.fetch_deals_xandr import FetchDealsXandr
from old_dags.medicx.id_res.operators.fetch_deals.fetch_deals_viant import FetchDealsViant
from old_dags.medicx.id_res.operators.fetch_deals.deal_schema import Deal, DSP

from old_dags.medicx.id_res.operators.stage_selected_ips_v2 import StageSelectedIPsOperator
from old_dags.medicx.id_res.operators.stage_targeted_audience import StageTargetedAudienceOperator
from old_dags.medicx.id_res.operators.upload_results_v2 import UploadResultsOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.decorators import task
from etdag import ETDAG
from datetime import timedelta
from airflow.models.param import Param
from datetime import datetime, timedelta
from old_dags.medicx.id_res.operators.gen_output_v2 import GenerateOutput



docs = """
# ID Rez V2

Background Info:
* Medicx asks us to process deals for them weekly and share segments to their xandr curate/viant seats through audience_onboarding
* Medicx wants more info on who they targeted through their xandr deals from audience_onboarding
* This Dag gets the current active deals' weblog data (ip level) and adds a one to one join with the originally targeted addresses
    * For this process' sake, 1 ip = 1 address
* The final output to medicx includes the original weblogs plus new columns (aiq_hhid/npi, ethash_v1)
    * 1 address = 1 aiq_hhid (one to one)
    * 1 address = x npis (one to many)
    * Whether the final output has an npi or aiq_hhid column depends on the original audience onboarding deal that medicx created

Failure Scenario:
* There are 3 retries, failure is unexpected and will have to be troubleshoot in case a day or deal fails

Source Data:
* Medicx DB in Postgres to fetch the deals' selected ips s3 location. schema: audience, tables: metadata, quote_logs, deploy_logs
* Starburst's olympus.bronze_medicx_xandr.xandr_curate_feed for the deals' weblogs

Result Data Destination:  
* Medicx external s3 bucket. s3://claimsteam-mdx-eltoro-transfer-files/id_resolution/logs/{dsp}/{deal_id}/output/{log_date}/results.csv

Escalation Path:  
* Contact Jonesy for DataRep
* Contact Will Bradford for SalesRep

In Case Data Source Breaks:  
* Dag responsible for source weblog data: medix_xandr_log_level_load
* Dag responsible for postgres deal data: audience_onboarding_medicx
"""

@task
def sync_trino(schema, table_names, trino_conn_id='trino_conn'):
    trino_hook = TrinoHook(trino_conn_id=trino_conn_id)
    for table_name in table_names:
        sync_query = f"CALL s3.system.sync_partition_metadata('{schema}', '{table_name}', 'ADD')"
        trino_hook.run(sync_query)
        print(f"Sync successful for table: {schema}.{table_name}")

@task
def deal_combiner(
    xandr_enabled: str, viant_enabled: str, 
    xandr_deals: list[Deal], viant_deals: list[Deal]
) -> list[Deal]:
    deals = list()
    if eval(xandr_enabled):
        deals.extend(xandr_deals)
    if eval(viant_enabled):
        deals.extend(viant_deals)
    return deals

with ETDAG(
    dag_id="id_rez_v2",
    default_args= {
        "owner": "Panama", 
        "retries":3, 
        "retry_delay": timedelta(seconds=300),
    },
    is_paused_upon_creation=True,
    schedule_interval="0 12 * * *", #8am EDT every day
    max_active_runs=1,
    catchup=False,
    params={
        "log_date": Param(
            default=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
            type="string"
        ),
        "xandr_enabled": Param(
            default=True,
            type="boolean"
        ),
        "viant_enabled": Param(
            default=True,
            type="boolean"
        )
    },
    tags=["id_resolution", "medicx"],
) as dag:
    dag.doc_md = docs
    
    fetch_active_xandr_deals = FetchDealsXandr(
        task_id="fetch_active_xandr_deals",
        log_date="{{ params.log_date }}"
    )

    fetch_active_viant_deals = FetchDealsViant(
        task_id="fetch_active_viant_deals",
        log_date="{{ params.log_date }}"
    )
    deal_combiner_task = deal_combiner(
        xandr_enabled="{{ params.xandr_enabled }}",
        viant_enabled="{{ params.viant_enabled }}",
        xandr_deals=fetch_active_xandr_deals.output,
        viant_deals=fetch_active_viant_deals.output
    )
    stage_selected_ips = StageSelectedIPsOperator.partial(
        task_id="stage_selected_ips",
        max_active_tis_per_dag=5,
    ).expand(deal=deal_combiner_task)

    stage_targeted_ips = StageTargetedAudienceOperator.partial(
        task_id="stage_targeted_ips",
        log_date = "{{ params.log_date }}",
        max_active_tis_per_dag=5,
    ).expand(deal=deal_combiner_task)
    
    sync_selected_ips = sync_trino.override(task_id="sync_selected_ips_table")(
        schema="bronze_medicx", 
        table_names=["id_rez_selected_ips"],
    )
    sync_targeted_ips = sync_trino.override(task_id="sync_targeted_audience_table")(
        schema="silver_medicx", 
        table_names=["id_rez_targeted_audience"],
    )
    generate_results = GenerateOutput.partial(
        task_id="generate_results",
        log_date="{{ params.log_date }}",
        max_active_tis_per_dag=5
    ).expand(deal=deal_combiner_task)

    sync_results = sync_trino.override(task_id="sync_results_table")(
        schema="silver_medicx",
        table_names=["id_rez_results_xandr", "id_rez_results_viant"],
    )

    upload_results = UploadResultsOperator.partial(
        task_id="upload_results",
        log_date = "{{ params.log_date }}",
        max_active_tis_per_dag=5
    ).expand(deal=deal_combiner_task)
    
    [fetch_active_xandr_deals, fetch_active_viant_deals] >> deal_combiner_task >>\
    stage_selected_ips >> sync_selected_ips >>\
    stage_targeted_ips >> sync_targeted_ips >>\
    generate_results >> sync_results >>\
    upload_results