from old_dags.medicx.tagging.operators.etl_operator import WeblogETLOperator
from old_dags.medicx.tagging.operators.metrics_gen_operator import MetricsGenOperator
from airflow.models import Variable
from airflow.providers.amazon.aws.transfers.sql_to_s3 import SqlToS3Operator
from etdag import ETDAG

with ETDAG(
    dag_id="tag_weblogs_etl",
    schedule_interval="0 5 * * *",  # Run daily at 5 am utc
    catchup=False,
    default_args={"owner": "Panama", "retries": 0},
    is_paused_upon_creation=True,
    max_active_runs=3,
    tags=["medicx"],
) as dag:
    env = Variable.get("environment")
    pq_lake_s3_url = f"s3://et-datalake-pixel-logs-{env}/pq_weblogs/"
    tagging_etl_instance = WeblogETLOperator(
        task_id="weblogs_to_pq",
        s3_input_bucket="us-east-1-prod-tagging-pixel-logs",
        s3_url_output=pq_lake_s3_url,
    )
    creative_metrics_generation_instance = MetricsGenOperator(
        task_id="creative_metrics_generation",
        pq_s3_url=pq_lake_s3_url,
        weblog_type="creative",
    )
    site_metrics_generation_instance = MetricsGenOperator(
        task_id="site_metrics_generation", pq_s3_url=pq_lake_s3_url, weblog_type="site"
    )
    creative_metrics_exporter_instance = SqlToS3Operator(
        task_id="creative_metrics_export",
        query="SELECT * from tagging.creative_log_metrics",
        s3_bucket="vr-timestamp",
        s3_key=f"bi_sources/medicx/{env}_creative_weblog_metrics.csv",
        sql_conn_id="postgres_medicx",
        replace=True,
        pd_kwargs={"index": False},
    )
    site_metrics_exporter_instance = SqlToS3Operator(
        task_id="site_metrics_export",
        query="SELECT * from tagging.site_log_metrics",
        s3_bucket="vr-timestamp",
        s3_key=f"bi_sources/medicx/{env}_site_weblog_metrics.csv",
        sql_conn_id="postgres_medicx",
        replace=True,
        pd_kwargs={"index": False},
    )
    tagging_etl_instance >> [
        creative_metrics_generation_instance,
        site_metrics_generation_instance,
    ]
    creative_metrics_generation_instance >> creative_metrics_exporter_instance
    site_metrics_generation_instance >> site_metrics_exporter_instance
