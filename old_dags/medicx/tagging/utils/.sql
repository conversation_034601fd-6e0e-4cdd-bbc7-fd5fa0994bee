
CREATE TABLE tagging.log_metrics (
	id SERIAL, --this kinda sucks as a pk, TODO get something more meaningful
	weblog_time_hour TIMESTAMPTZ,
	tagging_type TEXT,
	product_type TEXT,  -- Fixed value defined by El Toro
	client_id TEXT, -- Fixed value defined by El Toro
	campaign_id VARCHAR(20), -- Unique numeric ID for the campaign identifier - up to 20 digits
	creative_id VARCHAR(20), -- Unique numeric ID for the creative identifier - up to 20 digits
	placement_id TEXT, -- Unique numeric ID for the placement identifier (use 1 if no macro)
	audience_id TEXT, -- AudienceID from El Toro
	testcontrol VARCHAR(1), -- 1 for test, 2 for control (just the digit), or empty. if neither
	site_id TEXT, -- Unique numeric ID for the site identifier - use a 1 if no macro
	advertising_id TEXT, -- Unique device ID (IDFA) (usually must be a macro for a MAID)
	creativesize VARCHAR(20), -- Size value - up to 20 digits (ex :  300x250)
	supply_type TEXT, -- Only use if Macro Available, or known value for the campaign
	referralurl TEXT, -- Only use if a Macro is supported by DSP Serving Ad
	adserver_id TEXT, -- Fixed values defined by serving platform (see chart below)
	click_to_play VARCHAR(1), -- 0 is Autoplay, 1 is Click-to-Play (just the digit)
	imps INT -- number of impressions
)

CREATE TABLE tagging.processed_files (
	id SERIAL,
	filename TEXT,
	processed_time TIMESTAMPTZ
)
