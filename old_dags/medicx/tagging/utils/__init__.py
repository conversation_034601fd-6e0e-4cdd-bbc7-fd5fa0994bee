
import pandas as pd
import user_agents
from urllib.parse import urlparse, parse_qs


site_mapping = {
    "e1": "product_type",
    "e2": "client_id",
    "e3": "campaign_id",
    "e10": "testcontrol",
    "e11": "site_id",
    "e14": "supply_type",
}

creative_mapping = {
    "e1": "product_type", #Fixed value defined by El Toro
    "e2": "client_id", #Fixed value defined by El Toro
    "e3": "campaign_id", #Unique numeric ID for the campaign identifier - up to 20 digits
    "e4": "creative_id", #Unique numeric ID for the creative identifier - up to 20 digits
    "e5": "placement_id", #Unique numeric ID for the placement identifier (use 1 if no macro)
    "e6": "audience_id", #AudienceID from El Toro
    "e10": "testcontrol", #1 for test, 2 for control (just the digit), or empty. if neither
    "e11": "site_id", #Unique numeric ID for the site identifier - use a 1 if no macro
    "e12": "advertising_id", #Unique device ID (IDFA) (usually must be a macro for a MAID)
    "e13": "creativesize", #Size value - up to 20 digits (ex :  300x250)
    "e14": "supply_type", #Only use if Macro Available, or known value for the campaign
    "e15": "referralurl", #Only use if a Macro is supported by DSP Serving Ad
    "e16": "adserver_id", #Fixed values defined by serving platform (see chart below)
    "ectp": "click_to_play", #0 is Autoplay, 1 is Click-to-Play (just the digit)
}

creative_only_columns = ["creative_id","placement_id","audience_id",
                         "advertising_id","creativesize","referralurl",
                         "adserver_id","click_to_play"]


site_group_by_columns = [
    "tagging_type",
    "product_type",
    "client_id",
    "campaign_id",
    "testcontrol",
    "site_id",
    "supply_type",
]

creative_group_by_columns = [
    "tagging_type",
    "product_type",
    "client_id",
    "campaign_id",
    "creative_id",
    "placement_id",
    "audience_id",
    "testcontrol",
    "site_id",
    "advertising_id",
    "creativesize",
    "supply_type",
    "referralurl",
    "adserver_id",
    "click_to_play",
]

cs_uri_columns = [
    "tagging_type",
    "product_type",
    "client_id",
    "campaign_id",
    "creative_id",
    "placement_id",
    "audience_id",
    "testcontrol",
    "site_id",
    "advertising_id",
    "creativesize",
    "supply_type",
    "referralurl",
    "adserver_id",
    "click_to_play",
]

raw_weblog_df_schema = {
    "date": str,
    "time": str,
    "x-edge-location": str,
    "sc-bytes": int,
    "c-ip": str,
    "cs-method": str,
    "cs(Host)": str,
    "cs-uri-stem": str,
    "sc-status": int,
    "cs(Referer)": str,
    "cs(User-Agent)": str,
    "cs-uri-query": str,
    "cs(Cookie)": str,
    "x-edge-result-type": str,
    "x-edge-request-id": str,
    "x-host-header": str,
    "cs-protocol": str,
    "cs-bytes": int,
    "time-taken": float,
    "x-forwarded-for": str,
    "ssl-protocol": str,
    "ssl-cipher": str,
    "x-edge-response-result-": str,
    "cs-protocol-version": str,
    "fle-status": str,
    "fle-encrypted-fields": str,
    "c-port": int,
    "time-to-first-byte": float,
    "x-edge-detailed-result-": str,
    "sc-content-type": str,
    "sc-content-len": str,
    "sc-range-start": str,
    "sc-range-en": str,
}

processed_weblog_df_schema = {
    "date": pd.StringDtype(storage="pyarrow"),
    "time": pd.StringDtype(storage="pyarrow"),
    "x-edge-location": pd.StringDtype(storage="pyarrow"),
    "sc-bytes": pd.Int32Dtype(),
    "c-ip": pd.StringDtype(storage="pyarrow"),
    "cs-method": pd.StringDtype(storage="pyarrow"),
    "cs(Host)": pd.StringDtype(storage="pyarrow"),
    "cs-uri-stem": pd.StringDtype(storage="pyarrow"),
    "sc-status": pd.Int32Dtype(),
    "cs(Referer)": pd.StringDtype(storage="pyarrow"),
    "cs(User-Agent)": pd.StringDtype(storage="pyarrow"),
    "cs-uri-query": pd.StringDtype(storage="pyarrow"),
    "cs(Cookie)": pd.StringDtype(storage="pyarrow"),
    "x-edge-result-type": pd.StringDtype(storage="pyarrow"),
    "x-edge-request-id": pd.StringDtype(storage="pyarrow"),
    "x-host-header": pd.StringDtype(storage="pyarrow"),
    "cs-protocol": pd.StringDtype(storage="pyarrow"),
    "cs-bytes": pd.Int32Dtype(),
    "time-taken": pd.Float32Dtype(),
    "x-forwarded-for": pd.StringDtype(storage="pyarrow"),
    "ssl-protocol": pd.StringDtype(storage="pyarrow"),
    "ssl-cipher": pd.StringDtype(storage="pyarrow"),
    "x-edge-response-result-": pd.StringDtype(storage="pyarrow"),
    "cs-protocol-version": pd.StringDtype(storage="pyarrow"),
    "fle-status": pd.StringDtype(storage="pyarrow"),
    "fle-encrypted-fields": pd.StringDtype(storage="pyarrow"),
    "c-port": pd.Int32Dtype(),
    "time-to-first-byte": pd.Float32Dtype(),
    "x-edge-detailed-result-": pd.StringDtype(storage="pyarrow"),
    "sc-content-type": pd.StringDtype(storage="pyarrow"),
    "sc-content-len": pd.StringDtype(storage="pyarrow"),
    "sc-range-start": pd.StringDtype(storage="pyarrow"),
    "sc-range-en": pd.StringDtype(storage="pyarrow"),
    "tagging_type": pd.StringDtype(storage="pyarrow"),
    "product_type": pd.StringDtype(storage="pyarrow"),
    "client_id": pd.StringDtype(storage="pyarrow"),
    "campaign_id": pd.StringDtype(storage="pyarrow"),
    "creative_id": pd.StringDtype(storage="pyarrow"),
    "placement_id": pd.StringDtype(storage="pyarrow"),
    "audience_id": pd.StringDtype(storage="pyarrow"),
    "testcontrol": pd.StringDtype(storage="pyarrow"),
    "site_id": pd.StringDtype(storage="pyarrow"),
    "advertising_id": pd.StringDtype(storage="pyarrow"),
    "creativesize": pd.StringDtype(storage="pyarrow"),
    "supply_type": pd.StringDtype(storage="pyarrow"),
    "referralurl": pd.StringDtype(storage="pyarrow"),
    "adserver_id": pd.StringDtype(storage="pyarrow"),
    "click_to_play": pd.StringDtype(storage="pyarrow"),
    "user-browser": pd.StringDtype(storage="pyarrow"),
    "user-os": pd.StringDtype(storage="pyarrow"),
    "user-device-brand": pd.StringDtype(storage="pyarrow"),
    "user-device-model": pd.StringDtype(storage="pyarrow"),
    "is-mobile": pd.BooleanDtype(),
    "is-tablet": pd.BooleanDtype(),
    "is-pc": pd.BooleanDtype(),
    "is-bot": pd.BooleanDtype(),
}


def parse_user_agent(user_agent: str) -> pd.Series:
    parsed_user_agent = user_agents.parse(user_agent)
    return pd.Series([parsed_user_agent.browser.family, 
        parsed_user_agent.os.family, 
        parsed_user_agent.device.brand, 
        parsed_user_agent.device.model,
        parsed_user_agent.is_mobile,
        parsed_user_agent.is_tablet,
        parsed_user_agent.is_pc,
        parsed_user_agent.is_bot],
        index=['user-browser', 'user-os', 'user-device-brand', 'user-device-model', 
        'is-mobile', 'is-tablet', 'is-pc', 'is-bot'])

def parse_cs_uri_stem(row: pd.Series):
    none_dict = {
        "tagging_type": None,
        "product_type": None,
        "client_id": None,
        "campaign_id": None,
        "creative_id": None,
        "placement_id": None,
        "audience_id": None,
        "testcontrol": None,
        "site_id": None,
        "advertising_id": None,
        "creativesize": None,
        "supply_type": None,
        "referralurl": None,
        "adserver_id": None,
        "click_to_play": None,
    }
    query_params = parse_qs(row["cs-uri-query"])
    if len(row["cs-uri-stem"])<2:
        return none_dict
    query_params = {k: v[0] for k, v in query_params.items()}
    mapped_data_dict = {}
    if "/c." in row["cs-uri-stem"]:
        print(f"parsing creative: {query_params}")
        mapped_data_dict = {new_key: query_params.get(old_key, None) for old_key, new_key in creative_mapping.items()}
        mapped_data_dict["tagging_type"] = "creative"

    elif "/s." in row["cs-uri-stem"]:
        print(f"parsing site: {query_params}")
        mapped_data_dict = {new_key: query_params.get(old_key, None) for old_key, new_key in site_mapping.items()}
        mapped_data_dict["tagging_type"] = "site"

    else:
        return none_dict
    
    for column_name in cs_uri_columns:
        if column_name not in mapped_data_dict:
            mapped_data_dict[column_name] = None
    return mapped_data_dict