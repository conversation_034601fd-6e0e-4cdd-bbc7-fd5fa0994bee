from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import pandas as pd
import old_dags.medicx.tagging.utils as utils
from airflow.providers.postgres.hooks.postgres import PostgresHook
from typing import List
from datetime import datetime

class WeblogETLOperator(BaseOperator):
    template_fields = ["s3_input_bucket", "s3_url_output"]

    @apply_defaults
    def __init__(
        self,
        *,
        s3_url_output: str,
        s3_input_bucket: str = "us-east-1-prod-tagging-pixel-logs",
        medicx_db_id: str = "postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.s3_input_bucket = s3_input_bucket
        self.s3_url_output = s3_url_output
        self.medicx_db_id = medicx_db_id

    def execute(self, context):
        """
        This custom operator looks up in postgres for all the files that have already been processed
        Appends all unprocessed files into a master csv for the day and processes them
        Finally the processed list of files in postgres is updated
        """
        run_date = context['execution_date'].date().isoformat()
        psql_engine = PostgresHook(self.medicx_db_id).get_sqlalchemy_engine()
        processed_files = list(pd.read_sql(
            f"""
                SELECT filename
                FROM tagging.processed_files
                WHERE processed_time::date = '{run_date}'
            """,
            con=psql_engine
        )["filename"])
        processed_files = []
        weblog_df = self.__extract_weblog_df(run_date, processed_files)
        if weblog_df.empty:
            return
        weblog_df = self.__process_weblog_df(weblog_df)
        
        self.__upload_weblog_df(weblog_df)

        ingested_files = weblog_df["filename"].unique()
        ingested_files_df = pd.DataFrame({"filename": ingested_files})
        ingested_files_df["processed_time"] = datetime.utcnow()
        ingested_files_df.to_sql(
            "processed_files",
            schema="tagging",
            if_exists="append",
            index=False,
            con=psql_engine
        )
    
    def __upload_weblog_df(self, weblog_df: pd.DataFrame):
        weblog_df.query(
            "tagging_type=='creative'"
        ).to_parquet(
            f"{self.s3_url_output}creative/",
            partition_cols=[
                "date",
            ],
            index=False,
        )
        weblog_df.query(
            "tagging_type=='site'"
        ).drop(
            columns=utils.creative_only_columns
        ).to_parquet(
            f"{self.s3_url_output}site/",
            partition_cols=[
                "date",
            ],
            index=False,
        )

    def __extract_weblog_df(self, run_date: str, processed_files: List[str]) -> pd.DataFrame:
        s3_hook = S3Hook()
        df_master = pd.DataFrame()
        all_files = s3_hook.list_keys(self.s3_input_bucket)
        report_files = [file for file in all_files if run_date in file]
        report_files = [file for file in report_files if file not in processed_files]
        for report_file in report_files:
            print(f"extracting: {report_file}")
            df = pd.read_csv(
                f"s3://{self.s3_input_bucket}/{report_file}",
                compression="gzip",
                sep="\t",
                skiprows=2,
                names=list(utils.raw_weblog_df_schema.keys()),
            )
            df["filename"] = report_file
            df_master = pd.concat([df_master, df])
        return df_master

    def __process_weblog_df(self, weblog_df: pd.DataFrame):
        # parse the cs-uri-stem
        site_creative_df = weblog_df.apply(utils.parse_cs_uri_stem, axis=1).apply(pd.Series)
        weblog_df = pd.concat([weblog_df, site_creative_df], axis=1)
        # parse the user_agent
        user_agent_df = weblog_df["cs(User-Agent)"].apply(utils.parse_user_agent).apply(pd.Series)
        weblog_df = pd.concat([weblog_df, user_agent_df], axis=1)

        weblog_df = weblog_df.fillna(value={"campaign_id":"NA"})
        weblog_df = weblog_df.astype(
            utils.processed_weblog_df_schema
        )
        return weblog_df
