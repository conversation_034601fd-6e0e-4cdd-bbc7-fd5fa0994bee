from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
import pandas as pd
from airflow.providers.postgres.hooks.postgres import PostgresHook
import old_dags.medicx.tagging.utils as utils 

weblog_type_site = "site"
weblog_type_creative = "creative"

class MetricsGenOperator(BaseOperator):
    template_fields = ["weblog_type", "pq_s3_url"]

    @apply_defaults
    def __init__(
        self,
        *,
        pq_s3_url: int,
        weblog_type: str,
        psql_conn_id: str="postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.pq_s3_url=pq_s3_url
        self.psql_conn_id=psql_conn_id
        self.weblog_type=weblog_type

    def execute(self, context):
        psql_engine = PostgresHook(self.psql_conn_id).get_sqlalchemy_engine()
        run_date = context['execution_date'].date().isoformat()
        if self.weblog_type==weblog_type_site:
            columns = {k: v for k, v in utils.processed_weblog_df_schema.items() if k not in utils.creative_only_columns}
            groupby_columns = utils.site_group_by_columns
        else:
            columns = utils.processed_weblog_df_schema
            groupby_columns = utils.creative_group_by_columns
        columns.pop("date")
        try:
            weblog_df = pd.read_parquet(
                f"{self.pq_s3_url}{self.weblog_type}/date={run_date}", 
                columns=columns
            )
        except FileNotFoundError:
            return
        if weblog_df.empty:
            return
        try:
            psql_engine.execute(f"DELETE FROM tagging.{self.weblog_type}_log_metrics WHERE weblog_time_hour::date = '{run_date}'")
        except:
            pass
        weblog_df['weblog_time_hour'] = pd.to_datetime(run_date + ' ' + weblog_df['time'])
        
        log_metrics = weblog_df.groupby(
            by=[pd.Grouper(key="weblog_time_hour", freq="H")] + groupby_columns,
            dropna=False
        ).agg(
            imps=pd.NamedAgg(column="time", aggfunc="count")
        ).reset_index()
        log_metrics = log_metrics.replace({'NA': None})
        log_metrics.to_sql(
            f"{self.weblog_type}_log_metrics",
            schema="tagging",
            if_exists="append",
            index=False,
            con=psql_engine
        ) 