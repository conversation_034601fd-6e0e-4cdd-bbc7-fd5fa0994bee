{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> requests\n", "Medicx sends an email every Tuesday and Thursday requesting audience onboarding files to be processed. \n", "\n", "#### S3 sync\n", "Medicx will transfer the files mentioned on the email by placing them on their s3 bucket, `s3://claimsteam-mdx-eltoro-transfer-files/`. We have an airflow dag that syncs the bucket hourly into our eltoro s3, located here, `s3://eltoro-data-sources/medicx/sync/`\n", "\n", "#### Metadata Importer\n", "With each request medicx will upload metadata files. The metadata files need to be extracted and sent to the medicx postgres database. The `metadata.py` contains a function that syncs the metadata data from s3 into the medicx postgres database, `medicx.bigdata.k8s.eltoro.com`. Medicx will send the metadata file through email, after that we can upload to s3 in the meantime for the metadata importer to import to postgres.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["extracting metadata for deal: OPRX_31407719090_DX_A1_DEMO_ZIPS\n", "extracting metadata for deal: OPRX_31407719090_DX_A2_DEMO_ZIPS\n", "extracting metadata for deal: OPRX_31407719090_DX_A3_DEMO_ZIPS\n"]}], "source": ["from old_dags.operators.utils.metadata_import import MetaFileProcessor\n", "import datetime\n", "import re\n", "from sqlalchemy import engine\n", "from awswrangler import secretsmanager\n", "from requests.auth import HTTPBasicAuth\n", "\n", "# change this s3_url\n", "s3_url = \"s3://eltoro-data-sources/medicx/audience_onboarding/metadata/ELToro MD File 02262025.xlsx\"\n", "\n", "# Extract date from s3_url\n", "date_str = re.search(r'(\\d{8})', s3_url).group(1)\n", "metadata_input_date = datetime.datetime.strptime(date_str, '%m%d%Y').date()\n", "airflow_creds = secretsmanager.get_secret_json(\"prod/bi/airflow\")\n", "psql_engine = engine.create_engine(\n", "    secretsmanager.get_secret_json(\"prod/bi/medicx\")[\"connection_string\"]\n", ")\n", "meta_processor = MetaFileProcessor(psql_engine)\n", "meta_processor.upload_meta_to_db(\n", "    metadata_s3_url=s3_url, \n", "    metadata_input_date=metadata_input_date, \n", "    share_segments=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get the Queued Audiences in airflow to reorder the queue, smallest audiences first"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "conf", "rawType": "object", "type": "unknown"}, {"name": "dag_id", "rawType": "object", "type": "unknown"}, {"name": "dag_run_id", "rawType": "object", "type": "unknown"}, {"name": "data_interval_end", "rawType": "object", "type": "unknown"}, {"name": "data_interval_start", "rawType": "object", "type": "unknown"}, {"name": "end_date", "rawType": "object", "type": "unknown"}, {"name": "execution_date", "rawType": "object", "type": "unknown"}, {"name": "external_trigger", "rawType": "bool", "type": "boolean"}, {"name": "last_scheduling_decision", "rawType": "object", "type": "unknown"}, {"name": "logical_date", "rawType": "object", "type": "unknown"}, {"name": "note", "rawType": "object", "type": "unknown"}, {"name": "run_type", "rawType": "object", "type": "unknown"}, {"name": "start_date", "rawType": "object", "type": "unknown"}, {"name": "state", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "ebbdfd99-0e40-41ea-ba2a-68275fdd8d31", "rows": [], "shape": {"columns": 14, "rows": 0}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>conf</th>\n", "      <th>dag_id</th>\n", "      <th>dag_run_id</th>\n", "      <th>data_interval_end</th>\n", "      <th>data_interval_start</th>\n", "      <th>end_date</th>\n", "      <th>execution_date</th>\n", "      <th>external_trigger</th>\n", "      <th>last_scheduling_decision</th>\n", "      <th>logical_date</th>\n", "      <th>note</th>\n", "      <th>run_type</th>\n", "      <th>start_date</th>\n", "      <th>state</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [conf, dag_id, dag_run_id, data_interval_end, data_interval_start, end_date, execution_date, external_trigger, last_scheduling_decision, logical_date, note, run_type, start_date, state]\n", "Index: []"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "import pandas as pd\n", "\n", "sess = requests.session()\n", "# change this session token\n", "queued_df =pd.DataFrame(sess.get(\n", "        url=\"https://airflow.k8s.eltoro.com/api/v1/dags/audience_onboarding_oprx_v2/dagRuns\",\n", "        headers={\n", "            \"Content-Type\": \"application/json\",\n", "        },\n", "        auth=HTTPBasicAuth(airflow_creds[\"user\"], airflow_creds[\"password\"])\n", "\n", ").json()[\"dag_runs\"])\n", "queued_df = queued_df[queued_df['state'] == 'queued']\n", "queued_metadata_ids = list()\n", "for _, row in queued_df.iterrows():\n", "    queued_metadata_ids.append(row[\"conf\"][\"metadata_id\"])\n", "queued_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### TODO: Delete dag runs that are queued in order to reorder the queue"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Querying the Metadata Table\n", "Once the metadata has been exported to postgres, we need to get the metadata ids of the files we want to run the audience onboarding process on. \n", "\n", "The following query fetches the latest metadata extracts by sorting based on `metadata_last_modified` (this is a limitation of s3 in which we aren't able to know when a file was created, only when it was last modified) Also `metadata_last_modified` technically refers to the medicx batch rather than whenever it was uploaded to s3; scope changes and all."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_315175/874886202.py:19: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  deals_metadata = pd.read_sql(\n"]}, {"data": {"text/plain": ["[4402, 4401, 4403]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "if queued_df.empty:\n", "    query = f\"\"\"\n", "        SELECT metadata_id, deal_name \n", "        FROM audience.metadata\n", "        WHERE metadata_last_modified = '{metadata_input_date.strftime('%Y-%m-%d')}'\n", "        ORDER BY input_content_bytes ASC\n", "    \"\"\"\n", "else:\n", "    query = f\"\"\"\n", "    SELECT metadata_id, deal_name \n", "    FROM audience.metadata\n", "    WHERE metadata_last_modified = '{metadata_input_date.strftime('%Y-%m-%d')}'\n", "    OR metadata_id in ({','.join(map(str, queued_metadata_ids))})\n", "    AND targets_processed = False\n", "    ORDER BY input_content_bytes ASC\n", "    \"\"\"\n", "deals_metadata = pd.read_sql(\n", "    query, con=psql_engine.raw_connection()\n", ")\n", "\n", "#TODO add a way to cancel the dag runs that were previously queued in order to requeue then\n", "metadata_ids = list(deals_metadata[\"metadata_id\"])\n", "metadata_ids"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trigger the Airflow DAG\n", "Using the previously acquired metadata_ids, send the post request to the `audience_onboarding_medicx` DAG.\\\n", "The session token can be acquired from the browser cookies session."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'conf': {'metadata_id': 4402}, 'dag_id': 'audience_onboarding_oprx_v2', 'dag_run_id': '4402_OPRX_31407719090_DX_A2_DEMO_ZIPS', 'data_interval_end': '2025-02-26T19:26:03.577336+00:00', 'data_interval_start': '2025-02-26T19:26:03.577336+00:00', 'end_date': None, 'execution_date': '2025-02-26T19:26:03.577336+00:00', 'external_trigger': True, 'last_scheduling_decision': None, 'logical_date': '2025-02-26T19:26:03.577336+00:00', 'note': None, 'run_type': 'manual', 'start_date': None, 'state': 'queued'}\n", "{'conf': {'metadata_id': 4401}, 'dag_id': 'audience_onboarding_oprx_v2', 'dag_run_id': '4401_OPRX_31407719090_DX_A1_DEMO_ZIPS', 'data_interval_end': '2025-02-26T19:26:04.135362+00:00', 'data_interval_start': '2025-02-26T19:26:04.135362+00:00', 'end_date': None, 'execution_date': '2025-02-26T19:26:04.135362+00:00', 'external_trigger': True, 'last_scheduling_decision': None, 'logical_date': '2025-02-26T19:26:04.135362+00:00', 'note': None, 'run_type': 'manual', 'start_date': None, 'state': 'queued'}\n", "{'conf': {'metadata_id': 4403}, 'dag_id': 'audience_onboarding_oprx_v2', 'dag_run_id': '4403_OPRX_31407719090_DX_A3_DEMO_ZIPS', 'data_interval_end': '2025-02-26T19:26:04.482823+00:00', 'data_interval_start': '2025-02-26T19:26:04.482823+00:00', 'end_date': None, 'execution_date': '2025-02-26T19:26:04.482823+00:00', 'external_trigger': True, 'last_scheduling_decision': None, 'logical_date': '2025-02-26T19:26:04.482823+00:00', 'note': None, 'run_type': 'manual', 'start_date': None, 'state': 'queued'}\n"]}], "source": ["import requests\n", "\n", "sess = requests.session()\n", "# change this session token\n", "for i, metadata_row in deals_metadata.iterrows():\n", "    print(sess.post(\n", "        url=\"https://airflow.k8s.eltoro.com/api/v1/dags/audience_onboarding_oprx_v2/dagRuns\",\n", "        headers={\n", "            \"Content-Type\": \"application/json\",\n", "        },\n", "        json={\n", "            \"dag_run_id\": f\"{metadata_row['metadata_id']}_{metadata_row['deal_name']}\",\n", "            \"conf\": {\"metadata_id\": metadata_row['metadata_id']}\n", "        },\n", "        auth=HTTPBasicAuth(airflow_creds[\"user\"], airflow_creds[\"password\"])\n", "\n", "    ).json())\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}