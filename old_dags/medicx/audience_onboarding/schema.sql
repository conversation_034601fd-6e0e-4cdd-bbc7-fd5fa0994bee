CREATE TABLE IF NOT EXISTS s3.bronze_medicx.audience_onboarding_zip9_input(
    zip9 VARCHAR,
    input_etag VARCHAR
)
WITH (
    external_location = 's3://eltoro-data-sources/medicx/audience_onboarding/prod/zip9_processed/',
    format = 'CSV',
    skip_header_line_count=1,
    partitioned_by = ARRAY['input_etag']
);

CREATE TABLE IF NOT EXISTS s3.bronze_medicx.audience_onboarding_npi_input(
    npi VARCHAR,
    input_etag VARCHAR

)
WITH (
    format='CSV',
    external_location='s3://eltoro-data-sources/medicx/audience_onboarding/prod/npi_processed/',
    partitioned_by = ARRAY['input_etag']
);

CREATE TABLE IF NOT EXISTS s3.bronze_medicx.audience_onboarding_address_input(
    ethash VARCHAR,
    zip9 VARCHAR,
    adr1 VARCHAR,
    adr2 VARCHAR,
    zip VARCHAR,
    input_etag VARCHAR
)
WITH (
    format='ORC',
    partitioned_by = ARRAY['input_etag']
);


CREATE TABLE IF NOT EXISTS s3.silver_medicx.audience_onboarding_deviceid_output_gen3(
    deviceid VARCHAR,
    input_etag VARCHAR
)
WITH (
    format='PARQUET',
    external_location='s3://eltoro-data-sources/medicx/audience_onboarding/prod/did_processed/'
    partitioned_by = ARRAY['input_etag']
);

-- DEPRECATED
CREATE TABLE IF NOT EXISTS s3.silver_medicx.audience_onboarding_deviceid_output(
    deviceid VARCHAR,
    input_etag VARCHAR
)
WITH (
    format='ORC',
    partitioned_by = ARRAY['input_etag']
);
