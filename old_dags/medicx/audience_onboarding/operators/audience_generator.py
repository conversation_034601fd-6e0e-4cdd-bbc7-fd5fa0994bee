from old_dags.medicx.audience_onboarding.operators.common import FileType, OutputType
from airflow.decorators import task
from datetime import timedelta
from airflow.utils.trigger_rule import TriggerRule

@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    max_active_tis_per_dag=2,
    retries=3,
    retry_delay=timedelta(seconds=60),
    trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    pip_install_options=["--upgrade"]
)
def generate_audience(
    metadata_id: int,
    input_file_s3_url: str,
    max_row_count_per_part: int,
    file_type: FileType,
    output_type: OutputType,
    medicx_db_id: str = "postgres_medicx",
    aws_conn_id: str = "aws_default",

):
    from airflow.models import Variable
    from airflow.providers.postgres.hooks.postgres import PostgresHook
    from airflow.providers.amazon.aws.hooks.s3 import S3Hook
    from datetime import datetime
    import json
    from old_dags.medicx.audience_onboarding.operators.utils.db_tables import MetaDataLogs, QuoteLogs
    import pandas as pd
    from pygene.target_api import NextGenTargetAPI, TargetFileType
    from pygene.audience_api import NextGenAudienceAPI, AudienceType, Audience, AudienceStatus
    from pytz import timezone
    from sqlalchemy import select, insert, update, and_
    import tempfile
    import uuid
    from urllib.parse import urlparse
    from old_dags.medicx.audience_onboarding.operators.common import FileType, OutputType

    def __get_s3_etag(s3_url, s3_hook):
        # Parse the S3 URL
        parsed_url = urlparse(s3_url)

        # Get the bucket name and key from the parsed URL
        bucket_name = parsed_url.netloc
        key = parsed_url.path.lstrip('/')

        # Get the object's metadata
        obj = s3_hook.get_key(key, bucket_name)

        # Return the ETag
        return obj.e_tag
        
    def __quote_s3_url(
            s3_url: str,
            audience_name: str,
            file_type: FileType,
            output_type: OutputType,
            s3_hook,
            env
        ) -> Audience:
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        s3_hook = S3Hook(aws_conn_id="aws_default")
        with open(temp_file.name, 'wb') as f:
            bucket, key = s3_hook.parse_s3_url(s3_url)
            f.write(
                s3_hook.read_key(
                    key,
                    bucket
                ).encode()
            )
        target_api = NextGenTargetAPI(
            client_id=gene_client_id,
            client_secret=gene_client_secret,
            org_id=gene_org_id,
            env=env
        )
        if file_type == FileType.ZIP9:
            target = target_api.upload_targets(
                local_file=temp_file.name,
                header_columns=[
                    {"index": 0, "value": "zip", "type": "zip"},
                    {"index": 1, "value": "zip4", "type": "zip4"},
                ],
                file_type=TargetFileType.ZIP
            )
            if output_type == OutputType.IP:
                audience = gene_aud_api.create_audience(
                    target_id=target.id,
                    audience_type=AudienceType.IPS_FOR_ZIP_NINE,
                    name=audience_name,
                    poll=False
                )
            elif output_type == OutputType.DID:
                audience = gene_aud_api.create_audience(
                    target_id=target.id,
                    audience_type=AudienceType.DEVICES_FOR_ZIP_NINE,
                    name=audience_name,
                    poll=False
                )
        elif file_type == FileType.NPI:
            target = target_api.upload_targets(
                local_file=temp_file.name,
                header_columns=[
                    {"index": 0, "value": "npi", "type": "npi"},
                ],
                file_type=TargetFileType.NPI
            )
            if output_type == OutputType.IP:
                audience = gene_aud_api.create_audience(
                    target_id=target.id,
                    audience_type=AudienceType.IPS_FOR_NPI,
                    name=audience_name,
                    poll=False
                )
            elif output_type == OutputType.DID:
                audience = gene_aud_api.create_audience(
                    target_id=target.id,
                    audience_type=AudienceType.DEVICES_FOR_NPI,
                    name=audience_name,
                    poll=False
                )
        return audience

    def __fetch_metadata(psql_engine) -> dict:
        metadata_entry = psql_engine.execute(
            select(
                MetaDataLogs,
            ).where(
                MetaDataLogs.metadata_id == metadata_id
            )
        ).fetchone()
        if not metadata_entry:
            Exception(f"no metadata entries found for metadata_id: {metadata_id}")
        return dict(metadata_entry)

    def __last_part_check(psql_engine):
        df = pd.read_sql(
            select(
                QuoteLogs.part_number
            ).where(
                and_(
                    QuoteLogs.metadata_id == metadata_id,
                    QuoteLogs.selected_ips_file_s3_url.isnot(None)
                )
            ).order_by(
                QuoteLogs.part_number.desc()
            ),
            con=psql_engine
        )
        if len(df.index) == df.part_number[0]:
            psql_engine.execute(
                update(
                    MetaDataLogs
                ).values(
                    targets_processed=True
                ).where(
                    MetaDataLogs.metadata_id==metadata_id
                )
            )

    env=Variable.get("environment")
    optimize_rx_creds=json.loads(Variable.get(f"optimizerx_gene_creds_{env}"))
    gene_client_id=optimize_rx_creds["client_id"]
    gene_client_secret=optimize_rx_creds["client_secret"]
    gene_org_id=optimize_rx_creds["org_id"]
    s3_hook = S3Hook(aws_conn_id)
    part_number = int(input_file_s3_url.split("/")[-1].split("-")[0][2:])
    print(f"part number: {part_number}")
    gene_aud_api = NextGenAudienceAPI(
        client_id=gene_client_id,
        client_secret=gene_client_secret,
        org_id=gene_org_id,
        env=env
    )
    psql_engine = PostgresHook(
        medicx_db_id,
        pool_pre_ping=True, 
        keepalives_idle=60
    ).get_sqlalchemy_engine()

    prev_quote_log = pd.read_sql(
        select(
            QuoteLogs
        ).where(
            and_(
                QuoteLogs.part_number==part_number,
                QuoteLogs.metadata_id==metadata_id,
                # QuoteLogs.quote_end_time==None
            )        
        ), con=psql_engine
    )
    if prev_quote_log.empty:
        metadata_entry = __fetch_metadata(psql_engine)
        ref_id = uuid.uuid4().hex
        if file_type == FileType.ZIP9:
            input_df = pd.read_csv(
                input_file_s3_url,
                usecols=["zip", "zip4"],
                dtype={"zip": object, "zip4": object}
            )
        elif file_type == FileType.NPI:
            input_df = pd.read_csv(
                input_file_s3_url,
                usecols=["npi"],
                dtype={"npi": object}
            )
        audience = __quote_s3_url(
            s3_url=input_file_s3_url,
            audience_name=f"{metadata_entry['deal_name']}-Part{part_number}",
            file_type=file_type,
            output_type=output_type,
            s3_hook=s3_hook,
            env=env
        )
        audience_id = audience.id 
        # update the db with the new quote entry current info
        quote_log_id = psql_engine.execute(
            insert(
                QuoteLogs
            ).values(
                quote_ref_id=ref_id,
                target_id=audience_id,
                part_number=part_number,
                address_count=len(input_df.index), #change this to input_count
                max_row_count_per_part=max_row_count_per_part,
                job_type=f"{file_type.value}_to_{output_type.value}", #add this to the db
                quote_start_time=datetime.now(timezone("EST5EDT")).replace(tzinfo=None),
                metadata_id=metadata_id
            ).returning(
                QuoteLogs.quote_log_id
            )
        ).fetchone().quote_log_id
    elif len(prev_quote_log.index)>1:
        raise Exception(f"more than one previous quote entry found for a single part, {part_number}, metadata_id:{metadata_id}")
    else:
        audience_id = prev_quote_log["target_id"][0]
        audience = gene_aud_api.get_audience(audience_id)
        quote_log_id = prev_quote_log["quote_log_id"][0]
    
    if audience.status == AudienceStatus.ERRORED:
        # requote the audience that previously errored
        gene_aud_api._generate_audience(audience.id)

    gene_aud_api._poll_for_audience_status_ready(audience_id, wait_time_secs=30)
    audience = gene_aud_api.get_audience(audience_id)

    selected_s3_url = f"s3://{audience.result.selected_file_location}"
    psql_engine = PostgresHook(
        medicx_db_id,
        pool_pre_ping=True,
        keepalives_idle=60
    ).get_sqlalchemy_engine()
    psql_engine.execute(
        update(
            QuoteLogs
        ).values(
            quote_end_time=datetime.now().astimezone(timezone("EST5EDT")).replace(tzinfo=None),
            selected_ips_file_s3_url=selected_s3_url,
            selected_ips_etag=__get_s3_etag(selected_s3_url, s3_hook),
            ips_matched_count=audience.result.matched,
        ).where(
            QuoteLogs.quote_log_id==int(quote_log_id)
        )
    )
    __last_part_check(psql_engine)
    return int(quote_log_id)

