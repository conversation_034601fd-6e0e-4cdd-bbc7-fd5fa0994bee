from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.providers.postgres.hooks.postgres import PostgresHook
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
from sqlalchemy import *
from airflow.providers.trino.hooks.trino  import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import pandas as pd
from tempfile import NamedTemporaryFile
from airflow.providers.sftp.operators.sftp import SFTPOperator
from concurrent.futures import ThreadPoolExecutor
import threading

class AODIDToSFTPOperator(BaseOperator):
    template_fields = ["metadata_id"]
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        medicx_db_id: str = "postgres_medicx",
        trino_conn_id: str = "trino_conn",
        trino_schema: str = "silver_medicx",
        sftp_conn_id: str = "eltoro_sftp_id",
        trino_device_table_name: str = "audience_onboarding_deviceid_output_gen3",
        aws_conn_id: str ="aws_default",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.medicx_db_id=medicx_db_id
        self.trino_conn_id=trino_conn_id
        self.trino_schema=trino_schema
        self.trino_device_table_name=trino_device_table_name
        self.sftp_conn_id=sftp_conn_id
        self.sftp_device_id_path="/incoming/medicx_device_ids"
        self.aws_conn_id=aws_conn_id
    
    def execute(self, context):
        self.psql_engine = PostgresHook(
            postgres_conn_id=self.medicx_db_id,
            pool_pre_ping=True
        ).get_sqlalchemy_engine()
        metadata_df = self.__fetch_metadata()
        if metadata_df.empty:
            raise ValueError("No selected files to deploy found")
        deal_name = metadata_df["deal_name"].iloc[0]
        def upload_selected_file(s3_url, part_number):
            s3_upload_key = f"s3://eltoro-data-sources/medicx/audience_onboarding/prod/did_processed/input_etag={self.etag}/pt{part_number}.pq"
            if self.s3hook.check_for_key(s3_upload_key):
                return
            print(f"Reading pt{part_number} chunk from: {s3_url}")
            df = pd.read_csv(s3_url, usecols=[0], header=None, dtype=str)
            df.columns = ["deviceid"]
            df = df.drop_duplicates()
            df.to_parquet(s3_upload_key, index=False)
            
        self.etag = metadata_df["input_etag"].iloc[0]
        self.s3hook = S3Hook(self.aws_conn_id)
        s3_urls = list(metadata_df["selected_ips_file_s3_url"])
        part_numbers = list(metadata_df["part_number"])
        with ThreadPoolExecutor(max_workers=10) as executor:
            executor.map(upload_selected_file, s3_urls, part_numbers)

        self.trino_hook = TrinoHook(
            trino_conn_id=self.trino_conn_id,
        )
        self.trino_hook.run(
            f"""CALL s3.system.sync_partition_metadata('{self.trino_schema}', '{self.trino_device_table_name}', 'ADD')"""
        )
        with NamedTemporaryFile(suffix=".csv") as temp:
            df_chunks = self.trino_hook.get_pandas_df_by_chunks(
                f"""
                    SELECT distinct(deviceid) 
                    FROM s3.{self.trino_schema}.{self.trino_device_table_name}
                    WHERE input_etag = '{self.etag}'
                    AND deviceid NOT IN (SELECT deviceid FROM s3.silver_optout.device)
                """,
                chunksize=1_000_000
            )
            for df_chunk in df_chunks:
                df_chunk.to_csv(temp.name, mode="a", header=False, index=False)
            print("Uploading to sftp")
            SFTPOperator(
                task_id="did_to_sftp",
                dag=self.dag,
                ssh_conn_id=self.sftp_conn_id,
                local_filepath=temp.name,
                remote_filepath=f"{self.sftp_device_id_path}/{deal_name}_device_ids_g3.csv",
            ).execute(context)
        # connection drops so it is necessary to start a new engine
        self.psql_engine = PostgresHook(
            postgres_conn_id=self.medicx_db_id,
            pool_pre_ping=True
        ).get_sqlalchemy_engine()

        self.psql_engine.execute(
            update(
                MetaDataLogs
            ).values(
                audience_deployed=True
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            )
        )

    def __fetch_metadata(self) -> pd.DataFrame:
        return pd.read_sql(
            select(
                MetaDataLogs.deal_name,
                QuoteLogs.selected_ips_file_s3_url,
                MetaDataLogs.input_etag,
                QuoteLogs.part_number,

            ).join(
                QuoteLogs
            ).where(
                and_(
                    MetaDataLogs.metadata_id==self.metadata_id,
                    QuoteLogs.selected_ips_file_s3_url.isnot(None)
                )
            ),
            con=self.psql_engine
        )
        