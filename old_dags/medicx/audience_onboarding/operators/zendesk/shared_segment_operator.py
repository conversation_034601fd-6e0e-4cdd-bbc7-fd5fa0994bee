from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from typing import List
from sqlalchemy import *
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import pandas as pd
from airflow.providers.zendesk.hooks.zendesk import ZendeskHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from zenpy.lib.api_objects import Ticket, CustomField, Comment
from zenpy.lib.exception import APIException
from airflow.models import Variable
import time
import tempfile



class AOZendeskSharedSegmentOperator(BaseOperator):
    template_fields = ["metadata_id"]
    
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        medicx_db_id: str="postgres_medicx",
        zendesk_conn_id: str="eltoro_zendesk",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.medicx_db_id=medicx_db_id
        self.zendesk_conn_id=zendesk_conn_id
        self.env=Variable.get("environment")


    def execute(self, context):
        zendesk_cc_ids = {
            "<EMAIL>":389695812012,
            "<EMAIL>":8491651978523,
            "<EMAIL>":117079216951,
            "<EMAIL>":393616340712,
            "<EMAIL>":11237854945179
        }
        self.zendesk_hook = ZendeskHook(self.zendesk_conn_id)
        self.psql_engine = PostgresHook(  
            postgres_conn_id=self.medicx_db_id, 
            pool_pre_ping=True
        ).get_sqlalchemy_engine()
        if self.__sent_zendesk_ticket_before():
            return
        if self.env != "prod":
            zendesk_cc_ids = {}
        segment_results_df = self.__fetch_results()
        with tempfile.NamedTemporaryFile(mode="w+", suffix=".csv") as temp:
            segment_results_df.to_csv(temp.name, index=False)
            attachment_res = self.zendesk_hook.zenpy_client.attachments.upload(
                temp.name, 
                target_name=f"{segment_results_df.deal_name[0]}_results.csv"
            ) 
            ticket_resp = self.zendesk_hook.create_tickets(
                Ticket(
                    subject=f"Medicx Shared Segments {segment_results_df.deal_name[0]}",
                    description=f"Segments have been shared for {segment_results_df.deal_name[0]}\nIncoming segment csv info to follow.",
                    collaborator_ids=list(zendesk_cc_ids.values()),
                    custom_fields=[CustomField(id=22549614, value="audience_request")], #custom ticket type in zendesk
                    tags=["medicx_request", "shared_segments"],
                    comment=Comment(
                        body=f"segment results for deal {segment_results_df.deal_name[0]}", 
                        uploads=[attachment_res.token]
                    )
                )
            )
        self.__close_ticket(ticket_resp.ticket.id)
        self.psql_engine.execute(
            update(
                MetaDataLogs
            ).values(
                zendesk_ticket_id=ticket_resp.ticket.id
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            )
        )

    def __fetch_results(self):
        segment_results_df = pd.read_sql(
            select(
                MetaDataLogs.deal_name,
                MetaDataLogs.campaign,
                MetaDataLogs.metadata_last_modified.label("batch_date"),
                MetaDataLogs.destination,
                MetaDataLogs.media_type,
                DeployLogs.segment_name,
                DeployLogs.segment_id
            ).join(
                MetaDataLogs,
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            ),
            con=self.psql_engine
        )
        return segment_results_df


    def __close_ticket(self, ticket_id: int, retries=3, delay=1):
        ticket = self.zendesk_hook.zenpy_client.tickets(id=ticket_id)
        ticket.status = "closed"
        for _ in range(retries):
            try:
                self.zendesk_hook.zenpy_client.tickets.update(ticket)
                break
            except APIException as e:
                if e.error == "DatabaseConflict":
                    time.sleep(delay)
                else:
                    raise

    def __sent_zendesk_ticket_before(self) -> bool:
        zendesk_ticket_id = self.psql_engine.execute(
            select(
                MetaDataLogs.zendesk_ticket_id
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            )
        ).fetchone()["zendesk_ticket_id"]
        if zendesk_ticket_id:
            return True
        return False
