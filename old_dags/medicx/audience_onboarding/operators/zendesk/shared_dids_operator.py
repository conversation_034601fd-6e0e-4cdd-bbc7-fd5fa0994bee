from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from sqlalchemy import *
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
from airflow.providers.zendesk.hooks.zendesk import ZendeskHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from zenpy.lib.api_objects import Ticket, CustomField
from airflow.models import Variable

class AOZendeskSharedDIDsOperator(BaseOperator):
    template_fields = ["metadata_id"]
    
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        medicx_db_id: str="postgres_medicx",
        zendesk_conn_id: str = "eltoro_zendesk",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.medicx_db_id=medicx_db_id
        self.zendesk_conn_id=zendesk_conn_id
        self.env=Variable.get("environment")


    def execute(self, context):
        self.zendesk_hook = ZendeskHook(self.zendesk_conn_id)
        self.psql_engine = PostgresHook(  
            postgres_conn_id=self.medicx_db_id, 
            pool_pre_ping=True
        ).get_sqlalchemy_engine()

        if self.__sent_zendesk_ticket_before():
            return
        metadata_entry = self.__fetch_metadata()
        if not metadata_entry.deal_name:
            deal_name = metadata_entry.input_filename
        else:
            deal_name = metadata_entry.deal_name

        zendesk_cc_ids = {
            "<EMAIL>":389695812012,
            "<EMAIL>":117079216951,
            "<EMAIL>":1356988086,
            "<EMAIL>":23273777101083,
            "<EMAIL>":11237854945179
        }
        
        if self.env != "prod":
            zendesk_cc_ids = {}
        
        ticket_resp = self.zendesk_hook.create_tickets(
            Ticket(
                subject=f"Medicx Facebook Deal {deal_name}",
                description=f"Device IDs need to be shared for {deal_name}. The credentials are {metadata_entry.seat_id}",
                collaborator_ids=list(zendesk_cc_ids.values()),
                custom_fields=[CustomField(id=22549614, value="audience_request")], #custom ticket type in zendesk
                tags=["medicx_request", "facebook_audience"],
            )
        )
        self.psql_engine.execute(
            update(
                MetaDataLogs
            ).values(
                zendesk_ticket_id=ticket_resp.ticket.id
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            )
        )
    
    def __fetch_metadata(self):
        metadata_entry = self.psql_engine.execute(
            select(
                MetaDataLogs.deal_name,
                MetaDataLogs.seat_id,
                MetaDataLogs.input_filename
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id,
            )
        ).fetchone()
        return metadata_entry

    def __sent_zendesk_ticket_before(self) -> bool:
        zendesk_ticket_id = self.psql_engine.execute(
            select(
                MetaDataLogs.zendesk_ticket_id
            ).where(
                MetaDataLogs.metadata_id == self.metadata_id
            )
        ).fetchone()["zendesk_ticket_id"]
        if zendesk_ticket_id:
            return True
        return False
