from airflow.providers.eltoro.operators.deploytoxandr import DeployToXandrOperator
from airflow.providers.eltoro.sensors.xandrdeploy import XandrDeploySensor
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
from sqlalchemy import *
from airflow.models import Variable
from datetime import datetime
from pytz import timezone
import pandas as pd

class AODeployXandrOperator(BaseOperator):
    template_fields = ["metadata_id", "quote_log_id"]
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        quote_log_id: int,
        medicx_db_id: str = "postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id = metadata_id
        self.quote_log_id = quote_log_id
        self.medicx_db_id = medicx_db_id
        self.env = Variable.get("environment")
        if self.env=="prod":
            self.medicx_advertiser_id = "c43eccab-5130-4273-b84e-a580c6cb2f4d"
        else:
            self.medicx_advertiser_id = "6a08ebba-5247-483f-af74-cd78b1c15477"
        self.buyer_member_id_curate = 14381
        self.member_data_sharing_id_curate = 87916

    def execute(self, context):
        self.psql_engine = PostgresHook(
            self.medicx_db_id,
            pool_pre_ping=True, 
        ).get_sqlalchemy_engine()
        prev_deploy_log = self.__fetch_previous_deploy()
        if prev_deploy_log.empty:
            metadata_entry = self.__fetch_metadata()
            deploy_results = DeployToXandrOperator(
                task_id=f"deploy_to_xandr_pt{metadata_entry['part_number']}",
                dag=self.dag,
                ref_id=metadata_entry["quote_ref_id"],
                selected_file_s3_url=metadata_entry["selected_ips_file_s3_url"],
                segment_name=f"{metadata_entry['deal_name']}-Part{metadata_entry['part_number']}",
                advertiser_id=self.medicx_advertiser_id,
                share_segment=metadata_entry["segment_sharing"],
                member_data_sharing_id=self.member_data_sharing_id_curate,
                buyer_member_id=self.buyer_member_id_curate,
                # add deploy dev url or something but then you gotta also do mongo for the segment code gen to match
            ).execute(context)
            self.psql_engine.execute(
                insert(
                    DeployLogs
                ).values(
                    quote_log_id=self.quote_log_id,
                    metadata_id=self.metadata_id,
                    segment_id=deploy_results["segment_id"],
                    deploy_id=deploy_results["deploy_id"],
                    segment_name=deploy_results["segment_name"],
                    segment_code=deploy_results["segment_code"],
                    segment_shared=metadata_entry.segment_sharing,
                    destination="xandr",
                    deploy_start_time=datetime.now(timezone("EST5EDT")).replace(tzinfo=None)
                )
            )
            segment_id = deploy_results["segment_id"]
            part_number = metadata_entry['part_number']
        elif len(prev_deploy_log.index)>1:
            raise Exception(f"more than one previous deploy entry found for a single quote_log_id, {self.quote_log_id}, metadata_id:{self.metadata_id}")
        else:
            segment_id = prev_deploy_log["segment_id"][0]
            part_number = prev_deploy_log['part_number'][0]

        if Variable.get("environment") == "prod":
            event = XandrDeploySensor(
                task_id=f"waiting_for_deploy_to_finish_pt{part_number}",
                dag=self.dag,
                segment_id=segment_id,
                poke_interval=30,
            ).execute(context)
            psql_engine = PostgresHook(self.medicx_db_id).get_sqlalchemy_engine()
            psql_engine.execute(
                update(
                    DeployLogs
                ).values(
                    deploy_end_time=pd.to_datetime(event["deploy_end_time"], unit="s", utc=True).astimezone(timezone("EST5EDT")).replace(tzinfo=None),
                ).where(
                    DeployLogs.segment_id==event["segment_id"]
                )
            )
            psql_engine.execute(
                update(
                    MetaDataLogs
                ).values(
                    audience_deployed=True
                ).where(
                    MetaDataLogs.metadata_id==self.metadata_id
                )
            )
        else:
            self.psql_engine.execute(
                update(
                    DeployLogs
                ).values(
                    deploy_end_time=datetime.now().astimezone(timezone("EST5EDT")).replace(tzinfo=None),
                ).where(
                    DeployLogs.segment_id==segment_id
                )
            )

    def __fetch_metadata(self):
        return self.psql_engine.execute(
            select(
                MetaDataLogs,
                QuoteLogs
            ).where(
                QuoteLogs.quote_log_id==self.quote_log_id
            ).join(
                QuoteLogs
            )
        ).fetchone()

    def __fetch_previous_deploy(self) -> pd.DataFrame:
        return pd.read_sql(
            select(
                DeployLogs,
                QuoteLogs
            ).where(
                and_(
                    DeployLogs.metadata_id==self.metadata_id,
                    DeployLogs.quote_log_id==self.quote_log_id,
                    DeployLogs.destination=="xandr"
                    # DeployLogs.deploy_end_time==None
                )
            ).join(
                QuoteLogs, 
                QuoteLogs.quote_log_id == DeployLogs.quote_log_id
            ), con=self.psql_engine
        )