from airflow.providers.postgres.hooks.postgres import <PERSON>g<PERSON>Hook
from datetime import datetime
from sqlalchemy import *
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import dateutil.relativedelta
import pandas as pd
from airflow.decorators import task
from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults


class DeployResultsCopyOperator(BaseOperator):
    template_fields = ["metadata_id"]
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        medicx_db_id: str = "postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.medicx_db_id=medicx_db_id
    
    def execute(self, context):
        self.psql_engine = PostgresHook(self.medicx_db_id).get_sqlalchemy_engine()
        metadata_entry = __fetch_metadata()
        __ips_to_xandr_duplicate_etags(metadata_entry)
        return



def __fetch_metadata(self):
    return self.psql_engine.execute(
        select(
            MetaDataLogs.__table__,
            QuoteLogs.__table__
        ).where(
            QuoteLogs.metadata_id==self.metadata_id
        ).join(
            QuoteLogs.__table__
        )
    ).fetchone()


def __ips_to_xandr_duplicate_etags(self, metadata_entry):
    duplicate_etag_entry = pd.read_sql(
        select(
            DeployLogs.__table__,
        ).join(
            QuoteLogs.__table__,
            QuoteLogs.quote_log_id==DeployLogs.quote_log_id
        ).where(
            and_(
                DeployLogs.metadata_id!=metadata_entry.metadata_id,
                QuoteLogs.selected_ips_etag==metadata_entry.selected_ips_etag,
                QuoteLogs.quote_log_id!=metadata_entry.quote_log_id,
                QuoteLogs.selected_ips_etag!="d41d8cd98f00b204e9800998ecf8427e", #etag of an empty file
                DeployLogs.segment_shared==metadata_entry.segment_sharing
            )
        ).order_by(
            DeployLogs.metadata_id.asc()
        ),
        con=self.psql_engine
    )

    print(f"found duplicate etag entry for metadata_id: {metadata_entry.metadata_id}, quote_log: {metadata_entry.quote_log_id}. created log copy")
    duplicate_etag_entry = pd.read_sql(
        select(
            DeployLogs.__table__
        ).join(
            QuoteLogs.__table__,
            QuoteLogs.quote_log_id==DeployLogs.quote_log_id
        ).where(
            and_(
                DeployLogs.metadata_id==int(duplicate_etag_entry["metadata_id"][0]),
                QuoteLogs.selected_ips_etag==metadata_entry.selected_ips_etag
            )
        ),
        con=self.psql_engine
    )
    duplicate_etag_entry["metadata_id"] = metadata_entry.metadata_id
    duplicate_etag_entry["quote_log_id"] = metadata_entry.quote_log_id
    duplicate_etag_entry.drop(columns=["deploy_log_id"], inplace=True)
    duplicate_etag_entry.to_sql(
        name=DeployLogs.__tablename__,
        con=self.psql_engine,
        schema="audience",
        if_exists="append",
        index=False)
    return True