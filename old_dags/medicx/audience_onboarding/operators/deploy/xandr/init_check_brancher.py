from airflow.providers.postgres.hooks.postgres import <PERSON>g<PERSON>Hook
from datetime import datetime
from sqlalchemy import *
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import dateutil.relativedelta
import pandas as pd
from airflow.decorators import task

@task.branch
def xandr_deploy_init_check(
    quote_log_id: int,
    medicx_db_id: str = "postgres_medicx",

):
    psql_engine = PostgresHook(medicx_db_id).get_sqlalchemy_engine()
    metadata_entry = __fetch_metadata(psql_engine, quote_log_id)
    if __ips_to_xandr_shared_audience_before(metadata_entry, psql_engine):
        return None
    if __ips_to_xandr_duplicate_etags(metadata_entry):
        return None
    return "deploy_tg.deploy_to_xandr"

def __fetch_metadata(psql_engine, quote_log_id):
    return psql_engine.execute(
        select(
            MetaDataLogs.__table__,
            QuoteLogs.__table__
        ).where(
            QuoteLogs.quote_log_id==quote_log_id
        ).join(
            QuoteLogs.__table__
        )
    ).fetchone()

def __ips_to_xandr_shared_audience_before(metadata_entry, psql_engine):
    entry_result = psql_engine.execute(
        select(
            DeployLogs.__table__
        ).where(
            DeployLogs.quote_log_id==metadata_entry["quote_log_id"]
        )
    ).fetchone()
    if not entry_result:
        return False
    return True


def __ips_to_xandr_duplicate_etags(metadata_entry, psql_engine):
    duplicate_etag_entry = pd.read_sql(
        select(
            DeployLogs.__table__,
        ).join(
            QuoteLogs.__table__,
            QuoteLogs.quote_log_id==DeployLogs.quote_log_id
        ).where(
            and_(
                DeployLogs.metadata_id!=metadata_entry.metadata_id,
                QuoteLogs.selected_ips_etag==metadata_entry.selected_ips_etag,
                QuoteLogs.quote_log_id!=metadata_entry.quote_log_id,
                QuoteLogs.selected_ips_etag!="d41d8cd98f00b204e9800998ecf8427e", #etag of an empty file
                DeployLogs.deploy_end_time > datetime.now() - dateutil.relativedelta.relativedelta(months=1),
                DeployLogs.segment_shared==metadata_entry.segment_sharing
            )
        ).order_by(
            DeployLogs.metadata_id.asc()
        ),
        con=psql_engine
    )
    if duplicate_etag_entry.empty:
        return False
    print(f"found duplicate etag entry for metadata_id: {metadata_entry.metadata_id}, quote_log: {metadata_entry.quote_log_id}. created log copy")
    duplicate_etag_entry = pd.read_sql(
        select(
            DeployLogs.__table__
        ).join(
            QuoteLogs.__table__,
            QuoteLogs.quote_log_id==DeployLogs.quote_log_id
        ).where(
            and_(
                DeployLogs.metadata_id==int(duplicate_etag_entry["metadata_id"][0]),
                QuoteLogs.selected_ips_etag==metadata_entry.selected_ips_etag
            )
        ),
        con=psql_engine
    )
    duplicate_etag_entry["metadata_id"] = metadata_entry.metadata_id
    duplicate_etag_entry["quote_log_id"] = metadata_entry.quote_log_id
    duplicate_etag_entry.drop(columns=["deploy_log_id"], inplace=True)
    duplicate_etag_entry.to_sql(
        name=DeployLogs.__tablename__,
        con=psql_engine,
        schema="audience",
        if_exists="append",
        index=False)
    return True