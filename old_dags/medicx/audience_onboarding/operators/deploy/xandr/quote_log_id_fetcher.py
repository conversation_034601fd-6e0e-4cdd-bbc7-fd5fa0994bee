from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.providers.postgres.hooks.postgres import PostgresHook
import pandas as pd

class QuoteLogIdFetcher(BaseOperator):
    template_fields=["metadata_id"]
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        medicx_db_id: str = "postgres_medicx",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.medicx_db_id=medicx_db_id
    
    def execute(self, context):
        self.psql_engine = PostgresHook(self.medicx_db_id).get_sqlalchemy_engine()
        df = pd.read_sql(
            f"""
            SELECT quote_log_id
            FROM audience.quote_logs
            WHERE metadata_id = {self.metadata_id}
            """,
            con=self.psql_engine
        )
        return list(df["quote_log_id"])