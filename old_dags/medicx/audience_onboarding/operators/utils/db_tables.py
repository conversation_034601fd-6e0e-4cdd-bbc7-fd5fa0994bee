from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, ForeignKey, String, TIMESTAMP, Integer, Boolean, UniqueConstraint


Base = declarative_base()

class MetaDataLogs(Base):
    __tablename__ = "metadata"
    __table_args__ = (
        UniqueConstraint("metadata_etag", "deal_name", "input_etag", "destination", "seat_id", "media_type"),
        {"schema": "audience"}
    )
    metadata_id = Column(Integer, primary_key=True, autoincrement=True)
    metadata_etag = Column(String)
    metadata_eltoro_s3_url = Column(String)
    metadata_last_modified = Column(TIMESTAMP(timezone=False))
    campaign = Column(String)
    deal_name = Column(String)
    deal_uuid = Column(String)
    deal_line_item_name = Column(String)
    input_type = Column(String)
    input_filename = Column(String)
    input_eltoro_s3_url = Column(String)
    input_etag = Column(String)
    input_content_bytes = Column(Integer)
    input_row_count = Column(Integer)
    input_last_modified = Column(TIMESTAMP(timezone=False))
    destination = Column(String)
    seat_id = Column(String)
    cpm = Column(String)
    margin = Column(String)
    media_type = Column(String)
    targets_processed = Column(Boolean)
    audience_deployed = Column(Boolean)
    ticket_closed = Column(Boolean)
    log_entry_time = Column(TIMESTAMP(timezone=False))
    zendesk_ticket_id = Column(String)
    segment_sharing = Column(Boolean)

class QuoteLogs(Base):
    __tablename__ = "quote_logs"
    __table_args__ = {"schema": "audience"}
    
    quote_log_id = Column(Integer, primary_key=True, autoincrement=True)
    quote_ref_id = Column(String)
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))
    target_id = Column(String)
    selected_ips_file_s3_url = Column(String)
    selected_ips_etag = Column(String)
    address_count = Column(Integer)
    job_type = Column(String)
    ips_matched_count = Column(Integer)
    part_number = Column(Integer)
    max_row_count_per_part = Column(Integer)
    quote_start_time = Column(TIMESTAMP(timezone=False))
    quote_end_time = Column(TIMESTAMP(timezone=False))
    

class DeployLogs(Base):
    __tablename__ = "deploy_logs"
    __table_args__ = {"schema": "audience"}

    deploy_log_id = Column(Integer, primary_key=True, autoincrement=True)
    quote_log_id = Column(String, ForeignKey("audience.quote_logs.quote_log_id"))
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))
    segment_id = Column(String)
    deploy_id = Column(String)
    segment_name = Column(String)
    segment_code = Column(Integer)
    segment_shared= Column(Boolean)
    destination = Column(String)
    deploy_start_time = Column(TIMESTAMP(timezone=False))
    deploy_end_time = Column(TIMESTAMP(timezone=False))



class PolymorphLogs(Base):
    __tablename__ = "polymorph_logs"
    __table_args__ = {"schema": "audience"}

    polymorph_log_id = Column(Integer, primary_key=True, autoincrement=True)
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))
    input_type = Column(String)
    input_count = Column(Integer)
    output_type = Column(String) 
    output_count = Column(Integer)
    output_s3_url = Column(String)
    output_s3_etag = Column(String) #deprecated column, redundant
    polymorph_start_time = Column(TIMESTAMP(timezone=False))
    polymorph_end_time = Column(TIMESTAMP(timezone=False))


class Madhive(Base):
    __tablename__ = "madhive_logs"
    __table_args__ = {"schema": "audience"}

    madhive_log_id = Column(Integer, primary_key=True, autoincrement=True)
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))
    identifier_s3_url = Column(String)
    identifier_audience_count = Column(Integer)
    audience_id = Column(String)
    audience_name = Column(String)
    description = Column(String)
    gcp_upload_time = Column(TIMESTAMP(timezone=False))


class SlackLogs(Base):
    __tablename__ = "slack"
    __table_args__ = {"schema": "audience"}

    ts = Column(String, primary_key=True)
    channel_id = Column(String)
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))


# DEPRECATED only around for reference
class SharedSegments(Base):
    __tablename__ = "shared_segments"
    __table_args__ = (
        UniqueConstraint("segment_id", "metadata_id"),
        {"schema": "audience"}
    )

    segment_share_log_id= Column(Integer, primary_key=True, autoincrement=True)
    segment_id = Column(String)
    deploy_log_id = Column(Integer, ForeignKey("audience.deploy_logs.deploy_log_id"))
    metadata_id = Column(Integer, ForeignKey("audience.metadata.metadata_id"))
    segment_shared_at = Column(TIMESTAMP(timezone=False))
    sharing_enabled = Column(Boolean, server_default="True")