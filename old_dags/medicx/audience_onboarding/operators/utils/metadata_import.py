from datetime import datetime
import uuid
import pandas as pd
from urllib.parse import urlparse
import boto3
from datetime import datetime
from pytz import timezone
from old_dags.operators.utils.db_tables import MetaDataLogs
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import engine

class MetaFileProcessor():
    def __init__(self, psql_engine: engine.mock.MockConnection):
        self.psql_engine = psql_engine

    def __download_meta_file(self, s3_url: str):
        try:
            df = pd.read_csv(s3_url)
            df.dropna(inplace=True, how="all")
        except:
            df = pd.read_excel(s3_url)
            df.dropna(inplace=True, how="all")
        return df

    def __get_file_last_modified(self, file_metadata) -> datetime:
        last_modified = file_metadata["LastModified"]
        last_modified = last_modified.astimezone(timezone("EST5EDT")).replace(tzinfo=None),
        return last_modified

    def __get_file_s3_metadata(self, s3_url: str):
        parsed_s3_url = urlparse(s3_url, allow_fragments=False)
        s3_metadata = boto3.client("s3").head_object(
            Bucket=parsed_s3_url.netloc,
            Key=parsed_s3_url.path.lstrip("/")
        )
        return s3_metadata

    def __count_input_row_count(self, s3_url: str) -> int:
        df = pd.read_csv(s3_url)
        return len(df)

    def upload_meta_to_db(self, metadata_s3_url: str, metadata_input_date: datetime, share_segments: bool = False):
        df = self.__download_meta_file(metadata_s3_url)
        for _, row in df.iterrows():
            try:
                if row["HCP"] == 1:
                    input_type = "hcp"
                else:
                    input_type = "zip9"
            except:
                try:
                    if row["Data Type"] == "Zips" or row["Data Type"] == "ZIPS" or row["Data Type"] == "ZIP":
                        input_type = "zip9"
                    else:
                        input_type = "hcp"
                except:
                    input_type = "zip9"
                
            filepath = row["Zip9 File Name (in S3)"].strip()
            filepath =  "/".join(filepath.split("/")[1:])
            if not filepath[-3:] in ("csv", "txt"):
                filepath = f"{filepath}.csv"
            input_eltoro_s3_url = f"s3://eltoro-data-sources/medicx/sync/{filepath}"
            try:
                deal_name = row["Deal Name"]
            except KeyError:
                try:
                    deal_name = row["Requested Deal Name"]
                except KeyError:
                    deal_name = None
            print(f"extracting metadata for deal: {deal_name}")
            try:
                seat_id = row["Seat ID"]
            except KeyError:
                seat_id = row["Credentials"]
            try:
                cpm = row["CPM"]
            except KeyError:
                cpm = 0
            try:
                margin = row["Margin"]
            except KeyError:
                margin = 0
            
            # Xandr Deal Line Item Code
            deal_uuid = uuid.uuid4().hex
            if row["Destination"].lower() in ["facebook", "madhive"]:
                deal_line_item_name = None
            else:
                if "BANNER" in deal_name or "OTT" in deal_name or "CTV" in deal_name or "OLV" in deal_name or "Banner" in deal_name:
                    deal_line_item_name = deal_name + "_" + deal_uuid
                else:
                    mediatype = str(row["Media Type"])
                    mediatype = mediatype.upper() 
                    if mediatype == "BANNERS" or mediatype == "DISPLAY/BANNERS" or mediatype == "DISPLAY" or mediatype == "DISPLAYS":
                        mediatype = "BANNER"
                    deal_line_item_name = deal_name + "_" + mediatype +"_"+ deal_uuid
            metadata_file_metadata = self.__get_file_s3_metadata(metadata_s3_url)
            try:
                input_file_metadata = self.__get_file_s3_metadata(input_eltoro_s3_url)
                input_row_count = self.__count_input_row_count(input_eltoro_s3_url)
            except:
                print(f"could not download: {input_eltoro_s3_url}")
                continue
            insert_statement = insert(MetaDataLogs.__table__).values(
                metadata_etag=metadata_file_metadata['ETag'][1:-1],
                metadata_eltoro_s3_url=metadata_s3_url,
                metadata_last_modified=metadata_input_date,
                campaign=row["Campaign"],
                deal_name=deal_name,
                deal_uuid = deal_uuid,
                deal_line_item_name = deal_line_item_name,
                input_type=input_type,
                input_filename=row["Zip9 File Name (in S3)"],
                input_eltoro_s3_url=input_eltoro_s3_url,
                input_etag=input_file_metadata['ETag'][1:-1],
                input_content_bytes=input_file_metadata["ContentLength"],
                input_last_modified=self.__get_file_last_modified(input_file_metadata),
                input_row_count=input_row_count,
                destination=row["Destination"],
                seat_id=seat_id,
                cpm=cpm,
                margin=margin,
                media_type=row["Media Type"],
                targets_processed=False,
                audience_deployed=False,
                log_entry_time=datetime.now(timezone("EST5EDT")).replace(tzinfo=None),
                segment_sharing=share_segments
            ).on_conflict_do_nothing()
            self.psql_engine.execute(insert_statement)
        return

def sync_metadata(metadata_s3_url: str, metadata_input_date: datetime, share_segments: bool = False):
    meta_processor = MetaFileProcessor()
    print(f"extracting metadata from {metadata_s3_url}")
    meta_processor.upload_meta_to_db(metadata_s3_url, metadata_input_date, share_segments)