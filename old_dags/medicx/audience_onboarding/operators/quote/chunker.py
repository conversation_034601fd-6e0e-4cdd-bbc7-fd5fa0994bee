from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.providers.postgres.hooks.postgres import PostgresHook
from sqlalchemy import select, update
from airflow.models import Variable
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
import pandas as pd
from old_dags.medicx.audience_onboarding.operators.common import FileType

class AOQuoteChunkOperator(BaseOperator):
    template_fields = ["metadata_id", "chunk_size"]
    @apply_defaults
    def __init__(
        self,
        *,
        metadata_id: int,
        chunk_size: int,
        file_type: str,
        medicx_db_id: str = "postgres_medicx",
        trino_conn_id: str = "trino_conn",
        trino_address_table_name: str = "audience_onboarding_address_input",
        **kwargs
    ):
        super().__init__(**kwargs)
        self.metadata_id=metadata_id
        self.chunk_size=chunk_size
        self.medicx_db_id=medicx_db_id
        self.trino_conn_id=trino_conn_id
        self.trino_address_table_name=trino_address_table_name
        self.env = Variable.get("environment")
        self.destination_dir="s3://eltoro-data-sources/medicx/airflow/chunks_to_quote/"
        self.file_type=file_type
    
    def execute(self, context):
        psql_engine = PostgresHook(
            self.medicx_db_id,
            pool_pre_ping=True,
        ).get_sqlalchemy_engine()
        metadata = self.__fetch_metadata(psql_engine)
        input_eltoro_s3_url = metadata["input_eltoro_s3_url"]
        input_etag = metadata["input_etag"]
        if self.file_type == FileType.NPI:
            possible_column_names = ["NPI", "npi"]
            final_column_name = "npi"
        if self.file_type == FileType.ZIP9:
            possible_column_names = ["Z9", "z9", "zip", "ZIP", "zip9", "ZIP9"]
            final_column_name = "zip9"

        for possible_column_name in possible_column_names:
            try:
                df = pd.read_csv(
                    input_eltoro_s3_url,
                    usecols=[possible_column_name],
                    dtype={possible_column_name: object}
                )
                df = df.rename(columns={possible_column_name: final_column_name})
                if self.file_type == FileType.ZIP9:
                    df['zip9'] = df['zip9'].str.zfill(9)
                    df[['zip', 'zip4']] = df['zip9'].str.extract(r'(\d{5})(\d{4})')
                    df = df.drop(columns=['zip9'])
                break
            except ValueError:
                continue
        i=0
        s3_urls = list()
        df = df.drop_duplicates()
        input_count = df.index
        psql_engine.execute(
            update(MetaDataLogs).where(
            MetaDataLogs.metadata_id == self.metadata_id
            ).values(input_row_count=len(input_count))
        )
        for df in [
            df[i:i + self.chunk_size] for i in range(0, len(df), self.chunk_size)
        ]:
            s3_output_url = f"{self.destination_dir}pt{i+1}-{input_etag}.csv"
            df.to_csv(s3_output_url, index=False)
            s3_urls.append(s3_output_url)
            i+=1
        return s3_urls

    def __fetch_metadata(self, psql_engine) -> str:
        return psql_engine.execute(
            select(
                MetaDataLogs.input_etag, 
                MetaDataLogs.input_eltoro_s3_url
            ).where(
                MetaDataLogs.metadata_id==self.metadata_id
            )
        ).fetchone()