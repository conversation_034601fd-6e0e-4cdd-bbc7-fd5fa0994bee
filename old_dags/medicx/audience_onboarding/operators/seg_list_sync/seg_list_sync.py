from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.providers.postgres.hooks.postgres import PostgresHook
import pandas as pd
from sqlalchemy import select, insert, update, func, and_
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import *
from airflow.providers.amazon.aws.hooks.s3 import S3Hook

class SegmentListSyncOperator(BaseOperator):
    @apply_defaults
    def __init__(
        self,
        *,
        medicx_db_id: str = "postgres_medicx",
        aws_conn_id: str = "medicx_s3",
        **kwargs
    ):
        self.medicx_db_id=medicx_db_id
        self.aws_conn_id=aws_conn_id
        super().__init__(**kwargs)
    
    def execute(self, context):
        self.psql_engine = PostgresHook(
            self.medicx_db_id,
            pool_pre_ping=True
        ).get_sqlalchemy_engine()
        s3_hook = S3Hook(
            aws_conn_id=self.aws_conn_id
        )
        segment_df = pd.read_sql(
            select(
                DeployLogs.metadata_id.label("deal_id"),
                MetaDataLogs.campaign,
                MetaDataLogs.deal_name,
                MetaDataLogs.input_type,
                MetaDataLogs.destination,
                MetaDataLogs.metadata_last_modified.label("entry_time"),
                DeployLogs.segment_name,
                QuoteLogs.part_number,
            ).join(
                MetaDataLogs,
                MetaDataLogs.metadata_id == DeployLogs.metadata_id
            ).join(
                QuoteLogs, 
                QuoteLogs.quote_log_id == DeployLogs.quote_log_id
            ),
            con=self.psql_engine
        )
        madhive_df = pd.read_sql(
            select(
                MetaDataLogs.metadata_id.label("deal_id"),
                MetaDataLogs.campaign,
                MetaDataLogs.deal_name,
                MetaDataLogs.input_type,
                MetaDataLogs.destination,
                MetaDataLogs.metadata_last_modified.label("entry_time"),
                Madhive.audience_id.label("madhive_audience_id"),
            ).join(
                Madhive
            ),
            con=self.psql_engine
        )
        audience_df = pd.concat([segment_df, madhive_df]).sort_values(by=["entry_time", "deal_name", "part_number"], ascending=False)
        audience_df = audience_df.drop(columns=["part_number"])

        s3_hook.load_string(
            string_data=audience_df.to_csv(index=False), 
            key=f"id_resolution/metadata/audience_ids.csv", 
            bucket_name="claimsteam-mdx-eltoro-transfer-files",
            replace=True
        )