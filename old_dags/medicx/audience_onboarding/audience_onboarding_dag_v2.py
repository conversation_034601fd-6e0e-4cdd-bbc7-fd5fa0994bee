from etdag import ETDAG
from airflow.models.param import Param
from airflow.decorators import task, task_group
from airflow.providers.postgres.hooks.postgres import PostgresHook
from sqlalchemy import select
from old_dags.medicx.audience_onboarding.operators.utils.db_tables import MetaDataLogs
from old_dags.medicx.audience_onboarding.operators.quote.chunker import AOQuoteChunkOperator
from old_dags.medicx.audience_onboarding.operators.common import FileType, OutputType
from old_dags.medicx.audience_onboarding.operators.audience_generator import generate_audience
from old_dags.medicx.audience_onboarding.operators.deploy.xandr.ao_deploy import AODeployXandrOperator
from old_dags.medicx.audience_onboarding.operators.facebook.didtosftp import AODIDToSFTPOperator
from old_dags.medicx.audience_onboarding.operators.zendesk.shared_dids_operator import AOZendeskSharedDIDsOperator
from old_dags.medicx.audience_onboarding.operators.zendesk.shared_segment_operator import AOZendeskSharedSegmentOperator
from old_dags.medicx.audience_onboarding.operators.seg_list_sync.seg_list_sync import SegmentListSyncOperator

with ETDAG(
    dag_id="audience_onboarding_oprx_v2",
    schedule_interval=None,
    catchup=False,
    default_args={
        "owner": "panama",
        "retries": 0,
    },
    params={
        "metadata_id": Param(
            default=None,
            type=["integer"],
            description="The metadata id of the deal to run",
        ),
    },
    max_active_runs=1,
    is_paused_upon_creation=True,
    et_failure_msg=False,
) as dag:
    zip9_to_ip_chunk_size = 50_000
    zip9_to_did_chunk_size = 50_000
    npi_to_ip_chunk_size = 500_000
    npi_to_did_chunk_size = 500_000

    @task.branch(task_id="input_destination_branching")
    def input_destination_brancher(metadata_id: int):
        psql_hook = PostgresHook(
            postgres_conn_id="postgres_medicx", 
            pool_pre_ping=True
        )
        engine = psql_hook.get_sqlalchemy_engine()
        metadata_results = engine.execute(
            select(
                MetaDataLogs.input_type, MetaDataLogs.destination
            ).where(
                MetaDataLogs.metadata_id == metadata_id
            )
        ).fetchone()
        input_type: str = metadata_results["input_type"].lower().replace(" ", "")
        destination: str = metadata_results["destination"].lower().replace(" ", "")

        if input_type == "zip9":
            if destination == "facebook":
                return "zip9_to_did"
            elif "xandr" in destination\
            or "invest" in destination\
            or "tradedesk" in destination:
                return "zip9_to_ip"
            else:
                raise ValueError(f"Destination {destination} is not supported for input type {input_type}")
        if input_type == "hcp":
            if destination == "facebook":
                return "npi_to_did"
            elif "xandr" in destination\
            or "invest" in destination\
            or "tradedesk" in destination:
                return "npi_to_ip"
            else:
                raise ValueError(f"Destination {destination} is not supported for input type {input_type}")
    

    @task_group(group_id="zip9_to_ip")
    def zip9_to_ip(metadata_id: int, zip9_to_ip_chunk_size: int) -> None:
        zip9_to_ip_chunker = AOQuoteChunkOperator(
            task_id="zip9_to_ip_chunker",
            metadata_id=metadata_id, 
            chunk_size=zip9_to_ip_chunk_size,
            file_type=FileType.ZIP9,
        )
        @task_group(group_id="zip9_to_ip_generator_and_deploy")
        def zip9_to_ip_generator_and_deploy(metadata_id: int, input_file_s3_url: str, zip9_to_ip_chunk_size: int):
            generate_audience_task = generate_audience(
                metadata_id=metadata_id,
                max_row_count_per_part=zip9_to_ip_chunk_size,
                file_type=FileType.ZIP9,
                output_type=OutputType.IP,
                input_file_s3_url=input_file_s3_url,
            )
            deploy_to_xandr = AODeployXandrOperator(
                task_id="deploy_to_xandr",
                metadata_id=metadata_id,
                quote_log_id=generate_audience_task,
            )
            generate_audience_task >> deploy_to_xandr
        zip9_to_ip_chunker >> zip9_to_ip_generator_and_deploy.partial(
        metadata_id=metadata_id,
        zip9_to_ip_chunk_size=zip9_to_ip_chunk_size
        ).expand(input_file_s3_url=zip9_to_ip_chunker.output)
    
    @task_group(group_id="zip9_to_did")
    def zip9_to_did(metadata_id: int, zip9_to_did_chunk_size: int) -> None:
        zip9_to_did_chunker = AOQuoteChunkOperator(
            task_id="zip9_to_did_chunker",
            metadata_id=metadata_id, 
            chunk_size=zip9_to_did_chunk_size,
            file_type=FileType.ZIP9,
        )
        zip9_to_did_audience_generator = generate_audience.partial(
            metadata_id=metadata_id,
            max_row_count_per_part=zip9_to_did_chunk_size,
            file_type=FileType.ZIP9,
            output_type=OutputType.DID,
        ).expand(input_file_s3_url=zip9_to_did_chunker.output)
        
        zip9_dids_to_sftp = AODIDToSFTPOperator(
            task_id="zip9_dids_to_sftp",
            metadata_id=metadata_id,
        )
        zip9_to_did_chunker >> zip9_to_did_audience_generator >> zip9_dids_to_sftp
    
    @task_group(group_id="npi_to_ip")
    def npi_to_ip(metadata_id: int, npi_to_ip_chunk_size: int) -> None:
        npi_to_ip_chunker = AOQuoteChunkOperator(
            task_id="npi_to_ip_chunker",
            metadata_id=metadata_id, 
            chunk_size=npi_to_ip_chunk_size,
            file_type=FileType.NPI,
        )
        @task_group(group_id="npi_to_ip_generator_and_deploy")
        def npi_to_ip_generator_and_deploy(metadata_id: int, input_file_s3_url: str, npi_to_ip_chunk_size: int):
            generate_audience_task = generate_audience(
                metadata_id=metadata_id,
                max_row_count_per_part=npi_to_ip_chunk_size,
                file_type=FileType.NPI,
                output_type=OutputType.IP,
                input_file_s3_url=input_file_s3_url,
            )
            deploy_to_xandr = AODeployXandrOperator(
                task_id="deploy_to_xandr",
                metadata_id=metadata_id,
                quote_log_id=generate_audience_task,
            )
            generate_audience_task >> deploy_to_xandr
        npi_to_ip_chunker >> npi_to_ip_generator_and_deploy.partial(
        metadata_id=metadata_id,
        npi_to_ip_chunk_size=npi_to_ip_chunk_size
        ).expand(input_file_s3_url=npi_to_ip_chunker.output)
        
    @task_group(group_id="npi_to_did")
    def npi_to_did(metadata_id: int, npi_to_did_chunk_size: int) -> None:
        npi_to_did_chunker = AOQuoteChunkOperator(
            task_id="npi_to_did_chunker",
            metadata_id=metadata_id, 
            chunk_size=npi_to_did_chunk_size,
            file_type=FileType.NPI,
        )
        npi_to_did_audience_generator = generate_audience.partial(
        metadata_id=metadata_id,
                max_row_count_per_part=npi_to_did_chunk_size,
                file_type=FileType.NPI,
                output_type=OutputType.DID,
        ).expand(input_file_s3_url=npi_to_did_chunker.output)
        npi_dids_to_sftp = AODIDToSFTPOperator(
            task_id="npi_dids_to_sftp",
            metadata_id=metadata_id,
        )
        npi_to_did_chunker >> npi_to_did_audience_generator >> npi_dids_to_sftp

    zip9_to_ip_task = zip9_to_ip("{{ params.metadata_id }}", zip9_to_ip_chunk_size)
    npi_to_ip_task = npi_to_ip("{{ params.metadata_id }}", npi_to_ip_chunk_size)
    npi_to_did_task = npi_to_did("{{ params.metadata_id }}", npi_to_did_chunk_size)
    zip9_to_did_task = zip9_to_did("{{ params.metadata_id }}", zip9_to_did_chunk_size)
    did_zendesk_ticket_task = AOZendeskSharedDIDsOperator(task_id="did_send_zendesk_ticket", metadata_id="{{ params.metadata_id }}", trigger_rule="none_failed_min_one_success")
    ip_zendesk_ticket_task = AOZendeskSharedSegmentOperator(task_id="ip_send_zendesk_ticket", metadata_id="{{ params.metadata_id }}", trigger_rule="none_failed_min_one_success")
    segment_list_sync_instance = SegmentListSyncOperator(task_id="sync_segment_lists")
    (
        input_destination_brancher("{{ params.metadata_id }}")
        >> [
            zip9_to_ip_task,
            npi_to_ip_task,
            zip9_to_did_task,
            npi_to_did_task,
        ]
    )
    zip9_to_did_task >> did_zendesk_ticket_task
    npi_to_did_task >> did_zendesk_ticket_task
    zip9_to_ip_task >> ip_zendesk_ticket_task >> segment_list_sync_instance
    npi_to_ip_task >> ip_zendesk_ticket_task >> segment_list_sync_instance