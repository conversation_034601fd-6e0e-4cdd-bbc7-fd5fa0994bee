from airflow.decorators import task
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
default_args = {
    "owner": "<PERSON>",
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 3,
}
with ETDAG(
    dag_id="check_starburst_udfs",
    description="Insure udfs are available to use in queries.",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 * * * *",
    catchup=False,
    tags=["starburst"],
) as dag:

    functions = ["ethash_v1", "ethash_v2"]
    func = """
    Calculates a V1 EtHash - ethash_v1
    (streetNumber, streetName, zipcode, zip4, suffix, preDir, postDir, aptNum)
    """
    address = """
    552 E. Market Street
    4th Floor
    Louisville, KY 40202
    """
    params = ["'522 E. Market Street, 40202'", "'522', 'Market Street 4th Floor', '40202', '', '', 'E', '', ''"  ]

    geocoder_query = """
    SELECT geocodeAddress('6609 NE 71st Ave, Vancouver, WA',ARRAY['etHash','geometryWKB'])
    """

    queries = []
    for p in params:
        funcs = []
        for f in functions:
            f_query = f"{f}({p})"
            funcs.append(f_query)
        funcs = ", ".join(funcs)
        query = f"SELECT {funcs}"
        queries.append(query)


    ethash_parts = SQLExecuteQueryOperator(
        task_id="ethash_parts",
        conn_id="starburst",
        sql=queries[0],
        handler=list,
    )

    ethash_atlas = SQLExecuteQueryOperator(
        task_id="ethash_atlas",
        conn_id="starburst",
        sql=queries[1],
        handler=list,
    )
    geocode_atlas = SQLExecuteQueryOperator(
        task_id="geocode_atlas",
        conn_id="starburst",
        sql=geocoder_query,
        handler=list,
    )
    ethash_parts >> ethash_atlas >> geocode_atlas
