from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from airflow.operators.bash import <PERSON><PERSON><PERSON><PERSON><PERSON>
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.models.param import Param
from etdag import ETDAG
import pandas as pd
import time
import boto3
import json
from typing import TypedDict
from copy import deepcopy


class S3Loc(TypedDict):
    s3_bucket: str  # vr-timestamp
    s3_key: str  # bi_sources/auto_tables/auto_bucklocs.csv


class Job(TypedDict):
    reportName: str  # "auto_bucklocs",
    enable: bool  # true,
    LargeDataSet: str  # false,
    schedule_type: str  # "cron",
    schedule_value: str  # "0 1 * * *",
    trino_query: str  # "SELECT * FROM s3.gold_auto_intender.locations_new_and_used",
    compress_to_gzip: bool  # false,
    s3_locations: list[S3Loc]


# Define default_args for the main DAG
default_args = {
    "owner": "<PERSON>ang",
    "retries": 0,
}

docs = ""


@task
def read_jobs_from_s3(bucket_name: str, file_key):
    s3 = boto3.client("s3")
    response = s3.get_object(Bucket=bucket_name, Key=file_key)
    return list(json.loads(response["Body"].read().decode("utf-8")).values())


def fetch_and_write_to_s3(job_config: Job):
    print(job_config["trino_query"])
    df_chunks = TrinoHook(trino_conn_id="trino_conn").get_pandas_df_by_chunks(
        sql=job_config["trino_query"], chunksize=100_000
    )
    for i, df_chunk in enumerate(df_chunks):
        process_and_upload_data(df_chunk, job_config, i + 1)


def process_and_upload_data(df: pd.DataFrame, job_config: Job, chunk_number: int):
    # Convert processed data to a Pandas DataFrame
    print("Saving Dataframe in s3------")
    # default file saving settings
    mode = "w"
    header = True
    s3_locs = deepcopy(job_config["s3_locations"])
    if job_config["LargeDataSet"]:  # split the result file into parts
        # add the part number to the file name
        s3_locs = [
            {
                "s3_bucket": entry["s3_bucket"],
                "s3_key": f"{entry['s3_key'].split('.')[0]}_{chunk_number}.{entry['s3_key'].split('.')[1]}",
            }
            for entry in s3_locs
        ]
    else:  # if the file doesn't need to be split into parts then the chunks have to be appended
        if chunk_number > 1:
            mode = "a"
            header = False
    if job_config["compress_to_gzip"]:
        compression = "gzip"
        # add the gzip suffix
        s3_locs = [
            {"s3_bucket": entry["s3_bucket"], "s3_key": entry["s3_key"] + ".gz"}
            for entry in job_config["s3_locations"]
        ]
    else:
        compression = "infer"  # default compression
    for location in s3_locs:
        s3_url = f"s3://{location['s3_bucket']}/{location['s3_key']}"
        print(f"chunk: {chunk_number}, uploading file to: {s3_url}")
        df.to_csv(
            s3_url,
            index=False,
            encoding="utf-8",
            compression=compression,
            mode=mode,
            header=header,
        )
    del df  # Empty Dataframe


@task(max_active_tis_per_dag=5)
def main_process_task(job: Job) -> None:
    # for job_name, job_config in schedule_config.items():
    starting_time = time.process_time_ns()
    print(f"Starting at {starting_time}")
    # print(job_name)
    print(job["reportName"])
    if job["enable"]:
        fetch_and_write_to_s3(job)
    ending_time = time.process_time_ns()
    print(f"End Time: {ending_time}")
    print(
        f"Elapsed Time from starting to end in Seconds: {(ending_time - starting_time) / 1_000_000_000}"
    )
    print("Next Process-----------------")


with ETDAG(
    dag_id="bi_report_b2c_matchrate",
    default_args=default_args,
    description="Match Rate report",
    schedule_interval="00 11 * * *",
    catchup=False,
    params={
        "jobs_s3_config_bucket": Param(default="vr-timestamp", type="string"),
        "jobs_s3_config_key": Param(
            default="bi_sources/airflow_config_sb_s3_sync/b2c_matchrate_by_state/b2c_matchrate_by_state_config.json",
            type="string",
        ),
    },
    # concurrency=1,
    start_date=days_ago(2),
    tags=["starburst", "bi-report", "match_rate"],
) as dag:
    dag.doc_md = docs

    jobs_instance = read_jobs_from_s3(
        bucket_name="{{ params.jobs_s3_config_bucket }}",
        file_key="{{ params.jobs_s3_config_key }}",
    )

    main_process_task.expand(job=jobs_instance)
