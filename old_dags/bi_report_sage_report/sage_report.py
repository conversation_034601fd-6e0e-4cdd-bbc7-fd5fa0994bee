from airflow.decorators import task
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.utils.dates import days_ago
from airflow.models.param import Param
from etdag import ETDAG
import pandas as pd
import time
import boto3
import tempfile
import json
from typing import TypedDict

class S3Loc(TypedDict):
    s3_bucket: str  # vr-timestamp
    s3_key: str  # bi_sources/auto_tables/auto_bucklocs.csv


class Job(TypedDict):
    reportName: str  # "auto_bucklocs",
    enable: bool  # true,
    LargeDataSet: str  # false,
    schedule_type: str  # "cron",
    schedule_value: str  # "0 1 * * *",
    trino_query: str  # "SELECT * FROM s3.gold_auto_intender.locations_new_and_used",
    compress_to_gzip: bool  # false,
    s3_locations: list[S3Loc]


# Define default_args for the main DAG
default_args = {
    "owner": "<PERSON>",
    "retries": 0,
}

docs = ""


@task
def read_jobs_from_s3(bucket_name: str, file_key):
    s3 = boto3.client("s3")
    response = s3.get_object(Bucket=bucket_name, Key=file_key)
    return list(json.loads(response["Body"].read().decode("utf-8")).values())


def fetch_and_write_to_s3(job_config: Job):
    print(job_config["trino_query"])
    s3_key = job_config["s3_locations"][0]["s3_key"]
    bucket_name = job_config["s3_locations"][0]["s3_bucket"]
    df_chunks = TrinoHook(trino_conn_id="trino_conn").get_pandas_df_by_chunks(
        sql=job_config["trino_query"], chunksize=1_000_000
    )

    with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
            temp_file_path = temp_file.name
    for i, chunk in enumerate(df_chunks):
        print(f"writing chunk {i}")
        if i == 0:
            chunk.to_csv(temp_file_path, index=False)
        else:
            chunk.to_csv(temp_file_path, mode="a", header=False, index=False)
    
    s3_hook = S3Hook(aws_conn_id="s3_conn")
    s3_hook.load_file(
                filename=temp_file_path,
                key=s3_key,
                bucket_name=bucket_name,
                replace=True,
            )
    del df_chunks  # Empty Dataframe


@task(max_active_tis_per_dag=5)
def main_process_task(job: Job) -> None:
    # for job_name, job_config in schedule_config.items():
    starting_time = time.process_time_ns()
    print(f"Starting at {starting_time}")
    # print(job_name)
    print(job["reportName"])
    if job["enable"]:
        fetch_and_write_to_s3(job)
    ending_time = time.process_time_ns()
    print(f"End Time: {ending_time}")
    print(
        f"Elapsed Time from starting to end in Seconds: {(ending_time - starting_time) / 1_000_000_000}"
    )
    print("Next Process-----------------")



with ETDAG(
    dag_id="sage_bi_report",
    default_args=default_args,
    description="Dynamically Generate sage for BI team",
    schedule_interval="0 10 * * *",
    catchup=False,
    params={
        "jobs_s3_config_bucket": Param(default="vr-timestamp", type="string"),
        "jobs_s3_config_key": Param(
            default="bi_sources/airflow_config_sb_s3_sync/sage_config/sage_accounting_config.json",
            type="string",
        ),
    },
    # concurrency=1,
    start_date=days_ago(2),
    tags=["starburst", "bi-report", "auto intent"],
) as dag:
    dag.doc_md = docs

    jobs_instance = read_jobs_from_s3(
        bucket_name="{{ params.jobs_s3_config_bucket }}",
        file_key="{{ params.jobs_s3_config_key }}",
    )

    main_process_task.expand(job=jobs_instance)
