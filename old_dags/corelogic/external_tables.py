"""CopyFilesPartitioned finds newly transferred dataset files and copies them to an `external` location using a partitioned folder scheme.

The expected pattern of source files is currently based on Corelogic deliveries;
* The data files are either zip `.zip` or csv gzipped `.csv.gzip`.
*
"""

import pandas as pd
import boto3
from zipfile import ZipFile
import os
import tempfile
import re
import datetime as dt
from trino.dbapi import connect
from trino.auth import JWTAuthentication
import duckdb
import botocore
import re
import contextlib
from pathlib import Path

# from datetime import datetime, date
# import os


class S3:
    def __init__(self, env="local", s3_client: botocore.client = None):
        if s3_client is not None:
            self.s3 = s3_client
        else:
            if env == "local":
                endpoint_url = os.environ.get("AWS_ENDPOINT_URL_S3")
                if endpoint_url is None:
                    endpoint_url = "http://localhost:9000"
                self.s3 = boto3.client(
                    "s3",
                    endpoint_url=endpoint_url,
                    aws_access_key_id="admin",
                    aws_secret_access_key="password",
                    verify=False,
                )
            if env == "prod":
                self.s3 = boto3.client("s3")

    def get_files(self, bucket: str, prefix: str):
        paginator = self.s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
        file_paths = []
        for page in pages:
            paths = page.get("Contents", {})
            file_paths += paths
        return file_paths

    def download_file(self, bucket: str, key: str, download_path: str):
        """Download source file from s3 to local directory.

        Args:
            key (str): s3 key to object.
            download_folder (str): Absolute folder path to download file.
        """
        print(f"Downloading file - {bucket}/{key}")

        # filename = key.split("/")[-1]
        # print(f"Downloaded filename -- {filename}")
        # download_path = f"{download_path}/{filename}"
        self.s3.download_file(bucket, key, download_path)

        print("Download succeeded!")

    def upload_file(self, bucket, key, source_path):
        print(f"Uploading file - {bucket}/{key}")
        self.s3.upload_file(source_path, bucket, key)
        print("Upload succeeded!")

    def copy_file(self, source_bucket, source_key, dest_bucket, dest_key):
        s3 = self.s3
        s3.copy_object(
            CopySource={"Bucket": source_bucket, "Key": source_key},
            Bucket=dest_bucket,
            Key=dest_key,
        )

    def unzip(self, file_path: str):
        print(f"Unzipping file - {file_path}")
        download_dir = "/".join(file_path.split("/")[:-1])
        with ZipFile(file_path, "r") as zip_ref:
            # Extract all the contents to the same directory
            zip_ref.extractall(download_dir)
        # Remove zip file
        os.remove(file_path)
        print("Unzip succeeded!")

    def upload_dir(self, bucket: str, prefix: str, download_dir: str):
        for root, _dirs, files in os.walk(download_dir):
            for file in files:
                if file.endswith(".zip"):
                    continue
                source_path = os.path.join(root, file)
                dest_key = f"{prefix}/{file}"
                self.upload_file(bucket, dest_key, source_path)

                # # Remove file from temp directory.
                os.remove(source_path)


class Trino:
    def __init__(
        self,
        env: str = "local",
        token: str = None,
        conn: dict[str] = None,
    ):
        self.env = env
        if conn is None:
            if env == "local":
                self.trino_conn = self._local_trino_conn()
            if env == "prod":
                self.trino_conn = self._prod_trino_conn(token)
        else:
            self.trino = self._trino_connect(conn)

    def _trino_connect(self, trino_conn: dict[str]):
        """Establish a trino connection using config variables.

        Example:
            trino_conn={
                "conn_type": "trino",
                "login": "admin",
                "host": "localhost",
                "port": 8000,
                "schema": "corelogic"
            }
        """
        conn = {}
        conn["user"] = trino_conn["login"]
        conn["host"] = trino_conn["host"]
        conn["port"] = trino_conn["port"]
        self.trino_conn = connect(**conn)

    def _local_trino_conn(self):
        return connect(
            user="admin",
            host="localhost",
            port=8000,
            http_scheme="http",
            catalog="iceberg",
            schema="corelogic_local",
        )

    def _prod_trino_conn(self, token):
        return connect(
            auth=JWTAuthentication(token),
            host="starburst.k8s.eltoro.com",
            port=443,
            http_scheme="https",
        )

    def query(self, query: str) -> list[list[object]]:
        # Quell noisey logging.
        with open(os.devnull, "w") as devnull:
            with contextlib.redirect_stdout(devnull):
                cur = self.trino_conn.cursor()
                cur.execute(query)

                return cur

    def create_table(
        self,
        table_name: str,
        schema: str,
        bucket: str,
        s3_prefix: str,
        col_defs: list[str],
        partition_cols: list[str],
    ):
        table = f"s3.{schema}.{table_name}"
        create_schema_query = f"""
        CREATE SCHEMA IF NOT EXISTS s3.{schema}
        AUTHORIZATION USER admin
        WITH (
            location = 's3a://{bucket}/'
        )"""
        partition = ""
        if len(partition_cols) > 1:
            partition = f"ARRAY{str(partition_cols)}"
        elif len(partition_cols) == 1:
            partition = f"'{partition_cols[0]}'"
        else:
            raise ValueError(
                f"Must provide a list of partition columns: {partition_cols}"
            )

        columns = "\n             ".join(col_defs)
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table} (
            {columns}
        )
        WITH (
            external_location = 's3a://{bucket}/{s3_prefix}',
            format = 'PARQUET',
            partitioned_by = {partition}
        )
        """
        if self.env == "local":
            print(create_schema_query)
            self.query(create_schema_query)
        print(create_table_query)
        self.query(create_table_query)


class DuckDb:
    def get_duckdb_conn(self):
        sess = boto3.Session()
        creds = sess.get_credentials()
        access_key = creds.access_key
        secret_key = creds.secret_key
        token = creds.token
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs")
        conn.execute("LOAD httpfs")
        conn.execute("SET s3_region='us-east-1'")
        conn.execute(f"SET s3_access_key_id='{access_key}'")
        conn.execute(f"SET s3_secret_access_key='{secret_key}'")
        if token is not None:
            conn.execute(f"SET s3_session_token='{token}'")

        return conn

    def get_schema(self, file_url):
        conn = self.get_duckdb_conn()
        file_name = file_url.split("/")[-1]

        # Create table with first row to get types.
        create_query = ""
        if file_name.endswith(".parquet"):
            create_query = f"""
            create table temporary as select *
            from read_parquet('{file_url}')
            limit 1
            """
        elif file_name.endswith(".txt.gz"):
            create_query = f"""
            create table temporary as select *
            from read_csv('{file_url}')
            limit 1
            """
        else:
            raise ValueError(
                "Can't parse file extension for file url: \n {file_url}"
            )

        conn.execute(create_query)

        describe_query = "describe temporary"
        res = conn.execute(describe_query)
        schema = res.fetchall()
        # Make way for the next one.
        drop_query = "drop table temporary"
        conn.execute(drop_query)

        return schema


class CorelogicSourceFiles:
    """Receive a list of filenames and"""

    def __init__(self, file_path: str):
        """Parse the filename for the relevant attributes.

        Args:
        filename: str = Name of the file to parse.

        Example:
        'el_toro_building_detail1_01436778_20230511_031500_delta_data.txt.gz'
        """
        filename = file_path.split("/")[-1]
        source_pattern = r"el_toro_(?P<dataset_name>[\w\_]+)_\d+_(?P<date_string>\d{8})_\d+_(?P<file_transfer_type>\w+)_(?P<file_type>\w+)\.(?P<extension>\w+)"
        match = re.match(source_pattern, filename)

        if match:
            self.dataset_name = match.group(
                "dataset_name"
            )  # 'building_detail1'
            self.date_string = match.group("date_string")  # '20230511'
            self.file_transfer_type = match.group(
                "file_transfer_type"
            )  # 'delta'
            self.file_type = match.group("file_type")  # 'data'
            self.extension = match.group("extension")  # 'gz'


class ExternalFiles:
    """The model for parsing the file path"""

    def __init__(self, file_path: str):
        """Parse the filename for the relevant attributes.

        The pattern of data attributes found in ;
            `<prefix>/<dataset>/<schema_version>/<year>/<month>/<day>/<file_name>`
        Args:
        filename: str = Name of the file to parse.

        Example:
            'external/customextract_v1_dpc/schema_version_20230512/\
            year=2023/month=05/day=12/\
            El_Toro_customextract_v1_dpc_01447977_20230512_210139_data_000000000000.parquet'
        'el_toro_building_detail1_01436778_20230511_031500_delta_data.txt.gz'
        """
        destination_pattern = r"(?P<prefix>[^/]+)/(?P<dataset>[\w\_]+)/schema_version_(?P<schema_version>\d{8})/year=(?P<year>\d{4})/month=(?P<month>\d{2})/day=(?P<day>\d{2})/(?P<file_name>[\w\.\-\_]+)"
        match = re.match(destination_pattern, file_path)

        self.prefix = match.group("prefix")  # 'external'
        self.dataset = match.group("dataset")  # 'customextract_v1_dpc'
        self.schema_version = match.group(
            "schema_version"
        )  # 'schema_version_20230512'
        self.year = match.group("year")  # '2023'
        self.month = match.group("month")  # '05'
        self.day = match.group("day")  # '12'
        self.file_name = match.group("file_name")


class ExternalTables:
    # [TODO] move data(buckets, datasets, etc.) to calling function/dag.
    # [TODO] abstract file parsing and pattern for source and destination files.
    source_bucket = "eltoro-data-sources"
    source_prefix = "corelogic"
    dest_bucket = "et-data-staging"
    # dest_bucket = "et-data-dev"
    dest_prefix = "external"
    corelogic_datasets = [
        "building_detail1",
        "buildingpermit_3",
        "digital_audiences",
        "mls_listings_v1_dpc",
        "customextract_v1_dpc",
        "mortgage_basic3",
        "ownertransfer_v3",
        "propensityscore1_dpc",
        "property_basic2",
        "solarcon1_dpc",
        "thvxcpf1_dpc",
        "trigger_events",
        "vol_lien_status_m2_dpc",
    ]

    def __init__(
        self,
        env="local",
        s3_client: botocore.client = None,
        trino_token: str = None,
        trino_conn: dict[str] = None,
    ):
        """
        Args:
            s3_client (botocore.client, optional): The client used to access S3
                file storage.
            trino_conn (:obj:`dict` of :obj:`str`, optional): The connection
                variables for a trino connection.
            env (str, optional): The environment in which the module is being
                run (local|prod).
        """
        self.source_files: pd.DataFrame = None
        self.dest_files: pd.DataFrame = None
        self.unprocessed_files: pd.DataFrame = None
        self.temp_dir = tempfile.TemporaryDirectory()
        # self.temp_dir = "./data"
        self.last_schema = None
        self.last_version = None

        self.s3 = S3(env=env, s3_client=s3_client)
        self.trino = Trino(env=env, token=trino_token, conn=trino_conn)
        self.duckdb = DuckDb()

    def set_source_files(self):
        """Get the S3 file paths for corelogic datasets and create a dataframe.

        Parse the relevant transfer information for datasets in the `corelogic_datasets` list.

        An example filename for a source file is;
            'el_toro_building_detail1_01436778_20230511_031500_delta_data.txt.gz'
        Which has the following interpretation;
            'el_toro_<dataset name>_<unused: numeric value>_<date string>_<unused: numeric value>_<file transfer type>_<file type>.<extension>
        Note: the `dataset name` contains an arbitrary number of underscore characters between the word parts.
        """
        bucket = self.source_bucket
        prefix = self.source_prefix
        paths = self.s3.get_files(bucket, prefix)
        files = {
            "dataset": [],
            "transfer_type": [],
            "transfer_date": [],
            "filename": [],
            "key": [],
            "file_size": [],
        }
        for fp in paths:
            path = fp["Key"].split("/")
            # if len(path) == 2:
            filename = path[-1]
            name_parts = filename.split(".")[0].split("_")

            match = False
            for d in self.corelogic_datasets:
                if d in filename:
                    match = True
                    files["dataset"].append(d)
            if match is False:
                files["dataset"].append(None)

            transfer_type = None
            for t in ["init", "full", "delta"]:
                if t in fp:
                    transfer_type = t
            if transfer_type is not None:
                files["transfer_type"].append(t)
            else:
                files["transfer_type"].append("full")

            transfer_date = self.get_transfer_date(filename)

            files["transfer_date"].append(transfer_date)
            files["filename"].append(filename)
            files["key"].append(fp["Key"])
            files["file_size"].append(fp["Size"])

        df = pd.DataFrame(files)
        df = df[~df.dataset.isna()]

        self.source_files = df

    def get_transfer_date(self, filename: str) -> str | None:
        """Find the first of the 8-digit date string in the filename using a regular
        expression."""
        matches = re.findall(r"(?<=_)\d{8}(?=_)", filename)

        date = None
        # Validate each date string and print it out as a datetime object
        for match in matches:
            try:
                dt.datetime.strptime(match, "%Y%m%d")
                date = match
                break
            except ValueError:
                pass

        return date

    def filter_source_files(self):
        """Filter out non-data files."""
        df = self.source_files
        for d in self.corelogic_datasets:
            # `customextract_v1_dpc` files are zipped.
            if d == "customextract_v1_dpc":
                data_only = (~df.filename.str.endswith("_data.zip")) | (
                    df.filename.str.contains("_counts_")
                )
                df = df[~((df.dataset == d) & data_only)]
            else:
                data_only = ~df.filename.str.endswith(".txt.gz")
                df = df[~((df.dataset == d) & data_only)]

        self.source_files = df

    def set_dest_files(self):
        """The constructed S3 filepath is like;
            `<prefix>/<dataset>/<schema_version>/<year>/<month>/<day>/<file_name>`
        To allow for a arbitrary number of paths in the prefix all indexing to the
        file path values should be indexed from the end.
            `dataset`: -6
            `schema_version`: -5
            `year`: -4
            `month`: -3
            `day`: -2
            `file_name`: -1
        """
        bucket = self.dest_bucket
        prefix = self.dest_prefix
        paths = self.s3.get_files(bucket, prefix)
        print("number of destination files", len(paths))
        files = {
            "dataset": [],
            "transfer_date": [],
            "filename": [],
            "key": [],
            "file_size": [],
        }
        for fp in paths:
            path = fp["Key"].split("/")
            if len(path) < 6:
                continue
            dataset = path[-6]
            year = path[-4].split("=")[-1]
            month = path[-3].split("=")[-1]
            day = path[-2].split("=")[-1]
            transfer_date = f"{year}{month}{day}"
            # Test the `transfer_date` string against the expected date format.
            self._check_date_string(transfer_date)
            filename = path[-1]
            files["dataset"].append(dataset)
            files["transfer_date"].append(transfer_date)
            files["filename"].append(filename)
            files["key"].append(fp["Key"])
            files["file_size"].append(fp["Size"])

        self.dest_files = pd.DataFrame(files)

    def _check_date_string(self, date: str):
        """Raise exception if date string doesn't match expected date format."""
        try:
            dt.datetime.strptime(date, "%Y%m%d")
        except ValueError:
            raise ValueError(f"Date string could not be parsed: {date}")

    def set_unprocessed_files(self):
        """Derive a list of all the unprocessed files from the
        source files.

        Perform a left join with source files on destination files. The join key
        will be determined by the file type.
            `.csv.gzip` files will use the `transfer_date` and `dataset` values.
            `.zip` files will use the `filename` value.
        """
        self.set_source_files()
        self.filter_source_files()
        self.set_dest_files()
        sf = self.source_files
        df = self.dest_files
        # Dedupe source files.
        # Remove `transfer_type == "init"`
        result = pd.merge(
            sf,
            df,
            on=["transfer_date", "dataset"],
            how="left",
            suffixes=("_source", "_dest"),
        )
        # Filtering out destination keys that have a filepath string.
        result = result[result["key_dest"].isna()]
        result = result.sort_values(by=["transfer_date"])
        result = result.reset_index()
        result = self.clean_merged_columns(result)

        self.unprocessed_files = result

    def clean_merged_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """The merge combines all the columns and labels the columns by appending
        the name of the source table; `_source` and `_dest` in this case.

        We are only interested in the files that need to be transfered so we will
        use the data from the source columns.
        """
        columns = df.columns
        # Remove columns with suffix "_dest".
        columns = [c for c in columns if not c.endswith("_dest")]
        df = pd.DataFrame(df, columns=columns)
        source_columns = [c for c in columns if c.endswith("_source")]
        # Remove suffix "_source".
        columns_clean = [c.replace("_source", "") for c in source_columns]
        new_columns = {}
        for i, c in enumerate(source_columns):
            new_columns[c] = columns_clean[i]
        df = df.rename(columns=new_columns)

        return df

    def stage_dataset(self, dataset, limit=None):
        """Iterate through each source file that has not been copied
        to the destination path.

        Steps to copy data files to the destination location:
                # Download zip file.
                # Unzip zip file to temporary directory.
                # Scan first file.
                # Get schema from any of the unzipped files.
                # Compare `last_schema` with `current_schema`.
                # Set `last_schema` and `last_version`
                # Calculate file prefix.
                # Upload to S3.
        """
        df = self.unprocessed_files
        # Select only files matching `dataset`.
        df = df[df.dataset == dataset]
        df = df.reset_index()
        source_bucket = self.source_bucket
        dest_bucket = self.dest_bucket
        temp_dir = self.temp_dir.name

        for i, row in df.iterrows():
            if i == limit:
                break
            if row.filename.endswith(".zip"):
                # dest_prefix = self.get_dest_prefix(row)
                source_key = row.key

                download_path = f"{temp_dir}/{row.filename}"
                self.s3.download_file(source_bucket, source_key, download_path)

                zipfile = f"{temp_dir}/{row.filename}"
                self.s3.unzip(zipfile)

                _version = self.get_row_version(row)

                dest_prefix = self.get_dest_prefix(row)
                self.s3.upload_dir(self.dest_bucket, dest_prefix, temp_dir)

    def get_dest_prefix(self, file_row: pd.Series):
        # s3://bucket/external/dataset/schema_version_20231201/year=2023/month=12/day=1/{csv/gz files}
        dataset = file_row.dataset
        date = dt.datetime.strptime(file_row.transfer_date, "%Y%m%d")
        year = str(date.year)
        month = str(date.month).rjust(2, "0")
        day = str(date.day).rjust(2, "0")
        prefix = f"external/{dataset}/{self.last_version}/year={year}/month={month}/day={day}"

        return prefix

    def get_row_version(self, row: pd.Series) -> str:
        """Find the schema version for the current file.

        Set the `last_version` and last_versionlast_schema` which will
        be used to determine if the schema version needs to update.
        """
        version = self.last_version
        schema = self.last_schema
        temp_dir = self.temp_dir.name
        dataset = row.dataset
        
        # Get the first file in the temp directory.
        supported_exts = [".parquet", ".txt.gz"]
        sample_file_path = ""

        # Recursively look for the first supported file type
        for p in Path(temp_dir).rglob("*"):
            if p.suffix in supported_exts or any(
                str(p).endswith(ext) for ext in supported_exts
            ):
                sample_file_path = str(p)
                break
        if not sample_file_path:
            raise FileNotFoundError("No supported file (.parquet or .txt.gz) found after unzip")

        # file_url = f"{self.temp_dir}/{row.filename}"
        row_schema = self.duckdb.get_schema(sample_file_path)

        if version is None and schema is None:
            version = self.get_last_version(dataset)
            if version is None:
                date = self.get_first_date(dataset)
                version = f"schema_version_{date}"

            self.last_schema = row_schema
            self.last_version = version
            print(f">>>>> setting version -- {version}")
            print(f">>>>> schema -- /n {row_schema}")

        elif len(version) > 0 and schema is not None:
            print(">>>>> checking schema")
            if schema != row_schema:
                print(">>>>> changing schema")
                print(f">>>>> schema -- /n {row_schema}")
                # date = self.get_transfer_date_from_key(row.key)
                date = row.transfer_date
                new_version = f"schema_version_{date}"

                self.last_schema = row_schema
                self.last_version = new_version
        else:
            if version is None:
                print("Current Version has not been set.")
            if schema is None:
                print("Current Schema has not been set.")

            raise Exception("Undefined state.")

        assert version.startswith("schema_version")

        return version

    def get_last_version(self, dataset) -> str | None:
        df = self.dest_files
        df = df[df.dataset == dataset]
        if df.shape[0] == 0:
            return None
        file_path = df.key.iloc[-1]
        version = file_path.split("/")[-5]

        return version

    def get_first_date(self, dataset) -> str:
        df = self.source_files
        df = df[df.dataset == dataset]
        first_date = str(df.transfer_date.min())
        # Test that the transfer date string matches the expedted date pattern.
        try:
            dt.datetime.strptime(first_date, "%Y%m%d")
        except ValueError:
            raise ValueError(
                f"First transfer date could not be parsed: {first_date}"
            )

        return first_date

    def get_table_name(self, dataset: str):
        schema_version = self.get_last_version(dataset)
        version = schema_version.split("_")[-1]
        if dataset == "customextract_v1_dpc":
            dataset = "mls_listings_replace"
        table = f"{dataset}_v{version}"

        return table

    def sync_metadata(self, dataset: str):
        """
        mls_listings_replace_v20231005
        """
        table = self.get_table_name(dataset)

        query = f"""
            CALL s3.system.sync_partition_metadata('external_corelogic', '{table}', 'ADD')
        """

        print(query)
        self.trino.query(query)

    def create_table(self, dataset):
        partition_cols = ["year", "month", "day"]
        table = self.get_table_name(dataset)
        # Get last key.
        self.set_dest_files()
        df = self.dest_files
        key = df.key.iloc[-1]
        # Get the last schema
        s3_url = f"s3://{self.dest_bucket}/{key}"
        col_defs = self.duckdb.get_schema(s3_url)
        col_defs = [
            f"{c[0].lower()} {c[1]},"
            for c in col_defs
            if c[0] not in partition_cols
        ]
        col_defs.extend(["year VARCHAR,", "month VARCHAR,", "day VARCHAR"])

        schema = "external_corelogic"
        bucket = "et-data-staging"

        path = ExternalFiles(key)
        s3_prefix = f"{path.prefix}/{dataset}/schema_version_{path.schema_version}"

        self.trino.create_table(
            table, schema, bucket, s3_prefix, col_defs, partition_cols
        )

    def find_schema_change(self, dataset):
        """Iterate through files and output dates/files where
        schema changed.
        """
        df = self.dest_files
        bucket = self.dest_bucket
        current_schema = None
        schemas = []
        for i, row in df.iterrows():
            s3_url = f"s3://{bucket}/{row.key}"
            row_schema = self.duckdb.get_schema(s3_url)
            if current_schema != row_schema:
                current_schema = row_schema
                schemas.append((row.transfer_date, current_schema))

        return schemas


def test(trino_token):
    job = ExternalTables(env="prod", trino_token=trino_token)
    job.set_unprocessed_files()
    job.stage_dataset("customextract_v1_dpc", 1)
    return job