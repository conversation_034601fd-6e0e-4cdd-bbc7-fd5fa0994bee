from airflow.decorators import task
from airflow.models import Variable
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.amazon.aws.operators.s3 import S3ListOperator
from airflow.utils.dates import days_ago
from botocore.exceptions import ClientError
from etdag import ETDAG
import io
import json
import re
import zipfile
from old_dags.slack_alert import task_slack_alert


default_args = {
    "owner": "<PERSON> Carver",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
}


def extract_zipfile(s3_conn, source_bucket, zip_file_key, dest_bucket, dest_path):
    response = s3_conn.get_object(Bucket=source_bucket, Key=zip_file_key)
    zip_data = response["Body"].read()
    with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_ref:
        extracted_files = zip_ref.namelist()
        for filename in extracted_files:
            dest_filename = f"{dest_path}/{filename}"
            print(dest_filename)
            file_data = zip_ref.read(filename)
            s3_conn.put_object(Bucket=dest_bucket, Key=dest_filename, Body=file_data)
    return f"Extracted and transfered {zip_file_key} to {dest_path}"


def s3_copy(s3_conn, source_bucket, source_path, dest_bucket, dest_filename):
    copy_source = {"Bucket": source_bucket, "Key": source_path}
    s3_conn.copy(copy_source, dest_bucket, dest_filename)
    return True


def is_transfer_needed(dest_files, filename, path=False):
    # For extracts, i don't have the full filename yet, only the paths
    if path:
        return not any(filename in dest_filename for dest_filename in dest_files)
    return filename not in dest_files


with ETDAG(
    dag_id="corelogic_parsing",
    description="Parses the corelogic files in the source S3 location into their intended location for processing",
    start_date=days_ago(2),
    on_failure_callback=task_slack_alert,
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    tags=["corelogic", "ingestion"],
) as dag:
    """
    This Dag will parse files in the source S3 location to the processing location.
    """
    s3_connection = "s3_conn"
    source_bucket = Variable.get("corelogic_source_bucket")
    dest_bucket = Variable.get("corelogic_dest_bucket")
    parsing_dict = json.loads(Variable.get("corelogic_file_parse_dict"))

    @task
    def parse_transfered_files(transferred_files, dest_files, parsing_dict):
        transfer_list = []
        for data_type, details in parsing_dict.items():
            process = True
            extract = False
            if "process" in parsing_dict[data_type]:
                process = parsing_dict[data_type]["process"]
            if "extract" in parsing_dict[data_type]:
                extract = parsing_dict[data_type]["extract"]
            for file_type, paths in parsing_dict[data_type].items():
                if file_type not in ["process", "extract"]:
                    r = re.compile(paths["regex"])
                    type_files = list(filter(r.match, transferred_files))
                    for filename in type_files:
                        date = r.findall(filename)[0]
                        dest_filename = paths["path"].format(
                            date, filename.split("/")[-1]
                        )
                        if extract:
                            dest_filename = paths["path"].format(
                                date, f"zipfile/{filename.split('/')[-1]}"
                            )
                        if is_transfer_needed(dest_files, dest_filename, extract):
                            transfer_list.append(
                                (
                                    data_type,
                                    filename,
                                    dest_filename,
                                    process,
                                    date,
                                    file_type,
                                    extract,
                                )
                            )
        return transfer_list

    @task
    def transfer_parsed_files(s3_conn, transfer_list, source_bucket, dest_bucket):
        s3_hook = S3Hook(aws_conn_id=s3_conn)
        s3_conn = s3_hook.get_conn()
        transfer_status = []
        for transfer in transfer_list:
            print(f"transfering {transfer[0]}")
            try:
                (
                    dataset,
                    source_filename,
                    dest_filename,
                    process,
                    transfer_date,
                    transfer_type,
                    extract,
                ) = transfer
                source_bucket = Variable.get("corelogic_source_bucket")
                dest_bucket = Variable.get("corelogic_dest_bucket")
                if source_filename[0] == "/":
                    source_filename = source_filename[1:]

                s3_hook = S3Hook(aws_conn_id="s3_conn")
                s3_conn = s3_hook.get_conn()
                if extract:
                    s3_copy(
                        s3_conn,
                        source_bucket,
                        source_filename,
                        dest_bucket,
                        dest_filename,
                    )
                    dest_path = "/".join(dest_filename.split("/")[:4])
                    extract_zipfile(
                        s3_conn,
                        source_bucket,
                        source_filename,
                        dest_bucket,
                        dest_path,
                    )
                    transfer_status.append(
                        f"{source_filename} transfered to {dest_filename}"
                    )
                else:
                    s3_copy(
                        s3_conn,
                        source_bucket,
                        source_filename,
                        dest_bucket,
                        dest_filename,
                    )
                    transfer_status.append(
                        f"{source_filename} transfered to {dest_filename}"
                    )
            except ClientError as e:
                print("+++++++++++")
                print("failed to transfer:")
                print(transfer[0])
                print(transfer[1])
                print("+++++++++++")
                print(e)
                transfer_status[transfer[0]] = False
        return transfer_status

    source_s3_files = S3ListOperator(
        task_id="source_s3_files",
        bucket=source_bucket,
        prefix="corelogic/",
        aws_conn_id=s3_connection,
    )

    dest_s3_files = S3ListOperator(
        task_id="dest_s3_files",
        bucket=dest_bucket,
        prefix="source/",
        aws_conn_id=s3_connection,
    )

    parsed_transfers = parse_transfered_files(
        source_s3_files.output, dest_s3_files.output, parsing_dict
    )

    transfer_files = transfer_parsed_files(
        s3_connection, parsed_transfers, source_bucket, dest_bucket
    )

    source_s3_files >> dest_s3_files >> parsed_transfers >> transfer_files
