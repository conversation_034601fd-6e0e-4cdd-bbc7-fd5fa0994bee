INSERT INTO s3.gold_real_estate_intender.homeshopper_shopper_stats
SELECT DISTINCT shopper_state state, shopper_city city, shopper_county county,
--HS COUNTS BY SHOPPER STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county) Home_Shoppers_Shopping_FROM,
--HS COUNTS BY LISTING STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county) Home_Shoppers_Shopping_IN,
--VA HS COUNTS BY SHOPPER STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and shopper_mortgage_loan_type = 'VA') VA_Shoppers_Shopping_FROM,
--VA HS COUNTS BY LISTING STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    AND shopper_mortgage_loan_type= 'VA') VA_Shoppers_Shopping_IN,
--FHA ELIGIBLE BY SHOPPER STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.shopper_occ_code IN ('O','S')
    and cast(hc.listing_price as int) <= cast(hc.fips_fha_limit as int)
    ) as FHA_Eligible_Shopping_FROM,
--FHA ELIGIBLE BY LISTING STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.shopper_occ_code IN ('O','S')
    and cast(hc.listing_price as int) <= cast(hc.fips_fha_limit as int)
    ) as FHA_Eligible_Shopping_IN,
--FHA FIRST TIME HOME BUYER BY SHOPPER STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.shopper_occ_code IN ('A','T')
    and cast(hc.listing_price as int) <= cast(hc.fips_fha_limit as int)
    ) as FHA_and_First_Time_Buyer_Shopping_FROM,
--FHA FIRST TIME HOME BUYER BY LISTING STATE
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.shopper_occ_code IN ('A','T')
    and cast(hc.listing_price as int) <= cast(hc.fips_fha_limit as int)
    ) as FHA_and_First_Time_Buyer_Shopping_IN,
--New, inverse audience of fha_eligible_shopper_count
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.shopper_occ_code IN ('O','S')
    and cast(hc.listing_price as int) > cast(hc.fips_fha_limit as int)
    ) as NOT_FHA_Eligible_Shopping_FROM,
-- New, inverse audience of fha_eligible_by_listing_count
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.shopper_occ_code IN ('O','S')
    and cast(hc.listing_price as int) > cast(hc.fips_fha_limit as int)
    ) as NOT_FHA_Eligible_Shopping_IN,
-- Home Shopper, Owner Absent at current property, grouped by Listing Location
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.shopper_occ_code IN ('A','T')
    ) as Renter_Shopping_IN,
-- Home Shopper, Owner Absent at current property, grouped by Shopper Location
(select cast(cast(count(distinct shopper_clip) as varchar) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.shopper_occ_code IN ('A','T')
    ) as Renter_Shopping_FROM,
-- LMI Listings Home Shoppers by Shopper's State: Count of Home Shoppers observed at multiple Listings in LMI tracts grouped by Shoppers' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.listing_income_level_ind IN (1,2)
    ) as LMI_Shopper_Shopping_IN,
-- LMI Listings Home Shoppers by Listings' State: Count of Home Shoppers observed at multiple Listings in LMI tracts grouped by listings' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.listing_income_level_ind IN (1,2)
    ) as LMI_Shopper_Shopping_FROM,
-- LMI Shoppers LMI Listings by Shoppers' State: Count of Home Shoppers currently residing in LMI tracts, observed at multiple listings in LMI tracts grouped by shoppers' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from (select shopper_clip, shopper_state, shopper_city, shopper_county
        from s3.dev_prototyping.homeshopper_shopper_by_listing
        where 1=1
        and listing_income_level_ind IN (1,2)
        and shopper_income_level_ind IN (1,2)
        group by 1,2,3,4
        having count(*) > 1) as hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county) as LMI_Shopper_Shopping_IN_and_FROM_by_shopper,
-- LMI Shoppers LMI Listings by Listings' State: Count of Home Shoppers currently residing in LMI tracts, observed at multiple listings in LMI tracts grouped by Listings' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from (select shopper_clip, listing_state, listing_city, listing_county
        from s3.dev_prototyping.homeshopper_shopper_by_listing
        where 1=1
        and listing_income_level_ind IN (1,2)
        and shopper_income_level_ind IN (1,2)
        group by 1,2,3,4
        having count(*) > 1) as hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county) as LMI_Shopper_Shopping_IN_and_FROM_by_Listing,
-- MMT Listings Home Shoppers by Shopper's State: Count of Home Shoppers observed at multiple Listings in MMT tracts grouped by Shoppers' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county
    and hc.listing_pct_min > 50
    ) as MMT_Shopper_Shopping_FROM,
-- MMT Listings Home Shoppers by Listings' State: Count of Home Shoppers observed at multiple Listings in MMT tracts grouped by listings' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from s3.dev_prototyping.homeshopper_shopper_by_listing hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county
    and hc.listing_pct_min > 50
    ) as MMT_Shopper_Shopping_IN,
-- MMT Shoppers MMT Listings by Shoppers' State: Count of Home Shoppers currently residing in MMT tracts, observed at multiple listings in MMT tracts grouped by shoppers' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from (select shopper_clip, shopper_state, shopper_city, shopper_county
        from s3.dev_prototyping.homeshopper_shopper_by_listing
        where 1=1
        and listing_pct_min > 50
        and shopper_pct_min > 50
        group by 1,2,3,4
        having count(*) > 1) as hc
    where hc.shopper_state = hct.shopper_state
    and hc.shopper_city = hct.shopper_city
    and hc.shopper_county = hct.shopper_county) as MMT_Shopper_Shoopping_IN_and_FROM_by_shopper,
-- MMT Shoppers MMT Listings by Listings' State: Count of Home Shoppers currently residing in MMT tracts, observed at multiple listings in MMT tracts grouped by Listings' state, city.
(select cast(count(distinct shopper_clip) as varchar)
    from (select shopper_clip, listing_state, listing_city, listing_county
        from s3.dev_prototyping.homeshopper_shopper_by_listing
        where 1=1
        and listing_pct_min > 50
        and shopper_pct_min > 50
        group by 1,2,3,4
        having count(*) > 1) as hc
    where hc.listing_state = hct.shopper_state
    and hc.listing_city = hct.shopper_city
    and hc.listing_county = hct.shopper_county) as MMT_Shopper_Shoopping_IN_and_FROM_by_listing,
cast(current_date as varchar) as run_date
FROM "s3"."dev_prototyping"."homeshopper_shopper_by_listing" hct
