from airflow.decorators import task, task_group
from airflow.datasets import Dataset
from etdag import ETDAG
from datetime import datetime
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
import re
from airflow.utils.dates import timedelta
import pendulum
from airflow.exceptions import AirflowSkipException
import logging
from old_dags.corelogic.silver_propensity.queries import (
    MAX_PARTITION,
    MODEL_RUN_DATE,
    SILVER_LOAD_QUERIES,
)


logging.basicConfig()
logger = logging.getLogger(__name__)


docs = """
### CoreLogic Silver Propensity DAG

This DAG is responsible for staging CoreLogic propensity datasets into an Olympus table. 
It performs the following steps for each dataset:

1. **Fetch Partition and Run Date**:
    - Retrieves the latest partition date from the dataset.
    - Retrieves the run date for the dataset.

2. **Compare Dates**:
    - Compares the partition date with the run date to determine if the dataset needs to be updated.

3. **Branching Logic**:
    - If the partition date is earlier than the run date, the DAG proceeds to load the dataset into the silver table.
    - Otherwise, it skips the load process.

4. **Load Silver Table**:
    - Executes the SQL query to load the dataset into the silver table if the condition is met.

#### Parameters
- **run_date**: The date to compare against the dataset's partition date. Defaults to today's date in EST.

#### Datasets Processed
The DAG processes the following datasets:
- `propensityscore1_dpc_purchase`
- `propensityscore1_dpc_heloc`
- `propensityscore1_dpc_refinance`
- `propensityscore1_dpc_list_for_sale`
- `propensityscore1_dpc_list_to_rent`

#### Tags
- `corelogic`
- `silver`
- `olympus`
- `DND`

#### Tasks:
- get_partition: Retrieves the maximum partition for the given dataset from the database.
- get_run_date: Retrieves the model run date for the given dataset from the database.
- compare_results: Compares the outputs of `get_partition` and `get_run_date`.
- router: Routes the workflow based on the comparison result.
- skip: Skips the silver data loading if the comparison result is false.
- load_silver: Loads the silver data into the database if the comparison result is true.

#### Workflow:
1. Fetch the maximum partition and model run date for the dataset.
2. Compare the results of the two queries.
3. Route the workflow based on the comparison:
    - If the comparison is false, skip the silver data loading.
    - If the comparison is true, proceed to load the silver data.

"""


@task
def compare_results(purchase_partition: list, run_date_results: list) -> bool:
    """
    Compares a partition date with a run date and determines if an action is needed.

    Returns:
        bool: Returns `True` if the partition date is earlier than the run date,
        indicating that an action is needed. Returns `False` otherwise.
    """
    print("Comparing partition date with run date...")

    raw_partition = (
        purchase_partition[0][0]
        if purchase_partition and purchase_partition[0]
        else None
    )
    raw_run_date = (
        run_date_results[0][0] if run_date_results and run_date_results[0] else None
    )

    # now if *either* side is None, skip
    if raw_partition is None or raw_run_date is None:
        raise AirflowSkipException("No results to compare (got NULL).")

    part_str = str(raw_partition)
    run_str = str(raw_run_date)

    try:
        if re.fullmatch(r"\d{8}", part_str):
            part_dt = pendulum.from_format(part_str, "YYYYMMDD")
        else:
            part_dt = pendulum.parse(part_str)
        part_date = part_dt.date()
    except Exception as e:
        logger.error(f"Error parsing partition date '{part_str}': {e}")
        raise Exception("Invalid run date format.")

    try:
        if re.fullmatch(r"\d{8}", run_str):
            run_dt = pendulum.from_format(run_str, "YYYYMMDD")
        else:
            run_dt = pendulum.parse(run_str)
        run_date = run_dt.date()
    except Exception as e:
        logger.error(f"Error parsing run date '{run_str}': {e}")
        raise Exception("Invalid run date format.")

    logger.info(f"Partition date: {part_date}, Run date: {run_date}")

    if part_date < run_date:
        logger.info("Partition date < run date. Action needed.")
        return True
    else:
        logger.info("Partition date ≥ run date. No action needed.")
        return False


@task.branch
def router(cond: bool, skip_id: str, load_id: str) -> str:
    return load_id if cond else skip_id


@task
def run_if_false():
    logger.info("Silver Tables are up to date. Skipping load.")


default_args = {
    "depends_on_past": False,
    "start_date": datetime(2025, 5, 5),
    "email": ["<EMAIL>"],
    "owner": "Bryan Price",
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
}


@task_group
def sync_silver(dataset: str):
    """
    Synchronizes the silver dataset by comparing the latest partition and model run date,
    and conditionally loads the silver data based on the comparison result.

    Args:
        dataset (str): The name of the dataset to be synchronized.
    """
    get_partition = SQLExecuteQueryOperator(
        task_id="get_partition",
        conn_id="trino_conn",
        sql=MAX_PARTITION.format(dataset=dataset),
        handler=lambda cur: cur.fetchall(),
        retries=2,
        retry_delay=timedelta(seconds=5),
    )

    get_run_date = SQLExecuteQueryOperator(
        task_id="get_run_date",
        conn_id="trino_conn",
        sql=MODEL_RUN_DATE[dataset],
        handler=lambda cur: cur.fetchall(),
        retries=2,
        retry_delay=timedelta(seconds=5),
    )

    comp = compare_results(get_partition.output, get_run_date.output)
    skip = run_if_false.override(task_id="skip")()
    load = SQLExecuteQueryOperator(
        task_id="load_silver",
        conn_id="trino_conn",
        sql=SILVER_LOAD_QUERIES[dataset],
        handler=lambda cur: cur.fetchall(),
    )
    branch = router(comp, skip.operator.task_id, load.task_id)

    comp >> branch >> [skip, load]


with ETDAG(
    dag_id="corelogic_silver_propensity_dag",
    description="Stage corelogic propensity datasets in an Olympus table.",
    default_args=default_args,
    schedule_interval="0 6 * * *",
    catchup=False,
    params={"run_date": (pendulum.today("EST") - timedelta(days=1)).to_date_string()},
    tags=["corelogic", "silver", "olympus", "DND"],
) as dag:
    dag.doc_md = docs

    def silver_dag():

        datasets = [
            "propensityscore1_dpc_purchase",
            "propensityscore1_dpc_heloc",
            "propensityscore1_dpc_refinance",
            "propensityscore1_dpc_list_for_sale",
            "propensityscore1_dpc_list_to_rent",
        ]

        for ds in datasets:

            sync_silver.override(group_id=f"sync_{ds}")(ds)

    dag = silver_dag()
