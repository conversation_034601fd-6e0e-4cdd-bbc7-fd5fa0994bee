MAX_PARTITION = """
    SELECT max(partition.model_run_date) FROM olympus.silver_corelogic."{dataset}$partitions"
    """

# run_date SQL per dataset
MODEL_RUN_DATE = {
    "propensityscore1_dpc_purchase": """
        SELECT purchase_mortgage_model_propensity_score_run_date
        FROM s3.external_corelogic.propensityscore1_dpc
        WHERE
            transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
            AND purchase_mortgage_model_propensity_score_run_date != ''
        LIMIT 1
    """,
    "propensityscore1_dpc_heloc": """
        SELECT heloc_model_propensity_score_run_date
        FROM s3.external_corelogic.propensityscore1_dpc
        WHERE
            transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
            AND heloc_model_propensity_score_run_date != ''
        LIMIT 1
    """,
    "propensityscore1_dpc_list_for_sale": """
        SELECT list_for_sale_model_propensity_score_run_date
        FROM s3.external_corelogic.propensityscore1_dpc
        WHERE
            transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
            AND list_for_sale_model_propensity_score_run_date != ''
        LIMIT 1
    """,
    "propensityscore1_dpc_list_to_rent": """
        SELECT list_to_rent_model_propensity_score_run_date
        FROM s3.external_corelogic.propensityscore1_dpc
        WHERE
            transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
            AND list_to_rent_model_propensity_score_run_date != ''
        LIMIT 1
    """,
    "propensityscore1_dpc_refinance": """
        SELECT refinance_model_propensity_score_run_date
        FROM s3.external_corelogic.propensityscore1_dpc
        WHERE
            transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
            AND refinance_model_propensity_score_run_date != ''
        LIMIT 1
    """,
}

# silver‐write SQL per dataset
SILVER_LOAD_QUERIES = {
    "propensityscore1_dpc_purchase": """
        INSERT INTO "olympus"."silver_corelogic"."propensityscore1_dpc_purchase"
        SELECT
            clip,
            transfer_date,
            TRY_CAST(purchase_mortgage_model_propensity_score AS INT) AS score,
            try(
                date_parse(
                purchase_mortgage_model_propensity_score_run_date,
                '%Y%m%d'
                )
            ) AS model_run_date
        FROM "s3"."external_corelogic"."propensityscore1_dpc"
        WHERE transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}') and purchase_mortgage_model_propensity_score_run_date != '' 
    """,
    "propensityscore1_dpc_heloc": """
        INSERT INTO "olympus"."silver_corelogic"."propensityscore1_dpc_heloc"
        SELECT 
            clip, 
            transfer_date,
            TRY_CAST(heloc_model_propensity_score AS INT) AS score, 
            try(
                date_parse(
                heloc_model_propensity_score_run_date,
                '%Y%m%d'
                )
            ) AS model_run_date
        FROM "s3"."external_corelogic"."propensityscore1_dpc"
        WHERE transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}') and heloc_model_propensity_score_run_date != '' 
    """,
    "propensityscore1_dpc_list_for_sale": """
        INSERT INTO "olympus"."silver_corelogic"."propensityscore1_dpc_list_for_sale"
        SELECT 
            clip, 
            transfer_date,
            TRY_CAST(list_for_sale_model_propensity_score AS INT) AS score, 
            try(
                date_parse(
                list_for_sale_model_propensity_score_run_date,
                '%Y%m%d'
                )
            ) AS model_run_date
        FROM "s3"."external_corelogic"."propensityscore1_dpc"
        WHERE transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}') and list_for_sale_model_propensity_score_run_date != '' 
    """,
    "propensityscore1_dpc_list_to_rent": """
        INSERT INTO "olympus"."silver_corelogic"."propensityscore1_dpc_list_to_rent"
        SELECT 
            clip, 
            transfer_date,
            TRY_CAST(list_to_rent_model_propensity_score AS INT) AS score, 
            try(
                date_parse(
                list_to_rent_model_propensity_score_run_date,
                '%Y%m%d'
                )
            ) AS model_run_date
        FROM "s3"."external_corelogic"."propensityscore1_dpc"
        WHERE transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}') and list_to_rent_model_propensity_score_run_date != '' 
    """,
    "propensityscore1_dpc_refinance": """
        INSERT INTO "olympus"."silver_corelogic"."propensityscore1_dpc_refinance"
        SELECT 
            clip, 
            transfer_date,
            TRY_CAST(refinance_model_propensity_score AS INT) AS score, 
            try(
                date_parse(
                refinance_model_propensity_score_run_date,
                '%Y%m%d'
                )
            ) AS model_run_date
        FROM "s3"."external_corelogic"."propensityscore1_dpc"
        WHERE transfer_date = DATE('{{ dag_run.conf.get("run_date", params.run_date) }}') and refinance_model_propensity_score_run_date != ''
    """,
}
