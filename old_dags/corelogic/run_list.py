"""A module to prepare a list of s3 file paths for building bronze models with
a dbt dag.
"""

import botocore
import boto3
import pandas as pd
from trino.dbapi import connect
from trino.exceptions import TrinoUserError
from trino.auth import OAuth2Authentication
import os


class RunList:
    """Generate a list of source file paths to for invoking dbt bronze model
    runs.

    This module is designed to run in a dag so the type of data received
    in the init function must be hashable i.e. a basic python data type like
    a dict.
    """

    def __init__(
        self,
        s3_client: botocore.client = None,
        trino_conn: dict[str] = None,
        env: str = "local",
        trino_user: str = None,
        s3_path_list: list[dict] = None,
        iceberg_path_list: list[dict] = None,
    ):
        """Establish a session of with an S3 client and a trino client.

        If an S3 client is not provided the environment variable will determine
        the type of client to set. In the case where this module is used by
        Airflow the S3Hook should be supplied. If the environment is 'local' it
        will be assumed the client will be configured to access local minio
        storage. If the environment is 'prod' and no S3 client is provided it
        will be assumed that the client will be configured to access the S3
        account with credentials located in your home .aws directory.


        Args:
            s3_client (botocore.client, optional): The client used to access S3
                file storage.
            trino_conn (:obj:`dict` of :obj:`str`, optional): The connection
                variables for a trino connection.
            env (str, optional): The environment in which the module is being
                run (local|prod).
            trino_user (str, optional): User name when running in prod from local
                machine.
            s3_path_list (:obj:`list` of :obj:`dict` of :obj:`str`): A list of
                file path parameters from the s3 source bucket.
            iceberg_path_list (:obj:`list` of :obj:`dict` of :obj:`str`): A list of
                file path parameters from files loaded into the iceberg table.
        """
        self.s3 = s3_client
        self.env = env
        self.source_bucket = "et-datalake-corelogic-staging"
        self.schema = "bronze_corelogic"
        if env == "local":
            self.schema += "_local"
            self.source_bucket += "-local"
        self.source_prefix = "source"

        if s3_client is None:
            if env == "local":
                self.s3 = boto3.client(
                    "s3",
                    endpoint_url="http://localhost:9000",
                    aws_access_key_id="admin",
                    aws_secret_access_key="password",
                    verify=False,
                )
            if env == "prod":
                self.s3 = boto3.client("s3")
        else:
            self.s3 = s3_client

        if trino_conn is None:
            if env == "local":
                self.trino_conn = connect(
                    user=trino_user,
                    host="localhost",
                    port=8000,
                    http_scheme="http",
                    catalog="iceberg",
                    schema=self.schema,
                )
            if env == "prod":
                self.trino_conn = connect(
                    user=trino_user,
                    auth=OAuth2Authentication(),
                    host="starburst.k8s.eltoro.com",
                    port=443,
                    http_scheme="https",
                    catalog="iceberg",
                    schema=self.schema,
                )
        else:
            self.trino = self._trino_connect(trino_conn)

        self.s3_path_list = s3_path_list
        self.iceberg_path_list = iceberg_path_list

    def _trino_connect(self, trino_conn: dict[str]):
        """Establish a trino connection using config variables.

        Example:
            trino_conn={
                "conn_type": "trino",
                "login": "admin",
                "host": "localhost",
                "port": 8000,
                "schema": "corelogic"
            }
        """
        conn = {}
        conn["user"] = trino_conn["login"]
        conn["host"] = trino_conn["host"]
        conn["port"] = trino_conn["port"]
        conn["schema"] = self.schema
        conn["catalog"] = "olympus"
        self.trino_conn = connect(**conn)

    def _query_trino(self, query: str) -> list[list[object]]:
        cur = self.trino_conn.cursor()
        cur.execute(query)

        # Output some cursor metrics to diagnose empty response issue.
        warnings = cur.warnings
        array_size = cur.arraysize
        stats = cur.stats
        print("================================")
        print(f"stats \n {stats}")
        print("================================")
        print(f"warnings \n {warnings}")
        print("================================")
        print(f"array_size \n {array_size}")
        print("================================")

        return cur

    def _prepare_file_paths(self, file_list: list[dict[str]]):
        file_paths = []
        for fp in file_list:
            path = fp["Key"].split("/")
            if len(path) == 5 and path[4] != "" and path[0] == "source":
                file_paths.append(
                    {
                        "dataset": path[1],
                        "transfer_type": path[2],
                        "transfer_date": path[3],
                    }
                )

        return file_paths

    def get_transfers(self, dataset: str) -> pd.DataFrame:
        query = f"""
        SELECT file_transfer_type, file_transfer_date
        FROM olympus.{self.schema}.{dataset}
        GROUP BY file_transfer_date, file_transfer_type
        ORDER BY file_transfer_date, file_transfer_type ASC
        """
        try:
            res = self._query_trino(query).fetchall()
        # Handle case where the table doesn't exist.
        except TrinoUserError as e:
            print(e)
            return pd.DataFrame(
                {"dataset": [], "transfer_type": [], "transfer_date": []}
            )
        df = pd.DataFrame(res, columns=["transfer_type", "transfer_date"])
        df.transfer_date = pd.to_datetime(df.transfer_date)
        df.transfer_date = df.transfer_date.dt.strftime("%Y%m%d")
        df["dataset"] = dataset

        return df

    def get_s3_paths(self, dataset: str) -> pd.DataFrame:
        if self.s3_path_list is not None:
            df = pd.DataFrame(self.s3_path_list, dtype="string")
        else:
            bucket = self.source_bucket
            prefix = f"{self.source_prefix}/{dataset}"
            paginator = self.s3.get_paginator("list_objects_v2")
            pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
            file_list = []
            for page in pages:
                paths = page.get("Contents", {})
                file_list += paths

            file_paths = self._prepare_file_paths(file_list)
            df = pd.DataFrame(file_paths, dtype="string")

        def sort_transfer_type(column):
            order = ["init", "full", "delta"]
            cat = pd.Categorical(column, categories=order, ordered=True)
            return pd.Series(cat)

        df = df.sort_values(by=["transfer_type"], key=sort_transfer_type)
        df = df.sort_values(
            ["transfer_date", "transfer_type"],
            ascending=[True, False],
        )
        # Reduce model file list to unique dataset/transfer_type/transfer_date.
        df = df.drop_duplicates()

        df.reset_index(drop=True, inplace=True)

        return df

    def _calculate_start_index(self, paths: pd.DataFrame, start_date: str):
        """Calculate starting index of object path to run.

        If a start_date is not specified the last init file(if present) is used
        as the starting point otherwise the very first file is used."""
        start_idx = None
        if start_date is not None:
            start_idx = paths[paths.transfer_date == start_date].index[0]
        else:
            init = paths[paths.transfer_type == "init"].index
            if len(init):
                start_idx = init[-1]

        if start_idx is None:
            start_idx = 0

        print(f"start index >> {start_idx}")

        return start_idx

    def prepare_run_list(
        self,
        dataset: str,
        start_date: str,
        stop_date: str,
        auto_fill: bool,
        **kwargs,
    ) -> list[dict[str, str]]:
        if auto_fill is True:
            run_list = self._prepare_run_list_diff(dataset, start_date)
        else:
            run_list = self._prepare_run_list(dataset, start_date, stop_date)

        if len(run_list) > 0:
            print("****************************************")
            print("run_list range:")
            print("start date >> ", run_list[0]["transfer_date"])
            print("stop date >> ", run_list[-1]["transfer_date"])
            print("run_list --> ")
            print("transfer_date - transfer_type")
            for rl in run_list:
                print(rl["transfer_type"], "   -   ", rl["transfer_date"])
            print("****************************************")
        else:
            print(f"No files to process for {dataset}!!")

        return run_list

    def _prepare_run_list(
        self,
        dataset: str,
        start_date: str,
        stop_date: str,
        **kwargs,
    ) -> list[dict[str, str]]:
        df = self.get_s3_paths(dataset)
        start_idx = self._calculate_start_index(df, start_date)

        # Construct the list of object paths to be run.
        if stop_date is not None:
            stop_idx = df[df.transfer_date == stop_date].index[-1]
            # fmt: off
            run_list = df.iloc[start_idx:stop_idx+1]
            # fmt: on
        else:
            run_list = df.iloc[start_idx:]

        # Convert the <NA> types to string and the dataframe to a dict list
        # in order to be serialized when passing between dag tasks.
        run_list = run_list.fillna("").to_dict("records")

        return run_list

    def _prepare_run_list_diff(
        self,
        dataset: str,
        start_date: str,
    ) -> list[dict[str, str]]:
        """Prepare a run list by comparing the source file transfer dates
        and types to the iceberg table contents.

        Args:
            dataset (str): The name of the dataset to compare.
        Returns:
            pd.DataFrame: The diff of s3 source files minus the transferred
                files type/date.
        """
        paths = self.get_s3_paths(dataset)

        # Find starting index and remove paths before start.
        start_idx = self._calculate_start_index(paths, start_date)
        paths = paths.iloc[start_idx:]
        transfers = self.get_transfers(dataset)

        merge = paths.merge(
            transfers,
            on=["dataset", "transfer_type", "transfer_date"],
            how="outer",
            indicator=True,
        )
        run_list = merge[merge._merge == "left_only"]

        # Convert the <NA> types to string and the dataframe to a dict list
        # in order to be serialized when passing between dag tasks.
        run_list = run_list.astype("string")
        run_list = run_list.fillna("").to_dict("records")

        return run_list
