FIND_MISSING_DATES = """
SELECT
    s3_max.transfer_date AS s3_max_date,
    olympus_max.transfer_date AS olympus_max_date
FROM (
    SELECT MAX(transfer_date) AS transfer_date
    FROM "s3"."external_corelogic"."vol_lien_status_m2_dpc$partitions"
) s3_max,
(
    SELECT MAX(partition.transfer_date) AS transfer_date
    FROM "olympus"."silver_corelogic"."vol_lien_status_m2_dpc$partitions"
) olympus_max
"""


REMOVE_OLD_DATA = """
DELETE FROM "olympus"."silver_corelogic"."vol_lien_status_m2_dpc"
"""


INSERT_OLYMPUS = """
INSERT INTO "olympus"."silver_corelogic"."vol_lien_status_m2_dpc"
SELECT
   clip ,
   previous_clip ,
   fips_code ,
   owner_occupancy_code,
   owner_1_corporate_indicator,
   owner_2_corporate_indicator,
   situs_core_based_statistical_area_cbsa,
   TRY_CAST(estimated_value_mktg AS DOUBLE) AS estimated_value_mktg ,
   estimated_value_high_mktg ,
   estimated_value_low_mktg ,
   TRY_CAST(confidence_score_mktg AS DOUBLE) AS confidence_score_mktg,
    TRY(
        DATE_PARSE(
            NULLIF(
                REGEXP_REPLACE(
                    REGEXP_REPLACE(
                        REGEXP_REPLACE(
                            REGEXP_REPLACE(
                                REGEXP_EXTRACT("purchase_recording_date", '^[12][0-9]{7}$'),
                                '(?<=.{6})00$', '01'
                            ),
                            '(?<=.{4})00(?=[0-9]{2}$)', '01'
                        ),
                        '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'
                    ),
                    '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'
                ),
                ''
            ),
            '%Y%m%d'
        )
    ) AS "purchase_recording_date",
   purchase_amount,
   total_number_of_open_mortgage_liens,
   total_amount_of_open_mortgage_liens ,
   purchase_combined_ltv_loan_to_value ,
   TRY_CAST(estimated_equity AS DOUBLE) AS estimated_equity,
   estimated_combined_ltv_loan_to_value ,
   first_position_composite_transaction_id ,
   first_position_mortgage_ltv_loan_to_value ,
   first_position_multi_or_split_parcel_code ,
   first_position_mortgage_type_code ,
   first_position_mortgage_purpose_code ,
   TRY(
    DATE_PARSE(
        NULLIF(
        REGEXP_REPLACE(
            REGEXP_REPLACE(
            REGEXP_REPLACE(
                REGEXP_REPLACE(
                REGEXP_EXTRACT("first_position_mortgage_date", '^[12][0-9]{7}$'),
                '(?<=.{6})00$', '01'
                ),
                '(?<=.{4})00(?=[0-9]{2}$)', '01'
            ),
            '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'
            ),
            '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'
        ),
        ''
        ),
    '%Y%m%d'
    )
    ) AS "first_position_mortgage_date",
    TRY(
    DATE_PARSE(
        NULLIF(
        REGEXP_REPLACE(
            REGEXP_REPLACE(
            REGEXP_REPLACE(
                REGEXP_REPLACE(
                REGEXP_EXTRACT("first_position_mortgage_recording_date", '^[12][0-9]{7}$'),
                '(?<=.{6})00$', '01'
                ),
                '(?<=.{4})00(?=[0-9]{2}$)', '01'
            ),
            '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'
            ),
            '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'
        ),
        ''
        ),
    '%Y%m%d'
    )
    ) AS "first_position_mortgage_recording_date",
   first_position_mortgage_recorded_document_year ,
   first_position_mortgage_recorded_document_number ,
   first_position_mortgage_recorded_document_book_number ,
   first_position_mortgage_recorded_document_page_number ,
   first_position_mortgage_amount ,
   first_position_mortgage_loan_type_code ,
   first_position_mortgage_subordinate_type_code ,
   first_position_mortgage_interest_rate ,
   first_position_mortgage_interest_rate_type_code ,
   first_position_mortgage_term ,
   first_position_mortgage_term_code ,
   TRY(
    DATE_PARSE(
        NULLIF(
        REGEXP_REPLACE(
            REGEXP_REPLACE(
            REGEXP_REPLACE(
                REGEXP_REPLACE(
                REGEXP_EXTRACT("first_position_mortgage_maturity_date", '^[12][0-9]{7}$'),
                '(?<=.{6})00$', '01'
                ),
                '(?<=.{4})00(?=[0-9]{2}$)', '01'
            ),
            '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'
            ),
            '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'
        ),
        ''
        ),
    '%Y%m%d'
    )
    ) AS first_position_mortgage_maturity_date,
   transfer_date 
from s3.external_corelogic.vol_lien_status_m2_dpc
WHERE transfer_date = DATE '{{ transfer_date }}'
  AND MOD(ABS(from_big_endian_64(xxhash64(CAST(clip AS VARBINARY)))), 10) = {{ bucket_id }}
"""
