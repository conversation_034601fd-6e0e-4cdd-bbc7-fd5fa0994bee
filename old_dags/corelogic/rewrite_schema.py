import pandas as pd
import duckdb
import boto3
import botocore
from functools import cache


class S3:
    def __init__(self, env="local", s3_client: botocore.client = None):
        if s3_client is not None:
            self.s3 = s3_client
        else:
            if env == "local":
                endpoint_url = os.environ.get("AWS_ENDPOINT_URL_S3")
                if endpoint_url is None:
                    endpoint_url = "http://localhost:9000"
                self.s3 = boto3.client(
                    "s3",
                    endpoint_url=endpoint_url,
                    aws_access_key_id="admin",
                    aws_secret_access_key="password",
                    verify=False,
                )
            if env == "prod":
                self.s3 = boto3.client("s3")

    @cache
    def get_file_paths(self, bucket: str, prefix: str):
        paginator = self.s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
        file_paths = []
        for page in pages:
            paths = page.get("Contents", {})
            file_paths += paths
        return file_paths

    def __getattr__(self, name):
        """Pass along boto3 S3 methods to the instance of class S3."""
        return getattr(self.s3, name)


class Parquet(S3):
    def __init__(
        self, bucket, prefix, env="local", s3_client: botocore.client = None
    ):
        self.bucket = bucket
        self.test_bucket = "et-data-dev"
        self.prefix = prefix
        self.example_schema = []
        # self.source = "all_varchar"
        self.source = None

        super().__init__(env, s3_client)

    def get_duckdb_conn(self):
        sess = boto3.Session()
        creds = sess.get_credentials()
        access_key = creds.access_key
        secret_key = creds.secret_key
        token = creds.token
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs")
        conn.execute("LOAD httpfs")
        conn.execute("SET s3_region='us-east-1'")
        conn.execute(f"SET s3_access_key_id='{access_key}'")
        conn.execute(f"SET s3_secret_access_key='{secret_key}'")
        conn.execute(f"SET s3_session_token='{token}'")

        return conn

    def set_file_paths(self):
        paths = self.get_file_paths(self.bucket, self.prefix)
        files = [p for p in paths if not p["Key"].endswith("locations/")]
        contents = pd.DataFrame(files)
        contents["file_transfer_date"] = (
            contents.Key.str.split("/").str[2].str.split("=").str[-1]
        )
        contents.sort_values("file_transfer_date", inplace=True)
        print(f"min --> {contents.file_transfer_date.min()}")
        print(f"max --> {contents.file_transfer_date.max()}")

        self.contents = contents

    def set_example_schema(self):
        latest_object = self.contents.iloc[-1]
        print("latest_object --> ")
        print(f"Key: {latest_object.Key}")
        latest_url = f"s3://{self.bucket}/{latest_object.Key}"
        example_schema = self._get_schema(latest_url)
        if self.source == "all_varchar":
            example_schema = [(c[0], "VARCHAR", *c[2:]) for c in example_schema]

        print("latest schema -->")
        self._print_schema(example_schema)

        self.example_schema = example_schema

    def check_schemas(self):
        keys = self.contents.Key.to_list()
        schema_list = []
        for i, k in enumerate(keys):
            url = f"s3://{self.bucket}/{k}"
            schema = self._get_schema(url)
            if schema != self.example_schema:
                schema_list.append(schema)
                print("--------------------------")
                print(f"Index: {i}")
                print(f"Key: {self.contents.iloc[i].Key}")
                print(f"LastModified: {self.contents.iloc[i].LastModified}")
                self._print_schema(schema)
                print("--------------------------")

        return schema_list

    def check_nulls(self, col):
        keys = self.contents.Key.to_list()
        nulls_list = []
        for i, k in enumerate(keys):
            url = f"s3://{self.bucket}/{k}"
            if self._has_null_column(url, col) is True:
                nulls_lists.append(url)
                print("--------------------------")
                print(f"Index: {i}")
                print(f"Key: {self.contents.iloc[i].Key}")
                print(f"LastModified: {self.contents.iloc[i].LastModified}")
                print("--------------------------")

        return nulls_list

    def _get_type_for_column(self, column_name):
        for c in self.example_schema:
            if c[0] == column_name:
                return c[1]

    def _cast_query(self, url, current_schema):
        """Pull data and cast with new schema.

        Only cast columns that have a corresponding column in the current schema.
        """
        example_schema = self.example_schema
        query = "CREATE TABLE temporary AS SELECT"
        example_columns = [c[0] for c in example_schema]
        column_names = [c[0] for c in current_schema if c[0] in example_columns]
        # Cast all columns to the corresponding type from the example schema.
        for i, col in enumerate(column_names):
            col_type = self._get_type_for_column(col)
            query += f"\n CAST({col} AS {col_type}) AS {col},"
        # Remove last comma
        query = query[:-1]
        query += f"\n From read_parquet('{url}')"
        print("+++++---------------------+++++")
        print(query)
        print("+++++---------------------+++++")
        return query

    def _alter_column_query(self, col):
        # Alter column name.
        alter_query = f"""
        alter table temporary
        alter {col} type varchar
        """

        return alter_query

    def cast_parquet(self, url, current_schema, test_bucket=False):
        conn = self.get_duckdb_conn()
        transform_query = self._cast_query(url, current_schema)
        conn.execute(transform_query)
        # alter_query = self._alter_column_query("name")
        # conn.execute(alter_query)
        if test_bucket == True:
            bucket = self.test_bucket
            key = "/".join(url.split("/")[3:])
            url = f"s3://{bucket}/{key}"

        copy_query = f"COPY temporary TO '{url}'"
        conn.execute(copy_query)

        # Make way for the next one.
        drop_query = "drop table temporary"
        conn.execute(drop_query)

    def transform_schemas(self, limit=None, test_bucket=False) -> list[str]:
        keys = self.contents.Key.to_list()
        transformed_list = []
        bucket = self.bucket
        for i, k in enumerate(keys):
            url = f"s3://{bucket}/{k}"
            schema = self._get_schema(url)
            print(schema)
            if schema != self.example_schema:
                self.cast_parquet(url, schema, test_bucket)
                transformed_list.append(url)
                print("--------------------------")
                print(f"Index: {i}")
                print(f"Key: {self.contents.iloc[i].Key}")
                print(f"LastModified: {self.contents.iloc[i].LastModified}")
                print("--------------------------")
            if i == limit:
                return transformed_list

        return transformed_list

    def _get_schema(self, url):
        conn = self.get_duckdb_conn()
        # Create table with first row to get types.
        create_query = f"""
        create table temporary as select *
        from read_parquet('{url}')
        limit 1
        """
        conn.execute(create_query)

        # Describe
        describe_query = "describe temporary"
        res = conn.execute(describe_query)
        schema = res.fetchall()
        # Make way for the next one.
        drop_query = "drop table temporary"
        conn.execute(drop_query)

        return schema

    def _print_schema(self, schema):
        for col in schema:
            print(col[0], col[1])


def test():
    bucket = "et-data-staging"
    prefix = "external/customextract_v1_dpc"

    job = Parquet(bucket, prefix, "prod")

    job.set_file_paths()
    job.set_example_schema()
    return job

    return job.transform_schemas(limit=1, test_bucket=True)
