import json
from airflow.decorators import task
from airflow.providers.sftp.operators.sftp import SFTPOperator
from airflow.operators.python import PythonOperator
from airflow.providers.trino.hooks.trino  import TrinoHook
from airflow.utils.dates import days_ago
from etdag import ETDAG
# import polars as pl
import pandas as pd
from datetime import datetime

doc = """
`corelogic_homeshopper_stats` will transfer all of the data in the
`s3.gold_real_estate_intender.homeshopper_shopper_stats` table to the Corelogic
sftp server.

The underlying materialized view `s3.dev_business_intelligence.homeshopper_shopper_by_listing`
is updated on the 1st and 15th of each month at minute 0. This dag is scheduled to run 5 minutes
after the materialized view updates, and will execute a prepared statement to load in the newest
data to `s3.gold_real_estate_intender.homeshopper_shopper_stats`.
"""

default_args = {
    "owner": "Clay Morton",
}

with ETDAG(
    dag_id="corelogic_homeshopper_stats",
    description="Queries dataset from Trino, exports to parquet and delivers via sftp",
    default_args=default_args,
    start_date=datetime(2024, 3, 1),
    schedule_interval="5 0 1,15 * *",
    catchup=False,
    tags=["corelogic", "homeshopper"],
) as dag:

    dag.doc_md = doc

    @task()
    def transfer_stats():
        trino = TrinoHook(trino_conn_id="starburst")
        sql_file_path = "/opt/airflow/dags/repo/old_dags/corelogic/homeshopper_stats.sql"
        query = ""
        with open(sql_file_path) as f:
            query = f.read()
        trino.run(query)
        # Read the table.
        query = "select * from s3.gold_real_estate_intender.homeshopper_shopper_stats"
        df = trino.get_pandas_df(query)
        print("********************************")
        print(df.columns)
        print("********************************")
        # Write to csv.
        df.to_csv("/tmp/homeshopper.csv", header=True, index=False)

        transfer_data = SFTPOperator(
            task_id="file_to_sftp",
            ssh_conn_id="corelogic_sftp",
            local_filepath='/tmp/homeshopper.csv',
            remote_filepath="et_data_use_cases/biweekly_hs_stats/home_shopper_stats.csv",
            operation="put",
            create_intermediate_dirs=True,
            confirm=True,
            dag=dag,
        )

        transfer_data.execute(None)


    transfer_stats()
