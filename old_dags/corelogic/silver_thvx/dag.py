from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import BranchPythonOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.utils.dates import days_ago
from airflow.decorators import task
from airflow.providers.trino.hooks.trino import TrinoHook
from jinja2 import Template

from datetime import timedelta
from old_dags.corelogic.silver_thvx.queries import (
    FIND_MISSING_DATES,
    REMOVE_OLD_DATA,
    INSERT_OLYMPUS,
)

default_args = {
    "owner": "Bryan Price",
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id="silver_thvx",
    default_args=default_args,
    start_date=days_ago(1),
    schedule_interval="@daily",
    catchup=False,
    description="Parallel insert into Olympus Iceberg table using expand() + TrinoHook",
    tags=["corelogic", "olympus", "expand"],
) as dag:

    @task
    def compare_partitions():
        hook = TrinoHook(trino_conn_id="trino_conn")
        result = hook.get_records(FIND_MISSING_DATES)
        if not result:
            return {"action": "skip"}

        s3_max_date, olympus_max_date = result[0]
        print(f"S3 Max Date: {s3_max_date}, Olympus Max Date: {olympus_max_date}")

        if olympus_max_date is None:
            return {"action": "generate_chunk_params", "transfer_date": s3_max_date}
        elif s3_max_date > olympus_max_date:
            return {"action": "remove_then_insert", "transfer_date": s3_max_date}
        else:
            return {"action": "skip"}

    @task
    def process_data(**context):
        """Single task that handles all the logic"""
        partition_info = context["ti"].xcom_pull(task_ids="compare_partitions")
        action = partition_info["action"]

        print(f"Action: {action}")

        if action == "skip":
            print("Skipping processing - no new data")
            return []

        transfer_date = partition_info["transfer_date"]

        if action == "remove_then_insert":
            # Remove old data first
            print(f"Removing old data for date: {transfer_date}")
            hook = TrinoHook(trino_conn_id="trino_conn")
            hook.run(REMOVE_OLD_DATA)  # Uncomment when ready
            print("Old data removed")

        # Generate chunk parameters
        print(f"Generating chunks for date: {transfer_date}")
        return [{"bucket_id": i, "transfer_date": transfer_date} for i in range(10)]

    @task(max_active_tis_per_dag=2)
    def insert_chunk(partition_info: dict):
        if not partition_info:  # Handle empty case
            print("No data to process")
            return

        bucket_id = partition_info["bucket_id"]
        transfer_date = partition_info["transfer_date"]

        sql = Template(INSERT_OLYMPUS).render(
            bucket_id=bucket_id, transfer_date=transfer_date
        )

        print(f"Processing bucket {bucket_id} for date {transfer_date}")
        print(f"SQL to run: {sql}")
        TrinoHook(trino_conn_id="trino_conn").run(sql)  # Uncomment when ready
        print("Chunk processed successfully")

    # Task flow
    partition_info = compare_partitions()
    chunk_params = process_data()
    insert_tasks = insert_chunk.expand(partition_info=chunk_params)

    # Dependencies
    partition_info >> chunk_params >> insert_tasks
