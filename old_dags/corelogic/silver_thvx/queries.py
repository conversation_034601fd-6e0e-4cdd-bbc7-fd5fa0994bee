FIND_MISSING_DATES = """
SELECT
    s3_max.transfer_date AS s3_max_date,
    olympus_max.transfer_date AS olympus_max_date
FROM (
    SELECT MAX(transfer_date) AS transfer_date
    FROM "s3"."external_corelogic"."thvxcpf1_dpc$partitions"
) s3_max,
(
    SELECT MAX(partition.transfer_date) AS transfer_date
    FROM "olympus"."silver_corelogic"."thvxcpf1_dpc$partitions"
) olympus_max

"""


REMOVE_OLD_DATA = """
DELETE FROM "olympus"."silver_corelogic"."thvxcpf1_dpc"
"""


INSERT_OLYMPUS = """
INSERT INTO olympus.silver_corelogic.thvxcpf1_dpc
SELECT
    clip,
    previous_clip,
    fips_code,
    apn_parcel_number_unformatted,
    TRY_CAST(apn_sequence_number AS INTEGER),
    composite_property_linkage_key,
    original_apn,
    tax_account_number,
    online_formatted_parcel_id,
    alternate_parcel_id,

    TRY_CAST(situs_house_number AS INTEGER),
    situs_house_number_suffix,
    situs_house_number_2,
    situs_direction,
    situs_street_name,
    situs_mode,
    situs_quadrant,
    situs_unit_number,
    situs_city,
    situs_state,
    situs_zip_code,
    situs_county,
    situs_carrier_route,
    situs_street_address,
    situs_city_state_zip_source,

    land_use_code,
    county_land_use_description,
    state_land_use_description,
    manufactured_home_indicator,
    zoning_code,
    zoning_code_description,
    property_indicator_code,
    TRY_CAST(number_of_buildings AS INTEGER),

    owner_1_full_name,
    owner_1_last_name,
    owner_1_first_name_middle_initial,
    owner_2_full_name,
    owner_2_last_name,
    owner_2_first_name_middle_initial,

    TRY_CAST(mailing_house_number AS INTEGER),
    mailing_house_number_suffix,
    mailing_house_number_2,
    mailing_direction,
    mailing_street_name,
    mailing_mode,
    mailing_quadrant,
    mailing_unit_number,
    mailing_city,
    mailing_state,
    mailing_zip_code,
    mailing_carrier_route,
    mailing_street_address,
    owner_care_of_name,
    mailing_opt_out_indicator,

    -- handle date parsing from string format like '2025-05-12'
    CASE
        WHEN REGEXP_LIKE(last_market_sale_derived_date, '^[12][0-9]{7}$') THEN
            TRY(DATE_PARSE(last_market_sale_derived_date, '%Y%m%d'))
        WHEN REGEXP_LIKE(last_market_sale_derived_date, '^[12][0-9]{5}00$') THEN
            TRY(DATE_PARSE(SUBSTR(last_market_sale_derived_date, 1, 6) || '01', '%Y%m%d'))
        WHEN REGEXP_LIKE(last_market_sale_derived_date, '^[12][0-9]{3}00[0-9]{2}$') THEN
            TRY(DATE_PARSE(SUBSTR(last_market_sale_derived_date, 1, 4) || '01' || SUBSTR(last_market_sale_derived_date, 6, 2), '%Y%m%d'))
        WHEN REGEXP_LIKE(last_market_sale_derived_date, '^[12][0-9]{3}(1[3-9])[0-9]{2}$') THEN
            TRY(DATE_PARSE(SUBSTR(last_market_sale_derived_date, 1, 4) || '12' || SUBSTR(last_market_sale_derived_date, 6, 2), '%Y%m%d'))
        WHEN REGEXP_LIKE(last_market_sale_derived_date, '^[12][0-9]{5}(29|3[0-9]|[4-9][0-9])$') THEN
            TRY(DATE_PARSE(SUBSTR(last_market_sale_derived_date, 1, 6) || '28', '%Y%m%d'))
        ELSE NULL
    END AS last_market_sale_derived_date,
    TRY_CAST(last_market_sale_amount AS INTEGER),
    last_market_sale_multi_or_split_parcel_code,

    model_name_cons,
    model_version_cons,
    CASE
        WHEN REGEXP_LIKE(value_as_of_date_cons, '^[12][0-9]{7}$') THEN
            TRY(DATE_PARSE(value_as_of_date_cons, '%Y%m%d'))
        WHEN REGEXP_LIKE(value_as_of_date_cons, '^[12][0-9]{5}00$') THEN
            TRY(DATE_PARSE(SUBSTR(value_as_of_date_cons, 1, 6) || '01', '%Y%m%d'))
        WHEN REGEXP_LIKE(value_as_of_date_cons, '^[12][0-9]{3}00[0-9]{2}$') THEN
            TRY(DATE_PARSE(SUBSTR(value_as_of_date_cons, 1, 4) || '01' || SUBSTR(value_as_of_date_cons, 6, 2), '%Y%m%d'))
        WHEN REGEXP_LIKE(value_as_of_date_cons, '^[12][0-9]{3}(1[3-9])[0-9]{2}$') THEN
            TRY(DATE_PARSE(SUBSTR(value_as_of_date_cons, 1, 4) || '12' || SUBSTR(value_as_of_date_cons, 6, 2), '%Y%m%d'))
        WHEN REGEXP_LIKE(value_as_of_date_cons, '^[12][0-9]{5}(29|3[0-9]|[4-9][0-9])$') THEN
            TRY(DATE_PARSE(SUBSTR(value_as_of_date_cons, 1, 6) || '28', '%Y%m%d'))
        ELSE NULL
    END AS value_as_of_date_cons,
    TRY_CAST(estimated_value_cons AS INTEGER),
    TRY_CAST(estimated_value_high_cons AS INTEGER),
    TRY_CAST(estimated_value_low_cons AS INTEGER),
    TRY_CAST(confidence_score_cons AS INTEGER),
    TRY_CAST(forecast_standard_deviation_cons AS INTEGER),
    result_code_cons,
    result_description_cons,

    -- THVX past
    TRY_CAST(thvx_year_1_past_value AS INTEGER),
    TRY_CAST(thvx_year_2_past_value AS INTEGER),
    TRY_CAST(thvx_year_3_past_value AS INTEGER),
    TRY_CAST(thvx_year_4_past_value AS INTEGER),
    TRY_CAST(thvx_year_5_past_value AS INTEGER),
    TRY_CAST(thvx_year_6_past_value AS INTEGER),
    TRY_CAST(thvx_year_7_past_value AS INTEGER),
    TRY_CAST(thvx_year_8_past_value AS INTEGER),
    TRY_CAST(thvx_year_9_past_value AS INTEGER),
    TRY_CAST(thvx_year_10_past_value AS INTEGER),

    -- THVX forecast
    TRY_CAST(thvx_year_1_forecast_value AS INTEGER),
    TRY_CAST(thvx_year_2_forecast_value AS INTEGER),
    TRY_CAST(thvx_year_3_forecast_value AS INTEGER),
    TRY_CAST(thvx_year_4_forecast_value AS INTEGER),
    TRY_CAST(thvx_year_5_forecast_value AS INTEGER),
    TRY_CAST(thvx_year_10_forecast_value AS INTEGER),

    record_action_indicator,
    transfer_date

FROM s3.external_corelogic.thvxcpf1_dpc
WHERE transfer_date = DATE '{{ transfer_date }}'
  AND MOD(ABS(from_big_endian_64(xxhash64(CAST(clip AS VARBINARY)))), 10) = {{ bucket_id }}

"""
