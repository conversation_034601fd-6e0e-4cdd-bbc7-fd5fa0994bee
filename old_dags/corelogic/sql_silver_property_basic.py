SILVER_PROPERTY_BASIC2_MAT_VIEW = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS "olympus"."silver_corelogic"."property_basic2"
    WITH (
       format = 'PARQUET',
       format_version = 2,
       storage_schema = 'silver_corelogic'
    ) AS
    SELECT
      clip
    , previous_clip
    , fips_code
    , apn_parcel_number_unformatted
    , apn_sequence_number
    , composite_property_linkage_key
    , original_apn
    , tax_account_number
    , online_formatted_parcel_id
    , alternate_parcel_id
    , previous_parcel_number
    , previous_parcel_sequence_number
    , previous_parcel_number_formatted
    , census_id
    , range
    , township
    , section
    , quarter_section
    , municipality_name
    , municipality_code
    , jurisdiction_county_code
    , town_code
    , tax_rate_area_code
    , tax_district_county
    , municipality_tax_district
    , school_district
    , fire_district
    , water_district
    , elementary_school_district_county_description
    , high_school_district_county_description
    , neighborhood_description
    , situs_core_based_statistical_area_cbsa
    , cbsa_type
    , land_use_code
    , county_use_description
    , state_use_description
    , CAST((NULLIF(regexp_extract(mobile_home_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) mobile_home_indicator
    , zoning_code
    , zoning_code_description
    , property_indicator_code
    , CAST(NULLIF(regexp_extract(number_of_buildings, '^[0-9]{1,3}$'), '') AS INTEGER) number_of_buildings
    , view_code
    , location_influence_code
    , foreclosure_stage_code
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_replace(regexp_replace(regexp_replace(regexp_extract(last_foreclosure_transaction_date, '^[12][0-9]{7}$'), '(?<=.{6})00$', '01'), '(?<=.{4})00(?=[0-9]{2}$)', '01'), '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'), '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'), ''), '%Y%m%d') AS DATE) last_foreclosure_transaction_date
    , CAST(NULLIF(regexp_extract(block_level_latitude, '^[0-9]{2}\.[0-9]{6}$'), '') AS DECIMAL(10, 6)) block_level_latitude
    , CAST(NULLIF(regexp_extract(block_level_longitude, '^[0-9]{2}\.[0-9]{6}$'), '') AS DECIMAL(10, 6)) block_level_longitude
    , CAST(NULLIF(regexp_extract(parcel_level_latitude, '^[0-9]{2}\.[0-9]{6}$'), '') AS DECIMAL(10, 6)) parcel_level_latitude
    , CAST(NULLIF(regexp_extract(parcel_level_longitude, '^[0-9]{2}\.[0-9]{6}$'), '') AS DECIMAL(10, 6)) parcel_level_longitude
    , situs_house_number
    , situs_house_number_suffix
    , situs_house_number_2
    , situs_direction
    , situs_street_name
    , situs_mode
    , situs_quadrant
    , situs_unit_number
    , situs_city
    , situs_state
    , situs_zip_code
    , situs_county
    , situs_carrier_route
    , situs_street_address
    , situs_city_state_zip_source
    , situs_delivery_point_validation_code
    , situs_delivery_point_validation_vacant_indicator
    , legal_block_number
    , legal_block_number_suffix
    , legal_lot_number
    , legal_lot_number_suffix
    , subdivision_tract_number
    , subdivision_plat_book
    , subdivision_plat_page
    , subdivision_name
    , legal_description
    , owner_1_full_name
    , owner_1_last_name
    , owner_1_first_name_middle_initial
    , owner_2_full_name
    , owner_2_last_name
    , owner_2_first_name_middle_initial
    , CAST((NULLIF(regexp_extract(owner_1_corporate_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) owner_1_corporate_indicator
    , CAST((NULLIF(regexp_extract(owner_2_corporate_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) owner_2_corporate_indicator
    , owner_3_full_name
    , owner_3_last_name
    , owner_3_first_name_middle_initial
    , CAST((NULLIF(regexp_extract(owner_3_corporate_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) owner_3_corporate_indicator
    , owner_4_full_name
    , owner_4_last_name
    , owner_4_first_name_middle_initial
    , CAST((NULLIF(regexp_extract(owner_4_corporate_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) owner_4_corporate_indicator
    , owner_etal_code
    , owner_ownership_rights_code
    , owner_relationship_type_code
    , owner_occupancy_code
    , mailing_house_number
    , mailing_house_number_suffix
    , mailing_house_number_2
    , mailing_direction
    , mailing_street_name
    , mailing_mode
    , mailing_quadrant
    , mailing_unit_number
    , mailing_city
    , mailing_state
    , mailing_zip_code
    , mailing_carrier_route
    , mailing_street_address
    , owner_care_of_name
    , CAST((NULLIF(regexp_extract(mailing_opt_out_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) mailing_opt_out_indicator
    , mailing_delivery_point_validation_code
    , CAST(NULLIF(regexp_extract(mailing_delivery_point_validation_vacant_indicator, '^[01]$'), '') AS BOOLEAN) mailing_delivery_point_validation_vacant_indicator
    , CAST(NULLIF(regexp_extract(total_value_calculated, '^[0-9]{1,11}$'), '') AS BIGINT) total_value_calculated
    , CAST(NULLIF(regexp_extract(land_value_calculated, '^[0-9]{1,11}$'), '') AS BIGINT) land_value_calculated
    , CAST(NULLIF(regexp_extract(improvement_value_calculated, '^[0-9]{1,11}$'), '') AS BIGINT) improvement_value_calculated
    , calculated_value_source_indicator
    , CAST(NULLIF(regexp_extract(assessed_total_value, '^[0-9]{1,11}$'), '') AS BIGINT) assessed_total_value
    , CAST(NULLIF(regexp_extract(assessed_land_value, '^[0-9]{1,11}$'), '') AS BIGINT) assessed_land_value
    , CAST(NULLIF(regexp_extract(assessed_improvement_value, '^[0-9]{1,11}$'), '') AS BIGINT) assessed_improvement_value
    , CAST(NULLIF(regexp_extract(market_total_value, '^[0-9]{1,11}$'), '') AS BIGINT) market_total_value
    , CAST(NULLIF(regexp_extract(market_land_value, '^[0-9]{1,11}$'), '') AS BIGINT) market_land_value
    , CAST(NULLIF(regexp_extract(market_improvement_value, '^[0-9]{1,11}$'), '') AS BIGINT) market_improvement_value
    , CAST(NULLIF(regexp_extract(appraised_total_value, '^[0-9]{1,11}$'), '') AS BIGINT) appraised_total_value
    , CAST(NULLIF(regexp_extract(appraised_land_value, '^[0-9]{1,11}$'), '') AS BIGINT) appraised_land_value
    , CAST(NULLIF(regexp_extract(appraised_improvement_value, '^[0-9]{1,11}$'), '') AS BIGINT) appraised_improvement_value
    , CAST(NULLIF(regexp_extract(tax_amount, '^[0-9]{1,12}\.[0-9]{1,2}$'), '') AS DECIMAL(14, 2)) tax_amount
    , CAST(NULLIF(regexp_extract(tax_year, '^[12][0-9]{3}$'), '') AS INTEGER) tax_year
    , CAST(NULLIF(regexp_extract(assessed_year, '^[12][0-9]{3}$'), '') AS INTEGER) assessed_year
    , tax_area_code
    , CAST(NULLIF(regexp_extract(tax_total_rate_percent, '^[0-9]{1,4}\.[0-9]{1,3}$'), '') AS DECIMAL(7, 3)) tax_total_rate_percent
    , CAST(NULLIF(regexp_extract(taxable_improvement_value, '^[0-9]{1,9}$'), '') AS INTEGER) taxable_improvement_value
    , CAST(NULLIF(regexp_extract(taxable_land_value, '^[0-9]{1,9}$'), '') AS INTEGER) taxable_land_value
    , CAST(NULLIF(regexp_extract(taxable_other_value, '^[0-9]{1,9}$'), '') AS INTEGER) taxable_other_value
    , CAST(NULLIF(regexp_extract(net_taxable_amount, '^[0-9]{11}\.[0-9]{1,2}$'), '') AS DECIMAL(13, 2)) net_taxable_amount
    , CAST((NULLIF(regexp_extract(homestead_exempt_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) homestead_exempt_indicator
    , CAST((NULLIF(regexp_extract(senior_exempt_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) senior_exempt_indicator
    , CAST((NULLIF(regexp_extract(disabled_exempt_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) disabled_exempt_indicator
    , CAST((NULLIF(regexp_extract(veteran_exempt_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) veteran_exempt_indicator
    , CAST((NULLIF(regexp_extract(widow_exempt_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) widow_exempt_indicator
    , CAST(NULLIF(regexp_extract(exempt_value_amount, '^[0-9]{1,11}'), '') AS BIGINT) exempt_value_amount
    , CAST(NULLIF(regexp_extract(tax_exempt_amount_total, '^[0-9]{1,11}'), '') AS BIGINT) tax_exempt_amount_total
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_replace(regexp_replace(regexp_replace(regexp_extract(transaction_batch_date, '^[12][0-9]{7}$'), '(?<=.{6})00$', '01'), '(?<=.{4})00(?=[0-9]{2}$)', '01'), '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'), '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'), ''), '%Y%m%d') AS DATE) transaction_batch_date
    , CAST(NULLIF(regexp_extract(transaction_batch_sequence_number, '^[0-9]{1,5}$'), '') AS INTEGER) transaction_batch_sequence_number
    , multi_or_split_parcel_code
    , sale_recorded_document_number
    , sale_recorded_document_book_number
    , sale_recorded_document_page_number
    , sale_document_type_code
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_replace(regexp_replace(regexp_replace(regexp_extract(sale_recording_date, '^[12][0-9]{7}$'), '(?<=.{6})00$', '01'), '(?<=.{4})00(?=[0-9]{2}$)', '01'), '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'), '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'), ''), '%Y%m%d') AS DATE) sale_recording_date
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_replace(regexp_replace(regexp_replace(regexp_extract(sale_date, '^[12][0-9]{7}$'), '(?<=.{6})00$', '01'), '(?<=.{4})00(?=[0-9]{2}$)', '01'), '(?<=.{4})(1[3-9]|2[0-9]|3[01])(?=[0-9]{2}$)', '12'), '(?<=.{6})(2[9]|3[0-9]|[4-9][0-9])$', '28'), ''), '%Y%m%d') AS DATE) sale_date
    , CAST(NULLIF(regexp_extract(sale_amount, '^[0-9]{1,12}\.[0-9]{1,2}$'), '') AS DECIMAL(14, 2)) sale_amount
    , sale_code
    , transaction_type_code
    , title_company_code
    , title_company_name
    , CAST((NULLIF(regexp_extract(residential_model_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) residential_model_indicator
    , seller_name
    , CAST(NULLIF(regexp_extract(front_footage, '^[0-9]{1,8}\.[0-9]{1,4}$'), '') AS DECIMAL(12, 4)) front_footage
    , CAST(NULLIF(regexp_extract(depth_footage, '^[0-9]{1,8}\.[0-9]{1,4}$'), '') AS DECIMAL(12, 4)) depth_footage
    , CAST(NULLIF(regexp_extract(acres, '^[0-9]{1,10}\.[0-9]{1,4}$'), '') AS DECIMAL(14, 4)) acres
    , CAST(NULLIF(regexp_extract(land_square_footage, '^[0-9]{1,14}'), '') AS BIGINT) land_square_footage
    , easement_type_code
    , CAST(NULLIF(regexp_extract(year_built, '^[12][0-9]{3}$'), '') AS INTEGER) year_built
    , CAST(NULLIF(regexp_extract(effective_year_built, '^[12][0-9]{3}$'), '') AS INTEGER) effective_year_built
    , CAST(NULLIF(regexp_extract(bedrooms_all_buildings, '^[0-9]{1,5}$'), '') AS INTEGER) bedrooms_all_buildings
    , CAST(NULLIF(regexp_extract(total_rooms_all_buildings, '^[0-9]{1,5}$'), '') AS INTEGER) total_rooms_all_buildings
    , CAST(NULLIF(regexp_extract(total_bathrooms_all_buildings, '^[0-9]{1,3}$'), '') AS INTEGER) total_bathrooms_all_buildings
    , CAST(NULLIF(regexp_extract(number_of_bathrooms, '^[0-9]{5}\.[0-9]{1,2}$'), '') AS DECIMAL(7, 2)) number_of_bathrooms
    , CAST(NULLIF(regexp_extract(full_baths_all_buildings, '^[0-9]{1,5}$'), '') AS INTEGER) full_baths_all_buildings
    , CAST(NULLIF(regexp_extract(half_baths_all_buildings, '^[0-9]{1,5}$'), '') AS INTEGER) half_baths_all_buildings
    , CAST(NULLIF(regexp_extract("1qtr_baths_all_buildings", '^[0-9]{1,5}$'), '') AS INTEGER) "1qtr_baths_all_buildings"
    , CAST(NULLIF(regexp_extract("3qtr_baths_all_buildings", '^[0-9]{1,5}$'), '') AS INTEGER) "3qtr_baths_all_buildings"
    , CAST(NULLIF(regexp_extract(number_of_bath_fixtures, '^[0-9]{1,5}$'), '') AS INTEGER) number_of_bath_fixtures
    , CAST(NULLIF(regexp_extract(total_number_of_bath_fixtures_all_buildings, '^[0-9]{1,5}$'), '') AS INTEGER) total_number_of_bath_fixtures_all_buildings
    , air_conditioning_code
    , basement_finish_code
    , basement_type_code
    , building_code
    , building_improvement_code
    , building_improvement_condition_code
    , construction_type_code
    , exterior_wall_code
    , CAST((NULLIF(regexp_extract(fireplace_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) fireplace_indicator
    , number_of_fireplaces
    , fireplace_type_code
    , foundation_type_code
    , floor_type_code
    , frame_code
    , garage_code
    , heating_type_code
    , CAST(NULLIF(regexp_extract(number_of_parking_spaces, '^[0-9]{1,5}$'), '') AS INTEGER) number_of_parking_spaces
    , parking_type_code
    , CAST((NULLIF(regexp_extract(pool_indicator, '^Y$'), '') = 'Y') AS BOOLEAN) pool_indicator
    , pool_code
    , building_quality_code
    , roof_cover_code
    , roof_type_code
    , stories_code
    , CAST(NULLIF(regexp_extract(stories_number, '^[0-9]{1,4}\.[0-9]{1,2}$'), '') AS DECIMAL(6, 2)) stories_number
    , building_style_code
    , CAST(NULLIF(regexp_extract(number_of_units, '^[0-9]{1,5}$'), '') AS INTEGER) number_of_units
    , CAST(NULLIF(regexp_extract(universal_building_square_feet, '^[0-9]{1,9}$'), '') AS INTEGER) universal_building_square_feet
    , universal_building_square_feet_source_indicator_code
    , CAST(NULLIF(regexp_extract(building_square_feet, '^[0-9]{1,9}$'), '') AS INTEGER) building_square_feet
    , CAST(NULLIF(regexp_extract(living_square_feet_all_buildings, '^[0-9]{1,10}$'), '') AS BIGINT) living_square_feet_all_buildings
    , CAST(NULLIF(regexp_extract(ground_floor_square_feet, '^[0-9]{1,10}$'), '') AS BIGINT) ground_floor_square_feet
    , CAST(NULLIF(regexp_extract(building_gross_square_feet, '^[0-9]{1,10}$'), '') AS BIGINT) building_gross_square_feet
    , CAST(NULLIF(regexp_extract(adjusted_gross_square_feet, '^[0-9]{1,10}$'), '') AS BIGINT) adjusted_gross_square_feet
    , CAST(NULLIF(regexp_extract(basement_square_feet, '^[0-9]{1,10}$'), '') AS BIGINT) basement_square_feet
    , CAST(NULLIF(regexp_extract(garage_or_parking_square_feet, '^[0-9]{1,10}\.[0-9]{1,3}$'), '') AS DECIMAL(13, 3)) garage_or_parking_square_feet
    , CAST(NULLIF(regexp_extract(second_floor_square_feet, '^[0-9]{1,8}\.[0-9]{1,3}$'), '') AS DECIMAL(11, 3)) second_floor_square_feet
    , CAST(NULLIF(regexp_extract(finished_basement_square_feet, '^[0-9]{1,8}\.[0-9]{1,3}$'), '') AS DECIMAL(11, 3)) finished_basement_square_feet
    , CAST(NULLIF(regexp_extract(unfinished_basement_square_feet, '^[0-9]{1,8}\.[0-9]{1,3}$'), '') AS DECIMAL(11, 3)) unfinished_basement_square_feet
    , fuel_code
    , electricity_wiring_code
    , sewer_code
    , utilities_code
    , water_code
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_extract(last_assessor_update_date, '^[12][0-9]{3}-[0-9]{2}-[0-9]{2}$'), '(?<=.{4})(?<!1)-00(?!1)', '01'), ''), '%Y-%m-%d') AS DATE) last_assessor_update_date
    , TRY_CAST(date_parse(NULLIF(regexp_replace(regexp_extract(taxroll_certification_date, '^[12][0-9]{3}-[0-9]{2}-[0-9]{2}$'), '(?<=.{4})(?<!1)-00(?!1)', '01'), ''), '%Y-%m-%d') AS DATE) taxroll_certification_date
    , CAST(NULLIF(regexp_extract(total_square_footage_open_areas, '^[0-9]{1,11}\.[0-9]{1,4}$'), '') AS DECIMAL(15, 4)) total_square_footage_open_areas
    , CAST(NULLIF(regexp_extract(total_square_footage_all_buildings, '^[0-9]{1,11}\.[0-9]{1,4}$'), '') AS DECIMAL(15, 4)) total_square_footage_all_buildings
    , CAST(NULLIF(regexp_extract(total_square_footage_office_space, '^[0-9]{1,11}\.[0-9]{1,4}$'), '') AS DECIMAL(15, 4)) total_square_footage_office_space
    , CAST(NULLIF(regexp_extract(total_number_of_elevators, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_elevators
    , CAST(NULLIF(regexp_extract(total_number_of_loading_docks, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_loading_docks
    , CAST(NULLIF(regexp_extract(total_number_of_rail_spurs, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_rail_spurs
    , CAST(NULLIF(regexp_extract(total_number_of_truck_doors, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_truck_doors
    , CAST(NULLIF(regexp_extract(total_number_of_1_bedrooms, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_1_bedrooms
    , CAST(NULLIF(regexp_extract(total_number_of_2_bedrooms, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_2_bedrooms
    , CAST(NULLIF(regexp_extract(total_number_of_3_bedrooms, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_3_bedrooms
    , CAST(NULLIF(regexp_extract(total_number_of_efficiency_units, '^[0-9]{1,3}$'), '') AS INTEGER) total_number_of_efficiency_units
    , record_action_indicator
    , file_transfer_date date
    FROM
      (
       SELECT
         clip
       , previous_clip
       , fips_code
       , apn_parcel_number_unformatted
       , apn_sequence_number
       , composite_property_linkage_key
       , original_apn
       , tax_account_number
       , online_formatted_parcel_id
       , alternate_parcel_id
       , previous_parcel_number
       , previous_parcel_sequence_number
       , previous_parcel_number_formatted
       , census_id
       , range
       , township
       , section
       , quarter_section
       , municipality_name
       , municipality_code
       , jurisdiction_county_code
       , town_code
       , tax_rate_area_code
       , tax_district_county
       , municipality_tax_district
       , school_district
       , fire_district
       , water_district
       , elementary_school_district_county_description
       , high_school_district_county_description
       , neighborhood_description
       , situs_core_based_statistical_area_cbsa
       , cbsa_type
       , land_use_code
       , county_use_description
       , state_use_description
       , mobile_home_indicator
       , zoning_code
       , zoning_code_description
       , property_indicator_code
       , number_of_buildings
       , view_code
       , location_influence_code
       , foreclosure_stage_code
       , last_foreclosure_transaction_date
       , block_level_latitude
       , block_level_longitude
       , parcel_level_latitude
       , parcel_level_longitude
       , situs_house_number
       , situs_house_number_suffix
       , situs_house_number_2
       , situs_direction
       , situs_street_name
       , situs_mode
       , situs_quadrant
       , situs_unit_number
       , situs_city
       , situs_state
       , situs_zip_code
       , situs_county
       , situs_carrier_route
       , situs_street_address
       , situs_city_state_zip_source
       , situs_delivery_point_validation_code
       , situs_delivery_point_validation_vacant_indicator
       , legal_block_number
       , legal_block_number_suffix
       , legal_lot_number
       , legal_lot_number_suffix
       , subdivision_tract_number
       , subdivision_plat_book
       , subdivision_plat_page
       , subdivision_name
       , legal_description
       , owner_1_full_name
       , owner_1_last_name
       , owner_1_first_name_middle_initial
       , owner_2_full_name
       , owner_2_last_name
       , owner_2_first_name_middle_initial
       , owner_1_corporate_indicator
       , owner_2_corporate_indicator
       , owner_3_full_name
       , owner_3_last_name
       , owner_3_first_name_middle_initial
       , owner_3_corporate_indicator
       , owner_4_full_name
       , owner_4_last_name
       , owner_4_first_name_middle_initial
       , owner_4_corporate_indicator
       , owner_etal_code
       , owner_ownership_rights_code
       , owner_relationship_type_code
       , owner_occupancy_code
       , mailing_house_number
       , mailing_house_number_suffix
       , mailing_house_number_2
       , mailing_direction
       , mailing_street_name
       , mailing_mode
       , mailing_quadrant
       , mailing_unit_number
       , mailing_city
       , mailing_state
       , mailing_zip_code
       , mailing_carrier_route
       , mailing_street_address
       , owner_care_of_name
       , mailing_opt_out_indicator
       , mailing_delivery_point_validation_code
       , mailing_delivery_point_validation_vacant_indicator
       , total_value_calculated
       , land_value_calculated
       , improvement_value_calculated
       , calculated_value_source_indicator
       , assessed_total_value
       , assessed_land_value
       , assessed_improvement_value
       , market_total_value
       , market_land_value
       , market_improvement_value
       , appraised_total_value
       , appraised_land_value
       , appraised_improvement_value
       , tax_amount
       , tax_year
       , assessed_year
       , tax_area_code
       , tax_total_rate_percent
       , taxable_improvement_value
       , taxable_land_value
       , taxable_other_value
       , net_taxable_amount
       , homestead_exempt_indicator
       , senior_exempt_indicator
       , disabled_exempt_indicator
       , veteran_exempt_indicator
       , widow_exempt_indicator
       , exempt_value_amount
       , tax_exempt_amount_total
       , transaction_batch_date
       , transaction_batch_sequence_number
       , multi_or_split_parcel_code
       , sale_recorded_document_number
       , sale_recorded_document_book_number
       , sale_recorded_document_page_number
       , sale_document_type_code
       , sale_recording_date
       , sale_date
       , sale_amount
       , sale_code
       , transaction_type_code
       , title_company_code
       , title_company_name
       , residential_model_indicator
       , seller_name
       , front_footage
       , depth_footage
       , acres
       , land_square_footage
       , easement_type_code
       , year_built
       , effective_year_built
       , bedrooms_all_buildings
       , total_rooms_all_buildings
       , total_bathrooms_all_buildings
       , number_of_bathrooms
       , full_baths_all_buildings
       , half_baths_all_buildings
       , "1qtr_baths_all_buildings"
       , "3qtr_baths_all_buildings"
       , number_of_bath_fixtures
       , total_number_of_bath_fixtures_all_buildings
       , air_conditioning_code
       , basement_finish_code
       , basement_type_code
       , building_code
       , building_improvement_code
       , building_improvement_condition_code
       , construction_type_code
       , exterior_wall_code
       , fireplace_indicator
       , number_of_fireplaces
       , fireplace_type_code
       , foundation_type_code
       , floor_type_code
       , frame_code
       , garage_code
       , heating_type_code
       , number_of_parking_spaces
       , parking_type_code
       , pool_indicator
       , pool_code
       , building_quality_code
       , roof_cover_code
       , roof_type_code
       , stories_code
       , stories_number
       , building_style_code
       , number_of_units
       , universal_building_square_feet
       , universal_building_square_feet_source_indicator_code
       , building_square_feet
       , living_square_feet_all_buildings
       , ground_floor_square_feet
       , building_gross_square_feet
       , adjusted_gross_square_feet
       , basement_square_feet
       , garage_or_parking_square_feet
       , second_floor_square_feet
       , finished_basement_square_feet
       , unfinished_basement_square_feet
       , fuel_code
       , electricity_wiring_code
       , sewer_code
       , utilities_code
       , water_code
       , last_assessor_update_date
       , taxroll_certification_date
       , total_square_footage_open_areas
       , total_square_footage_all_buildings
       , total_square_footage_office_space
       , total_number_of_elevators
       , total_number_of_loading_docks
       , total_number_of_rail_spurs
       , total_number_of_truck_doors
       , total_number_of_1_bedrooms
       , total_number_of_2_bedrooms
       , total_number_of_3_bedrooms
       , total_number_of_efficiency_units
       , record_action_indicator
       , file_transfer_type
       , file_transfer_date
       , rank() OVER (PARTITION BY clip ORDER BY file_transfer_date DESC) rnk
       FROM
         olympus.bronze_corelogic.property_basic2
    ) 
    WHERE (rnk = 1)
"""

REFRESH_SILVER_PROPERTY_BASIC2 = """
    REFRESH MATERIALIZED VIEW "olympus"."silver_corelogic"."property_basic2"
"""

CREATE_MAT_VIEW_S3_ETHASH_BRIDGE = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS "s3"."silver_corelogic"."clip_ethash_bridge"
        WITH (
           grace_period = '15.00m',
           max_import_duration = '1.00h',
           run_as_invoker = true
        ) AS
        SELECT
          max(clip) clip
        , max(addressline) addressline
        , max(cityline) cityline
        , max(streetnumber) streetnumber
        , max(address) address
        , max(province) province
        , max(county) county
        , max(city) city
        , max(state) state
        , max(country) country
        , max(zipcode) zipcode
        , max(zip4) zip4
        , max(dataset) dataset
        , max(matchcode) matchcode
        , max(matchdescription) matchdescription
        , max(buildingname) buildingname
        , max(firmname) firmname
        , max(streetaddress) streetaddress
        , max(streetnamepredir) streetnamepredir
        , max(streetnamepostdir) streetnamepostdir
        , max(streetnameprefix) streetnameprefix
        , max(streetnamesuffix) streetnamesuffix
        , max(streetnamebase) streetnamebase
        , max(latitude) latitude
        , max(longitude) longitude
        , max(isbestname) isbestname
        , max(isintersection) isintersection
        , max(streetside) streetside
        , max(unitnumber) unitnumber
        , max(unittype) unittype
        , max(unitnumberlowerbound) unitnumberlowerbound
        , max(unitnumberupperbound) unitnumberupperbound
        , max(unitrangetype) unitrangetype
        , max(geocodesurfaceareawkb) geocodesurfaceareawkb
        , max(geometrywkb) geometrywkb
        , max(fipscode) fipscode
        , max(rdi) rdi
        , ethashv1
        , max(ethashv2) ethashv2
        , max(geocoded_date) geocoded_date
        FROM
          olympus.silver_corelogic.clip_ethash_bridge
        WHERE ((matchcode <> 'E999') AND (ethashv1 <> ''))
        GROUP BY ethashv1
"""

REFRESH_MAT_VIEW_S3_ETHASH_BRIDGE = """
    REFRESH MATERIALIZED VIEW "s3"."silver_corelogic"."clip_ethash_bridge"
"""