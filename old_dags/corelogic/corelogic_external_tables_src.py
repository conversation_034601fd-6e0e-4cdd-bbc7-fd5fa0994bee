import pandas as pd
import boto3
from zipfile import ZipFile
import os
import tempfile
import datetime as dt
from trino.dbapi import connect
from trino.auth import JWTAuthentication
import duckdb
import botocore
import re
import contextlib
from boto3.s3.transfer import TransferConfig
from pathlib import Path
from airflow.exceptions import AirflowFailException, AirflowSkipException
from airflow.providers.trino.hooks.trino import TrinoHook


class S3:
    """
    A utility class for interacting with Amazon S3, providing methods for file management,
    including listing, downloading, uploading, copying, and unzipping files.

    Attributes:
        s3 (botocore.client): The S3 client used for interacting with S3.
    """

    def __init__(self, env="local", s3_client: botocore.client = None):
        if s3_client is not None:
            self.s3 = s3_client
        else:
            if env == "local":
                endpoint_url = os.environ.get("AWS_ENDPOINT_URL_S3")
                if endpoint_url is None:
                    endpoint_url = "http://localhost:9000"
                self.s3 = boto3.client(
                    "s3",
                    endpoint_url=endpoint_url,
                    aws_access_key_id="admin",
                    aws_secret_access_key="password",
                    verify=False,
                )
            if env == "prod":
                self.s3 = boto3.client("s3")

    def get_files(self, bucket: str, prefix: str):
        paginator = self.s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
        file_paths = []
        for page in pages:
            paths = page.get("Contents", {})
            file_paths += paths
        return file_paths

    def download_file(self, bucket: str, key: str, download_path: str):
        """
        Download a file from S3 to a local directory.

        Args:
            bucket (str): The name of the S3 bucket.
            key (str): The S3 key of the file to download.
            download_path (str): The local path where the file will be downloaded.

        Notes:
            - Files larger than 5 GB are downloaded using multipart download.
        """
        print(f"Downloading file - {bucket}/{key}")
        file_size = self.get_file_size(bucket, key)
        if file_size <= 5 * 1024 * 1024 * 1024:  # 5 GB
            self.s3.download_file(bucket, key, download_path)
        else:
            # Use multipart download for large files
            config = TransferConfig(
                multipart_threshold=1024 * 1024 * 25,  # 25 MB
                max_concurrency=10,
                multipart_chunksize=1024 * 1024 * 25,  # 25 MB
                use_threads=True,
            )
            self.s3.download_file(bucket, key, download_path, Config=config)

        print("Download succeeded!")

    def upload_file(self, bucket, key, source_path):
        print(f"Uploading file - {bucket}/{key}")
        self.s3.upload_file(source_path, bucket, key)
        print("Upload succeeded!")

    def get_file_size(self, bucket, key):
        response = self.s3.head_object(Bucket=bucket, Key=key)
        return response["ContentLength"]

    def copy_file(self, source_bucket, source_key, dest_bucket, dest_key):
        """
        Copy a file from one S3 location to another.

        Args:
            source_bucket (str): The name of the source S3 bucket.
            source_key (str): The S3 key of the source file.
            dest_bucket (str): The name of the destination S3 bucket.
            dest_key (str): The S3 key where the file will be copied.

        Notes:
            - Files larger than 5 GB are copied using multipart upload.
        """
        file_size = self.get_file_size(source_bucket, source_key)
        if file_size <= 5 * 1024 * 1024 * 1024:  # 5 GB
            self.s3.copy_object(
                CopySource={"Bucket": source_bucket, "Key": source_key},
                Bucket=dest_bucket,
                Key=dest_key,
            )
        else:
            self.multipart_copy(source_bucket, source_key, dest_bucket, dest_key)

    def multipart_copy(self, source_bucket, source_key, dest_bucket, dest_key):
        """Copy a file from one S3 location to another using multipart upload."""
        copy_source = {"Bucket": source_bucket, "Key": source_key}

        # Configure Transfer
        config = TransferConfig(
            multipart_threshold=1024 * 1024 * 25,  # 25 MB
            max_concurrency=10,
            multipart_chunksize=1024 * 1024 * 25,  # 25 MB
            use_threads=True,
        )
        self.s3.copy(
            copy_source,
            dest_bucket,
            dest_key,
            Config=config,
        )
        print("Copy succeeded!")

    def unzip(self, file_path: str):
        """
        Unzip a file in the local directory.

        Args:
            file_path (str): The path of the zip file to unzip.

        Notes:
            - The contents are extracted to the same directory as the zip file.
            - The zip file is deleted after extraction.
        """
        print(f"Unzipping file - {file_path}")
        download_dir = "/".join(file_path.split("/")[:-1])
        with ZipFile(file_path, "r") as zip_ref:
            # Extract all the contents to the same directory
            zip_ref.extractall(download_dir)
        # Remove zip file
        os.remove(file_path)
        print("Unzip succeeded!")

    def upload_dir(self, bucket: str, prefix: str, download_dir: str):
        """
        Upload all files from a local directory to S3.

        Args:
            bucket (str): The name of the S3 bucket.
            prefix (str): The S3 prefix where the files will be uploaded.
            download_dir (str): The local directory containing the files to upload.

        Notes:
            - Files with a `.zip` extension are skipped.
            - Files are deleted from the local directory after upload.
        """
        for root, _dirs, files in os.walk(download_dir):
            for file in files:
                if file.endswith(".zip"):
                    continue
                source_path = f"{download_dir}/{file}"
                dest_key = f"{prefix}/{file}"
                self.upload_file(bucket, dest_key, source_path)

                # Remove file from temp directory.
                os.remove(os.path.join(root, file))


class Trino:

    def __init__(
        self,
        env: str = "dev",
        trino_conn_id: str = "trino_conn",
    ):
        self.env = env
        self.trino_conn_id = trino_conn_id
        self.hook = TrinoHook(trino_conn_id=self.trino_conn_id)

    def query(self, query: str) -> list[list[object]]:
        self.hook.run(query)
        return

    def create_table(
        self,
        table_name: str,
        schema: str,
        bucket: str,
        s3_prefix: str,
        col_defs: list[str],
        partition_cols: list[str],
    ):
        """
        Create a table in Trino with the specified schema, columns, and partitioning.

        Args:
            table_name (str): The name of the table to create.
            schema (str): The schema in which the table will be created.
            bucket (str): The S3 bucket where the table data is stored.
            s3_prefix (str): The S3 prefix for the table data.
            col_defs (list[str]): A list of column definitions (e.g., "column_name TYPE").
            partition_cols (list[str]): A list of partition columns.

        Notes:
            - The table is created with external storage in S3.
            - The partition columns must include at least one column.
        """

        table = f"s3.{schema}.{table_name}"
        create_schema_query = f"""
        CREATE SCHEMA IF NOT EXISTS s3.{schema}
        AUTHORIZATION USER admin
        WITH (
            location = 's3a://{bucket}/'
        )"""
        if self.env == "local":
            print(create_schema_query)
            # self.query(create_schema_query)
        partition = ""
        if len(partition_cols) == 0:
            raise ValueError("Must provide at least one partition column")
        partition = (
            f"ARRAY{partition_cols}"
            if len(partition_cols) == 1
            else f"ARRAY{partition_cols}"
        )

        columns = "\n             ".join(col_defs)

        is_parquet = s3_prefix.lower().endswith(".parquet")
        with_lines = [f"external_location = 's3a://{bucket}/{s3_prefix}'"]

        if is_parquet:
            with_lines.append("format            = 'PARQUET'")

        else:
            # assume txt.gz → CSV
            with_lines += [
                "format                   = 'CSV'",
                "csv_separator            = '|'",
                "csv_quote                = '\"'",
                "skip_header_line_count   = 1",
            ]

        # finally always add partitioned_by
        with_lines.append(f"partitioned_by = {partition}")

        # join them into one big WITH clause
        with_clause = ",\n    ".join(with_lines)

        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table} (
            {columns}
        )
        WITH (
            {with_clause}
        )
        """

        self.query(create_table_query)


class DuckDb:
    """
    A utility class for interacting with DuckDB, providing methods for connecting to S3
    and retrieving schema information from files.

    Methods:
        get_duckdb_conn(): Establish a DuckDB connection with S3 credentials.
        get_schema(file_url): Retrieve the schema of a file (e.g., Parquet or CSV) using DuckDB.
    """

    def get_duckdb_conn(self):
        sess = boto3.Session()
        creds = sess.get_credentials()
        access_key = creds.access_key
        secret_key = creds.secret_key
        token = creds.token
        conn = duckdb.connect()
        conn.execute("INSTALL httpfs")
        conn.execute("LOAD httpfs")
        conn.execute("SET s3_region='us-east-1'")
        conn.execute(f"SET s3_access_key_id='{access_key}'")
        conn.execute(f"SET s3_secret_access_key='{secret_key}'")
        if token is not None:
            conn.execute(f"SET s3_session_token='{token}'")

        return conn

    def get_schema(self, file_url):
        """
        Retrieve the schema of a file (e.g., Parquet or CSV) using DuckDB.

        Args:
            file_url (str): The S3 URL of the file.

        Returns:
            list[tuple]: A list of tuples representing the schema, where each tuple contains
                         the column name and data type.
        """
        conn = self.get_duckdb_conn()
        file_name = file_url.split("/")[-1]

        # Create a temporary table with the first row to infer schema
        create_query = ""
        if file_name.endswith(".parquet"):
            create_query = f"""
            create table temporary as select *
            from read_parquet('{file_url}')
            limit 1
            """
        elif file_name.endswith(".txt.gz") or file_name.endswith(".gz"):
            create_query = f"""
            create table temporary as select *
            from read_csv('{file_url}')
            limit 1
            """
        else:
            raise ValueError("Can't parse file extension for file url: \n {file_url}")

        conn.execute(create_query)

        # Retrieve schema information
        describe_query = "describe temporary"
        res = conn.execute(describe_query)
        schema = res.fetchall()

        # Make way for the next one.
        drop_query = "drop table temporary"
        conn.execute(drop_query)

        return schema


# TODO Possbily change the destination patteren here
class ExternalFiles:
    """The model for parsing the file path"""

    def __init__(self, file_path: str):
        """Parse the filename for the relevant attributes.

        The pattern of data attributes found in ;
            `<prefix>/<dataset>/<transfer_date=year-month-day>/<file_name>`
        Args:
        filename: str = Name of the file to parse.

        Example:
            'external/building_detail1/transfer_date=2024-03-08/\
            el_toro_vol_lien_status_m2_dpc_01447644_20240308_023000_data.txt.gz

        """
        destination_pattern = r"(?P<prefix>[^/]+)/(?P<dataset>[\w\_]+)/transfer_date=(?P<transfer_date>\d{4}-\d{2}-\d{2})/(?P<file_name>[\w\.\-\_]+)"
        match = re.match(destination_pattern, file_path)
        if match is None:
            raise ValueError(f"Path didn’t match expected pattern: {file_path}")

        self.prefix = match.group("prefix")  # 'external'
        self.dataset = match.group("dataset")  # 'building_detail1'
        self.transfer_date = match.group("transfer_date")  # '2024-03-08'
        self.file_name = match.group(
            "file_name"
        )  # el_toro_vol_lien_status_m2_dpc_01447644_20240308_023000_data.txt.gz


class ExternalTable:
    """
    A class for managing CoreLogic datasets in S3, including identifying unprocessed files,
    staging datasets, creating tables in Trino, and syncing metadata.

    Attributes:
        source_bucket (str): The S3 bucket where source files are stored.
        source_prefix (str): The prefix in the source bucket for CoreLogic datasets.
        dest_bucket (str): The S3 bucket where processed files are stored.
        dest_prefix (str): The prefix in the destination bucket for processed datasets.
        corelogic_datasets (list[str]): A list of supported CoreLogic datasets.
    """

    source_bucket = "eltoro-data-sources"
    source_prefix = "corelogic"
    dest_bucket = "et-data-staging"
    dest_prefix = "external"
    corelogic_datasets = [
        "building_detail1",
        "buildingpermit_3",
        "Digital_Audiences",
        "mortgage_basic3",
        "ownertransfer_v3",
        "propensityscore1_dpc",
        "property_basic2",
        "solarcon1_dpc",
        "thvxcpf1_dpc",
        "Trigger_Events",
        "vol_lien_status_m2_dpc",
    ]

    def __init__(
        self,
        dataset: str,
        env="dev",
        s3_client: botocore.client = None,
    ):
        """
        Initialize the ExternalTable instance.

        Args:
            dataset (str): The name of the dataset to process.
            env (str, optional): The environment in which the module is being run (local|prod). Defaults to "local".
            s3_client (botocore.client, optional): An optional S3 client instance. If not provided, a new client is created.
        """
        self.dataset = dataset
        self.source_files: pd.DataFrame = None
        self.dest_files: pd.DataFrame = None
        self.unprocessed_files: pd.DataFrame = None
        self.temp_dir = tempfile.TemporaryDirectory()
        self.last_schema = None
        self.last_version = None

        # Initialize utility classes
        self.s3 = S3(env=env, s3_client=s3_client)
        self.trino = Trino(env=env)
        self.duckdb = DuckDb()

    def set_source_files(self):
        """
        Retrieve source files from S3 for the configured dataset and create a DataFrame.
        Filters files based on the dataset name and extracts metadata such as transfer type and transfer date.
        """
        bucket = self.source_bucket
        prefix = f"{self.source_prefix}"  # Specify dataset in the prefix for more direct querying.
        paths = self.s3.get_files(bucket, prefix)
        files = {
            "dataset": [],
            "transfer_type": [],
            "transfer_date": [],
            "filename": [],
            "key": [],
            "file_size": [],
        }

        for fp in paths:
            filename = fp["Key"].split("/")[-1]

            if self.dataset not in filename:
                continue
            files["dataset"].append(self.dataset)

            # determine transfer_type
            transfer_type = next(
                (t for t in ["init", "full", "delta"] if t in filename), "full"
            )
            files["transfer_type"].append(transfer_type)

            # Extract transfer date
            transfer_date = self.get_transfer_date(filename, self.dataset)
            files["transfer_date"].append(transfer_date)

            # Add other file details
            files["filename"].append(filename)
            files["key"].append(fp["Key"])
            files["file_size"].append(fp["Size"])

        if files[
            "filename"
        ]:  # Ensure there is at least one file before creating a DataFrame.
            self.source_files = pd.DataFrame(files)
        else:
            self.source_files = pd.DataFrame(columns=list(files.keys()))
            raise AirflowFailException(
                f"No files matched the pattern for dataset: {self.dataset}"
            )
        self.source_files = self.source_files[~self.source_files.dataset.isna()]
        print("length of source_files", len(self.source_files))

    def get_transfer_date(self, filename: str, dataset) -> str | None:
        """
        Extract the transfer date from the filename using a regular expression.

        Args:
            filename (str): The name of the file.
            dataset (str): The dataset name.

        Returns:
            str | None: The transfer date in YYYYMMDD format, or None if not found.
        """
        date = None
        if dataset in ("Digital_Audiences", "Trigger_Events"):
            # Extract the last 8-digit number in the filename (e.g. 20250327)
            matches = re.findall(r"\d{8}", filename)
            if matches:
                # Try from the last match backward
                for match in reversed(matches):
                    try:
                        dt.datetime.strptime(match, "%Y%m%d")
                        date = match
                        break
                    except ValueError:
                        pass

        else:
            matches = re.findall(r"(?<=_)\d{8}(?=_)", filename)
            # Validate each date string and print it out as a datetime object
            for match in matches:
                try:
                    dt.datetime.strptime(match, "%Y%m%d")
                    date = match
                    break
                except ValueError:
                    pass

        return date

    def set_dest_files(self):
        """
        Retrieve destination files from S3 for the configured dataset and create a DataFrame.
        Extracts metadata such as transfer date and file size.
        """
        bucket = self.dest_bucket
        prefix = f"{self.dest_prefix}/{self.dataset}"  # Target only files under the specific dataset prefix
        paths = self.s3.get_files(bucket, prefix)
        files = {
            "dataset": [],
            "transfer_date": [],
            "filename": [],
            "key": [],
            "file_size": [],
        }

        for fp in paths:
            path = fp["Key"].split("/")

            dataset = path[
                1
            ]  # Ensuring this matches the expected dataset level in the path
            transfer_date_segment = next(
                (seg for seg in path if seg.startswith("transfer_date=")), None
            )
            if transfer_date_segment:
                date_str = transfer_date_segment.split("=", 1)[1]  # e.g. "2022-02-07"
                # Remove dashes to match the format from the source files (YYYYMMDD)
                transfer_date = date_str.replace("-", "")
            else:
                # Fallback if the folder with transfer date isn't present
                transfer_date = None

            filename = path[-1]
            files["dataset"].append(dataset)
            files["transfer_date"].append(transfer_date)
            files["filename"].append(filename)
            files["key"].append(fp["Key"])
            files["file_size"].append(fp["Size"])
        if files["filename"]:  # Only create a DataFrame if there are files
            self.dest_files = pd.DataFrame(files)
        else:
            self.dest_files = pd.DataFrame(columns=files.keys())
            print("No destination files found for dataset:", self.dataset)
        print("length of dest_files", len(self.dest_files))

    def _check_date_string(self, date: str):
        """Raise exception if date string doesn't match expected date format."""
        try:
            dt.datetime.strptime(date, "%Y%m%d")
        except ValueError:
            raise ValueError(f"Date string could not be parsed: {date}")

    def filter_source_files(self):
        """Filter out non-data files."""
        df = self.source_files
        for d in self.corelogic_datasets:
            # `customextract_v1_dpc` files are zipped.
            if d == "customextract_v1_dpc":
                data_only = (~df.filename.str.endswith("_data.zip")) | (
                    df.filename.str.contains("_counts_")
                )
                df = df[~((df.dataset == d) & data_only)]
            elif d in ["Digital_Audiences", "Trigger_Events"]:
                data_only = (~df.filename.str.endswith(".gz")) | (
                    df.filename.str.contains("_Counts_", case=False)
                )

                df = df[~((df.dataset == d) & data_only)]

            else:
                data_only = ~df.filename.str.endswith(".txt.gz")
                df = df[~((df.dataset == d) & data_only)]

        self.source_files = df

    def set_unprocessed_files(self):
        """
        Derive a list of all the unprocessed files from the source files.
        It performs a left join with source files on destination files using appropriate keys based on the file type.
        This method also handles cases where destination data might be empty.
        """
        self.set_source_files()
        self.filter_source_files()
        self.set_dest_files()
        sf = self.source_files
        df = self.dest_files

        if self.dest_files.empty:
            print("No destination files available.")
            self.unprocessed_files = self.source_files.copy()
            self.unprocessed_files["unprocessed"] = True
            return

        # Assuming the join key should be determined dynamically:
        result = pd.merge(
            sf,
            df,
            on=["transfer_date", "dataset"],
            how="left",
            suffixes=("_source", "_dest"),
            indicator=True,
        )

        # Filter to find only the rows where there was no match in the destination files
        result = result[result["_merge"] == "left_only"]

        # Remove the columns related to the merge process and reset index
        result.drop(
            columns=[
                col
                for col in result.columns
                if col.endswith("_dest") or col == "_merge"
            ],
            inplace=True,
        )
        result = result.sort_values(by=["transfer_date"])
        result.reset_index(drop=True, inplace=True)
        result = self.clean_merged_columns(result)
        print(f"Unprocessed files: {result.shape[0]}")
        self.unprocessed_files = result

    def clean_merged_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """The merge combines all the columns and labels the columns by appending
        the name of the source table; `_source` and `_dest` in this case.

        We are only interested in the files that need to be transfered so we will
        use the data from the source columns.
        """
        columns = df.columns
        # Remove columns with suffix "_dest".
        columns = [c for c in columns if not c.endswith("_dest")]
        df = pd.DataFrame(df, columns=columns)
        source_columns = [c for c in columns if c.endswith("_source")]
        # Remove suffix "_source".
        columns_clean = [c.replace("_source", "") for c in source_columns]
        new_columns = {}
        for i, c in enumerate(source_columns):
            new_columns[c] = columns_clean[i]
        df = df.rename(columns=new_columns)

        return df

    def stage_dataset(self, dataset, limit=None):
        """
        Iterate through each source file that has not been copied to the destination path.
        Handles both .zip and .txt.gz files appropriately.
        """
        if self.unprocessed_files.empty:
            raise AirflowSkipException(
                f"No unprocessed files found for dataset: {dataset}"
            )
        df = self.unprocessed_files

        df = df[df.dataset == dataset]
        df = df.reset_index(drop=True)
        source_bucket = self.source_bucket
        dest_bucket = self.dest_bucket
        temp_dir = self.temp_dir.name

        for i, row in df.iterrows():
            if limit is not None and i >= limit:
                break
            source_key = row["key"]
            download_path = f"{temp_dir}/{row['filename']}"

            # Handle different file types
            if row["filename"].endswith(".zip"):
                self.s3.download_file(source_bucket, source_key, download_path)
                self.s3.unzip(download_path)
                _version = self.find_schema_change(row)

                # Assuming find_schema_change and get_dest_prefix methods handle specifics related to .zip files
                dest_prefix = self.get_dest_prefix(row)
                self.s3.upload_dir(dest_bucket, dest_prefix, temp_dir)

            elif row["filename"].endswith(".txt.gz") or row["filename"].endswith(".gz"):
                self.s3.download_file(source_bucket, source_key, download_path)

                _version = self.find_schema_change(row)
                dest_prefix = self.get_dest_prefix(row)
                dest_key = f"{dest_prefix}/{row['filename']}"
                print(
                    f"Copying file - {source_bucket}/{source_key} to {dest_bucket}/{dest_key}"
                )
                self.s3.copy_file(source_bucket, source_key, dest_bucket, dest_key)


    def get_dest_prefix(self, file_row: pd.Series):
        """
        Generate a dynamic S3 destination prefix based on the dataset, file date, and other attributes defined in config.
        Uses the schema_template defined per dataset to format the prefix.
        """
        # Extract basic date info
        date = dt.datetime.strptime(file_row["transfer_date"], "%Y%m%d")
        year = str(date.year)
        month = str(date.month).zfill(2)
        day = str(date.day).zfill(2)

        formatted_prefix = (
            f"{self.dest_prefix}/{self.dataset}/transfer_date={year}-{month}-{day}"
        )
        return formatted_prefix

    def find_schema_change(self, row: pd.Series) -> str:
        """Find the schema for the current file.

        If the schema is different from the last one, raise an exception.
        """
        temp_dir = self.temp_dir.name
        dataset = self.dataset

        supported_exts = [".parquet", ".txt.gz", ".gz"]
        sample_file_path = ""

        # Recursively look for the first supported file type
        for p in Path(temp_dir).rglob("*"):
            if p.suffix in supported_exts or any(
                str(p).endswith(ext) for ext in supported_exts
            ):
                sample_file_path = str(p)
                break
        if not sample_file_path:
            raise FileNotFoundError(
                "No supported file (.parquet or .txt.gz) found after unzip"
            )

        # file_url = f"{self.temp_dir}/{row.filename}"
        # get schema for the new file
        row_schema = self.duckdb.get_schema(sample_file_path)

        # find the most recent staged file
        last_file_path = self.get_most_recent_file(dataset)
        if last_file_path is not None:
            last_s3 = f"s3://{self.dest_bucket}/{last_file_path}"
            old_schema = self.duckdb.get_schema(last_s3)

            new_cols = {name: dtype for name, dtype, *_ in row_schema}
            old_cols = {name: dtype for name, dtype, *_ in old_schema}

            for d in ("transfer_date",):
                new_cols.pop(d, None)
                old_cols.pop(d, None)

            # determine file type by extension
            is_parquet = sample_file_path.lower().endswith(".parquet")

            # compare colums to find added/removed columns
            added = set(new_cols) - set(old_cols)
            removed = set(old_cols) - set(new_cols)

            # look for type changs in parquet files
            changed = {}
            if is_parquet:
                changed = {
                    col: (old_cols[col], new_cols[col])
                    for col in set(new_cols) & set(old_cols)
                    if old_cols[col] != new_cols[col]
                }

            # print what you found
            if added or removed or changed:
                print("⚠️ Schema drift detected:")
                if added:
                    print("Added columns:")
                    for c in sorted(added):
                        print(f"{c} ({new_cols[c]})")
                    raise AirflowFailException(
                        f"Schema drift detected: Added columns {added}"
                        f" in file {sample_file_path}"
                    )
                if removed:
                    print(" Removed columns:")
                    for c in sorted(removed):
                        print(f"{c} ({old_cols[c]})")
                    raise AirflowFailException(
                        f"Schema drift detected: Removed columns {removed}"
                        f" in file {sample_file_path}"
                    )
                if is_parquet and changed:
                    print("Changed column types:")
                    for c, (old_t, new_t) in changed.items():
                        print(f"      {c}: {old_t} → {new_t}")
                    raise AirflowFailException(
                        f"Schema drift detected: Changed column type {changed}"
                        f"in {dataset} from {old_t} to {new_t}"
                        f"in file {sample_file_path}"
                    )
            else:
                print("Schema is the same")

    def get_most_recent_file(self, dataset) -> str | None:
        """Get the most recent file for the given dataset."""
        df = self.dest_files
        df = df[df.dataset == dataset]
        if df.shape[0] == 0:
            return None
        file_path = df.key.iloc[-1]
        print(f"most_recent_file -- {file_path}")

        return file_path

    def get_first_date(self, dataset) -> str:
        df = self.source_files
        df = df[df.dataset == dataset]
        first_date = str(df.transfer_date.min())
        # Test that the transfer date string matches the expedted date pattern.
        try:
            dt.datetime.strptime(first_date, "%Y%m%d")
        except ValueError:
            raise ValueError(f"First transfer date could not be parsed: {first_date}")

        return first_date

    def get_table_name(self, dataset: str):
        if dataset == "customextract_v1_dpc":
            dataset = "mls_listings_replace"

        table = f"{dataset}"  # Use the dataset name as the table name
        return table

    def sync_metadata(self, dataset: str):
        table = self.get_table_name(dataset)
        query = f"""
            CALL s3.system.sync_partition_metadata('external_corelogic', '{table}', 'ADD')
        """
        self.trino.query(query)

    def create_table(self, dataset):
        """
        Create a table in Trino for the dataset using the schema inferred from the destination files.

        Args:
            dataset (str): The dataset name.
        """

        def to_snake(s: str) -> str:
            s = s.lower()
            s = re.sub(r"[^0-9a-z]+", "_", s)
            s = re.sub(r"_+", "_", s)
            snake = s.strip("_")

            if snake and snake[0].isdigit():
                return f'"{snake}"'
            return snake

        partition_cols = ["transfer_date"]

        table = self.get_table_name(dataset)
        self.set_dest_files()

        if self.dest_files.empty:
            print("No destination files found. Cannot create table without schema.")
            return

        # Get the schema details from the last file in the destination files
        key = self.dest_files["key"].iloc[-1]
        s3_url = f"s3://{self.dest_bucket}/{key}"
        print(f"s3_url -- {s3_url}")
        col_defs = self.duckdb.get_schema(s3_url)

        # Exclude partition columns from the main column definitions and format types
        if s3_url.endswith(".parquet"):
            non_partition_col_defs = [
                f"{c[0].lower()} {c[1]},"
                for c in col_defs
                if c[0].lower() not in partition_cols
            ]
        else:
            # Assuming the file is a CSV or similar format
            non_partition_col_defs = [
                f"{to_snake(c[0])} VARCHAR,"
                for c in col_defs
                if c[0].lower() not in partition_cols
            ]

        # Prepare partition column definitions, setting 'transfer_date' specifically as a DATE
        partition_col_defs = [
            f"{col} DATE" if col == "transfer_date" else f"{col} VARCHAR,"
            for col in partition_cols
        ]

        schema = "external_corelogic"
        bucket = "et-data-staging"

        # Retrieve the prefix using the last key to avoid configuration issues
        path = ExternalFiles(key)
        s3_prefix = f"{path.prefix}/{dataset}"

        # Create table with the correct partitioning and data types
        self.trino.create_table(
            table_name=table,
            schema=schema,
            bucket=bucket,
            s3_prefix=s3_prefix,
            col_defs=non_partition_col_defs + partition_col_defs,
            partition_cols=partition_cols,
        )
