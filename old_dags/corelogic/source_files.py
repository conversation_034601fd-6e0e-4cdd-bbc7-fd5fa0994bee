"""A module to handle chunking of source files to be ingested by starburst."""
import boto3
import pandas as pd
from tempfile import TemporaryDirectory
from zipfile import Zip<PERSON>ile
from datetime import datetime
import os
import gzip
import shutil
import billiard as mp

s3_config_local = {
    "endpoint_url": "http://localhost:9002",
    "aws_access_key_id": "admin",
    "aws_secret_access_key": "password",
    "verify": False,
}


class SourceFiles:
    """Prepare large source files into gzipped chunks."""

    def __init__(
        self,
        env: str = "local",
    ):
        """Establish a session of with an S3 client.

        If an S3 congig object is not provided the environment variable will
        determine the type of client to set. In the case where this module is
        used by Airflow the S3Hook config should be supplied. If the environment
        is 'local' it will be assumed the client will be configured to access
        local minio storage. If the environment is 'prod' and no S3 config
        object is provided it will be assumed that the client will be configured
        to access the S3 account with credentials located in your home .aws
        directory.

        Args:
            s3_config (dict[str], optional): The config object used to access S3
                file storage.
            env (str, optional): The environment in which the module is being
                run (local|prod).
        """
        self.env = env
        self.source_bucket = "eltoro-data-sources"
        self.source_prefix = "corelogic"
        self.destination_bucket = f"et-datalake-corelogic-{env}"
        self.destination_prefix = "source"
        self.tmp_dir = TemporaryDirectory()
        self.tmp_dir_path = self.tmp_dir.name
        self.source_file_path = ""
        self.dataset = ""
        self.transfer_type = ""
        self.transfer_date = ""
        # Desired size of file chunks (3.5gb)
        self.target_bytes = 3.5 * 1e9
        if env == "local":
            self.s3 = boto3.client("s3", **s3_config_local)
        if env == "prod":
            self.s3 = boto3.client("s3")

    def process_data_source(
        self,
        dataset: str,
        transfer_type: str,
        transfer_date: str,
        target_bytes: int = None,
    ):
        """Main function."""
        self.dataset = dataset
        self.transfer_type = transfer_type
        self.transfer_date = transfer_date
        key = self.get_source_key(dataset, transfer_type, transfer_date)
        self.download_unzip_source(key)
        lines_per_chunk = self.estimate_lines_per_gzip(target_bytes)
        self.process_file_chunks(lines_per_chunk)
        # Cleanup files.
        self.tmp_dir.cleanup()

    def get_file_paths(self) -> list[dict[str]]:
        """Get file paths from S3 source_bucket/source_prefix."""
        paginator = self.s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(
            Bucket=self.source_bucket, Prefix=self.source_prefix
        )
        file_paths = []
        for page in pages:
            paths = page.get("Contents", {})
            file_paths += paths
        return file_paths

    def dataset_sources(self, paths: list[dict[str]]) -> pd.DataFrame:
        """Take a list of file paths and return a dataframe of dataset
        attributes."""
        data_paths = [
            [p["Key"], p["Size"]] for p in paths if "meta" not in p["Key"]
        ]
        datasets = [
            "building_detail1",
            "digital_audiences",
            "mortgage_basic3",
            "ownertransfer_v3",
            "propensityscore1_dpc",
            "property_basic2",
            "solarcon1_dpc",
            "thv_consumers_forecast",
            "thvxcpf1_dpc",
            "trigger_events",
            "vol_lien_status_ms1",
        ]
        transfer_types = ["init", "full", "delta"]
        files = {
            "dataset": [],
            "transfer_type": [],
            "transfer_date": [],
            "extension": [],
            "filesize": [],
            "key": [],
        }
        for key, filesize in data_paths:
            path = key.split("/")
            if len(path) != 2:
                continue
            filename = path[-1].lower()
            extension = filename.split(".")[1]
            if extension == "parquet":
                continue
            name_parts = filename.split(".")[0].split("_")
            dataset = None
            for d in datasets:
                if d in filename:
                    dataset = d
                    break
            if dataset is None:
                continue
            transfer_type = None
            transfer_date = None
            for p in name_parts:
                if p in transfer_types:
                    transfer_type = transfer_types[transfer_types.index(p)]
                if len(p) == 8:
                    try:
                        datetime.strptime(p, "%Y%m%d")
                        transfer_date = p
                    except ValueError:
                        pass

            if transfer_type is None:
                if extension == "zip":
                    transfer_type = "init"
                else:
                    transfer_type = "full"

            files["dataset"].append(dataset)
            files["transfer_type"].append(transfer_type)
            files["transfer_date"].append(transfer_date)
            files["extension"].append(extension)
            files["filesize"].append(filesize)
            files["key"].append(key)

        return pd.DataFrame(files)

    def get_source_key(
        self, dataset: str, transfer_type: str, transfer_date: str
    ) -> str:
        fps = self.get_file_paths()
        ds = self.dataset_sources(fps)
        key = (
            ds[
                (ds.dataset == dataset)
                & (ds.transfer_type == transfer_type)
                & (ds.transfer_date == transfer_date)
            ]
            .iloc[0]
            .key
        )

        return key

    def download_unzip_source(self, key: str):
        """Download, unzip, chunk, gzip and upload.

        This can be used when a zip file was received instead of gzip file was
        received from corelogic. The lambda will download the zip file extract
        the data into a new file and  then upload to the same destination.
        """
        bucket = self.source_bucket
        tmp_dir = self.tmp_dir_path
        download_filename = key.split("/")[-1]
        download_file_path = f"{tmp_dir}/{download_filename}"

        print(f"Downloading file -- s3://{bucket}/{key}")
        self.s3.download_file(bucket, key, download_file_path)

        print(f"Unzipping file -- s3://{bucket}/{key}")
        filename = ""
        if download_file_path.endswith("zip"):
            with ZipFile(download_file_path) as zip_ref:
                zip_ref.extractall(tmp_dir)
                filename = zip_ref.namelist()[0]
                self.source_file_path = f"{tmp_dir}/{filename}"
        elif download_file_path.endswith("gz"):
            with gzip.open(download_file_path, "rb") as f_in:
                with open(download_file_path.removesuffix("gz"), "wb") as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            self.source_file_path = download_file_path
        print(f"Finished downloading and unzipping file -- s3://{bucket}/{key}")

    @classmethod
    def gzip_chunk(cls, chunk, output_file_path) -> int:
        with gzip.open(output_file_path, "wb") as f_out:
            f_out.writelines(chunk)
        return os.path.getsize(output_file_path)

    def estimate_lines_per_gzip(self, target_bytes: int = None) -> int:
        """Calculate the number of lines of the source file required to produce
        a gzipped chunk the size of 'target_bytes'."""
        if target_bytes is None:
            target_bytes = self.target_bytes

        sample_lines = 1_000_000
        sample = []
        with open(self.source_file_path, "rb") as f:
            # Grab up to the first 1000,000 lines.
            for li in range(sample_lines):
                line = f.readline()
                if len(line) == 0:
                    sample_lines = li
                    break
                sample.append(f.readline())

        sample_path = f"{self.tmp_dir_path}/sample.csv"
        gzip_bytes = self.gzip_chunk(sample, sample_path)
        # Estimate number of lines that will produce a gzipped file with the
        # size of target bytes.
        lines_estimate = int(target_bytes * (sample_lines / gzip_bytes))
        print(f"Estimated number of lines per file - {lines_estimate}")

        return lines_estimate

    def process_file_chunks(self, lines_per_chunk: int) -> list[str]:
        """Unzip source file then gzip chunks."""
        file_path = self.source_file_path
        filename = file_path.split("/")[-1]
        name = f"{self.dataset}_{self.transfer_type}_{self.transfer_date}_data"
        extension = filename.split(".")[-1]
        with open(file_path, "rb") as f:
            header = f.readline()
            finished = False
            file_number = 0
            while not finished:
                file_number += 1
                file_chunk = [header]
                for i in range(lines_per_chunk):
                    line = f.readline()
                    if len(line) == 0:
                        finished = True
                        print("***** Finished reading source file. ********")
                        break
                    file_chunk.append(line)
                chunk_name = f"{name}-{file_number}.{extension}"
                chunk_path = f"{self.tmp_dir_path}/{chunk_name}.gz"
                self.gzip_upload_chunk(file_chunk, chunk_path)

    def gzip_upload_chunk(
        self,
        file_chunk: list[bytes],
        chunk_path: str,
    ):
        size = self.gzip_chunk(file_chunk, chunk_path)
        print(f"gzipped chunk size: {size/1_000_000}mb")
        bucket = f"et-datalake-corelogic-{self.env}"
        prefix = f"source/{self.dataset}/{self.transfer_type}/{self.transfer_date}"
        chunk_name = chunk_path.split("/")[-1]
        key = f"{prefix}/{chunk_name}"
        print(f"Uploading chunk - {key}")
        self.s3.upload_file(chunk_path, bucket, key)

    def process_file_chunks_threads(self, lines_per_chunk: int) -> list[str]:
        """Unzip source file then gzip chunks with multiprocessing threads."""
        file_path = self.source_file_path
        filename = file_path.split("/")[-1]
        name = f"{self.dataset}_{self.transfer_type}_{self.transfer_date}_data"
        extension = filename.split(".")[-1]
        with open(file_path, "rb") as f:
            header = f.readline()
            finished = False
            processes = []
            file_number = 0
            while not finished:
                file_number += 1
                file_chunk = [header]
                for i in range(lines_per_chunk):
                    line = f.readline()
                    if len(line) == 0:
                        finished = True
                        print("***** Finished reading source file. ********")
                        break
                    file_chunk.append(line)
                chunk_name = f"{name}-{file_number}.{extension}"
                chunk_path = f"{self.tmp_dir_path}/{chunk_name}.gz"
                # mp.set_start_method('forkserver')
                p = mp.Process(
                    target=SourceFiles.gzip_upload_chunk_threads,
                    args=(
                        file_chunk,
                        chunk_path,
                        self.env,
                        self.dataset,
                        self.transfer_type,
                        self.transfer_date,
                        s3_config_local,
                    ),
                )
                p.start()
                processes.append(p)

        for p in processes:
            p.join()

    @classmethod
    def gzip_upload_chunk_threads(
        cls,
        file_chunk: list[bytes],
        chunk_path: str,
        env: str,
        dataset: str,
        transfer_type: str,
        transfer_date: str,
        s3_config: dict[str],
    ):
        size = SourceFiles.gzip_chunk(file_chunk, chunk_path)
        print(f"gzipped chunk size: {size/1_000_000}mb")
        bucket = f"et-datalake-corelogic-{env}"
        prefix = f"source/{dataset}/{transfer_type}/{transfer_date}"
        chunk_name = chunk_path.split("/")[-1]
        key = f"{prefix}/{chunk_name}"
        s3 = boto3.client("s3", **s3_config)
        print(f"Uploading chunk - {key}")
        s3.upload_file(chunk_path, bucket, key)
