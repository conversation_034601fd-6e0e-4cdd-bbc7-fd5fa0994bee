from airflow import Dataset
from etdag import ETDAG
import datetime
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.common.sql.operators.sql import SQLExecute<PERSON>ueryOperator
from starburst_geocoder_operator import StarburstGeocoderOperator, supported_geocoder_columns
from old_dags.corelogic.bronze_silver_mls_listings_sql.insert_into_mls_listings_sale_w_geocode import (
    CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE,
    INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE
)
from old_dags.corelogic.bronze_silver_mls_listings_sql.insert_mls_listings_sale_w_geocode_metrics import (
    CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE_METRICS,
    INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE_METRICS
)
from old_dags.corelogic.bronze_silver_mls_listings_sql.insert_into_mls_listings_sale_integrated import (
    CHECK_BEFORE_INSERT_MLS_SALE_INTEGRATED,
    INSERT_INTO_MLS_LISTINGS_SALE_INTEGRATED
)


schedule=[Dataset("s3/external_corelogic/customextract_v1_dpc")]
default_args = {
    "owner": "Rorie Lizenby"
}
docs = """
"""


with (ETDAG(
    dag_id="bronze_silver_mls_listings",
    description="",
    start_date=datetime.datetime(2025, 2, 14),
    schedule=[Dataset("s3/external_corelogic/customextract_v1_dpc")],
    default_args=default_args,
    catchup=False,
    tags=["corelogic", "bronze", "silver", "mls_listings"],
) as dag):
    dag.doc_md = docs

    EXTERNAL_TABLE_NAME = '"s3"."external_corelogic"."mls_listings_replace_v20241217"'
    ETHASH_BRIDGE_TABLE_NAME = '"olympus"."bronze_corelogic"."mls_listings_ethash_bridge"'
    BRONZE_MLS_LISTINGS_SALE_W_GEOCODE_TABLE_NAME = '"olympus"."bronze_corelogic"."mls_listings_sale_w_geocode"'

    sb_geocoder = StarburstGeocoderOperator(
        task_id="geocode_mls_listings",
        source_table_name=EXTERNAL_TABLE_NAME,
        source_row_identifier_column_name="CONCAT(situs_zip_code, '_', listing_id)",
        source_row_alias_column_name="listing_id",
        address1_column_name="situs_street_address",
        zipcode_column_name="situs_zip_code",
        bridge_table_name=ETHASH_BRIDGE_TABLE_NAME,
        geocoder_columns=list(supported_geocoder_columns.keys()),
        source_where_clause= f"""
            year = (SELECT year from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
            AND month = (SELECT month from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
            AND day = (SELECT day from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
        """
    )

    def check_partition1(**context):
        trino_hook = TrinoHook("starburst")
        records = trino_hook.get_records(CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE)
        if records[0][0] > 0:
            return "skip_insert_into_mls_listings_sale_w_geocode"
        else:
            return "insert_into_mls_listings_sale_w_geocode"

    check_partition_task1 = BranchPythonOperator(
        task_id="check_partition1",
        python_callable=check_partition1
    )

    insert_into_mls_listings_sale_w_geocode = SQLExecuteQueryOperator(
        task_id="insert_into_mls_listings_sale_w_geocode",
        conn_id="starburst",
        sql=INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE,
        split_statements=True,
        return_last=False,
    )

    skip_insert1 = DummyOperator(task_id="skip_insert_into_mls_listings_sale_w_geocode")
    join_after_geocode_step = DummyOperator(
        task_id="join_after_geocode_step",
        trigger_rule="none_failed_min_one_success"
    )


    def check_partition2():
        trino_hook = TrinoHook("starburst")
        records = trino_hook.get_records(CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE_METRICS)
        if records[0][0] > 0:
            return "skip_insert_into_mls_listings_sale_w_geocode_metrics"
        else:
            return "insert_into_mls_listings_sale_w_geocode_metrics"

    check_partition_task2 = BranchPythonOperator(
        task_id="check_partition2",
        python_callable=check_partition2
    )

    insert_into_mls_listings_sale_w_geocode_metrics = SQLExecuteQueryOperator(
        task_id="insert_into_mls_listings_sale_w_geocode_metrics",
        conn_id="starburst",
        sql=INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE_METRICS,
        split_statements=True,
        return_last=False,
    )

    skip_insert2 = DummyOperator(task_id="skip_insert_into_mls_listings_sale_w_geocode_metrics")
    join_after_metrics_step = DummyOperator(
        task_id="join_after_metrics_step",
        trigger_rule="none_failed_min_one_success"
    )


    def check_partition3():
        trino_hook = TrinoHook("starburst")
        records = trino_hook.get_records(CHECK_BEFORE_INSERT_MLS_SALE_INTEGRATED)
        if records[0][0] > 0:
            return "skip_insert_into_mls_listings_integrated"
        else:
            return "insert_into_mls_listings_integrated"

    check_partition_task3 = BranchPythonOperator(
        task_id="check_partition3",
        python_callable=check_partition3
    )

    insert_into_mls_listings_integrated = SQLExecuteQueryOperator(
        task_id="insert_into_mls_listings_integrated",
        conn_id="starburst",
        sql=INSERT_INTO_MLS_LISTINGS_SALE_INTEGRATED,
        split_statements=True,
        return_last=False,
    )

    skip_insert3 = DummyOperator(task_id="skip_insert_into_mls_listings_integrated")
    join_after_integrated_step = DummyOperator(
        task_id="join_after_integrated_step",
        trigger_rule="none_failed_min_one_success"
    )






    sb_geocoder >> check_partition_task1 >> [insert_into_mls_listings_sale_w_geocode, skip_insert1] >> join_after_geocode_step
    join_after_geocode_step >> check_partition_task2 >> [insert_into_mls_listings_sale_w_geocode_metrics, skip_insert2] >> join_after_metrics_step
    join_after_metrics_step >> check_partition_task3 >> [insert_into_mls_listings_integrated, skip_insert3] >> join_after_integrated_step