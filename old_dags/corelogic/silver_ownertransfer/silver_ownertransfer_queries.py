LOAD_SILVER_TABLE = """
INSERT INTO olympus.silver_corelogic.ownertransfer_v2 (  
    clip,
    previous_clip,
    fips_code,
    apn_parcel_number_unformatted,
    apn_sequence_number,
    composite_property_linkage_key,
    original_apn,
    tax_account_number,
    online_formatted_parcel_id,
    land_use_code_static,
    county_use_description_static,
    state_use_description_static,
    mobile_home_indicator,
    zoning_code_static,
    property_indicator_code_static,
    actual_year_built_static,
    effective_year_built_static,
    total_number_of_buildings,
    deed_situs_house_number_static,
    deed_situs_house_number_suffix_static,
    deed_situs_house_number_2_static,
    deed_situs_direction_static,
    deed_situs_street_name_static,
    deed_situs_mode_static,
    deed_situs_quadrant_static,
    deed_situs_unit_number_static,
    deed_situs_city_static,
    deed_situs_state_static,
    deed_situs_zip_code_static,
    deed_situs_county_static,
    deed_situs_carrier_route_static,
    deed_situs_street_address_static,
    standardized_address_confidence_code,
    transaction_fips_code,
    owner_transfer_composite_transaction_id,
    transaction_batch_date,
    transaction_batch_sequence_number,
    pending_record_indicator,
    multi_or_split_parcel_code,
    primary_category_code,
    deed_category_type_code,
    sale_type_code,
    sale_amount,
    sale_derived_date,
    sale_derived_recording_date,
    sale_document_type_code,
    sale_recorded_document_number,
    sale_recorded_document_book_number,
    sale_recorded_document_page_number,
    ownership_transfer_percentage,
    title_company_name,
    title_company_code,
    cash_purchase_indicator,
    mortgage_purchase_indicator,
    interfamily_related_indicator,
    investor_purchase_indicator,
    resale_indicator,
    new_construction_indicator,
    residential_indicator,
    short_sale_indicator,
    foreclosure_reo_indicator,
    foreclosure_reo_sale_indicator,
    buyer_1_full_name,
    buyer_1_last_name,
    buyer_1_first_name_and_middle_initial,
    buyer_2_full_name,
    buyer_2_last_name,
    buyer_2_first_name_and_middle_initial,
    buyer_1_corporate_indicator,
    buyer_2_corporate_indicator,
    buyer_3_full_name,
    buyer_3_last_name,
    buyer_3_first_name_and_middle_initial,
    buyer_3_corporate_indicator,
    buyer_4_full_name,
    buyer_4_last_name,
    buyer_4_first_name_and_middle_initial,
    buyer_4_corporate_indicator,
    buyer_etal_code,
    buyer_ownership_rights_code,
    buyer_relationship_type_code,
    buyer_care_of_name,
    buyer_occupancy_code,
    partial_interest_indicator,
    buyer_mailing_house_number,
    buyer_mailing_house_number_suffix,
    buyer_mailing_house_number_2,
    buyer_mailing_direction,
    buyer_mailing_street_name,
    buyer_mailing_mode,
    buyer_mailing_quadrant,
    buyer_mailing_unit_number,
    buyer_mailing_city,
    buyer_mailing_state,
    buyer_mailing_zip_code,
    buyer_mailing_carrier_route,
    buyer_mailing_street_address,
    buyer_mailing_opt_out_indicator,
    seller_1_full_name,
    seller_1_last_name,
    seller_1_first_name,
    seller_2_full_name,
    record_action_indicator,
    transfer_date,
    formatted_address,
    ethash_date,
    ethash_v1,
    ethash_v2
)
SELECT
    clip,
    previous_clip,
    fips_code,
    apn_parcel_number_unformatted,
    TRY_CAST(apn_sequence_number AS INTEGER),
    composite_property_linkage_key,
    original_apn,
    tax_account_number,
    online_formatted_parcel_id,
    land_use_code_static,
    county_use_description_static,
    state_use_description_static,
    mobile_home_indicator,
    zoning_code_static,
    property_indicator_code_static,
    actual_year_built_static,
    effective_year_built_static,
    TRY_CAST(total_number_of_buildings AS INTEGER),
    deed_situs_house_number_static,
    deed_situs_house_number_suffix_static,
    deed_situs_house_number_2_static,
    deed_situs_direction_static,
    deed_situs_street_name_static,
    deed_situs_mode_static,
    deed_situs_quadrant_static,
    deed_situs_unit_number_static,
    deed_situs_city_static,
    deed_situs_state_static,
    deed_situs_zip_code_static,
    deed_situs_county_static,
    deed_situs_carrier_route_static,
    deed_situs_street_address_static,
    standardized_address_confidence_code,
    transaction_fips_code,
    owner_transfer_composite_transaction_id,
    TRY_CAST(transaction_batch_date AS DATE),
    TRY_CAST(transaction_batch_sequence_number AS INTEGER),
    pending_record_indicator,
    multi_or_split_parcel_code,
    primary_category_code,
    deed_category_type_code,
    sale_type_code,
    TRY_CAST(sale_amount AS DOUBLE),
    TRY_CAST(sale_derived_date AS DATE),
    TRY_CAST(sale_derived_recording_date AS DATE),
    sale_document_type_code,
    sale_recorded_document_number,
    sale_recorded_document_book_number,
    sale_recorded_document_page_number,
    TRY_CAST(ownership_transfer_percentage AS INTEGER),
    title_company_name,
    title_company_code,
    cash_purchase_indicator,
    mortgage_purchase_indicator,
    interfamily_related_indicator,
    investor_purchase_indicator,
    resale_indicator,
    new_construction_indicator,
    residential_indicator,
    short_sale_indicator,
    foreclosure_reo_indicator,
    foreclosure_reo_sale_indicator,
    buyer_1_full_name,
    buyer_1_last_name,
    buyer_1_first_name_and_middle_initial,
    buyer_2_full_name,
    buyer_2_last_name,
    buyer_2_first_name_and_middle_initial,
    buyer_1_corporate_indicator,
    buyer_2_corporate_indicator,
    buyer_3_full_name,
    buyer_3_last_name,
    buyer_3_first_name_and_middle_initial,
    buyer_3_corporate_indicator,
    buyer_4_full_name,
    buyer_4_last_name,
    buyer_4_first_name_and_middle_initial,
    buyer_4_corporate_indicator,
    buyer_etal_code,
    buyer_ownership_rights_code,
    buyer_relationship_type_code,
    buyer_care_of_name,
    buyer_occupancy_code,
    partial_interest_indicator,
    buyer_mailing_house_number,
    buyer_mailing_house_number_suffix,
    buyer_mailing_house_number_2,
    buyer_mailing_direction,
    buyer_mailing_street_name,
    buyer_mailing_mode,
    buyer_mailing_quadrant,
    buyer_mailing_unit_number,
    buyer_mailing_city,
    buyer_mailing_state,
    buyer_mailing_zip_code,
    buyer_mailing_carrier_route,
    buyer_mailing_street_address,
    buyer_mailing_opt_out_indicator,
    seller_1_full_name,
    seller_1_last_name,
    seller_1_first_name,
    seller_2_full_name,
    record_action_indicator,
    transfer_date,
    CONCAT(
        deed_situs_street_address_static, ',',
        deed_situs_city_static, ',',
        deed_situs_state_static, ' ',
        deed_situs_zip_code_static
    ) AS formatted_address,
    NULL AS ethash_date,
    NULL AS ethash_v1,
    NULL AS ethash_v2
FROM s3.external_corelogic.ownertransfer_v3
WHERE transfer_date = DATE('{transfer_date}')
"""

MERGE_ETHASH_BRIDGE_TABLE = """
MERGE INTO olympus.silver_corelogic.ownertransfer_v2 t
USING (
    SELECT clip, ethashv1, ethashv2, geocoded_date
    FROM olympus.silver_corelogic.clip_ethash_bridge
) b
ON t.clip = b.clip
WHEN MATCHED THEN UPDATE SET
    ethash_v1 = b.ethashv1,
    ethash_v2 = b.ethashv2,
    ethash_date = b.geocoded_date
"""

SILVER_PARTITION = """
    SELECT
        partition.transfer_date AS partition_date
    FROM olympus.silver_corelogic."ownertransfer_v2$partitions"
    WHERE partition.transfer_date >= DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
    ORDER BY partition.transfer_date DESC
    """

BRONZE_PARTITION = """
    SELECT * FROM s3.external_corelogic."ownertransfer_v3$partitions"  WHERE TRY_CAST(transfer_date AS DATE) >= DATE('{{ dag_run.conf.get("run_date", params.run_date) }}')
    """
