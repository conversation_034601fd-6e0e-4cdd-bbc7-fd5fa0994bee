from airflow.decorators import task, task_group
from etdag import ETDAG
from datetime import datetime
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.corelogic.silver_ownertransfer.silver_ownertransfer_queries import (
    SILVER_PARTITION,
    BRONZE_PARTITION,
    MERGE_ETHASH_BRIDGE_TABLE,
    LOAD_SILVER_TABLE,
)
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
import re
from airflow.utils.dates import timedelta
import pendulum
from airflow.exceptions import AirflowSkipException, AirflowException
import logging


logging.basicConfig()
logger = logging.getLogger(__name__)

docs = """
### CoreLogic Silver OwnerTransfer DAG
DAG Name: silver_ownertransfer_dag

FALIURE HANDLING:
    - If the retry fails, please run the entire DAG again.

Description:
    This Airflow DAG stages CoreLogic owner transfer datasets into an Olympus "silver" table.
    It compares partition dates between the "bronze" and "silver" tables to identify missing transfer dates.
    For each missing date, it loads the relevant data into the silver table and performs a join operation with a bridge table.

Key Features:
    - Dynamically identifies transfer dates present in the bronze table but missing in the silver table.
    - Loads missing partitions into the silver table using Trino.
    - Joins the loaded data with an ethash bridge table to enrich the dataset.
    - Skips processing if no new dates are found.
    - Includes error handling and logging for data load and join steps.
    - Designed for idempotency and safe re-runs.
    - Can use Params to backfill after certain dates

Parameters:
    - run_date: The date for which the DAG should process data (defaults to yesterday in EST).

Tags:
    - corelogic
    - silver
    - olympus
    - DND

Owner:
    Bryan Price

"""
default_args = {
    "depends_on_past": False,
    "start_date": datetime(2025, 5, 18),
    "owner": "Bryan Price",
    "email_on_failure": False,
    "email_on_retry": False,
    "retry_delay": timedelta(minutes=1),
    "retries": 1,
}


@task()
def compare_transfer_dates(
    silver_partitions: list, bronze_partitions: list
) -> list[str]:
    """
    Compares a partition date with a run date and determines if an action is needed.

    Returns:
        list: A list of dates that are in bronze but not in silver.
    """
    logger.info("Comparing partition date with run date...")

    raw_silver = [item[0] for item in silver_partitions if item]

    raw_bronze = [item[0] for item in bronze_partitions if item]
    print(f"Raw Silver: {raw_silver}")
    print(f"Raw Bronze: {raw_bronze}")

    # Find dates that are in raw_bronze but not in raw_silver
    missing_dates = list(set(raw_bronze) - set(raw_silver))
    logger.info(f"Missing dates (in bronze but not in silver): {missing_dates}")
    if not missing_dates:
        logger.info("No missing dates found. Skipping load_and_join_group.")
        raise AirflowSkipException(
            "No missing dates found. Skipping load_and_join_group."
        )
    return sorted(missing_dates)


@task(
    max_active_tis_per_dag=1,
)
def load_and_join_group(missing_date: str):
    print(missing_date)
    tr = TrinoHook(trino_conn_id="trino_conn")
    query = f"""
        SELECT partition.transfer_date AS partition_date
        FROM olympus.silver_corelogic."ownertransfer_v2$partitions"
        WHERE partition.transfer_date = DATE('{missing_date}')
    """
    try:
        logger.info(f"Checking if partition exists for date: {missing_date}")
        result = tr.get_records(sql=query)
        partition_date = result[0][0] if result and result[0] else None
    except Exception as e:
        logger.error(f"Failed to check partition for {missing_date}: {e}")
        raise AirflowException(f"Partition check failed for {missing_date}")

    if partition_date:
        logger.info(f"Partition already exists for {missing_date}, skipping load.")
        try:
            logger.info(f"Joining bridge table for date: {missing_date}")
            tr.run(sql=MERGE_ETHASH_BRIDGE_TABLE)
        except Exception as e:
            logger.error(f"Failed to join bridge table for {missing_date}: {e}")
            raise AirflowException(f"Join step failed for {missing_date}")
    else:
        try:
            logger.info(f"Loading silver data for date: {missing_date}")
            tr.run(sql=LOAD_SILVER_TABLE.format(transfer_date=missing_date))
        except Exception as e:
            logger.error(f"Failed to insert into silver table for {missing_date}: {e}")
            raise AirflowException(f"Load step failed for {missing_date}")

        try:
            logger.info(f"Joining bridge table for date: {missing_date}")
            tr.run(sql=MERGE_ETHASH_BRIDGE_TABLE)
        except Exception as e:
            logger.error(f"Failed to join bridge table for {missing_date}: {e}")
            raise AirflowException(f"Join step failed for {missing_date}")


with ETDAG(
    dag_id="silver_ownertransfer_dag",
    description="Stage corelogic ownertransfer datasets in an Olympus table.",
    default_args=default_args,
    schedule_interval="0 10 * * *",  # Run daily at 10am UTC
    catchup=False,
    params={"run_date": (pendulum.today("EST") - timedelta(days=1)).to_date_string()},
    tags=["corelogic", "silver", "olympus", "DND"],
) as dag:
    dag.doc_md = docs

    silver_partitions = SQLExecuteQueryOperator(
        task_id="Find_Silver_Transfer_Dates",
        conn_id="trino_conn",
        sql=SILVER_PARTITION,
        handler=list,
    )
    bronze_partitions = SQLExecuteQueryOperator(
        task_id="Find_Bronze_Transfer_Dates",
        conn_id="trino_conn",
        sql=BRONZE_PARTITION,
        handler=list,
    )

    missing_dates = compare_transfer_dates(
        silver_partitions.output, bronze_partitions.output
    )

    load_and_join = load_and_join_group.expand(missing_date=missing_dates)
