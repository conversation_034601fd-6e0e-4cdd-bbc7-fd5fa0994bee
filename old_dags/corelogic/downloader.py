from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.ssh.hooks.ssh import SSHHook
from airflow.providers.slack.hooks.slack_webhook import SlackWebhookHook
import datetime
import polars as pl
import pytz
from tempfile import NamedTemporaryFile

utc = pytz.UTC


class CorelogicDownloader:
    def list_directory_files(self, sftp_client, sftp_path):
        filenames = sftp_client.listdir_attr(sftp_path)
        return filenames

    def get_all_sftp_files(self, sftp_client, sftp_path, previous_path=""):
        filenames = []

        if previous_path != "" and previous_path != ".":
            sftp_path = f"{previous_path}/{sftp_path}"

        tmp_list = self.list_directory_files(sftp_client, sftp_path)
        for item in tmp_list:
            if item.longname[0] == "d":
                filenames.extend(
                    self.get_all_sftp_files(sftp_client, item.filename, sftp_path)
                )
            else:
                # Files over 100gb seem to break the downloader.
                if item.st_size < 100_000_000_000:
                    filenames.append(
                        {
                            "filename": f"{sftp_path}/{item.filename}",
                            "size": item.st_size,
                            "last_modified": datetime.datetime.fromtimestamp(item.st_mtime),
                        }
                    )
        print("************ FILENAMES **************")
        print(filenames)
        print("************ ^^^^^^^^^ **************")

        return filenames

    def get_current_s3_files(self, s3_client, bucket, prefix):
        paginator = s3_client.get_paginator("list_objects_v2")
        response = paginator.paginate(Bucket=bucket, Prefix=prefix)
        keys = []
        for page in response:
            if "Contents" in page:
                keys.extend(iter(page["Contents"]))
        filename = []
        last_modified = []
        size = []
        transfer_date = []
        parsed = []

        for s3_file in keys:
            if s3_file["Key"][-1] == "/":
                continue
            filename.append(s3_file["Key"].replace(prefix, ""))
            last_modified.append(
                s3_file["LastModified"].astimezone().replace(tzinfo=None)
            )
            size.append(s3_file["Size"])
            transfer_date.append(datetime.datetime.now() - datetime.timedelta(days=2))
            parsed.append(False)
        df = pl.DataFrame(
            data={
                "filename": filename,
                "last_modified": last_modified,
                "size": size,
                "transfer_date": transfer_date,
                "parsed_to_datalake": parsed,
            }
        )
        return df

    def upload_parquet(self, s3_hook, df, bucket, prefix):
        with NamedTemporaryFile() as temp:
            df.write_parquet(temp.name)
            s3_hook.load_file(
                filename=temp.name,
                key=f"{prefix}corelogic_files.parquet",
                bucket_name=bucket,
                replace=True,
            )

    def is_transfer_needed(self, sftp_file, filtered_df):
        if filtered_df.shape[0] == 0:
            return True
        if sftp_file["size"] != filtered_df[0, 2]:
            return True
        if sftp_file["last_modified"].timestamp() > filtered_df[0, 1].timestamp():
            return True
        return False

    def transfer_file(self, sftp_client, s3_hook, sftp_filename, s3_bucket, s3_key):
        with NamedTemporaryFile("w") as f:
            sftp_client.get(sftp_filename, f.name)
            s3_hook.load_file(
                filename=f.name, key=s3_key, bucket_name=s3_bucket, replace=True
            )

    def get_transfers(self, sftp_files, current_files):
        transfers = []
        for sftp_file in sftp_files:
            filename = sftp_file["filename"]
            if filename[0] == "/":
                filename = filename[1:]
            filtered_df = current_files.filter(pl.col("filename") == filename)
            if self.is_transfer_needed(sftp_file, filtered_df):
                transfers.append(sftp_file)
        return transfers

    def process_transfers(
        self, transfers, current_files, bucket, prefix, sftp_client, s3_hook
    ):
        for transfer in transfers:
            filename = transfer["filename"]
            if transfer["filename"][0] == "/":
                filename = transfer["filename"][1:]
            print(f"Transfering file {filename}")
            transfer_df = pl.DataFrame(
                {
                    "filename": [filename],
                    "last_modified": [transfer["last_modified"]],
                    "size": [transfer["size"]],
                    "transfer_date": [datetime.datetime.now()],
                    "parsed_to_datalake": [False],
                }
            )

            s3_filename = "{}{}".format(prefix, filename)
            print(s3_filename)
            self.transfer_file(
                sftp_client, s3_hook, transfer["filename"], bucket, s3_filename
            )

            current_files = current_files.filter(pl.col("filename") != filename)
            current_files.extend(transfer_df)
        current_files.rechunk()
        return current_files

    def alert_slack(self, transfers, bucket, prefix):
        slack_msg = [
            ":file_cabinet: *Corelogic Files Downloaded*",
            f"The following files have been downloaded from Corelogic to s3:/{bucket}/{prefix}:",
            "\n".join([f" - {transfer['filename']}" for transfer in transfers]),
        ]

        hook = SlackWebhookHook(
            slack_webhook_conn_id="slack_alert_conn",
        )

        hook.send(text="\n".join(slack_msg))
        return True

    def main(self, bucket, prefix, s3_conn_id, sftp_conn_id):

        source_s3_hook = S3Hook(s3_conn_id)

        # Build the parquet list
        current_files = self.get_current_s3_files(
            source_s3_hook.get_client_type(), bucket, prefix
        )
        # Process the new transfers
        ssh_hook = SSHHook(ssh_conn_id=sftp_conn_id)
        sftp_client = ssh_hook.get_conn().open_sftp()

        sftp_files = self.get_all_sftp_files(sftp_client, "")

        print(f"SFTP files: {len(sftp_files)}")
        transfers = self.get_transfers(sftp_files, current_files)
        print(f"Transers needed: {len(transfers)}")

        current_files = self.process_transfers(
            transfers, current_files, bucket, prefix, sftp_client, source_s3_hook
        )

        if len(transfers) > 0:
            self.alert_slack(transfers, bucket, prefix)
        self.upload_parquet(source_s3_hook, current_files, bucket, prefix)

        return [transfer["filename"] for transfer in transfers]
