from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.decorators import task, task_group
from airflow.datasets import Dataset
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.operators.dummy import DummyOperator
from airflow.models import Connection
from etdag import ETDAG
from old_dags.corelogic.external_tables import ExternalTables
from datetime import datetime

docs = """The load_external_tables examines the source and target location for corelogic
datasets and determines which files need to be processed and transferred.

This dag is designed for performing backfills and can be run on a schedule utilizing the
`datasets` list of datasets to backfill.
"""

mls_dataset = Dataset("s3://et-data-staging/external/mls_listings/")

default_args = {
    "owner": "Airflow",
    "depends_on_past": False,
    "start_date": datetime(2024, 2, 27),
    "email": ["<EMAIL>"],
    "owner": "Clay Morton",
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 0,
}

with ETDAG(
    dag_id="load_external_tables",
    description="Backfill corelogic datasets.",
    default_args=default_args,
    schedule_interval="5 2 * * *", # Every day at 8:05pm.
    catchup=False,
    tags=["corelogic", "external"],
) as dag:
    dag.doc_md = docs

    datasets = ["customextract_v1_dpc"]

    @task
    def load_files(dataset):
        s3_client = S3Hook(aws_conn_id="s3_conn").get_conn()
        trino_conn = Connection.get_connection_from_secrets("starburst")
        job = ExternalTables(env="prod", s3_client=s3_client, trino_conn=trino_conn.__dict__)
        job.set_unprocessed_files()
        job.stage_dataset(dataset)
        # will only create if it does not already exist, due to SQL statement
        job.create_table(dataset)
        job.sync_metadata(dataset)



    for dataset in datasets:
        files = load_files.override(task_id=f"load-{dataset}")(dataset)
        outlet = Dataset(f"s3/external_corelogic/{dataset}")
        trigger_silver = DummyOperator(task_id="outlet", outlets=outlet)

        files >> trigger_silver