{"mls_listings_v1": {"process": false, "full": {"path": "source/mls_listings_v1_test/full/{}/{}", "regex": ".*El_Toro_mls_listings_v1_dpc_[0-9]*_([0-9]*)_[0-9]*_full_data_[0-9]*.parquet"}, "delta": {"path": "source/mls_listings_v1_test/delta/{}/{}", "regex": ".*El_Toro_mls_listings_v1_dpc_[0-9]*_([0-9]*)_[0-9]*_delta_data_[0-9]*.parquet"}}, "mortgage_basic3": {"process": true, "delta": {"path": "source/mortgage_basic3/delta/{}/{}", "regex": ".*el_toro_mortgage_basic3_[0-9]*_([0-9]*)_[0-9]*_delta_data.txt.gz"}, "init": {"path": "source/mortgage_basic3/init/{}/{}", "regex": ".*el_toro_mortgage_basic3_[0-9]*_([0-9]*)_[0-9]*_data.txt"}}, "ownertransfer_v3": {"process": true, "delta": {"path": "source/ownertransfer_v3/delta/{}/{}", "regex": ".*el_toro_ownertransfer_v3_[0-9]*_([0-9]*)_[0-9]*_delta_data.txt.gz"}, "init": {"path": "source/ownertransfer_v3/init/{}/{}", "regex": ".*el_toro_ownertransfer_v3_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "propensityscore1_dpc": {"process": true, "full": {"path": "source/propensityscore1_dpc/full/{}/{}", "regex": ".*el_toro_propensityscore1_dpc_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "property_basic2": {"process": true, "delta": {"path": "source/property_basic2/delta/{}/{}", "regex": ".*el_toro_property_basic2_[0-9]*_([0-9]*)_[0-9]*_delta_data.txt.gz"}, "full": {"path": "source/property_basic2/full/{}/{}", "regex": ".*el_toro_property_basic2_[0-9]*_([0-9]*)_[0-9]*_full_data.txt.gz"}, "init": {"path": "source/property_basic2/init/{}/{}", "regex": ".*el_toro_property_basic2_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "solarcon1_dpc": {"process": true, "full": {"path": "source/solarcon1_dpc/full/{}/{}", "regex": ".*el_toro_solarcon1_dpc_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "thv_consumers_forecast": {"process": false, "full": {"path": "source/thv_consumers_forecast/full/{}/{}", "regex": ".*ElToro_FR300000329730505_THV_Consumers_Forecast_Delivery_([0-9]*).gz"}}, "thvxcpf1_dpc": {"process": true, "full": {"path": "source/thvxcpf1_dpc/full/{}/{}", "regex": ".*el_toro_thvxcpf1_dpc_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "trigger_events": {"process": true, "full": {"path": "source/trigger_events/full/{}/{}", "regex": ".*ElToro_OP300000249073745_Trigger_Events_Delivery_([0-9]*).gz"}}, "vol_lien_status_ms1": {"process": false, "full": {"path": "source/vol_lien_status_ms1/full/{}/{}", "regex": ".*el_toro_vol_lien_status_ms1_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}, "vol_lien_status_m2_dpc": {"process": true, "full": {"path": "source/vol_lien_status_m2_dpc/full/{}/{}", "regex": ".*el_toro_vol_lien_status_m2_dpc_[0-9]*_([0-9]*)_[0-9]*_data.txt.gz"}}}