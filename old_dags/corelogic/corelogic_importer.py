from airflow.decorators import task
from airflow.models import Variable
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.operators.python import get_current_context
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.dates import days_ago
from old_dags.corelogic.downloader import CorelogicDownloader
from etdag import ETDAG
from operator import itemgetter
from datetime import timedelta
import io
import json
import re
import zipfile
import os


default_args = {
    "owner": "Clay Morton",
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}

def extract_zipfile(s3_conn, source_bucket, zip_file_key, dest_bucket, dest_path):
    response = s3_conn.get_object(Bucket=source_bucket, Key=zip_file_key)
    zip_data = response["Body"].read()
    with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_ref:
        extracted_files = zip_ref.namelist()
        for filename in extracted_files:
            dest_filename = f"{dest_path}/{filename}"
            file_data = zip_ref.read(filename)
            s3_conn.put_object(Bucket=dest_bucket, Key=dest_filename, Body=file_data)
    return f"Extracted and transfered {zip_file_key} to {dest_path}"


def s3_copy(s3_conn, source_bucket, source_path, dest_bucket, dest_filename):
    copy_source = {"Bucket": source_bucket, "Key": source_path}
    s3_conn.copy(copy_source, dest_bucket, dest_filename)
    return True

def get_parsing_dict():
    parsing_dict = {}
    dag_directory = os.path.dirname(os.path.abspath(__file__))
    parsing_file = os.path.join(dag_directory, "parsing_dict.json")
    with open(parsing_file) as f:
        parsing_dict = json.load(f)

    return parsing_dict


with ETDAG(
    dag_id="corelogic_downloader",
    description="Downloads corelogic data from JK Lists and uploads it s3",
    start_date=days_ago(2),
    schedule_interval="0 23 * * *",
    catchup=False,
    default_args=default_args,
    tags=["corelogic", "ingest"],
) as dag:
    """
    DAG: `corelogic_downloader`
    Schedule: 7pm EDT daily
    Owner: Clay Morton

    ## Summary
    Transfer all files from CoreLogic's SFTP site to Eltoro S3 unzipping
    if necessary and placing them in a prefix where they can be located by dataset
    and date. Datasets that are configured as `"process": true` in the `corelogic/parsing_dict.json`
    file will trigger a related Dag run of the `bronze_<dataset>` Dags.

    ## Time Dependent
    Dag runs are not time dependent; each run backfills all untransferred source data.

    ## Failures
    In the event of a failure this Dag can be rerun manually by clearing the task.

    ## Escalation
    If rerunning the Dag <NAME_EMAIL> or <EMAIL>.

    ## Dependencies
    The souce data comes from a Corelogic sft server.

    ## Results
    * The files are transferred to the S3 Bucket `eltoro-data-sources`
        into a prefix that contains the dataset name and the transfer date.
    * Datasets that are enabled to process will trigger a Dag run.
    """
    bucket = Variable.get("corelogic_source_bucket", "eltoro-data-sources")
    prefix = Variable.get("corelogic_source_prefix", "corelogic/")
    s3_conn_id = "s3_conn"
    sftp_conn_id = "corelogic_sftp"

    @task(retries=3, retry_delay=timedelta(minutes=10))
    def download():
        downloader = CorelogicDownloader()
        transfers = downloader.main(bucket, prefix, s3_conn_id, sftp_conn_id)
        return transfers

    @task
    def process_transfers(transferred_files):
        parsing_dict = get_parsing_dict()
        transfer_list = []
        for data_type, details in parsing_dict.items():
            process = True
            extract = False
            if "process" in parsing_dict[data_type]:
                process = parsing_dict[data_type]["process"]
            if "extract" in parsing_dict[data_type]:
                extract = parsing_dict[data_type]["extract"]
            for file_type, paths in parsing_dict[data_type].items():
                if file_type not in ["process", "extract"]:
                    r = re.compile(paths["regex"])
                    type_files = list(filter(r.match, transferred_files))
                    for filename in type_files:
                        date = r.findall(filename)[0]
                        dest_filename = paths["path"].format(
                            date, filename.split("/")[-1]
                        )
                        if extract:
                            dest_filename = paths["path"].format(
                                date, f"zipfile/{filename.split('/')[-1]}"
                            )
                        transfer_list.append(
                            (
                                data_type,
                                filename,
                                dest_filename,
                                process,
                                date,
                                file_type,
                                extract,
                            )
                        )

        return transfer_list

    @task
    def transfer_file(file_info):
        (
            dataset,
            source_filename,
            dest_filename,
            process,
            transfer_date,
            transfer_type,
            extract,
        ) = file_info
        source_bucket = Variable.get("corelogic_source_bucket", "eltoro-data-sources")
        dest_bucket = Variable.get("corelogic_dest_bucket", "et-datalake-corelogic-staging")
        if source_filename[0] == "/":
            source_filename = source_filename[1:]

        source_path = f"corelogic/{source_filename}"
        s3_hook = S3Hook(aws_conn_id="s3_conn")
        s3_conn = s3_hook.get_conn()
        if extract:
            s3_copy(
                s3_conn,
                source_bucket,
                source_path,
                dest_bucket,
                dest_filename,
            )
            dest_path = "/".join(dest_filename.split("/")[:4])

            return extract_zipfile(
                s3_conn, source_bucket, source_path, dest_bucket, dest_path
            )
        s3_copy(s3_conn, source_bucket, source_path, dest_bucket, dest_filename)
        return f"{source_filename} transfered to {dest_filename}"

    @task
    def process_triggers(transferred_files):
        triggers = []
        for file_info in transferred_files:
            (
                dataset,
                source_filename,
                dest_filename,
                process,
                transfer_date,
                transfer_type,
                extract,
            ) = file_info
            if process:
                trigger = [dataset, transfer_type, transfer_date]
                if trigger not in triggers:
                    triggers.append(trigger)
        return sorted(triggers, key=itemgetter(0), reverse=True)

    @task
    def trigger_dag(dag_to_trigger):
        context = get_current_context()
        (
            dataset,
            transfer_type,
            transfer_date,
        ) = dag_to_trigger
        print(f"Triggering dbt for {dataset}")
        dagrun = TriggerDagRunOperator(
            task_id=f"trigger_{dataset}",
            trigger_dag_id=f"bronze_{dataset}",
            conf={
                "dataset": dataset,
                "transfer_date": transfer_date,
                "transfer_type": transfer_type,
            },
        )
        dagrun.execute(context)

    transfer_list = download()
    files_to_process = process_transfers(transfer_list)
    transferred_files = transfer_file.expand(file_info=files_to_process)
    trigger_list = process_triggers(files_to_process)
    triggered_dags = trigger_dag.expand(dag_to_trigger=trigger_list)
    (
        transfer_list
        >> files_to_process
        >> transferred_files
        >> trigger_list
        >> triggered_dags
    )