"""The spark_init_dag iterates through source files sequentially by date and
triggers the respective bronze model dags to implement the updates.

This dag is designed for performing backfills or bulk updates to the bronze
models. It is triggered by config parameters that describes the model and source
files to be run. If an error is encountered when in sequential mode the run will
stop and can be retriggred from where it stopped or anywhere else in the sequence
of files.

For models that use the append strategy files can be processed concurently by
setting the config param run_sequential to true.

To rebuild a table with the current schema you will need to manually delete the
current records.

The auto_fill setting can be used to make up gaps in a append models ingestion of
source files.

config params:
    dataset: str - The underlying datasource is a bucket of files that
        correspond to a single datasource model coming from Corelogic.
    start_date: str - The start date is a year/month/day string(ex. "20220120")
        that corresponds to the transfer_date of a file. If specified a dbt
        model run will start with that date and process files since. If no
        start_date is specified the model will start with the last init file.
    end_date: str - The same format as start_date; if specified the dag run
        will stop at the specified file(s) with that date.
    run_sequential: bool - Wait for successfull completion of current running
        model dag before triggering the next.
    auto_fill: bool - This mode will generate the run list by finding all files
        that haven't been transferred yet.

"""
from airflow import DAG
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.decorators import task
from airflow.models import Variable
from airflow.utils.dates import days_ago
from old_dags.corelogic.run_list import RunList
from airflow.models import Connection
from typing import TypedDict

default_args = {
    "owner": "Airflow",
    "depends_on_past": False,
    "start_date": days_ago(2),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 0,
}

config_params = {
    "dataset": "{{ dag_run.conf['dataset'] }}",
    "start_date": "{{ dag_run.conf['start_from_date'] }}",
    "stop_date": "{{ dag_run.conf['stop_from_date'] }}",
    "run_sequential": "{{ dag_run.conf['run_sequential'] }}",
    "auto_fill": "{{ dag_run.conf['auto_fill'] }}",
}


class InitParams(TypedDict):
    """Processed config params."""

    dataset: str
    start_date: str
    stop_date: str
    run_sequential: bool
    auto_fill: bool


def clean_params(params: dict[str, str]) -> InitParams:

    """Cast config params to usable value types.

    Set all fields that don't have specified values to null. Then cast
    the boolean values.
    """
    cleaned: dict = {}
    for key, val in config_params.items():
        if params.get(key) == val:
            cleaned[key] = None
        else:
            cleaned[key] = params.get(key)

    # Default is false for auto_fill.
    if cleaned["auto_fill"] == None:
        cleaned["auto_fill"] = False
    else:
        cleaned["auto_fill"] = cleaned["auto_fill"].lower() == "true"

    # Default is true for run_sequential.
    if cleaned["run_sequential"] == None:
        cleaned["run_sequential"] = True
    else:
        cleaned["run_sequential"] = cleaned["run_sequential"].lower() != "false"

    return InitParams(cleaned)


with DAG(
    dag_id="spark_corelogic_init",
    description="Backfill corelogic datasets.",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    template_searchpath="/opt/airflow/dags/repo",
    params=config_params,
    tags=["corelogic", "spark", "iceberg", "bronze"],
) as dag:

    @task
    def generate_file_list(**kwargs):
        s3_client = S3Hook(aws_conn_id="s3_conn").get_conn()
        trino_conn = Connection.get_connection_from_secrets("starburst")
        env = Variable.get("environment")
        run = RunList(s3_client, trino_conn.__dict__, env)

        params = clean_params(kwargs["params"])
        print("************************")
        print(params)
        print("************************")

        run_list = run.prepare_run_list(**params)

        return run_list

    @task
    def spark_run_files(dataset_files, **kwargs):
        params = clean_params(kwargs["params"])
        run_sequential = params["run_sequential"]
        for i, file in enumerate(dataset_files):
            dataset = file["dataset"]
            transfer_type = file["transfer_type"]
            transfer_date = file["transfer_date"]
            # Handle case with partitioned folders(task_id can only have limited characters).
            date = transfer_date.split("=")[-1]
            task_id = f"bronze-{dataset}-{transfer_type}-{date}"
            dagrun = TriggerDagRunOperator(
                task_id=task_id,
                trigger_dag_id=f"bronze_{dataset}",
                conf={
                    "dataset": dataset,
                    "transfer_type": transfer_type,
                    "transfer_date": transfer_date,
                },
                wait_for_completion=run_sequential,
                reset_dag_run=False,
            )
            dagrun.execute(kwargs)

    run_list = generate_file_list()
    dagruns = spark_run_files(run_list)

    run_list >> dagruns
