from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.decorators import task, task_group
from airflow.datasets import Dataset
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.operators.dummy import DummyOperator
from airflow.models import Connection
from etdag import ETDAG
from old_dags.corelogic.corelogic_external_tables_src import ExternalTable
from datetime import datetime
from airflow.models import Variable

docs = """
DAG Name: stage_corelogic_external_tables

Description:
------------
This DAG is designed to stage CoreLogic datasets by examining the source and target locations
in S3, determining which files need to be processed, and transferring them to the appropriate
destination. It uses the `ExternalTable` class from `corelogic_external_tables_src.py` to handle the logic
for identifying unprocessed files, staging datasets, creating tables, and syncing metadata.

Key Components:
---------------
1. **Datasets**:
   - The DAG processes a predefined list of CoreLogic datasets, such as `building_detail1`,
     `Digital_Audiences`, and `ownertransfer_v3`.

2. **Tasks**:
   - Each dataset is processed by a dynamically generated task using the `load_files` function.
   - The `load_files` function initializes an `ExternalTable` object and calls its methods to:
     - Identify unprocessed files (`set_unprocessed_files`).
     - Stage the dataset (`stage_dataset`).
     - Create the corresponding table in Trino (`create_table`).
     - Sync metadata (`sync_metadata`).

3. **ExternalTable Class**:
   - The `ExternalTable` class, defined in `corelogic_external_tables_src.py`, encapsulates the logic for
     handling CoreLogic datasets. Key methods include:
     - `set_source_files`: Retrieves source files from S3 and filters them based on the dataset.
     - `set_dest_files`: Retrieves destination files from S3 to determine already processed files.
     - `set_unprocessed_files`: Identifies files that need to be processed by comparing source
       and destination files.
     - `stage_dataset`: Stages the dataset for processing.
     - `create_table`: Creates a table in Trino for the dataset.
     - `sync_metadata`: Syncs metadata for the dataset.

4. **Environment**:
   - The DAG uses the `environment` variable from Airflow's Variables to determine the environment
     (e.g., `dev`, `prod`).

5. **Schedule**:
   - The DAG is scheduled to run daily at 2:00 AM.
   - each task can be retried in case of failure.

6. **Dependencies**:
   - Each dataset is processed independently, with no interdependencies between tasks.

Usage:
------
- To backfill datasets, ensure the `datasets` list contains the desired datasets.
- The DAG can be triggered manually or run on a schedule.
"""

env = Variable.get("environment", default_var="dev")
default_args = {
    "depends_on_past": False,
    "start_date": datetime(2025, 4, 22),
    "email": ["<EMAIL>"],
    "owner": "Bryan Price",
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
}

with ETDAG(
    dag_id="stage_corelogic_external_tables",
    description="stage corelogic datasets in an external table.",
    default_args=default_args,
    schedule_interval="0 2 * * *",  # Every day at 10:00pm.
    catchup=False,
    tags=["corelogic", "external", "DND"],
) as dag:
    dag.doc_md = docs
    datasets = [
        "building_detail1",
        "buildingpermit_3",
        "Digital_Audiences",
        "mortgage_basic3",
        "ownertransfer_v3",
        "propensityscore1_dpc",
        "property_basic2",
        "solarcon1_dpc",
        "thvxcpf1_dpc",
        "Trigger_Events",
        "vol_lien_status_m2_dpc",
    ]

    @task
    def load_files(dataset):
        s3_client = S3Hook(aws_conn_id="s3_conn").get_conn()
        job = ExternalTable(
            dataset=dataset,
            env=env,
            s3_client=s3_client,
        )
        job.set_unprocessed_files()
        job.stage_dataset(dataset=dataset)
        job.create_table(dataset=dataset)
        job.sync_metadata(dataset=dataset)

    for dataset in datasets:
        files = load_files.override(task_id=f"{dataset}_getting_staged")(dataset)
        # outlet = Dataset(f"s3/external_corelogic/{dataset}")
        # trigger_silver = DummyOperator(task_id="outlet", outlets=outlet)

        # files >> trigger_silver
