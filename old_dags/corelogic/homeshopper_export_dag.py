import json
import pyarrow as pa
from airflow.decorators import task
from airflow.models import Variable
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.utils.dates import days_ago
from etdag import ETDAG
import pandas as pd
from datetime import datetime, timedelta


env = Variable.get("cl_homeshopper_env", default_var="prod")

default_args = {
    "owner": "<PERSON><PERSON><PERSON>",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
}


def get_dataset_from_trino() -> pd.DataFrame:
    tr = TrinoHook(trino_conn_id="starburst")
    query = f"""
        SELECT * FROM s3.gold_real_estate_enrichment.prospecting_solutions_homeshoppers
    """
    results_df = tr.get_pandas_df(sql=query)
    return results_df


def get_parquet_schema():
    columns = [
        ('clip', pa.string()),
        ('ethash', pa.string()),
        ('rule', pa.int64()),
        ('last_updated', pa.date32()),
        ('action', pa.string()),
        ('identified_date', pa.date32()),
        ('observation_count', pa.int64()),
        ('first_identified', pa.date32()),
        ('latest_identified', pa.date32()),
        ('observation_location_count', pa.int64()),
        ('zips_seen', pa.string()),
        ('observation_count_per_loc', pa.string()),
        ('update_indicator', pa.bool_()),
        ('update_count_indicator', pa.bool_()),
        ('seen_listing_outofstate', pa.bool_()),
        ('et_inmarket_indicator', pa.int64()),
        ('listing_price_avg_low', pa.int64()),
        ('listing_price_avg_high', pa.int64()),
        ('count_lmi_listing', pa.int64()),
        ('custom_attributes', pa.string()),
    ]

    return pa.schema(columns)


with ETDAG(
    dag_id="corelogic_homeshopper_export",
    description="Queries dataset from Trino, exports to parquet and delivers via sftp",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 21 * * *",
    catchup=False,
    tags=["corelogic", "export", "prospecting-solutions"],
) as dag:
    bucket = "vr-timestamp"
    today = datetime.today() - timedelta(days=0)
    date_st = today.date().strftime("%Y%m%d")
    date_st_hyphen = today.date().strftime("%Y-%m-%d")
    filename = f"eltoro_prospecting_solutions_{date_st}.parquet"
    s3_path = f"bi_sources/realestate/homeshopper/{env}/transfer_date={date_st_hyphen}/{filename}"

    @task
    def write_homeshopper_parquet():
        df = get_dataset_from_trino()
        df["clip"] = df["clip"].astype(str)
        df["zips_seen"] = df["zips_seen"].apply(lambda x: json.dumps(x))
        df["observation_count_per_loc"] = df["observation_count_per_loc"].apply(
            lambda x: json.dumps(x)
        )

        df.to_parquet(
            path=f"s3://{bucket}/{s3_path}",
            index=False,
            schema=get_parquet_schema(),
            engine='pyarrow'
        )

    @task
    def write_trigger():
        sftp_hook = SFTPHook("corelogic_sftp")
        with open(f"/tmp/{date_st}_trigger.txt", "w") as fp:
            pass
        sftp_hook.store_file(
            remote_full_path=f"prospecting_solutions/from_eltoro/{date_st}_trigger.txt",
            local_full_path=f"/tmp/{date_st}_trigger.txt",
        )

    deliver_dataset = write_homeshopper_parquet()
    s3_to_sftp = S3ToSFTPOperator(
        task_id="s3_to_sftp",
        s3_bucket=bucket,
        s3_key=s3_path,
        sftp_path=f"prospecting_solutions/from_eltoro/{filename}",
        sftp_conn_id="corelogic_sftp",
        aws_conn_id="s3_conn",
    )
    trigger = write_trigger()
    call_starburst = SQLExecuteQueryOperator(
        task_id="update_starburst",
        conn_id="starburst",
        sql=f"CALL s3.system.sync_partition_metadata('bronze_real_estate_transfers', 'prospecting_solutions_homeshoppers', 'ADD')",
        handler=list,
    )

    deliver_dataset >> s3_to_sftp >> trigger >> call_starburst
