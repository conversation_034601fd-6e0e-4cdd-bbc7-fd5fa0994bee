CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE = """
    SELECT COUNT(*) AS cnt 
    FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode" 
    WHERE file_transfer_date = DATE(
        CONCAT(
            (SELECT year from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1)),
            '-',    
            (SELECT month from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1)),
            '-',    
            (SELECT day from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
        )
    )
"""

INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE = """
INSERT INTO "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode"
(
  listing_id,
  listing_transaction_type_code_derived,
  listing_status_code_standardized,
  listing_status_description,
  listing_address_street_address,
  listing_address_city,
  property_sub_type_description,
  total_baths,
  original_listing_date_and_time,
  last_listing_date_and_time,
  off_market_date_and_time,
  close_date,
  listing_address_state,
  listing_address_zip_code,
  current_listing_price,
  listing_agent_name,
  listing_office_name,
  clip,
  fips_code,
  situs_state,
  situs_city,
  situs_zip_code,
  situs_street_address,
  situs_unit_number,
  year_built,
  living_area_square_feet_standardized,
  active_ad_count,
  local_listing_id,
  listing_agent_identifier,
  buyer_agent_full_name,
  buyer_agent_identifier,
  listing_service_name,
  zip_source_used,
  geocoder_addressline,
  geocoder_cityline,
  geocoder_streetnumber,
  geocoder_address,
  geocoder_province,
  geocoder_county,
  geocoder_city,
  geocoder_state,
  geocoder_country,
  geocoder_zipcode,
  geocoder_zip4,
  geocoder_dataset,
  geocoder_matchcode,
  geocoder_matchdescription,
  geocoder_streetname,
  geocoder_streetaddress,
  geocoder_latitude,
  geocoder_longitude,
  geocoder_unitnumber,
  geocoder_unittype,
  geocoder_unitnumberlowerbound,
  geocoder_unitnumberupperbound,
  geocoder_unitrangetype,
  geocoder_geometrywkb,
  geocoder_fipscode,
  geocoder_rdi,
  geocoder_ethashv1,
  geocoder_ethashv1_no_zip9,
  geocoder_ethashv2,
  geocoded_date,
  file_transfer_date
)
WITH base AS (
  SELECT
    listing_id,
    listing_transaction_type_code_derived,
    listing_status_code_standardized,
    listing_status_description,
    listing_address_street_address,
    listing_address_city,
    property_sub_type_description,
    CAST(total_baths AS varchar) AS total_baths,
    CAST(original_listing_date_and_time AS timestamp(3)) AS original_listing_date_and_time,
    CAST(last_listing_date_and_time AS timestamp(3)) AS last_listing_date_and_time,
    CAST(off_market_date_and_time AS timestamp(3)) AS off_market_date_and_time,
    CAST(close_date AS timestamp(3)) AS close_date,
    listing_address_state,
    listing_address_zip_code,
    CAST(current_listing_price AS varchar) AS current_listing_price,
    listing_agent_name,
    listing_office_name,
    clip,
    fips_code,
    situs_state,
    situs_city,
    situs_zip_code,
    situs_street_address,
    situs_unit_number,
    CAST(year_built AS varchar) AS year_built,
    CAST(living_area_square_feet_standardized AS bigint) AS living_area_square_feet_standardized,
    active_ad_count,
    local_listing_id,
    listing_agent_identifier,
    buyer_agent_full_name,
    buyer_agent_identifier,
    listing_service_name,
    year,
    month,
    day,
    -- Determine the resolved zip by preferring situs_zip_code over listing_address_zip_code
    COALESCE(NULLIF(situs_zip_code, ''), NULLIF(listing_address_zip_code, '')) AS resolved_zip,
    -- Build a composite key: resolved_zip concatenated with listing_id
    CONCAT(COALESCE(NULLIF(situs_zip_code, ''), NULLIF(listing_address_zip_code, '')), '_', listing_id) AS resolved_pk,
    -- Capture which zip source was used
    CASE
      WHEN NULLIF(situs_zip_code, '') IS NOT NULL THEN 'situs'
      WHEN NULLIF(listing_address_zip_code, '') IS NOT NULL THEN 'listing'
      ELSE 'none'
    END AS zip_source_used,
    -- Construct file_transfer_date as a concatenation of year, month, day
    DATE(CONCAT(year, '-', month, '-', day)) AS file_transfer_date
  FROM "s3"."external_corelogic"."mls_listings_replace_v20241217"
  WHERE
    listing_transaction_type_code_derived = 'S'
    AND year = (SELECT year from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
    AND month = (SELECT month from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
    AND day = (SELECT day from (SELECT * from "s3"."external_corelogic"."mls_listings_replace_v20241217$partitions"
                ORDER BY 1 desc, 2 desc, 3 desc limit 1))
),
joined_data AS (
  SELECT
    b.listing_id,
    b.listing_transaction_type_code_derived,
    b.listing_status_code_standardized,
    b.listing_status_description,
    b.listing_address_street_address,
    b.listing_address_city,
    b.property_sub_type_description,
    b.total_baths,
    b.original_listing_date_and_time,
    b.last_listing_date_and_time,
    b.off_market_date_and_time,
    b.close_date,
    b.listing_address_state,
    b.listing_address_zip_code,
    b.current_listing_price,
    b.listing_agent_name,
    b.listing_office_name,
    b.clip,
    b.fips_code,
    b.situs_state,
    b.situs_city,
    b.situs_zip_code,
    b.situs_street_address,
    b.situs_unit_number,
    b.year_built,
    b.living_area_square_feet_standardized,
    b.active_ad_count,
    b.local_listing_id,
    b.listing_agent_identifier,
    b.buyer_agent_full_name,
    b.buyer_agent_identifier,
    b.listing_service_name,
    b.zip_source_used,
    bridge.addressline AS geocoder_addressline,
    bridge.cityline AS geocoder_cityline,
    bridge.streetnumber AS geocoder_streetnumber,
    bridge.address AS geocoder_address,
    bridge.province AS geocoder_province,
    bridge.county AS geocoder_county,
    bridge.city AS geocoder_city,
    bridge.state AS geocoder_state,
    bridge.country AS geocoder_country,
    bridge.zipcode AS geocoder_zipcode,
    bridge.zip4 AS geocoder_zip4,
    bridge.dataset AS geocoder_dataset,
    bridge.matchcode AS geocoder_matchcode,
    bridge.matchdescription AS geocoder_matchdescription,
    bridge.streetname AS geocoder_streetname,
    bridge.streetaddress AS geocoder_streetaddress,
    bridge.latitude AS geocoder_latitude,
    bridge.longitude AS geocoder_longitude,
    bridge.unitnumber AS geocoder_unitnumber,
    bridge.unittype AS geocoder_unittype,
    bridge.unitnumberlowerbound AS geocoder_unitnumberlowerbound,
    bridge.unitnumberupperbound AS geocoder_unitnumberupperbound,
    bridge.unitrangetype AS geocoder_unitrangetype,
    bridge.geometrywkb AS geocoder_geometrywkb,
    bridge.fipscode AS geocoder_fipscode,
    bridge.rdi AS geocoder_rdi,
    bridge.ethashv1 AS geocoder_ethashv1,
    CONCAT(SUBSTRING(bridge.ethashv1, 1, 6), '0000', SUBSTRING(bridge.ethashv1, 11)) AS geocoder_ethashv1_no_zip9,
    bridge.ethashv2 AS geocoder_ethashv2,
    bridge.geocoded_date,
    b.file_transfer_date
  FROM base b
  LEFT JOIN "olympus"."bronze_corelogic"."mls_listings_ethash_bridge" bridge
    ON b.resolved_pk = bridge.listing_id
)
SELECT *
FROM joined_data
"""
