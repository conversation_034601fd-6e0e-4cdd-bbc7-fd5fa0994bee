CHECK_BEFORE_INSERT_MLS_SALE_W_GEOCODE_METRICS = """
    SELECT COUNT(*) AS cnt 
    FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode_metrics" 
    WHERE date_parse(file_transfer_date, '%Y%m%d') = (
        SELECT max(partition.file_transfer_date) 
        FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode$partitions"
        )  
"""

INSERT_INTO_MLS_LISTINGS_SALE_W_GEOCODE_METRICS = """
INSERT INTO "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode_metrics" (
  file_transfer_date,
  total_records,
  unique_listing_ids,
  unique_composite_keys,
  clipped_count,
  listing_add_fallback_count,
  no_clip_or_list_add_count,
  geocoded_count,
  not_geocoded_count,
  status_S_count,
  status_null_count,
  status_P_count,
  status_IA_count,
  status_A_count,
  original_listing_date_and_time_null_count,
  last_listing_date_and_time_null_count,
  off_market_date_and_time_null_count,
  close_date_null_count,
  missing_listing_agent_name_count,
  missing_listing_office_name_count,
  missing_listing_agent_identifier_count,
  missing_buyer_agent_full_name_count,
  missing_buyer_agent_identifier_count,
  missing_listing_service_name_count,
  missing_year_built_count,
  missing_living_area_count,
  missing_local_listing_id_count,
  unique_ethashv2_count,
  geocoder_unmatched,
  navteqstreet_count,
  parcel_count,
  usps_count,
  distinct_geometries,
  micro_polygon_count,
  regular_polygon_count,
  multilinestr_count,
  invalid_polygon_count,
  active_while_closed_count,
  missing_bathroom_count
)
WITH typed AS (
    SELECT
        -- Bring through the columns you need for grouping or other metrics:
        file_transfer_date,
        listing_id,
        listing_transaction_type_code_derived,
        listing_status_code_standardized,
        listing_status_description,
        listing_address_street_address,
        listing_address_city,
        property_sub_type_description,
        total_baths,
        original_listing_date_and_time,
        last_listing_date_and_time,
        off_market_date_and_time,
        close_date,
        listing_address_state,
        listing_address_zip_code,
        current_listing_price,
        listing_agent_name,
        listing_office_name,
        clip,
        fips_code,
        situs_state,
        situs_city,
        situs_zip_code,
        situs_street_address,
        situs_unit_number,
        year_built,
        living_area_square_feet_standardized,
        active_ad_count,
        listing_agent_identifier,
        buyer_agent_full_name,
        buyer_agent_identifier,
        listing_service_name,
        local_listing_id,
        zip_source_used,
        geocoded_date,
        geocoder_ethashv2,
        geocoder_matchcode,
        geocoder_geometrywkb,
        geocoder_dataset,
        -- Decode base64 to VARBINARY, convert to geometry, then get type and area
        COALESCE(
            ST_GeometryType(
                TRY(ST_GeomFromBinary(from_base64(geocoder_geometrywkb)))
            ),
            'INVALID'
        ) AS geometry_type,
        TRY(
            ST_Area(ST_GeomFromBinary(from_base64(geocoder_geometrywkb)))
        ) AS geometry_area

    FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode"
    WHERE file_transfer_date = (SELECT max(partition).file_transfer_date from "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode$partitions")
)
SELECT
  date_format(file_transfer_date, '%Y%m%d') file_transfer_date,

  COUNT(*) AS total_records,
  COUNT(DISTINCT listing_id) AS unique_listing_ids,
  COUNT(DISTINCT CONCAT(COALESCE(NULLIF(situs_zip_code, ''), listing_address_zip_code), '_', listing_id)) AS unique_composite_keys,

  COUNT(CASE WHEN zip_source_used = 'situs' THEN 1 END) AS clipped_count,
  COUNT(CASE WHEN zip_source_used = 'listing' THEN 1 END) AS listing_add_fallback_count,
  COUNT(CASE WHEN zip_source_used = 'none' THEN 1 END) AS no_clip_or_list_add_count,

  COUNT(CASE WHEN geocoded_date IS NOT NULL THEN 1 END) AS geocoded_count,
  COUNT(CASE WHEN geocoded_date IS NULL THEN 1 END) AS not_geocoded_count,

  COUNT(CASE WHEN listing_status_code_standardized = 'S' THEN 1 END) AS status_S_count,
  COUNT(CASE WHEN listing_status_code_standardized IS NULL THEN 1 END) AS status_null_count,
  COUNT(CASE WHEN listing_status_code_standardized = 'P' THEN 1 END) AS status_P_count,
  COUNT(CASE WHEN listing_status_code_standardized = 'IA' THEN 1 END) AS status_IA_count,
  COUNT(CASE WHEN listing_status_code_standardized = 'A' THEN 1 END) AS status_A_count,

  COUNT(CASE WHEN original_listing_date_and_time IS NULL THEN 1 END) AS original_listing_date_and_time_null_count,
  COUNT(CASE WHEN last_listing_date_and_time IS NULL THEN 1 END) AS last_listing_date_and_time_null_count,
  COUNT(CASE WHEN off_market_date_and_time IS NULL THEN 1 END) AS off_market_date_and_time_null_count,
  COUNT(CASE WHEN close_date IS NULL THEN 1 END) AS close_date_null_count,

  COUNT(CASE WHEN listing_agent_name IS NULL OR listing_agent_name = '' THEN 1 END) AS missing_listing_agent_name_count,
  COUNT(CASE WHEN listing_office_name IS NULL OR listing_office_name = '' THEN 1 END) AS missing_listing_office_name_count,
  COUNT(CASE WHEN listing_agent_identifier IS NULL OR listing_agent_identifier = '' THEN 1 END) AS missing_listing_agent_identifier_count,
  COUNT(CASE WHEN buyer_agent_full_name IS NULL OR buyer_agent_full_name = '' THEN 1 END) AS missing_buyer_agent_full_name_count,
  COUNT(CASE WHEN buyer_agent_identifier IS NULL OR buyer_agent_identifier = '' THEN 1 END) AS missing_buyer_agent_identifier_count,
  COUNT(CASE WHEN listing_service_name IS NULL OR listing_service_name = '' THEN 1 END) AS missing_listing_service_name_count,

  COUNT(CASE WHEN year_built IS NULL OR year_built = '0' THEN 1 END) AS missing_year_built_count,
  COUNT(CASE WHEN living_area_square_feet_standardized IS NULL OR living_area_square_feet_standardized = 0 THEN 1 END) AS missing_living_area_count,
  COUNT(CASE WHEN local_listing_id IS NULL OR local_listing_id = '' THEN 1 END) AS missing_local_listing_id_count,

  COUNT(DISTINCT geocoder_ethashv2) AS unique_ethashv2_count,
  COUNT(CASE WHEN geocoder_matchcode = 'E9999' THEN 1 END) AS geocoder_unmatched,
  COUNT(CASE WHEN geocoder_dataset = 'NavteqStreet' THEN 1 END) AS navteqstreet_count,
  COUNT(CASE WHEN geocoder_dataset = 'Parcel' THEN 1 END) AS parcel_count,
  COUNT(CASE WHEN geocoder_dataset = 'USPS' THEN 1 END) AS usps_count,

  COUNT(DISTINCT geocoder_geometrywkb) AS distinct_geometries,
  COUNT(CASE WHEN geometry_type = 'ST_Polygon' AND geometry_area < 1e-8 THEN 1 END) AS micro_polygon_count,
  COUNT(CASE WHEN geometry_type = 'ST_Polygon' AND geometry_area > 1e-8 THEN 1 END) AS regular_polygon_count,
  COUNT(CASE WHEN geometry_type = 'ST_MultiLineString' AND geometry_area < 1e-8 THEN 1 END) AS multilinestr_count,
  COUNT(CASE WHEN geometry_type = 'INVALID' THEN 1 END) AS invalid_polygon_count,

  COUNT(CASE WHEN listing_status_code_standardized = 'A' AND close_date is not null THEN 1 END) AS active_while_closed_count,
  COUNT(CASE WHEN total_baths IS NULL or total_baths = '0' THEN 1 END) AS missing_bathroom_count

FROM typed
GROUP BY file_transfer_date

"""