CHECK_BEFORE_INSERT_MLS_SALE_INTEGRATED= """
    SELECT COUNT(*) AS cnt 
    FROM "olympus"."silver_corelogic"."mls_listings_integrated"
    WHERE snapshot_date = (
        SELECT max(partition.file_transfer_date) 
        FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode$partitions"
        )  
"""

INSERT_INTO_MLS_LISTINGS_SALE_INTEGRATED = """
INSERT INTO "olympus"."silver_corelogic"."mls_listings_integrated"
(
  clip,
  listing_id,
  ethashv1,
  ethashv1_no_zip9,
  ethashv2,
  listing_status_code_standardized,
  listing_status_description,
  property_sub_type_description,
  total_baths,
  original_listing_date_and_time,
  last_listing_date_and_time,
  first_seen,
  off_market_date_and_time,
  off_market_transition_date,
  close_date,
  closed_transition_date,
  current_listing_price,
  listing_agent_name,
  listing_office_name,
  listing_agent_identifier,
  buyer_agent_full_name,
  buyer_agent_identifier,
  listing_service_name,
  local_listing_id,
  fips_code,
  situs_state,
  situs_city,
  situs_zip_code,
  situs_street_address,
  situs_unit_number,
  year_built,
  living_area_square_feet_standardized,
  geocoder_dataset,
  geocoder_geometrywkb,
  geometry_type,
  effective_year_built,
  bedrooms_all_buildings,
  number_of_bathrooms,
  living_square_feet_all_buildings,
  land_use_code,
  snapshot_date,
  listing_period_id
)
WITH max_partition AS (
  SELECT MAX(partition.snapshot_date) AS max_snapshot_date
  FROM "olympus"."silver_corelogic"."mls_listings_integrated$partitions"
  WHERE partition.snapshot_date < (SELECT max(partition.file_transfer_date) from "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode$partitions")
),
previous_snapshot AS (
  SELECT
    t.*,
    '0' AS listing_period_id,
    0 AS new_period_flag
  FROM "olympus"."silver_corelogic"."mls_listings_integrated" t
  WHERE snapshot_date = (SELECT max_snapshot_date FROM max_partition)
),
today_resolved AS (
  SELECT *
  FROM (
    WITH combined AS (
      SELECT
        swg.file_transfer_date AS snapshot_date,
        swg.file_transfer_date AS first_seen,
        swg.clip,
        swg.listing_id,
        swg.listing_status_code_standardized,
        swg.listing_status_description,
        swg.property_sub_type_description,
        swg.total_baths,
        swg.original_listing_date_and_time,
        swg.last_listing_date_and_time,
        swg.off_market_date_and_time,
        CASE
            WHEN swg.off_market_date_and_time IS NOT NULL THEN swg.file_transfer_date
            ELSE NULL
        END AS off_market_transition_date,
        swg.close_date,
        CASE
            WHEN swg.close_date IS NOT NULL THEN swg.file_transfer_date
            ELSE NULL
        END AS closed_transition_date,
        swg.current_listing_price,
        swg.listing_agent_name,
        swg.listing_office_name,
        swg.listing_agent_identifier,
        swg.buyer_agent_full_name,
        swg.buyer_agent_identifier,
        swg.listing_service_name,
        swg.local_listing_id,
        swg.fips_code,
        swg.situs_state,
        swg.situs_city,
        swg.situs_zip_code,
        swg.situs_street_address,
        swg.situs_unit_number,
        swg.year_built,
        swg.living_area_square_feet_standardized,
        swg.geocoder_ethashv1,
        swg.geocoder_ethashv1_no_zip9,
        swg.geocoder_ethashv2,
        swg.geocoder_dataset,
        swg.geocoder_geometrywkb,
        -- Do not re-select file_transfer_date here (avoid duplicate)
        COALESCE(
          ST_GeometryType(TRY(ST_GeomFromBinary(from_base64(swg.geocoder_geometrywkb)))),
          'INVALID'
        ) AS geometry_type,
        pb.effective_year_built,
        pb.bedrooms_all_buildings,
        pb.number_of_bathrooms,
        pb.living_square_feet_all_buildings,
        pb.land_use_code,
        -- Compute a flag indicating a new listing period. (Adjust the logic if needed.)
        CASE WHEN swg.off_market_date_and_time IS NOT NULL THEN 1 ELSE 0 END AS new_period_flag,
        -- Add a placeholder for listing_period_id (to be computed later)
        '0' AS listing_period_id
      FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode" swg
      LEFT JOIN "olympus"."silver_corelogic"."property_basic2" pb
        ON swg.clip = pb.clip
      WHERE swg.file_transfer_date = (SELECT max(partition.file_transfer_date) from "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode$partitions")
    ),
    ranked AS (
      SELECT
        c.*,
        row_number() OVER (
          PARTITION BY c.clip
          ORDER BY
             CASE WHEN c.off_market_date_and_time IS NULL THEN 0 ELSE 1 END DESC,
             c.off_market_date_and_time DESC,
             c.original_listing_date_and_time DESC
        ) AS rn
      FROM combined c
    )
    SELECT *
    FROM ranked
    WHERE rn = 1
  )
),
union_data AS (
  SELECT * FROM (
    -- Previous snapshot branch:
    SELECT
      clip,
      listing_id,
      ethashv1,
      ethashv1_no_zip9,
      ethashv2,
      listing_status_code_standardized,
      listing_status_description,
      property_sub_type_description,
      total_baths,
      original_listing_date_and_time,
      last_listing_date_and_time,
      first_seen,
      off_market_date_and_time,
      off_market_transition_date,
      close_date,
      closed_transition_date,
      current_listing_price,
      listing_agent_name,
      listing_office_name,
      listing_agent_identifier,
      buyer_agent_full_name,
      buyer_agent_identifier,
      listing_service_name,
      local_listing_id,
      fips_code,
      situs_state,
      situs_city,
      situs_zip_code,
      situs_street_address,
      situs_unit_number,
      year_built,
      living_area_square_feet_standardized,
      geocoder_dataset,
      geocoder_geometrywkb,
      geometry_type,
      effective_year_built,
      bedrooms_all_buildings,
      number_of_bathrooms,
      living_square_feet_all_buildings,
      land_use_code,
      snapshot_date,
      0 AS new_period_flag
    FROM previous_snapshot
    UNION ALL
    -- Today resolved branch:
    SELECT
      clip,
      listing_id,
      geocoder_ethashv1 AS ethashv1,
      geocoder_ethashv1_no_zip9 AS ethashv1_no_zip9,
      geocoder_ethashv2 AS ethashv2,
      listing_status_code_standardized,
      listing_status_description,
      property_sub_type_description,
      total_baths,
      original_listing_date_and_time,
      last_listing_date_and_time,
      first_seen,
      off_market_date_and_time,
      off_market_transition_date,
      close_date,
      closed_transition_date,
      current_listing_price,
      listing_agent_name,
      listing_office_name,
      listing_agent_identifier,
      buyer_agent_full_name,
      buyer_agent_identifier,
      listing_service_name,
      local_listing_id,
      fips_code,
      situs_state,
      situs_city,
      situs_zip_code,
      situs_street_address,
      situs_unit_number,
      year_built,
      living_area_square_feet_standardized,
      geocoder_dataset,
      geocoder_geometrywkb,
      geometry_type,
      effective_year_built,
      bedrooms_all_buildings,
      number_of_bathrooms,
      living_square_feet_all_buildings,
      land_use_code,
      snapshot_date,
      new_period_flag
    FROM today_resolved
  )
),
labeled AS (
  SELECT
    *,
    CAST(
      SUM(new_period_flag) OVER (
        PARTITION BY clip
        ORDER BY original_listing_date_and_time
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      ) AS VARCHAR
    ) AS listing_period_id_computed
  FROM union_data
),
integrated AS (
  SELECT
    clip,
    max_by(listing_id, snapshot_date) AS listing_id,
    max_by(ethashv1, snapshot_date) AS ethashv1,
    max_by(ethashv1_no_zip9, snapshot_date) AS ethashv1_no_zip9,
    max_by(ethashv2, snapshot_date) AS ethashv2,
    max_by(listing_status_code_standardized, snapshot_date) AS listing_status_code_standardized,
    max_by(listing_status_description, snapshot_date) AS listing_status_description,
    max_by(property_sub_type_description, snapshot_date) AS property_sub_type_description,
    max_by(total_baths, snapshot_date) AS total_baths,
    max_by(original_listing_date_and_time, snapshot_date) AS original_listing_date_and_time,
    max_by(last_listing_date_and_time, snapshot_date) AS last_listing_date_and_time,
    MIN(first_seen) AS first_seen,
    MAX(off_market_date_and_time) AS off_market_date_and_time,
    MIN(off_market_transition_date) AS off_market_transition_date,
    MAX(close_date) AS close_date,
    MIN(closed_transition_date) AS closed_transition_date,
    max_by(current_listing_price, snapshot_date) AS current_listing_price,
    max_by(listing_agent_name, snapshot_date) AS listing_agent_name,
    max_by(listing_office_name, snapshot_date) AS listing_office_name,
    max_by(listing_agent_identifier, snapshot_date) AS listing_agent_identifier,
    max_by(buyer_agent_full_name, snapshot_date) AS buyer_agent_full_name,
    max_by(buyer_agent_identifier, snapshot_date) AS buyer_agent_identifier,
    max_by(listing_service_name, snapshot_date) AS listing_service_name,
    max_by(local_listing_id, snapshot_date) AS local_listing_id,
    max_by(fips_code, snapshot_date) AS fips_code,
    max_by(situs_state, snapshot_date) AS situs_state,
    max_by(situs_city, snapshot_date) AS situs_city,
    max_by(situs_zip_code, snapshot_date) AS situs_zip_code,
    max_by(situs_street_address, snapshot_date) AS situs_street_address,
    max_by(situs_unit_number, snapshot_date) AS situs_unit_number,
    max_by(year_built, snapshot_date) AS year_built,
    max_by(living_area_square_feet_standardized, snapshot_date) AS living_area_square_feet_standardized,
    max_by(geocoder_dataset, snapshot_date) AS geocoder_dataset,
    max_by(geocoder_geometrywkb, snapshot_date) AS geocoder_geometrywkb,
    max_by(geometry_type, snapshot_date) AS geometry_type,
    max_by(CAST(effective_year_built AS integer), snapshot_date) AS effective_year_built,
    max_by(CAST(bedrooms_all_buildings AS integer), snapshot_date) AS bedrooms_all_buildings,
    max_by(CAST(number_of_bathrooms AS integer), snapshot_date) AS number_of_bathrooms,
    max_by(living_square_feet_all_buildings, snapshot_date) AS living_square_feet_all_buildings,
    max_by(land_use_code, snapshot_date) AS land_use_code,
    MAX(snapshot_date),
    max_by(listing_period_id_computed, snapshot_date) AS listing_period_id
  FROM labeled
  GROUP BY clip, listing_period_id_computed
)
SELECT *
FROM integrated

"""