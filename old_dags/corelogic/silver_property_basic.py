from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from etdag import ETDAG
import datetime
from old_dags.corelogic.sql_silver_property_basic import *
from starburst_geocoder_operator import StarburstGeocoderOperator, supported_geocoder_columns


default_args = {
    "owner": "<PERSON><PERSON><PERSON>"
}

docs = """
# Overview
This dag contains the subsequent table maintenance that should occur after a new delivery of Property Basic.  
It handles 3 tables:
1. "olympus"."silver_corelogic"."property_basic2" a casted materialized view of property basic contains only the latest data per clip.
2. "olympus"."silver_corelogic"."clip_ethash_bridge" Maintained and populated by StarburstGeocoderOperator.  Not recommended to use this table directly, as there are inherently some not ideal things in it.
3. "s3"."silver_corelogic"."clip_ethash_bridge"  This view is recommended for BI's use.  It reduces records to 1 per etHash and removes all ungeocoded records. 

Error Handling
This dag should be being triggered by bronze_property_basic2.  Clearing and picking back up on any task is safe. 
Any successful run will remediate any previously failed runs. 
"""

with ETDAG(
    dag_id="silver_property_basic2",
    description="Maintains Materialized View containing latest record by clip and ethash:clip table",
    start_date=datetime.datetime(2024, 9, 6),
    schedule_interval="17 3 * * 6",
    default_args=default_args,
    catchup=False,
    tags=["corelogic", "silver", "property_basic"],
) as dag:
    dag.doc_md = docs

    create_silver_pb = SQLExecuteQueryOperator(
        task_id="create_silver_pb",
        conn_id="starburst",
        sql=SILVER_PROPERTY_BASIC2_MAT_VIEW,
        handler=list,
    )

    refresh_silver_pb = SQLExecuteQueryOperator(
        task_id="refresh_silver_pb",
        conn_id="starburst",
        sql=REFRESH_SILVER_PROPERTY_BASIC2,
        handler=list,
    )

    starburst_geocoder_operator = StarburstGeocoderOperator(
        source_table_name='"olympus"."silver_corelogic"."property_basic2"',
        source_row_identifier_column_name="clip",
        address1_column_name="situs_street_address",
        zipcode_column_name="situs_zip_code",
        bridge_table_name='"olympus"."silver_corelogic"."clip_ethash_bridge"',
        geocoder_columns=list(supported_geocoder_columns.keys()),
        task_id="geocode_property_basic"
    )

    create_silver_ethash_brdg = SQLExecuteQueryOperator(
        task_id="create_silver_ethash_brdg",
        conn_id="starburst",
        sql=CREATE_MAT_VIEW_S3_ETHASH_BRIDGE,
        handler=list,
    )

    refresh_silver_ethash_brdg = SQLExecuteQueryOperator(
        task_id="refresh_silver_ethash_brdg",
        conn_id="starburst",
        sql=REFRESH_MAT_VIEW_S3_ETHASH_BRIDGE,
        handler=list,
    )


    create_silver_pb >> refresh_silver_pb >> starburst_geocoder_operator >> create_silver_ethash_brdg >> refresh_silver_ethash_brdg
