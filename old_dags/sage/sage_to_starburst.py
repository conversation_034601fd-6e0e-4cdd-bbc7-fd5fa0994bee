from airflow.decorators import task
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from etdag import ETDAG
import json
from datetime import timedelta


### Documentation for the DAG
docs = """
Processing data from Sage to Starburst using a modified version of the Intacct SDK.
"""

### Declare Environment and Connection Variables
env = Variable.get("environment")
sage_conn = json.loads(Variable.get(f"sage_{env}"))

### Declare Default Arguments for the DAG
default_args = {
    "owner": "Daniel Sos",
    "retries": 3,
    "retry_delay": timedelta(minutes=3),  # Increased from 15 seconds for external API calls
    "retry_exponential_backoff": True,  # Handle API rate limiting
    "max_retry_delay": timedelta(minutes=15),  # Cap maximum delay
}

### Define the DAG
with ETDAG(
    dag_id="sage_data_to_starburst",
    description="Process data from Sage into Starburst",
    schedule="0 6 * * *",
    start_date=days_ago(2),
    default_args=default_args,
    catchup=False,
    tags=["sage", "bi-external"],
    concurrency=3,
) as dag:
    dag.doc_md = docs

    ### Tables to iterate over - could not identify the Subscription table
    tables = [
    "ar_account_label",
    "ar_adjustment", 
    "ar_invoices", 
    "ar_invoice_batch",
    "ar_payment_batch",
    "ar_term",
    "classes",
    "commission_group",
    "commission_type",
    "contacts",
    "customers",
    "customer_group",
    "cust_type",
    "departments",
    "employees",
    "items",
    "product_line",
    "sodocument",
    "sodocumententry",
    # "subscription",
    ]

    ### Declare virtual environment task - this will provide more flexibility
    @task.virtualenv(
        requirements=[
            "requirements.txt",
        ],
        system_site_packages=True,
    )

    ### Function to get data from Sage and create the bronze table query script - returns the SQL statement
    def get_data(sage_conn: dict, env:str, table: str, data_interval_start: str) -> str:
        import pandas as pd
        import polars as pl
        import s3fs
        from et_sageintacct_sdk import SageIntacctSDK

        # Get the S3 paths based on the table, date, and environment - returns the file path and external path
        def get_s3_paths(table: str, date: str, env: str) -> (str, str):
            base = f"et-datalake-bi-external/sage/{env}/new_{table}"
            external_path = f"s3://{base}/"
            file_path = f"{base}/export_date={date}/{date}-{table}.parquet"
            return file_path, external_path

        # Write the DataFrame to S3 in Parquet format - Justin's original code - returns nothing
        def write_parquet(input_df: pd.DataFrame, s3_uri: str) -> None:
            for c in input_df.columns:
                input_df[c] = input_df[c].astype(str)
            df = pl.DataFrame(input_df)
            fs = s3fs.S3FileSystem()
            with fs.open(s3_uri, mode="wb") as f:
                df.write_parquet(f, compression="snappy", use_pyarrow=True)

        # Create the SQL statement to create the bronze table using the columns from the response dataframe - this is a modified version of Justin's code - returns the SQL statement
        def create_bronze_table_statement(df: pd.DataFrame, table_name: str, external_location: str, env: str) -> str:
            columns = df.columns
            columns_definition = ",\n  ".join([f"{col.lower()} VARCHAR" for col in columns])
            columns_definition = columns_definition.replace(".", "_")

            create_table_statement = f"""
            CREATE TABLE IF NOT EXISTS s3.external_accounting.{env}_sage_{table_name} (
              {columns_definition},
              export_date date
            )
            WITH (
              external_location = '{external_location}',
              format = 'PARQUET',
              partitioned_by = ARRAY['export_date']
            )
            """

            return create_table_statement.strip()

        # Get connection to Sage Intacct using the provided credentials
        connection = SageIntacctSDK(
            sender_id=sage_conn["sender_id"],
            sender_password=sage_conn["sender_password"],
            user_id=sage_conn["user_id"],
            company_id=sage_conn["company_id"],
            user_password=sage_conn["user_password"],
        )

        # Get the data from Sage Intacct using the provided table name and call functions from above
        response = getattr(connection, table).get_all()
        df = pd.DataFrame(response)
        file_path, external_path = get_s3_paths(table, data_interval_start, env)
        write_parquet(df, file_path)
        create_table_stmt = create_bronze_table_statement(df, table, external_path, env)
        return create_table_stmt


    ### Create tasks for each table using a for loop
    for table in tables:
        print(f"Creating task for {table}")
        create_tbl_stmt = get_data.override(task_id=f"get_{table}")(
            sage_conn=sage_conn,
            env=env,
            table=table,
            data_interval_start="{{ data_interval_start.strftime('%Y-%m-%d') }}",
        )
        print(create_tbl_stmt)

        # Update Starburst task - creates table if not exists and syncs partition metadata
        update_starburst = SQLExecuteQueryOperator(
            task_id=f"update_{table}",
            conn_id="trino_conn",
            sql=[
                create_tbl_stmt,
                f"CALL s3.system.sync_partition_metadata('external_accounting', '{env}_sage_{table}', 'ADD')",
            ],
            handler=list,
            retries=4,  # Higher retries for database operations
            retry_delay=timedelta(minutes=2),  # Shorter delay for DB operations
        )

        # Set task dependencies
        create_tbl_stmt >> update_starburst