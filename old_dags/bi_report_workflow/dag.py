"""
BI Report Workflow DAG Template

This DAG processes BI reports by executing Trino queries, processing results,
uploading to S3, and sending notifications with comprehensive status callbacks.

Expected Input Configuration (passed via DAG conf):
{
    "execution_id": 123,
    "report_id": 456,
    "report_name": "Daily Sales Report",
    "trino_query": "SELECT * FROM sales WHERE date = current_date",
    "email": "<EMAIL>",
    "large_data_set": false,
    "gzip_compress": true,
    "s3_locations": [
        {
            "s3_bucket": "reports-bucket",
            "s3_key": "daily_reports/sales_2025_01_01.csv"
        }
    ],
    "materialized_view_dependencies": ["mv_sales_summary"],
    "execution_type": "manual",
    "triggered_by": "<EMAIL>",
    "api_callback_url": "https://api.company.com/api/v1/bi-report-executions/123/callback"
}

Callback API Contract:
POST {api_callback_url}
Content-Type: application/json
{
    "status": "running|completed|failed",
    "started_at": "2025-01-01T10:00:00Z",
    "completed_at": "2025-01-01T10:05:00Z",
    "result_s3_path": "s3://bucket/path/file.csv",
    "row_count": 1500,
    "file_size_bytes": 245760,
    "error_message": "Error details if failed",
    "execution_metadata": {"processing_time_seconds": 300}
}
"""

# test_conf =   {
#     "execution_id": 123,
#     "report_id": 456,
#     "report_name": "test_1",
#     "trino_query": "SELECT * FROM s3.dev_prototyping.auto_4eyes_segments limit 10;",
#     "email": "<EMAIL>",
#     "large_data_set": True,
#     "gzip_compress": True,
#     "s3_locations": [
#         {
#             "s3_bucket": "vr-timestamp",
#             "s3_key": "bi_sources/test/bi_config_test/test1.csv"
#         }
#     ],
#     "materialized_view_dependencies": [],
#     "execution_type": "manual",
#     "triggered_by": "<EMAIL>",
#     "api_callback_url": "https://bi-api.k8s.eltoro.com/api/v1/bi-report-executions/1/callback"
# }

from datetime import datetime, timedelta, timezone
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
import requests
import json
import logging
import gzip
import tempfile
from typing import Dict, Any

from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from pygene.base_api import NextGenBaseAPI
import time
from airflow.exceptions import AirflowFailException

# DAG Configuration
DEFAULT_ARGS = {
    'owner': 'data-services',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(hours=2),
}

# Environment-specific configuration
TRINO_CONNECTION_ID = "starburst" #Variable.get("trino_connection_id", "trino_default")
AWS_CONNECTION_ID = "s3_conn" #Variable.get("aws_connection_id", "aws_default")

logger = logging.getLogger(__name__)

def make_request_with_token_renewal(method, url, env, max_retries=5, **kwargs):
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    base_api = NextGenBaseAPI(client_id, client_secret, env=env)

    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }
    kwargs["headers"] = headers

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.request(method, url, timeout=10, **kwargs)
            if base_api._resp_handler(response):
                print(f"Attempt {attempt}: Refreshing token and retrying...")
                base_api.refresh_token()
                headers["Authorization"] = f"Bearer {base_api.access_token}"
                kwargs["headers"] = headers
                response = requests.request(method, url, timeout=10, **kwargs)

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt} failed: {e}")
            time.sleep(180)  # Wait for 3 minutes before retrying
            if attempt == max_retries:
                raise AirflowFailException(
                    f"Request to {url} failed after {max_retries} attempts: {e}"
                )

    return response

def send_status_callback(context: Dict[str, Any], status: str, **kwargs) -> None:
    """Send status update to the BI API callback endpoint"""
    conf = context['dag_run'].conf
    callback_url = conf.get('api_callback_url')
    execution_id = conf.get('execution_id')
    
    if not callback_url:
        logger.warning(f"No callback URL provided for execution {execution_id}")
        return
    
    # Prepare callback payload
    payload = {
        'status': status,
        **kwargs
    }
    
    # Add timestamps
    if status == 'running' and 'started_at' not in payload:
        payload['started_at'] = datetime.now(timezone.utc).isoformat()
    elif status in ['completed', 'failed'] and 'completed_at' not in payload:
        payload['completed_at'] = datetime.now(timezone.utc).isoformat()
    
    try:
        response = make_request_with_token_renewal("POST", callback_url, env="prod", json=payload) #hardcode env="prod" for now
        logger.info(f"Status callback sent successfully: {status} for execution {execution_id}")
        logger.info(f"Response: {response.json()}")
    except Exception as e:
        logger.error(f"Failed to send status callback: {str(e)}")
        # Don't fail the task if callback fails
        pass

def start_execution(**context) -> None:
    """Mark execution as started and validate inputs"""
    conf = context['dag_run'].conf
    
    # Validate required fields
    required_fields = ['execution_id', 'report_id', 'report_name', 'trino_query', 'email', 'api_callback_url']
    missing_fields = [field for field in required_fields if not conf.get(field)]
    
    if missing_fields:
        error_msg = f"Missing required configuration fields: {missing_fields}"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise ValueError(error_msg)
    
    logger.info(f"Starting BI report execution {conf['execution_id']} for report '{conf['report_name']}'")
    
    # Send running status callback
    send_status_callback(context, 'running')

def refresh_materialized_views(**context) -> None:
    """Refresh materialized view dependencies if specified"""
    conf = context['dag_run'].conf
    mv_dependencies = conf.get('materialized_view_dependencies', [])
    
    if not mv_dependencies:
        logger.info("No materialized view dependencies to refresh")
        return
    
    logger.info(f"Refreshing materialized views: {mv_dependencies}")
    
    trino_hook = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID)
    for mv in mv_dependencies:
        trino_hook.run(f"REFRESH MATERIALIZED VIEW {mv}")
    
    logger.info("Materialized view refresh completed")

def execute_trino_query(**context) -> str:
    """Execute the Trino query and return results file path"""
    conf = context['dag_run'].conf
    query = conf['trino_query']
    large_data_set = conf.get('large_data_set', False)
    
    logger.info(f"Executing Trino query for execution {conf['execution_id']}")
    logger.info(f"Query: {query}")
    logger.info(f"Large dataset mode: {large_data_set}")
    
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
            temp_file_path = temp_file.name
        row_count = 0

        if large_data_set:
            df_chunks = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID).get_pandas_df_by_chunks(
                sql=query, chunksize=1_000_000
            )
            for i, df_chunk in enumerate(df_chunks):
                chunk_rows = len(df_chunk)
                row_count += chunk_rows
                logger.info(f"Writing chunk {i}")
                if i == 0:
                    df_chunk.to_csv(temp_file_path, index=False)
                else:
                    df_chunk.to_csv(temp_file_path, mode="a", header=False, index=False)
        else:
            df = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID).get_pandas_df(sql=query)
            row_count = len(df)
            df.to_csv(temp_file_path, index=False)
            
        # Store results metadata in XCom
        context['task_instance'].xcom_push(key='row_count', value=row_count)
        context['task_instance'].xcom_push(key='temp_file_path', value=temp_file_path)
        
        return temp_file_path
        
    except Exception as e:
        error_msg = f"Trino query execution failed: {str(e)}"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise

def process_and_upload_results(**context) -> Dict[str, Any]:
    """Process query results and upload to specified S3 locations"""
    conf = context['dag_run'].conf
    s3_locations = conf.get('s3_locations', [])
    gzip_compress = conf.get('gzip_compress', True)
    
    # Get results from previous task
    temp_file_path = context['task_instance'].xcom_pull(task_ids='execute_query', key='temp_file_path')
    row_count = context['task_instance'].xcom_pull(task_ids='execute_query', key='row_count')
    
    if not temp_file_path or not s3_locations:
        error_msg = "No results file or S3 locations specified"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise ValueError(error_msg)
    
    logger.info(f"Processing results file: {temp_file_path}")
    logger.info(f"Uploading to {len(s3_locations)} S3 location(s)")
    
    try:
        s3_hook = S3Hook(aws_conn_id="s3_conn")

        uploaded_files = []
        total_file_size = 0
        
        for s3_location in s3_locations:
            bucket = s3_location['s3_bucket']
            key = s3_location['s3_key']
            
            logger.info(f"Uploading to s3://{bucket}/{key}")
            
            # Read and optionally compress the file
            with open(temp_file_path, 'rb') as f:
                file_data = f.read()
                
            if gzip_compress:
                # Compress the data
                compressed_data = gzip.compress(file_data)
                file_data = compressed_data
                # Add .gz extension if not present
                if not key.endswith('.gz'):
                    key += '.gz'
            
            file_size = len(file_data)
            total_file_size += file_size
            
            s3_hook.load_bytes(
                bytes_data=file_data,
                key=key,
                bucket_name=bucket,
                replace=True,
            )
            
            uploaded_files.append({
                'bucket': bucket,
                'key': key,
                'size_bytes': file_size,
                's3_path': f's3://{bucket}/{key}'
            })
            
            logger.info(f"Successfully uploaded to s3://{bucket}/{key} ({file_size} bytes)")
        
        # Clean up temporary file
        import os
        os.remove(temp_file_path)
        
        upload_metadata = {
            'uploaded_files': uploaded_files,
            'total_file_size_bytes': total_file_size,
            'row_count': row_count,
            'compression_enabled': gzip_compress
        }
        
        # Store metadata in XCom for final callback
        context['task_instance'].xcom_push(key='upload_metadata', value=upload_metadata)
        
        return upload_metadata
        
    except Exception as e:
        error_msg = f"File processing and upload failed: {str(e)}"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise

def send_completion_notification(**context) -> None:
    """Send email notification and final status callback"""
    conf = context['dag_run'].conf
    upload_metadata = context['task_instance'].xcom_pull(task_ids='process_upload', key='upload_metadata')
    
    execution_id = conf['execution_id']
    # report_name = conf['report_name']
    # email = conf['email']
    
    # Prepare completion callback
    primary_s3_path = upload_metadata['uploaded_files'][0]['s3_path'] if upload_metadata['uploaded_files'] else None
    
    completion_data = {
        'result_s3_path': primary_s3_path,
        'row_count': upload_metadata['row_count'],
        'file_size_bytes': upload_metadata['total_file_size_bytes'],
        'execution_metadata': {
            'uploaded_files': upload_metadata['uploaded_files'],
            'compression_enabled': upload_metadata['compression_enabled'],
            'processing_completed_at': datetime.now(timezone.utc).isoformat()
        }
    }
    
    # Send completion callback
    send_status_callback(context, 'completed', **completion_data)
    
    logger.info(f"BI report execution {execution_id} completed successfully")

def handle_failure(**context) -> None:
    """Handle task failures and send failure callback"""
    conf = context['dag_run'].conf
    
    # Get failure information
    task_instance = context.get('task_instance')
    exception = context.get('exception')
    
    error_message = str(exception) if exception else "Unknown error occurred"
    
    logger.error(f"BI report execution {conf['execution_id']} failed: {error_message}")
    
    # Send failure callback
    send_status_callback(
        context, 
        'failed', 
        error_message=error_message,
        execution_metadata={
            'failed_task': task_instance.task_id if task_instance else 'unknown',
            'failure_timestamp': datetime.now(timezone.utc).isoformat()
        }
    )

# Create the DAG
dag = DAG(
    dag_id='bi_report_workflow',
    default_args=DEFAULT_ARGS,
    description='BI Report Processing Workflow',
    schedule_interval=None,  # Triggered externally
    catchup=False,
    max_active_runs=10,
    tags=['bi-reports', 'data-processing'],
)

# Task definitions
start_task = PythonOperator(
    task_id='start_execution',
    python_callable=start_execution,
    dag=dag,
    on_failure_callback=handle_failure,
)

refresh_mv_task = PythonOperator(
    task_id='refresh_materialized_views',
    python_callable=refresh_materialized_views,
    dag=dag,
    on_failure_callback=handle_failure,
)

execute_query_task = PythonOperator(
    task_id='execute_query',
    python_callable=execute_trino_query,
    dag=dag,
    on_failure_callback=handle_failure,
)

process_upload_task = PythonOperator(
    task_id='process_upload',
    python_callable=process_and_upload_results,
    dag=dag,
    on_failure_callback=handle_failure,
)

completion_task = PythonOperator(
    task_id='send_completion',
    python_callable=send_completion_notification,
    dag=dag,
    on_failure_callback=handle_failure,
)

# Task dependencies
start_task >> refresh_mv_task >> execute_query_task >> process_upload_task >> completion_task