reporting_query = """
WITH extracted AS (
    SELECT
        *,
        REGEXP_EXTRACT(audience_name, '^(.*?)-\\d{{4}}-\\d{{2}}-\\d{{2}}', 1) AS type_of_audience
    FROM
        s3.external_dataservices_api.prod_compiled_audience
    WHERE
        id = {job_id}
),
match_target AS (
    SELECT
        ethashv1,
        audience_id,
        serve_start_date AS first_targeted,
        type_of_audience
    FROM
        (
            SELECT
                *,
                ROW_NUMBER() OVER (
                    PARTITION BY ethashv1,
                    type_of_audience
                    ORDER BY
                        serve_start_date ASC
                ) AS row_num
            FROM
                extracted
        ) sub
    WHERE
        row_num = 1
        AND matched = 'true'
),
max_partition AS (
    SELECT
        MAX(transfer_date) AS max_transfer_date
    FROM
        "s3"."external_garage"."sp_audience_owner$partitions"
),
ford_garage_data AS (
    SELECT
        DISTINCT sga.ethashv1
    FROM
        "olympus"."bronze_garage"."sp_garage_address_bridge_tbl" sga
        JOIN "s3"."external_garage"."sp_audience_owner" sao ON sao.polkid = sga.polkid
    WHERE
        sao.ownford_b = '1'
        AND sao.transfer_date = (
            SELECT
                max_transfer_date
            FROM
                max_partition
        )
)
SELECT
    mt.ethashv1,
    mt.audience_id,
    mt.first_targeted,
    mt.type_of_audience,
    SUBSTR(mt.ethashv1, 2, 5) AS zip_code,
    CASE
        WHEN fg.ethashv1 IS NOT NULL THEN 'yes'
        ELSE 'no'
    END AS ford_in_garage
FROM
    match_target mt
    LEFT JOIN ford_garage_data fg ON mt.ethashv1 = fg.ethashv1
ORDER BY
    mt.ethashv1 ASC,
    mt.first_targeted ASC;
    """


find_audience_query = """
SELECT order_line_id, org_id, order_line_name FROM "s3"."silver_platform_services"."order_lines"
WHERE org_id = 'MqqZzXAPkDXhziNyC'
  AND CAST(order_line_start AS DATE) = DATE('{start_date}')
  AND creative_type = 'CREATIVE_TYPE_BANNER'
  AND cardinality(audiences) > 0; """
