from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON>ook
from etdag import ETDAG
from old_dags.fdaf_reporting.queries import find_audience_query
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
from operators.compiled_audience_operator import CompiledAudienceOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.models import Variable
import logging

logger = logging.getLogger(__name__)


default_args = {
    "owner": "BP",
    "start_date": datetime(2025, 2, 20),
}
"""
    This Dag will grab order_line_ids from the previous month and use them to create a compiled audience.
    Once the OL ids are gathered, the compiled audience will be created
    and the fdaf_reporting_generator will be triggered with the compiled audience id.
"""


def get_first_day_of_previous_month():
    return (date.today().replace(day=1) - relativedelta(months=1)).strftime("%Y-%m-%d")


@task
def get_orderline_ids():
    start_date = get_first_day_of_previous_month()
    logger.info(f"🚀 Grabbing audeinces from OLs starting on {start_date}")
    tr = TrinoHook(trino_conn_id="trino_conn")
    df = tr.get_pandas_df(sql=find_audience_query.format(start_date=start_date))

    logger.info(f"Succesfully found {df.shape[0]} order_lines")
    # sample_df = df.sample(1)
    # list_of_audiences = sample_df["order_line_id"].unique().tolist()
    list_of_audiences = df["order_line_id"].unique().tolist()
    return list_of_audiences


with ETDAG(
    dag_id="fdaf_compiled_audiences",
    description="Generates a list of audicences to be used for the fdaf model",
    start_date=datetime(2025, 2, 20),
    default_args=default_args,
    catchup=False,
    et_failure_msg=True,
    schedule="0 9 3 * *",
    tags=["fdaf_reporting", "team:DND"],
) as dag:

    audience_gatherer = get_orderline_ids()

    create_compiled_audience = CompiledAudienceOperator(
        task_id="create_compiled_audience",
        operation="create",
        name="fdaf_south",
        order_line_ids=audience_gatherer,
        wait_for_job_to_finish=True,
        max_active_tis_per_dag=3,
        env=Variable.get("environment"),
    )

    trigger_reporting_generator = TriggerDagRunOperator(
        task_id="trigger_reporting_generator",
        trigger_dag_id="fdaf_reporting_generator",
        conf={
            "id": "{{ ti.xcom_pull(task_ids='create_compiled_audience', key='return_value')['id'] }}"
        },
    )

    audience_gatherer >> create_compiled_audience >> trigger_reporting_generator
