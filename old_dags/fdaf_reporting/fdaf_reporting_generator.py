from airflow.decorators import task
from airflow.operators.python import get_current_context
from airflow.providers.trino.hooks.trino import TrinoHook
from etdag import ETDAG
import pandas as pd
from old_dags.fdaf_reporting.queries import reporting_query
import tempfile
from datetime import datetime
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models.param import Param
from airflow.models import Variable
from airflow.exceptions import AirflowException
import logging

logger = logging.getLogger(__name__)


default_args = {
    "owner": "BP",
    "start_date": datetime(2025, 2, 20),
}

"""
    This Dag will be triggered by the fdaf_compiled_audiences dag using the job_id that is passed
    Once it has the job id it will sync the proper partiotions in trino
    Once the partitions are synced it will aggregate the data and upload it to s3
    
    FAILURE:.....
    if the previous dag fails due to a timeout and can not pass the job_id this dag can be rerun manually using the job_id
       
"""


S3_BUCKET = "vr-timestamp"

if Variable.get("environment") == "prod":
    S3_KEY = "bi_sources/auto_tables/fdaf_reporting/fdaf_south_reporting.csv"
if Variable.get("environment") == "dev":
    S3_KEY = "bi_sources/dev/auto_tables/fdaf_reporting/fdaf_south_reporting.csv"


@task
def get_id():
    context = get_current_context()
    job_id = None
    dag_run = context.get("dag_run")
    if dag_run and dag_run.conf:
        job_id = dag_run.conf.get("id")

    if job_id is None:
        job_id = context.get("params", {}).get("job_id")

    if job_id is None:
        raise AirflowException(
            "❌ `job_id` is required but was not provided! DAG will fail."
        )

    try:
        job_id = int(job_id)
    except ValueError:
        raise AirflowException(
            f"❌ Invalid `job_id` received: {job_id}. Expected an integer."
        )

    logger.info(f"✅ Retrieved job_id: {job_id}")

    return job_id


@task
def sync_trino(job_id):

    logger.info(
        f"🚀 Syncing table: external_dataservices_api.prod_compiled_audience for job_id: {job_id}"
    )
    trino_hook = TrinoHook(trino_conn_id="trino_conn")
    sync_query = f"CALL s3.system.register_partition('external_dataservices_api', 'prod_compiled_audience', ARRAY['id'], ARRAY['{job_id}'])"
    trino_hook.run(sync_query)
    logger.info(
        f"✅ Sync successful for table: external_dataservices_api.prod_compiled_audience for job_id: {job_id}"
    )
    return job_id


###W This will run the reporting query that will aggregate the compiled audinece data and join garage data
@task
def aggregate_data_and_upload(job_id):
    logger.info("🚀 Aggregating data with garage")
    tr = TrinoHook(trino_conn_id="trino_conn")
    df = tr.get_pandas_df(sql=reporting_query.format(job_id=job_id))
    print(df.shape)

    s3_hook = S3Hook(aws_conn_id="s3_conn")
    with tempfile.NamedTemporaryFile(
        mode="w+", delete=False, suffix=".csv"
    ) as tmp_file:
        temp_file_path = tmp_file.name

    try:
        s3_hook.get_conn().download_file(
            Bucket=S3_BUCKET, Key=S3_KEY, Filename=temp_file_path
        )
        logger.info(f"✅ File downloaded to temporary path: {temp_file_path}")
        existing_df = pd.read_csv(temp_file_path, dtype=str)
    except Exception as e:
        logger.info(f"❌ Error retrieving file from S3: {e}")
        existing_df = pd.DataFrame()

    if existing_df is not None and not existing_df.empty:
        updated_df = pd.concat([existing_df, df], ignore_index=True)
    else:
        updated_df = df
    updated_df.to_csv(temp_file_path, index=False)

    logger.info(f"🚀 Uploading to s3_path: s3://{S3_BUCKET}/{S3_KEY}")
    s3_hook.load_file(
        filename=temp_file_path, key=S3_KEY, bucket_name=S3_BUCKET, replace=True
    )

    logger.info(f"✅ Updated CSV uploaded successfully to s3://{S3_BUCKET}/{S3_KEY}")


with ETDAG(
    dag_id="fdaf_reporting_generator",
    description="Generates a list of audicences to be used for the fdaf model",
    start_date=datetime(2025, 2, 20),
    default_args=default_args,
    schedule=None,
    catchup=False,
    tags=["fdaf_reporting", "team:DND"],
    et_failure_msg=True,
    params={
        "job_id": Param(0, type="integer"),
    },
) as dag:
    job_id = get_id()
    sync = sync_trino(job_id=job_id)
    aggregate = aggregate_data_and_upload(job_id=job_id)

    job_id >> sync >> aggregate
