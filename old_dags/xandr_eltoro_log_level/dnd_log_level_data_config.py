SECRET_KEY = "prod/bi/quoteenrichment"

EXISTING_HOURS_STATEMENT_STANDARD_FEED = """
                     SELECT distinct(hour(partition_time_millis)) 
                     FROM "olympus"."bronze_et_xandr"."standard_feed" 
                     where DATE(partition_time_millis) = DATE('p_date')                                  
                  """


DROP_TABLE_STANDARD_FEED = """
               DROP TABLE IF EXISTS "s3"."bronze_et_xandr"."standard_feed"
             """

CREATE_TABLE_STATMENT_STANDARD_FEED = """
CREATE TABLE "s3"."bronze_et_xandr"."standard_feed" (
       auction_id_64 int ,
       date_time int ,
       user_tz_offset int ,
       creative_width int ,
       creative_height int ,
       media_type int ,
       fold_position int ,
       event_type varchar ,
       imp_type int ,
       payment_type int ,
       media_cost_cpm double ,
       revenue_type int ,
       media_cost double ,
       buyer_bid double ,
       ecp double ,
       eap double ,
       is_imp int ,
       is_learn int ,
       predict_type_rev int ,
       user_id_64 int ,
       ip_address varchar ,
       ip_address_trunc varchar ,
       country varchar ,
       region varchar ,
       operating_system int ,
       browser int ,
       language int ,
       venue_id int ,
       seller_member_id int ,
       publisher_id int ,
       site_id int ,
       site_domain varchar ,
       tag_id int ,
       external_inv_id int ,
       reserve_price double ,
       seller_revenue_cpm double ,
       media_buy_rev_share_pct double ,
       pub_rule_id int ,
       seller_currency varchar ,
       publisher_currency varchar ,
       publisher_exchange_rate double ,
       serving_fees_cpm double ,
       serving_fees_revshare double ,
       buyer_member_id int ,
       advertiser_id int ,
       brand_id int ,
       advertiser_frequency int ,
       advertiser_recency int ,
       insertion_order_id int ,
       line_item_id int ,
       campaign_id int ,
       creative_id int ,
       creative_freq int ,
       creative_rec int ,
       cadence_modifier double ,
       can_convert int ,
       user_group_id int ,
       is_control int ,
       control_pct double ,
       control_creative_id int ,
       is_click int ,
       pixel_id int ,
       is_remarketing int ,
       post_click_conv int ,
       post_view_conv int ,
       post_click_revenue double ,
       post_view_revenue double ,
       order_id varchar ,
       external_data varchar ,
       pricing_type varchar ,
       booked_revenue double ,
       booked_revenue_adv_curr double ,
       commission_cpm double ,
       commission_revshare double ,
       auction_service_deduction double ,
       auction_service_fees double ,
       creative_overage_fees double ,
       clear_fees double ,
       buyer_currency varchar ,
       advertiser_currency varchar ,
       advertiser_exchange_rate double ,
       latitude varchar ,
       longitude varchar ,
       device_unique_id varchar ,
       device_id int ,
       carrier_id int ,
       deal_id int ,
       view_result int ,
       application_id varchar ,
       supply_type int ,
       sdk_version varchar ,
       ozone_id int ,
       billing_period_id int ,
       view_non_measurable_reason int ,
       external_uid varchar ,
       request_uuid varchar ,
       dma int ,
       city int ,
       mobile_app_instance_id int ,
       traffic_source_code varchar ,
       external_request_id varchar ,
       deal_type int ,
       ym_floor_id int ,
       ym_bias_id int ,
       is_filtered_request int ,
       age int ,
       gender varchar ,
       is_exclusive int ,
       bid_priority int ,
       custom_model_id int ,
       custom_model_last_modified int ,
       custom_model_leaf_name varchar ,
       data_costs double ,
       device_type int ,
       postal_code varchar ,
       imps_for_budget_caps_pacing int ,
       hashed_user_id_64 varbinary ,
       latitude_trunc varchar ,
       longitude_trunc varchar ,
       partition_time_millis timestamp(3) ,
       split_id int ,
       tc_string varchar ,
       partner_fees double ,
       external_campaign_id varchar ,
       playback_method int ,
       video_context int ,
       player_size_id int ,
       error_code int ,
       personal_identifiers array(ROW(identity_type int, identity_value varchar)) ,
       device_make_id int ,
       postal_code_ext varchar ,
       extended_ids array(ROW(id_type int, id_value varchar)) ,
       segment_data_costs double ,
       feature_costs double ,
       fallback_ad_index int,
       is_private_auction int,
       private_auction_eligible int,
       chrome_traffic_label varchar
      )
    WITH (
       external_location = 's3://eltoro-apnx-reporting/s3_path/',
       format = 'AVRO',
       avro_schema_url = 's3a://eltoro-apnx-reporting/logleveldata/standard_feed/export/standard_feed/standard_feed.avsc'
    )
    """

INSERT_STATEMENT_STANDARD_FEED = """ 
                        insert into
                        olympus.bronze_et_xandr.standard_feed (
                           auction_id_64,
                           date_time,
                           user_tz_offset,
                           creative_width,
                           creative_height,
                           media_type,
                           fold_position,
                           event_type,
                           imp_type,
                           payment_type,
                           media_cost_cpm,
                           revenue_type,
                           media_cost,
                           buyer_bid,
                           ecp,
                           eap,
                           is_imp,
                           is_learn,
                           predict_type_rev,
                           user_id_64,
                           ip_address,
                           ip_address_trunc,
                           country,
                           region,
                           operating_system,
                           browser,
                           language,
                           venue_id,
                           seller_member_id,
                           publisher_id,
                           site_id,
                           site_domain,
                           tag_id,
                           external_inv_id,
                           reserve_price,
                           seller_revenue_cpm,
                           media_buy_rev_share_pct,
                           pub_rule_id,
                           seller_currency,
                           publisher_currency,
                           publisher_exchange_rate,
                           serving_fees_cpm,
                           serving_fees_revshare,
                           buyer_member_id,
                           advertiser_id,
                           brand_id,
                           advertiser_frequency,
                           advertiser_recency,
                           insertion_order_id,
                           line_item_id,
                           campaign_id,
                           creative_id,
                           creative_freq,
                           creative_rec,
                           cadence_modifier,
                           can_convert,
                           user_group_id,
                           is_control,
                           control_pct,
                           control_creative_id,
                           is_click,
                           pixel_id,
                           is_remarketing,
                           post_click_conv,
                           post_view_conv,
                           post_click_revenue,
                           post_view_revenue,
                           order_id,
                           external_data,
                           pricing_type,
                           booked_revenue,
                           booked_revenue_adv_curr,
                           commission_cpm,
                           commission_revshare,
                           auction_service_deduction,
                           auction_service_fees,
                           creative_overage_fees,
                           clear_fees,
                           buyer_currency,
                           advertiser_currency,
                           advertiser_exchange_rate,
                           latitude,
                           longitude,
                           device_unique_id,
                           device_id,
                           carrier_id,
                           deal_id,
                           view_result,
                           application_id,
                           supply_type,
                           sdk_version,
                           ozone_id,
                           billing_period_id,
                           view_non_measurable_reason,
                           external_uid,
                           request_uuid,
                           dma,
                           city,
                           mobile_app_instance_id,
                           traffic_source_code,
                           external_request_id,
                           deal_type,
                           ym_floor_id,
                           ym_bias_id,
                           is_filtered_request,
                           age,
                           gender,
                           is_exclusive,
                           bid_priority,
                           custom_model_id,
                           custom_model_last_modified,
                           custom_model_leaf_name,
                           data_costs,
                           device_type,
                           postal_code,
                           imps_for_budget_caps_pacing,
                           hashed_user_id_64,
                           latitude_trunc,
                           longitude_trunc,
                           partition_time_millis,
                           split_id,
                           tc_string,
                           partner_fees,
                           external_campaign_id,
                           playback_method,
                           video_context,
                           player_size_id,
                           error_code,
                           personal_identifiers,
                           device_make_id,
                           postal_code_ext,
                           extended_ids,
                           segment_data_costs,
                           feature_costs,
                           fallback_ad_index,
                           region_id,
                           is_private_auction,
                           private_auction_eligible,
                           chrome_traffic_label
                        )
                        SELECT
                        auction_id_64,
                        date_time,
                        user_tz_offset,
                        creative_width,
                        creative_height,
                        media_type,
                        fold_position,
                        event_type,
                        imp_type,
                        payment_type,
                        media_cost_cpm,
                        revenue_type,
                        media_cost,
                        buyer_bid,
                        ecp,
                        eap,
                        is_imp,
                        is_learn,
                        predict_type_rev,
                        user_id_64,
                        ip_address,
                        ip_address_trunc,
                        country,
                        region,
                        operating_system,
                        browser,
                        language,
                        venue_id,
                        seller_member_id,
                        publisher_id,
                        site_id,
                        site_domain,
                        tag_id,
                        external_inv_id,
                        reserve_price,
                        seller_revenue_cpm,
                        media_buy_rev_share_pct,
                        pub_rule_id,
                        seller_currency,
                        publisher_currency,
                        publisher_exchange_rate,
                        serving_fees_cpm,
                        serving_fees_revshare,
                        buyer_member_id,
                        advertiser_id,
                        brand_id,
                        advertiser_frequency,
                        advertiser_recency,
                        insertion_order_id,
                        line_item_id,
                        campaign_id,
                        creative_id,
                        creative_freq,
                        creative_rec,
                        cadence_modifier,
                        can_convert,
                        user_group_id,
                        is_control,
                        control_pct,
                        control_creative_id,
                        is_click,
                        pixel_id,
                        is_remarketing,
                        post_click_conv,
                        post_view_conv,
                        post_click_revenue,
                        post_view_revenue,
                        order_id,
                        external_data,
                        pricing_type,
                        booked_revenue,
                        booked_revenue_adv_curr,
                        commission_cpm,
                        commission_revshare,
                        auction_service_deduction,
                        auction_service_fees,
                        creative_overage_fees,
                        clear_fees,
                        buyer_currency,
                        advertiser_currency,
                        advertiser_exchange_rate,
                        latitude,
                        longitude,
                        device_unique_id,
                        device_id,
                        carrier_id,
                        deal_id,
                        view_result,
                        application_id,
                        supply_type,
                        sdk_version,
                        ozone_id,
                        billing_period_id,
                        view_non_measurable_reason,
                        external_uid,
                        request_uuid,
                        dma,
                        city,
                        mobile_app_instance_id,
                        traffic_source_code,
                        external_request_id,
                        deal_type,
                        ym_floor_id,
                        ym_bias_id,
                        is_filtered_request,
                        age,
                        gender,
                        is_exclusive,
                        bid_priority,
                        custom_model_id,
                        custom_model_last_modified,
                        custom_model_leaf_name,
                        NULL as data_costs,
                        device_type,
                        postal_code,
                        imps_for_budget_caps_pacing,
                        hashed_user_id_64,
                        latitude_trunc,
                        longitude_trunc,
                        partition_time_millis,
                        split_id,
                        tc_string,
                        partner_fees,
                        external_campaign_id,
                        playback_method,
                        video_context,
                        player_size_id,
                        error_code,
                        personal_identifiers,
                        device_make_id,
                        postal_code_ext,
                        extended_ids,
                        segment_data_costs,
                        feature_costs,
                        fallback_ad_index,
                        region_id,
                        is_private_auction,
                        private_auction_eligible,
                        chrome_traffic_label
                        FROM
                        "s3"."bronze_et_xandr"."standard_feed"
 """


###################################################################################################
# Segment Feed
###################################################################################################


# Get Date Partition
EXISTING_HOURS_STATEMENT_SEGMENT_FEED = """
                     SELECT distinct(hour(partition_time_millis)) 
                     FROM "olympus"."bronze_et_xandr"."segment_feed" 
                     where DATE(partition_time_millis) = DATE('p_date')                                  
                  """

# Drop Staging Table
DROP_TABLE_SEGMENT_FEED = """
               DROP TABLE IF EXISTS "s3"."bronze_et_xandr"."segment_feed"
             """
# Create staging Table and populate data from s3
CREATE_TABLE_STATMENT_SEGMENT_FEED = """
CREATE TABLE "s3"."bronze_et_xandr"."segment_feed" (
       date_time int ,
       user_id_64 int ,
       member_id int ,
       segment_id int ,
       is_daily_unique int ,
       is_monthly_unique int ,
       value int ,
       partition_time_millis varchar ,
       hashed_user_id_64 int
      )
    WITH (
       external_location = 's3://eltoro-apnx-reporting/s3_path/',
       format = 'AVRO',
       avro_schema_url = 's3://eltoro-apnx-reporting/logleveldata/segment_feed/export/segment_feed/segement_feed.avsc'
    )

    """

# Insert into main table from stg table
INSERT_STATEMENT_SEGMENT_FEED = """ insert into "olympus"."bronze_et_xandr"."segment_feed"
select * from "s3"."bronze_et_xandr"."segment_feed"
                        """


###################################################################################################
# Curate Feed
###################################################################################################

# Get Date Partition
EXISTING_HOURS_STATEMENT_CURATE_FEED = """
                     SELECT distinct(hour(partition_time_millis)) 
                     FROM "olympus"."bronze_et_xandr"."curate_feed" 
                     where DATE(partition_time_millis) = DATE('p_date')                                  
                  """

# Drop Staging Table
DROP_TABLE_CURATE_FEED = """
               DROP TABLE IF EXISTS "s3"."bronze_et_xandr"."curate_feed"
             """


# Create staging Table and populate data from s3
CREATE_TABLE_STATMENT_CURATE_FEED = """
CREATE TABLE "s3"."bronze_et_xandr"."curate_feed" (
       auction_id_64 int ,
       date_time double ,
       user_tz_offset int ,
       media_type int ,
       event_type varchar ,
       user_id_64 int ,
       hashed_user_id_64 int ,
       ip_address varchar ,
       ip_address_trunc varchar,
       country varchar,
       region varchar,
       dma int,
       city int,
       postal_code varchar,
       latitude varchar,
       latitude_trunc varchar,
       longitude varchar,
       longitude_trunc varchar,
       device_unique_id varchar,
       device_type int,
       tc_string varchar,
       curated_dea_id int,
       gross_revenue_dollars double,
       curator_margin double,
       total_tech_fees_dollars double,
       total_cost_dollars double,
       net_media_cost_dollars double,
       seller_member_id int,
       publisher_id int,
       site_id int,
       site_domain varchar,
       tag_id int,
       application_id varchar,
       mobile_app_instance_id int,
       buyer_member_id int,
       creative_id int,
       brand_id int,
       seller_deal_id int,
       view_result int,
       view_non_measurable_reason int,
       supply_type int,
       creative_width int,
       creative_height int,
       partition_time_millis timestamp(3),
       operation_system int,
       browser int,
       language int,
       device_id int,
       extended_ids array(ROW(id_type int, id_value varchar)),
       curate_deal_code varchar,
       split_id int,
       external_campaign_id varchar,
       external_birdrequest_imp_id bigint,
       external_birdrequest_id bigint,
       postal_code_ext_id int
      )
    WITH (
       external_location = 's3://eltoro-apnx-reporting/s3_path/',
       format = 'AVRO',
       avro_schema_url = 's3://eltoro-apnx-reporting/logleveldata/curate_feed/export/curator_feed/curate_feed.avsc'
    )

    """

# Insert inot main table from stg table
INSERT_STATEMENT_CURATE_FEED = """
insert into olympus.bronze_et_xandr.curate_feed
select * from s3.bronze_et_xandr.curate_feed
                              """
