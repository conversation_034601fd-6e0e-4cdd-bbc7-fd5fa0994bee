from airflow.decorators import task, dag
import boto3
import logging
from etdag import ETDAG
from datetime import datetime
from airflow.providers.trino.hooks.trino import TrinoHook
import old_dags.xandr_eltoro_log_level.dnd_log_level_data_config as config


docs = """
DAG Name: xandr_insert_log_level_data

Description:
This DAG processes log-level data from multiple datasets stored in an S3 bucket and loads it into a Starburst (Trino) database. The DAG iterates through three different datasets: `STANDARD_FEED`, `SEGMENT_FEED`, and `CURATE_FEED`. It performs the following steps for each dataset:
1. Retrieves a date parameter (either backfill or logical execution date) to define the time window for data extraction.
2. Fetches the existing log hours that have already been inserted for the specified date and dataset from Starburst to avoid redundant insertions.
3. Queries the S3 bucket for log paths that match the specified date and dataset.
4. Filters the log paths, removing those that have already been inserted into Starburst based on the retrieved existing hours.
5. Inserts the unprocessed log data into the corresponding Starburst table for each dataset.

The DAG utilizes Trino and S3 for data handling, iterating over the `STANDARD_FEED`, `SEGMENT_FEED`, and `CURATE_FEED` datasets.

Schedule: 
- Runs daily at 6:45 AM (UTC), 20 minutes after the `medicx_xandr_log_level_load` DAG.

On Failure:
 - Clear tasks on failure.  No harm will come if you do start a new run, but it will run for "yesterday" which may not be what you want.
 - Contact dag owner if error can not be easily diagnosed

Tags:
- application:xandr
- team:DND

Datasets:
- The DAG processes three datasets: `STANDARD_FEED`, `SEGMENT_FEED`, and `CURATE_FEED`, iterating through them in a loop to handle the insertion of log data.
"""


def insert_logs(s3_path: list, table_def: str):
    tr = TrinoHook(trino_conn_id="starburst")

    DROP_TBL_STMNT = ""
    CREATE_TBL_STMNT = ""
    INSERT_TBL_STMNT = ""

    if table_def == "STANDARD_FEED":
        DROP_TBL_STMNT = config.DROP_TABLE_STANDARD_FEED
        CREATE_TBL_STMNT = config.CREATE_TABLE_STATMENT_STANDARD_FEED.replace(
            "s3_path", f"{s3_path}"
        )
        INSERT_TBL_STMNT = config.INSERT_STATEMENT_STANDARD_FEED
    elif table_def == "SEGMENT_FEED":
        DROP_TBL_STMNT = config.DROP_TABLE_SEGMENT_FEED
        CREATE_TBL_STMNT = config.CREATE_TABLE_STATMENT_SEGMENT_FEED.replace(
            "s3_path", f"{s3_path}"
        )
        INSERT_TBL_STMNT = config.INSERT_STATEMENT_SEGMENT_FEED
    elif table_def == "CURATE_FEED":
        DROP_TBL_STMNT = config.DROP_TABLE_CURATE_FEED
        CREATE_TBL_STMNT = config.CREATE_TABLE_STATMENT_CURATE_FEED.replace(
            "s3_path", f"{s3_path}"
        )
        INSERT_TBL_STMNT = config.INSERT_STATEMENT_CURATE_FEED

    tr.run(DROP_TBL_STMNT)
    tr.run(CREATE_TBL_STMNT)
    tr.run(INSERT_TBL_STMNT)


# Default arguments with retries
default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'start_date': datetime(2024, 8, 25),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}

# DAG definition
with ETDAG(
    dag_id="xandr_insert_log_level_data",
    description="Process to pull log level data from S3 and load it into Starburst",
    schedule_interval="45 6 * * *",  # Runs daily at 6:45 AM
    catchup=False,
    default_args=default_args,
    params={"backfill_date_param": "{{ dag_run.conf['backfill_date_param'] }}"},
    tags=["application:xandr", "team:DND"],
) as dag:
    @task(retries=1)
    def get_airflow_parm(**kwargs) -> str:
        backfill_date = kwargs["params"].get("backfill_date_param", None)
        logical_date = kwargs["data_interval_start"].strftime("%Y-%m-%d")

        if backfill_date and len(str(backfill_date)) == 10:
            logging.info(f'Backfill date provided: {backfill_date}')
            return backfill_date
        else:
            logging.info(f'Using logical date: {logical_date}')
            return logical_date

    @task()
    def get_existing_hours_for_partition(date: str, table_def: str) -> list:
        log_query = ""
        if table_def == "STANDARD_FEED":
            log_query = config.EXISTING_HOURS_STATEMENT_STANDARD_FEED
        elif table_def == "SEGMENT_FEED":
            log_query = config.EXISTING_HOURS_STATEMENT_SEGMENT_FEED
        elif table_def == "CURATE_FEED":
            log_query = config.EXISTING_HOURS_STATEMENT_CURATE_FEED

        tr = TrinoHook(trino_conn_id="starburst")
        try:
            hours = tr.get_records(log_query.replace("p_date", date))
        except Exception as e:
            logging.error(f"Error fetching data from iceberg: {str(e)}")
            raise
        return [str(h[0]).zfill(2) for h in hours]

    @task()
    def get_log_paths_for_date(date: str, table_def: str) -> list:
        s3 = boto3.resource("s3")
        bucket_str = "eltoro-apnx-reporting"

        if table_def.lower() == "curate_feed":
            prefix = f"logleveldata/{table_def.lower()}/export/curator_feed/{date[:4]}/{date[5:7]}/{date[8:10]}/"
        else:
            prefix = f"logleveldata/{table_def.lower()}/export/{table_def.lower()}/{date[:4]}/{date[5:7]}/{date[8:10]}/"

        logging.info(f'Fetching logs from S3 with prefix: {prefix}')

        paths = []
        bucket = s3.Bucket(bucket_str)
        for object_summary in bucket.objects.filter(Prefix=prefix):
            key = object_summary.key
            if key.endswith(".avro"):
                path = "/".join(key.split("/")[:-1])
                paths.append(path)

        logging.info(f'Log paths found: {paths}')
        return paths

    @task()
    def filter_already_inserted_from_path_list(paths: list, hours_inserted: list) -> list:
        uninserted_log_paths = [path for path in paths if path.split("/")[7] not in hours_inserted]
        logging.info(f'Uninserted log paths: {uninserted_log_paths}')
        return uninserted_log_paths

    @task(retries=1)
    def insert_logs_data(pdate: str, paths_uninserted: list, table_def: str):
        if paths_uninserted:
            for path in paths_uninserted:
                logging.info(f'Inserting logs for date {pdate} at path {path}')
                insert_logs(path, table_def)
        else:
            logging.info(f'No logs to insert for date {pdate}')

    # Process each table definition
    table_definitions = ["STANDARD_FEED", "SEGMENT_FEED", "CURATE_FEED"]
    for table in table_definitions:
        date_value = get_airflow_parm()
        hours_inserted = get_existing_hours_for_partition(date_value, table)
        paths = get_log_paths_for_date(date_value, table)
        paths_uninserted = filter_already_inserted_from_path_list(paths, hours_inserted)
        insert_logs_data(date_value, paths_uninserted, table)
