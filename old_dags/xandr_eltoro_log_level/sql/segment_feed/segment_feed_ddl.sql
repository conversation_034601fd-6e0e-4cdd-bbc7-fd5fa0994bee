CREATE_TABLE_STATMENT_SEGMENT_FEED = """
CREATE TABLE "s3"."bronze_et_xandr"."segment_feed" (
       date_time int ,
       user_id_64 int ,
       member_id int ,
       segment_id int ,
       is_daily_unique int ,
       is_monthly_unique int ,
       value int ,
       partition_time_millis varchar ,
       hashed_user_id_64 int
      )
    WITH (
       external_location = 's3://eltoro-apnx-reporting/s3_path/',
       format = 'AVRO',
       avro_schema_url = 's3://eltoro-apnx-reporting/logleveldata/segment_feed/export/segment_feed/segement_feed.avsc'
    )


drop table "s3"."bronze_et_xandr"."segment_feed";

CREATE TABLE "olympus"."campaign_reporting"."segment_feed"(
    date_time bigint,
    user_id_64 bigint,
    member_id int,
    segment_id int,
    is_daily_unique int,
    is_monthly_unique int,
    value int,
    partition_time_millis timestamp(6),
    hashed_user_id_64 varbinary
);

select * from "s3"."bronze_et_xandr"."segment_feed" limit 100 ;
select * from "olympus"."campaign_reporting"."segment_feed" limit  100;

                     SELECT distinct(hour(partition_time_millis)) 
                     FROM "s3"."bronze_et_xandr"."segment_feed" 
                     where DATE(partition_time_millis) = DATE('2023-07-19');

    SELECT distinct(hour(partition_time_millis)) 
    FROM "olympus"."campaign_reporting"."segment_feed" 
    where DATE(partition_time_millis) = DATE('2023-07-19');   
  

insert into "olympus"."campaign_reporting"."segment_feed"
select * from "s3"."bronze_et_xandr"."segment_feed";

drop table "olympus"."campaign_reporting"."segment_feed";
