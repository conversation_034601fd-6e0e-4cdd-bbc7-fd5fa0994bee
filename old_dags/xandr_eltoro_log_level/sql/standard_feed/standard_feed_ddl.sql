drop table s3.bronze_et_xandr.standard_feed;

create table s3.bronze_et_xandr.standard_feed(
       auction_id_64 bigint , 
       date_time int ,
       user_tz_offset int ,
       creative_width int ,
       creative_height int ,
       media_type int ,
       fold_position int ,
       event_type varchar ,
       imp_type int ,
       payment_type int ,
       media_cost_cpm double ,
       revenue_type int ,
       media_cost double ,
       buyer_bid double ,
       ecp double ,
       eap double ,
       is_imp int ,
       is_learn int ,
       predict_type_rev int ,
       user_id_64 bigint ,
       ip_address varchar ,
       ip_address_trunc varchar ,
       country varchar ,
       region varchar ,
       operating_system int ,
       browser int ,
       language int ,
       venue_id int ,
       seller_member_id int ,
       publisher_id int ,
       site_id int ,
       site_domain varchar ,
       tag_id int ,
       external_inv_id int ,
       reserve_price double ,
       seller_revenue_cpm double ,
       media_buy_rev_share_pct double ,
       pub_rule_id int ,
       seller_currency varchar ,
       publisher_currency varchar ,
       publisher_exchange_rate double ,
       serving_fees_cpm double ,
       serving_fees_revshare double ,
       buyer_member_id int ,
       advertiser_id int ,
       brand_id int ,
       advertiser_frequency int ,
       advertiser_recency int ,
       insertion_order_id int ,
       line_item_id int ,
       campaign_id int ,
       creative_id int ,
       creative_freq int ,
       creative_rec int ,
       cadence_modifier double ,
       can_convert int ,
       user_group_id int ,
       is_control int ,
       control_pct double ,
       control_creative_id int ,
       is_click int ,
       pixel_id int ,
       is_remarketing int ,
       post_click_conv int ,
       post_view_conv int ,
       post_click_revenue double ,
       post_view_revenue double ,
       order_id varchar ,
       external_data varchar ,
       pricing_type varchar ,
       booked_revenue double ,
       booked_revenue_adv_curr double ,
       commission_cpm double ,
       commission_revshare double ,
       auction_service_deduction double ,
       auction_service_fees double ,
       creative_overage_fees double ,
       clear_fees double ,
       buyer_currency varchar ,
       advertiser_currency varchar ,
       advertiser_exchange_rate double ,
       latitude varchar ,
       longitude varchar ,
       device_unique_id varchar ,
       device_id int ,
       carrier_id int ,
       deal_id int ,
       view_result int ,
       application_id varchar ,
       supply_type int ,
       sdk_version varchar ,
       ozone_id int ,
       billing_period_id int ,
       view_non_measurable_reason int ,
       external_uid varchar ,
       request_uuid varchar ,
       dma int ,
       city int ,
       mobile_app_instance_id int ,
       traffic_source_code varchar ,
       external_request_id varchar ,
       deal_type int ,
       ym_floor_id int ,
       ym_bias_id int ,
       is_filtered_request int ,
       age int ,
       gender varchar ,
       is_exclusive int ,
       bid_priority int ,
       custom_model_id int ,
       custom_model_last_modified int ,
       custom_model_leaf_name varchar ,
       data_costs double ,
       device_type int ,
       postal_code varchar ,
       imps_for_budget_caps_pacing int ,
       hashed_user_id_64 varbinary ,
       latitude_trunc varchar ,
       longitude_trunc varchar ,
       partition_time_millis timestamp(3) ,
       split_id int ,
       tc_string varchar ,
       partner_fees double ,
       external_campaign_id varchar ,
       playback_method int ,
       video_context int ,
       player_size_id int ,
       error_code int ,
       personal_identifiers array(ROW(identity_type int, identity_value varchar)) ,
       device_make_id int ,
       postal_code_ext varchar ,
       extended_ids array(ROW(id_type int, id_value varchar)) ,
       segment_data_costs double ,
       feature_costs double ,
       fallback_ad_index int,
       is_private_auction int,
       private_auction_eligible int,
       chrome_traffic_label varchar
      )
    WITH (
       external_location = 's3://eltoro-apnx-reporting/s3_path/',
       format = 'AVRO',
       avro_schema_url = 's3a://eltoro-apnx-reporting/logleveldata/standard_feed/export/standard_feed/standard_feed.avsc'
    )
    ;

insert  into "olympus"."campaign_reporting"."standard_feed"
select * from s3.bronze_et_xandr.standard_feed;

select * from olympus.campaign_reporting.standard_feed limit 100;

select count(1) from olympus.campaign_reporting.standard_feed;

                     SELECT distinct(hour(partition_time_millis)) 
                     FROM "olympus"."campaign_reporting"."standard_feed" 
                     where DATE(partition_time_millis) = DATE('2023-07-18')  
                     order  by hour(partition_time_millis) ;

----------------------------------------------------------------------------------------------------------------------------

delete from olympus.campaign_reporting.standard_feed;

SELECT distinct(hour(partition_time_millis)) 
FROM "olympus"."campaign_reporting"."standard_feed" 
where DATE(partition_time_millis) = DATE('2023-07-19')  
order  by hour(partition_time_millis) ;

select * from s3.bronze_et_xandr.standard_feed;
    -- #Note 
    -- # anything that have 64 change datatype from int to bigint
    -- line_item_id (primary partition)
    -- partition_time_millis date

drop table "olympus"."campaign_reporting"."standard_feed";


create table "olympus"."campaign_reporting"."standard_feed"(
       auction_id_64 bigint , 
       date_time bigint ,
       user_tz_offset int ,
       creative_width int ,
       creative_height int ,
       media_type int ,
       fold_position int ,
       event_type varchar ,
       imp_type int ,
       payment_type int ,
       media_cost_cpm double ,
       revenue_type int ,
       media_cost double ,
       buyer_bid double ,
       ecp double ,
       eap double ,
       is_imp int ,
       is_learn int ,
       predict_type_rev int ,
       user_id_64 bigint ,
       ip_address varchar ,
       ip_address_trunc varchar ,
       country varchar ,
       region varchar ,
       operating_system int ,
       browser int ,
       language int ,
       venue_id int ,
       seller_member_id int ,
       publisher_id int ,
       site_id int ,
       site_domain varchar ,
       tag_id int ,
       external_inv_id int ,
       reserve_price double ,
       seller_revenue_cpm double ,
       media_buy_rev_share_pct double ,
       pub_rule_id int ,
       seller_currency varchar ,
       publisher_currency varchar ,
       publisher_exchange_rate double ,
       serving_fees_cpm double ,
       serving_fees_revshare double ,
       buyer_member_id int ,
       advertiser_id int ,
       brand_id int ,
       advertiser_frequency int ,
       advertiser_recency int ,
       insertion_order_id int ,
       line_item_id int ,
       campaign_id int ,
       creative_id int ,
       creative_freq int ,
       creative_rec int ,
       cadence_modifier double ,
       can_convert int ,
       user_group_id int ,
       is_control int ,
       control_pct double ,
       control_creative_id int ,
       is_click int ,
       pixel_id int ,
       is_remarketing int ,
       post_click_conv int ,
       post_view_conv int ,
       post_click_revenue double ,
       post_view_revenue double ,
       order_id varchar ,
       external_data varchar ,
       pricing_type varchar ,
       booked_revenue double ,
       booked_revenue_adv_curr double ,
       commission_cpm double ,
       commission_revshare double ,
       auction_service_deduction double ,
       auction_service_fees double ,
       creative_overage_fees double ,
       clear_fees double ,
       buyer_currency varchar ,
       advertiser_currency varchar ,
       advertiser_exchange_rate double ,
       latitude varchar ,
       longitude varchar ,
       device_unique_id varchar ,
       device_id int ,
       carrier_id int ,
       deal_id int ,
       view_result int ,
       application_id varchar ,
       supply_type int ,
       sdk_version varchar ,
       ozone_id int ,
       billing_period_id int ,
       view_non_measurable_reason int ,
       external_uid varchar ,
       request_uuid varchar ,
       dma int ,
       city int ,
       mobile_app_instance_id int ,
       traffic_source_code varchar ,
       external_request_id varchar ,
       deal_type int ,
       ym_floor_id int ,
       ym_bias_id int ,
       is_filtered_request int ,
       age int ,
       gender varchar ,
       is_exclusive int ,
       bid_priority int ,
       custom_model_id int ,
       custom_model_last_modified int ,
       custom_model_leaf_name varchar ,
       data_costs double ,
       device_type int ,
       postal_code varchar ,
       imps_for_budget_caps_pacing int ,
       hashed_user_id_64 varbinary ,
       latitude_trunc varchar ,
       longitude_trunc varchar ,
       partition_time_millis timestamp(6) ,
       split_id int ,
       tc_string varchar ,
       partner_fees double ,
       external_campaign_id varchar ,
       playback_method int ,
       video_context int ,
       player_size_id int ,
       error_code int ,
       personal_identifiers array(ROW(identity_type int, identity_value varchar)) ,
       device_make_id int ,
       postal_code_ext varchar ,
       extended_ids array(ROW(id_type int, id_value varchar)) ,
       segment_data_costs double ,
       feature_costs double ,
       fallback_ad_index int 
      )
    ;

select hours(partition_time_millis)
from olympus.campaign_reporting.standard_feed
where date(par)





-- select partition_time_millis from s3.default.standard_feed_20260621 where auction_id_64 = 5156062125827073207 order by event_type, date_time;
-- select count(auction_id_64), auction_id_64 from s3.default.standard_feed_20260621 group by auction_id_64 having  count(auction_id_64) > 1;
-- select * from s3.default.standard_feed limit 100;
-- select distinct(hour(partition_time_millis)) from standard_feed_20260621 limit 10;
-- drop table standard_feed_20260621;

-- SELECT distinct(hour(partition_time_millis)) 
-- FROM "olympus"."campaign_reporting"."standard_feed" 
-- where DATE(partition_time_millis) = DATE('2023-07-12');


-- select distinct hour(partition_time_millis)
--  from olympus.campaign_reporting.standard_feed 
--  where Date(partition_time_millis) = Date('2023-07-12') 
--  order by hour(partition_time_millis) limit 100;

-- select distinct partition_time_millis from s3.default.standard_feed;





-- SHOW CREATE TABLE "olympus"."campaign_reporting_prod"."standard_feed";


-- ALTER TABLE "olympus"."campaign_reporting_prod"."standard_feed" rename to "olympus"."bronze_et_xandr"."standard_feed";
-- ALTER TABLE "olympus"."campaign_reporting_prod"."curate_feed" rename to "olympus"."bronze_et_xandr"."curate_feed";

