from airflow.decorators import task
from airflow.models import Variable
from airflow.providers.amazon.aws.operators.s3 import S3<PERSON><PERSON>Operator
from old_dags.quote_file_group import quote_file
from airflow.providers.amazon.aws.sensors.s3 import S3KeySensor
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.utils.dates import days_ago
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.providers.amazon.aws.operators.s3 import S3CopyObjectOperator
from old_dags.autoalert.etl_queries import (
    AUDIENCE_SEGMENTS_QUERY,
    AUDIENCE_SEGMENTATION_QUERY,
    AUDIENCE_SEGMENTATION_ICEBERG_MERGE_QUERY,
    CREATE_DEALER_TABLE,
    DEALER_L2L_QUERY,
    DROP_DEALER_TABLE,
    ID_BRIDGE_QUERY,
    LIST_TABLES,
    MATCH<PERSON><PERSON>K_QUERY,
    CONVERSION_QUERY,
    SIGNALS_QUERY,
    SIG<PERSON>LS_CUSTOMER_QUERY,
    CAMPAIGN_STATS_QUERY,
    ORDERLINE_STATS_QUERY,
    CREATIVE_STATS_QUERY
)
from starburst_geocoder_operator import StarburstGeocoderOperator
from operators.compiled_audience_operator import CompiledAudienceOperator
from datetime import date
from etdag import ETDAG
import pandas as pd
from datetime import timedelta


default_args = {
    "owner": "Rorie Lizenby",
}


def parse_run_date(date):
    return f"{date[:4]}-{date[4:6]}-{date[6:]}"


with ETDAG(
    dag_id="autoalert_etl",
    description="Nightly ETL process for AutoAlert",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="30 10 * * *", 
    params={"run_date": date.today().strftime("%Y%m%d")},
    catchup=False,
    tags=["autoalert", "etl"],
) as dag:
    env = Variable.get("environment", "dev")
    environment = "production" if env == "prod" else "dev"
    conn_id = "starburst"
    formated_run_date = parse_run_date(dag.params["run_date"])

    STAGED_TARGETS_S3_URI = f"s3://vr-timestamp/bi_sources/autoalert_etl/{env}/staged_targets/date={formated_run_date}/staged_targets.csv"
    ANNOTATED_AUDIENCE_S3_URI = f"s3://vr-timestamp/bi_sources/autoalert_etl/{env}/annotated_audience/date={formated_run_date}/annotated_audience.csv"
    SELECTED_AUDIENCE_S3_URI = f"s3://vr-timestamp/bi_sources/autoalert_etl/{env}/selected_audience/date={formated_run_date}/selected_audience.csv"

    output_prefix = f"s3://eltoro-{env}-vpc-autoalert/to_autoalert/{dag.params['run_date']}"

    required_aa_files = [
        "activities.csv.gz",
        "customer.csv.gz",
        "dealer_address.csv.gz",
        "dealer_group.csv.gz",
        "entity.csv.gz",
        "entity_view.csv.gz",
        "onetoone_audience.csv.gz",
        "pending_sales.csv.gz",
        "sales.csv.gz",
        "service.csv.gz",
        "signals_traffic.csv.gz",
        "vehicle.csv.gz",
        "geo_alert_dealers.csv.gz",
    ]

    def write_id_brige(cursor) -> str:
        output_filename = f"{output_prefix}/et_id-aa_id.csv.gz"
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=[column.name for column in cursor.description])
        df.to_csv(output_filename, compression="gzip", index=False)
        return output_filename

    def write_audience_segments_file(cursor) -> str:
        output_filename = f"{output_prefix}/audience_segments.csv.gz"
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=[column.name for column in cursor.description])
        df.to_csv(output_filename, compression="gzip", index=False)
        return output_filename

    def write_campaign_stats_file(cursor) -> str:
        output_filename = f"{output_prefix}/stats/campaign.csv.gz"
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=[column.name for column in cursor.description])
        df.to_csv(output_filename, compression="gzip", index=False)
        return output_filename

    def write_orderline_stats_file(cursor) -> str:
        output_filename = f"{output_prefix}/stats/orderLine.csv.gz"
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=[column.name for column in cursor.description])
        df.to_csv(output_filename, compression="gzip", index=False)
        return output_filename

    def write_creative_stats_file(cursor) -> str:
        output_filename = f"{output_prefix}/stats/creative.csv.gz"
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=[column.name for column in cursor.description])
        df.to_csv(output_filename, compression="gzip", index=False)
        return output_filename

    def write_dealer_l2l_files(cursor) -> bool:
        data = cursor.fetchall()
        query_df = pd.DataFrame(data, columns=[column.name for column in cursor.description])

        # Define makelist of tags
        makelist = [
            "ACURA", "AUDI", "BMW", "BUICK", "CADILLAC", "CHEVROLET", "CHRYSLER", "DODGE", "FIAT",
            "FORD", "GMC", "HONDA", "HYUNDAI", "INFINITI", "LEXUS", "LINCOLN", "LUXURY_CARS", "KIA",
            "MAZDA", "MERCEDES-BENZ", "MINI", "MITSUBISHI", "NISSAN", "SUBARU", "TOYOTA", "VOLKSWAGEN", "VOLVO"
        ]
        # Determine time windows based on max date
        max_date = query_df["date"].max()
        cutoffs = {
            "30": max_date - timedelta(days=30),
            "60": max_date - timedelta(days=60),
            "90": max_date - timedelta(days=90),
        }

        print(query_df.head())
        # Base deduplicated set per ethash/customer/dealer/location/date
        base = query_df[[
            "ethashv1", "customer_id", "dealer_id", "buckloc_id", "tags", "date", "name"
        ]].drop_duplicates(subset=["ethashv1", "customer_id", "dealer_id", "buckloc_id", "date"])

        # Filter by time windows
        within_30 = base[base["date"] >= cutoffs["30"]]
        within_60 = base[base["date"] >= cutoffs["60"]]
        within_90 = base[base["date"] >= cutoffs["90"]]

        # Helper: count unique buckloc_id per time window
        def count_dealers(df, window):
            return (
                df.groupby(["ethashv1", "customer_id", "dealer_id"])["buckloc_id"]
                .nunique()
                .rename(f"dealers_visited_within_{window}")
                .reset_index()
            )

        agg_30 = count_dealers(within_30, "30")
        agg_60 = count_dealers(within_60, "60")
        agg_90 = count_dealers(within_90, "90")

        # Latest dealer visit per user
        latest_visits = (
            base.sort_values("date")
            .groupby(["ethashv1", "customer_id", "dealer_id"], as_index=False)
            .last()
        )[["ethashv1", "customer_id", "dealer_id", "name", "tags", "buckloc_id"]]

        # Combine base info with time-windowed aggregations
        merged = latest_visits.merge(agg_30, how="left").merge(agg_60, how="left").merge(agg_90, how="left")

        # Fill null counts with 0
        for col in ["dealers_visited_within_30", "dealers_visited_within_60", "dealers_visited_within_90"]:
            merged[col] = merged[col].fillna(0).astype(int)

        # Explode tags and filter to makelist
        tag_df = (
            query_df.explode("tags")
            .query("tags in @makelist")
            [["ethashv1", "customer_id", "dealer_id", "buckloc_id", "tags"]]
            .drop_duplicates()
        )
        print(tag_df.head())

        # Pivot: count unique locations visited per tag
        tag_counts = (
            tag_df.groupby(["ethashv1", "customer_id", "dealer_id", "tags"])["buckloc_id"]
            .nunique()
            .unstack(fill_value=0)
            .reset_index()
        )

        # Ensure all tags are present in pivot table
        for tag in makelist:
            if tag not in tag_counts.columns:
                tag_counts[tag] = 0

        # Merge tag counts into main table
        final_df = merged.merge(tag_counts, on=["ethashv1", "customer_id", "dealer_id"], how="left")
        for tag in makelist:
            final_df[tag] = final_df[tag].fillna(0).astype(int)

        # Add derived fields
        final_df["make_tags"] = final_df.apply(
            lambda row: "|".join([tag for tag in makelist if row[tag] > 0]), axis=1
        )
        final_df["zip_code"] = final_df["ethashv1"].str[1:6]
        final_df.rename(columns={"ethashv1": "ETHash", "name": "most_recent_visit"}, inplace=True)
        final_df["target"] = "t"

        # Final column order
        ordered_columns = (
                ["target", "ETHash", "customer_id", "dealer_id", "zip_code", "most_recent_visit", "make_tags"] +
                ["dealers_visited_within_30", "dealers_visited_within_60", "dealers_visited_within_90"] +
                makelist
        )
        final_df = final_df[ordered_columns]

        dest_filename = (
            f"{output_prefix}/geoalert.csv.gz"
        )
        final_df.to_csv(dest_filename, compression="gzip", index=False)

        return True

    @task(retries=3)
    def move_file_to_datalake(filepath):
        from_autoalert, date, filename = filepath.split("/")
        date_str = f"{date[0:4]}-{date[4:6]}-{date[6:]}"
        dataset, csv, gz = filename.split(".")
        s3_uri = f"s3://eltoro-prod-vpc-autoalert/{filepath}"
        dest_path = f"s3://eltoro-data-sources/autoalert/aa_{dataset}/transfer_date={date_str}/{date_str}-{dataset}.parquet"
        df = pd.read_csv(s3_uri, dtype="str", low_memory=False)
        count = len(df.index)
        if count > 0:
            df["transfer_date"] = date
            df = pd.DataFrame(df.fillna(""))
            df.to_parquet(
                path=dest_path,
                index=False,
                engine='pyarrow'
            )
        return dest_path

    @task
    def get_table_sql_statements(table_names):
        tables = []
        for table in table_names:
            tables.append(
                {
                    "sql": f"CALL s3.system.sync_partition_metadata('autoalert', '{table[0]}', 'FULL')",
                }
            )
        return tables

    @task
    def process_signals():
        # Read Files
        annotated_file = ANNOTATED_AUDIENCE_S3_URI

        annotated_df = pd.read_csv(annotated_file)
        annotated_df.drop_duplicates(subset=["etHash"], inplace=True)

        annotated_df.dropna(inplace=True)

        # Match to uuid
        trino = TrinoHook(trino_conn_id=conn_id)
        # match to customers
        hashes = annotated_df["etHash"].unique()
        customers_df = trino.get_pandas_df(
            SIGNALS_CUSTOMER_QUERY.format("','".join(hashes.tolist()))
        )
        # create output
        signals_out = annotated_df.merge(customers_df, on="etHash")

        signals_out.rename(columns={"etHash": "et_hash"}, inplace=True)
        signals_out = signals_out[['signals_uid', 'et_hash', 'customer_id']]
        signals_out.to_csv(
            f"{output_prefix}/signals_out.csv.gz", compression="gzip", index=False
        )

    @task(trigger_rule="all_done")
    def create_matchback():
        trino = TrinoHook(trino_conn_id="trino_conn")
        matchback = trino.get_pandas_df(MATCHBACK_QUERY)
        matchback_output_uri = f"{output_prefix}/sales_matchback.csv.gz"
        matchback.to_csv(matchback_output_uri, compression="gzip", index=False)

        conversion = trino.get_pandas_df(CONVERSION_QUERY)

        conversion_output_uri = f"{output_prefix}/dms_conversion.csv.gz"
        conversion.to_csv(conversion_output_uri, compression="gzip", index=False)

        return {matchback_output_uri, conversion_output_uri}

    @task
    def wrapper():
        pass

    @task(retries=3)
    def stage_reverse_quote_file():
        tr = TrinoHook(trino_conn_id="trino_conn")
        df = tr.get_pandas_df(
            sql=SIGNALS_QUERY.format(formated_run_date)
        )
        df.to_csv(STAGED_TARGETS_S3_URI, index=False)

    @task(retries=3)
    def get_active_order_line_ids():
        tr = TrinoHook(trino_conn_id="trino_conn")
        df = tr.get_pandas_df(
            sql=AUDIENCE_SEGMENTS_QUERY.format(formated_run_date, formated_run_date)
        )
        # Group order lines by dealer organization ID
        chunked_orderlines = []

        # Simple grouping by et_dealer_org_id with no maximum chunk size
        if df.empty:
            return []
        for org_id, group_df in df.groupby('et_dealer_org_id'):
            orderline_ids = group_df['orderLineId'].tolist()
            chunked_orderlines.append(orderline_ids)

        return chunked_orderlines


    @task(retries=3, trigger_rule="all_done")
    def create_audience_segmentation(compiled_audience_results):
        output_uri = f"{output_prefix}/audience_segmentation.csv.gz"

        # Handle case with no results
        if not compiled_audience_results:
            # Create empty dataframe with correct columns
            empty_df = pd.DataFrame(columns=["orderlineId", "et_hash", "customer_id"])
            empty_df.to_csv(output_uri, compression="gzip", index=False)
            return output_uri

        ca_id_string = ', '.join([str(r['id']) for r in compiled_audience_results])
        for result in compiled_audience_results:
            print(f"Compiled audience result: {result}")

        tr = TrinoHook(trino_conn_id="trino_conn")
        tr.run(
            "CALL s3.system.sync_partition_metadata('external_dataservices_api', 'prod_compiled_audience', 'FULL')"
        )

        # Update Iceberg Audience Segmentation table needed for matchback
        tr.run(
            AUDIENCE_SEGMENTATION_ICEBERG_MERGE_QUERY.format(ca_id_string)
        )

        # Produce File for AA
        df = tr.get_pandas_df(
            sql=AUDIENCE_SEGMENTATION_QUERY.format(ca_id_string)
        )

        df[["orderlineId", "et_hash", "customer_id"]].to_csv(
            output_uri, compression="gzip", index=False
        )
        return output_uri

    wait_for_files = S3KeySensor(
        task_id="wait_for_files",
        bucket_key=[
            f"s3://eltoro-prod-vpc-autoalert/from_autoalert/{dag.params['run_date']}/{filename}"
            for filename in required_aa_files
        ],
        aws_conn_id="s3_conn",
        deferrable=True,
    )

    get_all_files = S3ListOperator(
        task_id="get_new_files",
        bucket="eltoro-prod-vpc-autoalert",
        prefix=f"from_autoalert/{dag.params['run_date']}",
        aws_conn_id="s3_conn",
    )

    move_files = move_file_to_datalake.expand(filepath=get_all_files.output)

    query_tables = SQLExecuteQueryOperator(
        task_id="query_autoalert_tables",
        conn_id=conn_id,
        sql=LIST_TABLES,
        handler=list,
    )

    update_sql_statements = get_table_sql_statements(query_tables.output)

    update_tables = SQLExecuteQueryOperator.partial(
        task_id="update_autoalert_partitions",
        conn_id=conn_id,
        handler=list
    ).expand_kwargs(update_sql_statements)

    customer_ethash_bridge = StarburstGeocoderOperator(
        task_id='bronze_customer_geocoding_append',
        source_table_name='"s3"."autoalert"."customer"',
        source_row_identifier_column_name='customer_id',
        address1_column_name='address',
        address2_column_name='address2',
        zipcode_column_name='zip_code',
        bridge_table_name='"olympus"."autoalert"."customer_ethash_bdg"',
        geocoder_columns=['etHashV1', 'etHashV2', 'matchCode'],
        bridge_record_ttl_days=365,
        source_where_clause="transfer_date = '{}'".format(formated_run_date)
    )

    drop_dealers = SQLExecuteQueryOperator(
        task_id="drop_dealer_tables",
        conn_id=conn_id,
        sql=DROP_DEALER_TABLE,
        handler=list,
    )

    create_dealers = SQLExecuteQueryOperator(
        task_id="create_dealer_tables",
        conn_id=conn_id,
        sql=CREATE_DEALER_TABLE,
        handler=list,
    )

    end_groups = wrapper()

    audience_segments = SQLExecuteQueryOperator(
        task_id="write_audience_segments",
        conn_id=conn_id,
        sql=AUDIENCE_SEGMENTS_QUERY.format(formated_run_date, formated_run_date),
        handler=write_audience_segments_file,
    )

    chunked_order_line_ids = get_active_order_line_ids()

    compiled_audiences = CompiledAudienceOperator.partial(
        task_id="create_compiled_audience",
        retries=3,
        operation="create",
        name="autoalert_audience_segmentation",
        wait_for_job_to_finish=True,
        max_active_tis_per_dag=3,
        env="prod",
    ).expand(order_line_ids=chunked_order_line_ids)

    create_audience_segmentation_task = create_audience_segmentation(compiled_audiences.output)

    matchback = create_matchback()

    id_bridge_ouput = SQLExecuteQueryOperator(
        task_id="write_id_bridge",
        conn_id=conn_id,
        sql=ID_BRIDGE_QUERY,
        handler=write_id_brige,
    )

    dealer_l2l = SQLExecuteQueryOperator(
        task_id="write_dealer_l2l_files",
        conn_id=conn_id,
        sql=DEALER_L2L_QUERY.format(
            formated_run_date
        ),
        handler=write_dealer_l2l_files,
    )

    stage_reverse_quote_file_task = stage_reverse_quote_file()

    column_headers = [
        {"index": 0, "type": "pk", "value": "signals_uid"},
        {"index": 1, "type": "ip", "value": "ip_address"}
    ]
    quote_task = quote_file(
        STAGED_TARGETS_S3_URI,
        ANNOTATED_AUDIENCE_S3_URI,
        SELECTED_AUDIENCE_S3_URI,
        "eH3KEdoAdw9ZyR7c9",
        column_headers,
        target_file_type="IP",
        data_source="CLIENT",
        audience_type="REVERSEIP",
        chunk_size=500_000,
        env="prod"
    )

    process_quote = process_signals()

    # Stats
    campaign_stats = SQLExecuteQueryOperator(
        task_id="write_campaign_stats",
        conn_id=conn_id,
        sql=CAMPAIGN_STATS_QUERY.format(formated_run_date, formated_run_date, formated_run_date),
        handler=write_campaign_stats_file,
    )

    orderline_stats = SQLExecuteQueryOperator(
        task_id="write_orderline_stats",
        conn_id=conn_id,
        sql=ORDERLINE_STATS_QUERY.format(formated_run_date, formated_run_date, formated_run_date),
        handler=write_orderline_stats_file,
    )

    creative_stats = SQLExecuteQueryOperator(
        task_id="write_creative_stats",
        conn_id=conn_id,
        sql=CREATIVE_STATS_QUERY.format(formated_run_date, formated_run_date, formated_run_date),
        handler=write_creative_stats_file,
    )
    trigger_sync = TriggerDagRunOperator(
        task_id="trigger_autoalert_sync",
        trigger_dag_id="autoalert_sync",
        wait_for_completion=False,
    )

    (
        wait_for_files
        >> get_all_files
        >> move_files
        >> query_tables
        >> update_sql_statements
        >> update_tables
        >> customer_ethash_bridge
        >> drop_dealers
        >> create_dealers
        >> [audience_segments, dealer_l2l, stage_reverse_quote_file_task, id_bridge_ouput, campaign_stats]
    )

    audience_segments >> chunked_order_line_ids >> compiled_audiences >> create_audience_segmentation_task >> matchback >> end_groups
    dealer_l2l >> end_groups
    stage_reverse_quote_file_task >> quote_task >> process_quote >> end_groups
    id_bridge_ouput >> end_groups
    campaign_stats >> orderline_stats >> creative_stats >> end_groups
    end_groups >> trigger_sync


if __name__ == "__main__":
    pass
