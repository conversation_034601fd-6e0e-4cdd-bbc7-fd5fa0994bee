AUDIENCE_SEGMENTS_QUERY = """
SELECT DISTINCT
    campaign_id campaignId
    , campaign_name AS campaignName
    , order_line_id AS orderLineId
    , order_line_name as orderLineName
    , dealers.et_dealer_org_id
    , dealers.dealer_id
    , order_line_start start
    , order_line_stop stop
FROM "s3"."silver_platform_services"."order_lines" 
JOIN s3.silver_autoalert.dealers AS dealers
ON org_id = et_dealer_org_id
WHERE campaign_name like 'GeoAlert Campaign%'
AND order_line_created <= CAST('{}' AS date) and order_line_stop >= CAST('{}' AS date)
ORDER BY dealers.et_dealer_org_id
"""

AUDIENCE_SEGMENTATION_QUERY = """
SELECT DISTINCT
    ol_id "orderlineId",
    ca.ethashv1 et_hash,
    bdg.customer_id
FROM "s3"."external_dataservices_api"."prod_compiled_audience" ca
LEFT JOIN "olympus"."autoalert"."customer_ethash_bdg" bdg on bdg.ethashv1 = ca.ethashv1
WHERE id in ({})     
and deployed = 'true' and served = 'true' and matched = 'true'
and excluded = 'false'
"""

AUDIENCE_SEGMENTATION_ICEBERG_MERGE_QUERY = """
MERGE INTO olympus.autoalert.audience_segmentation AS target
USING (
    SELECT 
    ethashv1 ethash,
    ol_id as orderline_id,
    MIN(CAST(serve_start_date AS DATE)) first_date,
    MAX(CAST(serve_end_date AS DATE)) last_date
    FROM "s3"."external_dataservices_api"."prod_compiled_audience" WHERE id in ({})
    AND serve_start_date != '' and serve_end_date != ''
    GROUP BY 1, 2
) AS source
ON target.ethash = source.ethash AND target.orderline_id = source.orderline_id

WHEN MATCHED THEN
  UPDATE SET last_date = source.last_date

WHEN NOT MATCHED THEN
  INSERT (ethash, orderline_id, first_date, last_date, inserted_at)
  VALUES (source.ethash, source.orderline_id, source.first_date, source.last_date, current_timestamp)
"""

MATCHBACK_QUERY = """
    SELECT 
        segments.ethash AS ethash,
        segments.orderline_id as et_orderline_id,
        sales.sale_id AS aa_sale_id,
        sales.deal_date AS aa_sale_insert_date,
        customers.customer_id AS aa_customer_id,
        MIN(segments.first_date) AS first_et_target_date,
        MAX(segments.last_date) AS last_et_target_date
    FROM "olympus"."autoalert"."audience_segmentation" AS segments
    INNER JOIN "olympus"."autoalert"."customer_ethash_bdg" AS customers
    ON SUBSTRING(customers.ethashv1, 10, 40)= SUBSTRING(segments.ethash, 10, 40) 
    INNER JOIN (SELECT * FROM s3.autoalert."sales" WHERE sales.transfer_date = (SELECT max(transfer_date) from s3.autoalert."sales$partitions")) as sales
    ON sales.customer_id = customers.customer_id
    WHERE cast(SUBSTRING(sales.deal_date, 1, 10) as date) >= segments.first_date
    AND cast(SUBSTRING(sales.deal_date, 1, 10) as date) <= segments.last_date + interval '2' month
    AND sales.sale_type_name NOT IN ('Wholesale', 'InComplete')
    AND sales.transfer_date = (SELECT max(transfer_date) from s3.autoalert."sales$partitions")
    GROUP BY segments.ethash, customers.customer_id, sales.deal_date, sales.sale_id, segments.orderline_id
"""

CONVERSION_QUERY = """
    SELECT
        segments.ethash AS ethash,
        segments.orderline_id AS et_orderline_id,
        customer_id AS aa_customer_id,
        customers.insert_date AS aa_customer_insert_date,
        MIN(segments.first_date) AS first_et_target_date,
        MAX(segments.last_date) AS last_et_target_date
    FROM "olympus"."autoalert"."audience_segmentation" AS segments
    INNER JOIN "olympus"."autoalert"."customer_ethash_bdg" AS customers_ethash
    ON SUBSTRING(customers_ethash.ethashv1, 10, 40)= SUBSTRING(segments.ethash, 10, 40) 
    JOIN (SELECT * from "s3"."autoalert"."customer" where transfer_date = (SELECT max(transfer_date) from "s3"."autoalert"."customer$partitions")) customers
    USING(customer_id)
    WHERE cast(SUBSTRING(customers.insert_date, 1, 10) as date) >= segments.first_date
    AND cast(SUBSTRING(customers.insert_date, 1, 10) as date) <= segments.last_date + INTERVAL '2' MONTH
    GROUP BY segments.ethash, segments.orderline_id, customer_id, customers.insert_date
"""

CAMPAIGN_STATS_QUERY = """
SELECT
    day "Date"
    , SUBSTRING(hour, 1, 2) "Hour"
    , SUM(clicks) "Clicks"
    , SUM(imps) "Imps"
    , org_id "orgId"
    , order_lines.campaign_id "campaignId"
FROM "s3"."silver_platform_services"."order_lines" 
JOIN "s3"."silver_et_xandr"."network_analytics_hourly" na on order_lines.order_line_id = na.line_item_code
WHERE order_lines.campaign_name like 'GeoAlert Campaign%'
AND order_line_created <= CAST('{}' AS date) and order_line_stop >= CAST('{}' AS date)  -  interval '1' day
AND na.day = (DATE('{}') - interval '1' day)
GROUP BY 1, 2, 5, 6 
"""

ORDERLINE_STATS_QUERY = """
SELECT 
    day "Date"
    , SUBSTRING(hour, 1, 2) "Hour"
    , SUM(clicks) "Clicks"
    , SUM(imps) "Imps"
    , order_line_id AS "orderLineId"
    , order_lines.campaign_id "campaignId"
    , '1' "targetType"
    , creative_type "creativeType"
    , order_line_name "orderLineName"
    , order_lines.campaign_name "campaignName"
    , ref_id "refId"
    , order_line_start start
    , order_line_stop stop

FROM "s3"."silver_platform_services"."order_lines" 
JOIN "s3"."silver_et_xandr"."network_analytics_hourly" na on order_lines.order_line_id = na.line_item_code
WHERE order_lines.campaign_name like 'GeoAlert Campaign%'
AND order_line_created <= CAST('{}' AS date) and order_line_stop >= CAST('{}' AS date)  -  interval '1' day
AND na.day = (DATE('{}') - interval '1' day)
GROUP BY 1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13
"""

CREATIVE_STATS_QUERY = """
SELECT 
    day "Date"
    , SUBSTRING(hour, 1, 2) "Hour"
    , SUM(clicks) "Clicks"
    , SUM(imps) "Imps"
    , creative_code AS "creativeId"
    , order_line_id AS "orderLineId"
    , creative_name AS "creativeName"
FROM "s3"."silver_platform_services"."order_lines" 
JOIN "s3"."silver_et_xandr"."network_analytics_hourly" na on order_lines.order_line_id = na.line_item_code
WHERE order_lines.campaign_name like 'GeoAlert Campaign%'
AND order_line_created <= CAST('{}' AS date) and order_line_stop >= CAST('{}' AS date)  -  interval '1' day
AND na.day = (DATE('{}') - interval '1' day)
GROUP BY 1, 2, 5, 6, 7
"""

CREATE_DEALER_TABLE = """
CREATE TABLE s3.silver_autoalert.dealers
WITH ( format = 'ORC' ) AS
SELECT
    add.dealer_id
    , add.dealer_group_id
    , add.et_dealer_org_id
    , add.dealer_code
    , add.dealer_name
    , add.main_url
    , add.address
    , add.city
    , add.state
    , add.postal
    , add.billing_address_street
    , add.billing_address_city
    , add.billing_address_state
    , add.billing_address_postal_code
    , add.makes
    , ethash_beta(CONCAT(add.address, ', ', add.postal)) as ethash_beta
    , ethash_v1(CONCAT(add.address, ', ', add.postal)) as ethash_v1
    , CAST(NULL as VARCHAR) as ethash_v2
    , CASE WHEN geo.geoalert_active THEN true ELSE false END AS geoalert_active
FROM s3.autoalert.dealer_address AS add
LEFT JOIN (
    SELECT
        dealer_id
        , true AS geoalert_active
        FROM s3.autoalert.geo_alert_dealers
        WHERE CAST(transfer_date AS date) = CURRENT_DATE
    ) AS geo
ON add.dealer_id = geo.dealer_id
WHERE CAST(add.transfer_date AS date) = CURRENT_DATE
"""

DEALER_L2L_QUERY = """
WITH active_dealers as (
    SELECT dealer_id from s3.silver_autoalert.dealers where geoalert_active = TRUE
),
active_customers as (
    SELECT dealer_id, customer_id, ethashv1
    FROM active_dealers 
    JOIN "s3"."autoalert"."customer" USING(dealer_id)
    LEFT JOIN olympus.autoalert.customer_ethash_bdg customers USING(customer_id)
    WHERE transfer_date = '{}'
),
visits as (
    select dealer_id, customer_id, ethashv1, date, tags, name, location_ethash buckloc_id
    FROM active_customers
    JOIN "s3"."gold_auto_intender"."base" on base.parcel_ethash = active_customers.ethashv1
    LEFT JOIN (SELECT distinct location, name FROM "s3"."bronze_ts_reports"."locations") locations on locations.location = base.location_ethash
    WHERE ethashv1 != ''
    ORDER BY ethashv1, date
    )
select 
DISTINCT * from visits
"""

DROP_DEALER_TABLE = "DROP TABLE s3.silver_autoalert.dealers"

ID_BRIDGE_QUERY = """
SELECT DISTINCT
    dealers.dealer_group_id,
    grp.et_dealer_group_org_id,
    dealers.dealer_id,
    dealers.et_dealer_org_id
FROM s3.silver_autoalert.dealers
JOIN "yf-bi".autoalert.dealer_group AS grp
ON CAST(grp.dealer_group_id AS VARCHAR) = dealers.dealer_group_id
"""

LIST_TABLES = """SELECT table_name FROM s3.information_schema.tables
WHERE table_schema = 'autoalert' AND table_catalog = 's3' AND table_type = 'BASE TABLE'
ORDER BY table_name"""


SIGNALS_CUSTOMER_QUERY = """
SELECT
    customer_id,
    ethashv1 AS etHash
FROM "olympus"."autoalert"."customer_ethash_bdg"
WHERE ethashv1 IN ('{}')
"""

SIGNALS_QUERY = """
SELECT
    signals_uid,
    ip_address
FROM s3.autoalert.signals_traffic
WHERE transfer_date = '{}'
"""