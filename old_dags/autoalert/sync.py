from airflow.utils.dates import days_ago
from airflow.operators.python import Branch<PERSON>ythonOperator
from operators.s3_sync_operator import S3SyncOperator
from datetime import date
from etdag import ETDAG
from airflow.operators.empty import EmptyOperator

default_args = {"owner": "<PERSON><PERSON><PERSON>"}


def choose_sync_path(dag_run, **kwargs):
    # triggered by TriggerDagRunOperator
    if dag_run and dag_run.external_trigger:
        return [
            "sync_et_prod_to_aa_prod",
            "sync_et_prod_to_et_dev",
            # "sync_et_dev_to_aa_dev",
        ]
    # scheduled run
    else:
        return [
            "sync_aa_dev_to_et_dev",
            "sync_aa_prod_to_et_prod",
        ]


with ETDAG(
    dag_id="autoalert_sync",
    description="Sync our S3 autoalert locations with Autoalert's S3",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 10 * * *",
    params={"run_date": date.today().strftime("%Y%m%d")},
    catchup=False,
    tags=["autoalert", "etl"],
) as dag:

    branch_sync = BranchPythonOperator(
        task_id="branch_sync_path",
        python_callable=choose_sync_path,
        provide_context=True,
    )

    sync_their_dev_to_our_dev = S3SyncOperator(
        task_id=f"sync_aa_dev_to_et_dev",
        source_s3_conn="dev_aa_s3_conn",
        source_bucket="autoalert-dev-vpc-eltoro",
        source_prefix="to_eltoro/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        dest_s3_conn="s3_conn",
        dest_bucket="eltoro-dev-vpc-autoalert",
        dest_prefix="from_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        overwrite=True,
    )

    sync_their_prod_to_our_prod = S3SyncOperator(
        task_id="sync_aa_prod_to_et_prod",
        source_s3_conn="prod_aa_s3_conn",
        source_bucket="autoalert-prod-vpc-eltoro",
        source_prefix="to_eltoro/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        dest_s3_conn="s3_conn",
        dest_bucket="eltoro-prod-vpc-autoalert",
        dest_prefix="from_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        overwrite=True,
    )

    sync_our_prod_to_their_prod = S3SyncOperator(
        task_id="sync_et_prod_to_aa_prod",
        source_s3_conn="s3_conn",
        source_bucket="eltoro-prod-vpc-autoalert",
        source_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        dest_s3_conn="prod_aa_s3_conn",
        dest_bucket="autoalert-prod-vpc-eltoro",
        dest_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        overwrite=True,
    )

    sync_our_prod_to_our_dev = S3SyncOperator(
        task_id="sync_et_prod_to_et_dev",
        source_s3_conn="s3_conn",
        source_bucket="eltoro-prod-vpc-autoalert",
        source_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        dest_s3_conn="s3_conn",
        dest_bucket="eltoro-dev-vpc-autoalert",
        dest_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        overwrite=True,
    )

    sync_our_dev_to_their_dev = S3SyncOperator(
        task_id=f"sync_et_dev_to_aa_dev",
        source_s3_conn="s3_conn",
        source_bucket="eltoro-dev-vpc-autoalert",
        source_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        dest_s3_conn="dev_aa_s3_conn",
        dest_bucket="autoalert-dev-vpc-eltoro",
        dest_prefix="to_autoalert/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}/",
        overwrite=True,
    )

    end = EmptyOperator(task_id="end", trigger_rule="none_failed_min_one_success")

    branch_sync >> [sync_their_dev_to_our_dev, sync_their_prod_to_our_prod] >> end
    (
        branch_sync
        >> [
            sync_our_prod_to_their_prod,
            sync_our_prod_to_our_dev,
        ]
        >> sync_our_dev_to_their_dev
        >> end
    )
