# Airflow Process
DAG Name: trino_to_s3_main_dag </br>
Schedule Interval: 8, 12, and 3 From M to F</br>
owner: Tim


## Configuration
* The current configuration are currently located in the following AWS S3 location: s3://vr-timestamp/bi_sources/airflow_config_sb_s3_sync/bi_report_scripts_and_config/ Please download the report to your local environment. make any changes and reupload to the same location.
</br>

| Key                 |   Types | Notes                           | Example                            |
| -------------       | :-------| :----:                          | ----:                              |
| reportName          | string  |                                 |"auto_bucklocs"                     | 
| enable              | boolean |   To enable or disable process  |True/False                          |
| LargeDataSet         | boolean | will there be a large Dataset (>45mil)| True/False                   |
| schedule_type       | string  |                                 |"cron"                              |
| schedule_value      | string  |                                 |"0 1 * * *"                         | 
| trino_query         | string  |                                 |"SELECT * FROM s3.gold_auto_intender.locations_new_and_used" |
| s3_bucket           | string  |                                 |"vr-timestamp"                      |
| compression_to_gzip | boolean |Does the file need to be gzip    |True/False                          |
| s3_locations        | string[]|Does the report need to go multiple location                          |
             [
                {
                 "s3_bucket": "vr-timestamp",
                 "s3_key": "bi_sources/SAL_Models/SBSALCombinedLocs.csv"
             },
             {
                 "s3_bucket": "vr-timestamp",
                 "s3_key": "timestampReport/SALCombinedLocs.csv"
             }
             ] 

### Example 
    "job8": {
         "reportName": "SBSALCombinedLocs",
         "enable": true,
         "LargeDataSet": false,
         "schedule_type": "cron",
         "schedule_value": "0 1 * * *",
         "trino_query": "select * from s3.dev_business_intelligence.sal_loc",
         "compress_to_gzip": true,
         "s3_locations": [
             {
                 "s3_bucket": "vr-timestamp",
                 "s3_key": "bi_sources/SAL_Models/SBSALCombinedLocs.csv"
             },
             {
                 "s3_bucket": "vr-timestamp",
                 "s3_key": "timestampReport/SALCombinedLocs.csv"
             }
         ]
     }

