from airflow.decorators import task
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.providers.trino.hooks.trino import <PERSON>noHook
from etdag import ETDAG
import pandas as pd
from datetime import datetime, timedelta


default_args = {
    "owner": "<PERSON><PERSON><PERSON>",
    "depends_on_past": False,
    "email_on_failure": <PERSON>als<PERSON>,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(seconds=30),
}


def get_dataset_from_trino() -> pd.DataFrame:
    query = "select * from s3.dev_prototyping.rocketmtg_precisionmkt_results"
    tr = TrinoHook(trino_conn_id="trino_conn")
    results_df = tr.get_pandas_df(sql=query, dtype=str)
    return results_df


with ETDAG(
    dag_id="rktmtg_enrichment",
    description="Provides enrichment weekly, queried from starburst, delivered to sftp",
    start_date=datetime(2025, 4, 20),
    default_args=default_args,
    schedule_interval="0 6 * * 1,2,3,4,5",
    catchup=False,
    tags=["application:rocketmtg", "team:DND"],
) as dag:
    BUCKET_NAME = "vr-timestamp"
    today = datetime.today()
    date_st = today.date().strftime("%Y%m%d")
    date_st_hyphen = today.date().strftime("%Y-%m-%d")
    filename = f"RocketMtg_PrecisionMkt_Results_{date_st}.csv"
    S3_PATH = f"bi_sources/realestate/rocketmtg/enrichment/transfer_date={date_st_hyphen}/{filename}"

    @task
    def get_enrichment():
        df = get_dataset_from_trino()
        df.to_csv(f"s3://{BUCKET_NAME}/{S3_PATH}", index=False)

    deliver_dataset = get_enrichment()

    s3_to_sftp_cl = S3ToSFTPOperator(
        task_id="s3_to_sftp_cl",
        s3_bucket=BUCKET_NAME,
        s3_key=S3_PATH,
        sftp_path=f"/Precision Marketing/Output/{filename}",
        sftp_conn_id="rocketmtg_sftp",
        aws_conn_id="s3_conn",
    )

    deliver_dataset >> s3_to_sftp_cl
