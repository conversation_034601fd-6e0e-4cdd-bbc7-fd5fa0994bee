from airflow.decorators import task
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.eltoro.transfers.trinotos3 import TrinoToS3Operator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator

default_args = {
    "owner": "<PERSON>",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
}
with ETDAG(
    dag_id="cron_jobs_processor",
    description="Set up a bunch of Starburst Queries to S3 CSVs",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 5 * * *",
    concurrency=1,
    catchup=False,
    tags=["cron"],
) as dag:

    @task
    def get_params(table_names, schema):
        tables = []
        for table in table_names:
            table_name = f"{schema}.{table[0]}"
            tables.append(
                {
                    "trino_query": f"SELECT * FROM {table_name}",
                    "s3_key": f"bi_sources/legacy_bi/{table_name.replace('.','/')}.csv",
                }
            )
        return tables

    query_tables = SQLExecuteQueryOperator(
        task_id="query_tables",
        conn_id="starburst",
        sql="SHOW TABLES FROM s3.gold_company_dashboard",
        handler=list,
    )

    table_names = get_params(query_tables.output, "s3.gold_company_dashboard")

    TrinoToS3Operator.partial(
        task_id="drop_to_s3",
        trino_conn_id="starburst",
        aws_conn_id="s3_conn",
        s3_bucket="vr-timestamp",
    ).expand_kwargs(table_names)
