from airflow.decorators import dag
from airflow.models import Variable
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.slack.operators.slack import SlackAPIPostOperator
from airflow.utils.dates import days_ago
from airflow.utils.trigger_rule import TriggerRule
from old_dags.slack_alert import task_slack_alert
from tempfile import NamedTemporaryFile
import pandas as pd

default_args = {
    "owner": "airflow",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
}

env = Variable.get("environment")


def copy_postgres_to_s3(table):
    s3_conn = "s3_conn"
    pg_conn = "postgres_medicx"
    s3_hook = S3Hook(s3_conn)
    pg_hook = PostgresHook.get_hook(pg_conn)
    conn = pg_hook.get_conn()
    cursor = conn.cursor()

    tablename = table.split(".")[1]
    with NamedTemporaryFile(suffix=".csv") as temp_file:
        filename = temp_file.name
        s3_bucket = "vr-timestamp"
        s3_key = f"bi_sources/medicx/{env}_{tablename}.csv"
        column_headers_query = f"select column_name from information_schema.columns where table_schema = 'audience' and table_name = '{tablename}';"

        # Get headers from table.
        cursor.execute(column_headers_query)
        headers = [d[0] for d in cursor.fetchall()]

        # Dump table contents to file.
        pg_hook.bulk_dump(table, filename)

        # Add headers to file.
        df = pd.read_csv(filename, sep="\t")
        df.columns = headers
        df.to_csv(filename, index=False)

        # Upload file to csv.
        s3_hook.load_file(filename, s3_key, bucket_name=s3_bucket, replace=True)


@dag(
    dag_id="medicx_postgres_sync",
    description="Syncs Medicx postgres tables to S3",
    start_date=days_ago(2),
    on_failure_callback=task_slack_alert,
    default_args=default_args,
    schedule_interval="0 0-23/4 * * *",
    catchup=False,
    tags=["medicx"],
)
def medicx_postgres_etl():
    """
    This DAG is responsible for syncing Medicx Postgres log tables to S3
    """
    report_slacker_conn_id = "bi-report-slacker-token"
    channel_name = f"#medicx-notifications-{env}"
    tables = Variable.get("medicx_log_tables", deserialize_json=True)

    start = DummyOperator(task_id="start")

    for table in tables:
        push_table = PythonOperator(
            task_id=table,
            python_callable=copy_postgres_to_s3,
            op_kwargs={"table": table},
        )
        failure = SlackAPIPostOperator(
            task_id=f"slack_alert_{table}",
            username="Medicx File Sync",
            slack_conn_id=report_slacker_conn_id,
            text=f"Failed to sync postgres table {table}.",
            channel=channel_name,
            trigger_rule=TriggerRule.ONE_FAILED,
        )

        start >> push_table >> failure


medicx_postgres_sync_dag = medicx_postgres_etl()
