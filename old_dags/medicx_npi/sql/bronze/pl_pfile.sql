CREATE TABLE IF NOT EXISTS s3.bronze_medicx.pl_pfile(
NPI varchar,
Provider_Secondary_Practice_Location_Address_Address_Line_1 varchar,
Provider_Secondary_Practice_Location_Address_Address_Line_2 varchar,
Provider_Secondary_Practice_Location_Address_City_Name varchar,
Provider_Secondary_Practice_Location_Address_State_Name varchar,
Provider_Secondary_Practice_Location_Address_Postal_Code varchar,
Provider_Secondary_Practice_Location_Address_Country_Code_If_outside_US varchar,
Provider_Secondary_Practice_Location_Address_Telephone_Number varchar,
Provider_Secondary_Practice_Location_Address_Telephone_Extension varchar,
Provider_Practice_Location_Address_Fax_Number varchar,
current_month varchar
) 
WITH (
    external_location = 's3://vr-timestamp/bi_sources/medicx/NPPES_NPI/month/pl/',
    format = 'PARQUET',
    partitioned_by = ARRAY['current_month']
);