CREATE TABLE IF NOT EXISTS s3.bronze_medicx.npi_data(
    NPI varchar,
    Entity_Type_Code varchar,
    Replacement_NPI varchar,
    Employer_Identification_Number_EIN varchar,
    Provider_Organization_Name_Legal_Business_Name varchar,
    Provider_Last_Name_Legal_Name varchar,
    Provider_First_Name varchar,
    Provider_Middle_Name varchar,
    Provider_Name_Prefix_Text varchar,
    Provider_Name_Suffix_Text varchar,
    Provider_Credential_Text varchar,
    Provider_Other_Organization_Name varchar,
    Provider_Other_Organization_Name_Type_Code varchar,
    Provider_Other_Last_Name varchar,
    Provider_Other_First_Name varchar,
    Provider_Other_Middle_Name varchar,
    Provider_Other_Name_Prefix_Text varchar,
    Provider_Other_Name_Suffix_Text varchar,
    Provider_Other_Credential_Text varchar,
    Provider_Other_Last_Name_Type_Code varchar,
    Provider_First_Line_Business_Mailing_Address varchar,
    Provider_Second_Line_Business_Mailing_Address varchar,
    Provider_Business_Mailing_Address_City_Name varchar,
    Provider_Business_Mailing_Address_State_Name varchar,
    Provider_Business_Mailing_Address_Postal_Code varchar,
    Provider_Business_Mailing_Address_Country_Code_If_outside_US varchar,
    Provider_Business_Mailing_Address_Telephone_Number varchar,
    Provider_Business_Mailing_Address_Fax_Number varchar,
    Provider_First_Line_Business_Practice_Location_Address varchar,
    Provider_Second_Line_Business_Practice_Location_Address varchar,
    Provider_Business_Practice_Location_Address_City_Name varchar,
    Provider_Business_Practice_Location_Address_State_Name varchar,
    Provider_Business_Practice_Location_Address_Postal_Code varchar,
    Provider_Business_Practice_Location_Address_Country_Code_If_outside_US varchar,
    Provider_Business_Practice_Location_Address_Telephone_Number varchar,
    Provider_Business_Practice_Location_Address_Fax_Number varchar,
    Provider_Enumeration_Date varchar,
    Last_Update_Date varchar,
    NPI_Deactivation_Reason_Code varchar,
    NPI_Deactivation_Date varchar,
    NPI_Reactivation_Date varchar,
    Provider_Gender_Code varchar,
    Authorized_Official_Last_Name varchar,
    Authorized_Official_First_Name varchar,
    Authorized_Official_Middle_Name varchar,
    Authorized_Official_Title_or_Position varchar,
    Authorized_Official_Telephone_Number varchar,
    Healthcare_Provider_Taxonomy_Code_1 varchar,
    Provider_License_Number_1 varchar,
    Provider_License_Number_State_Code_1 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_1 varchar,
    Healthcare_Provider_Taxonomy_Code_2 varchar,
    Provider_License_Number_2 varchar,
    Provider_License_Number_State_Code_2 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_2 varchar,
    Healthcare_Provider_Taxonomy_Code_3 varchar,
    Provider_License_Number_3 varchar,
    Provider_License_Number_State_Code_3 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_3 varchar,
    Healthcare_Provider_Taxonomy_Code_4 varchar,
    Provider_License_Number_4 varchar,
    Provider_License_Number_State_Code_4 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_4 varchar,
    Healthcare_Provider_Taxonomy_Code_5 varchar,
    Provider_License_Number_5 varchar,
    Provider_License_Number_State_Code_5 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_5 varchar,
    Healthcare_Provider_Taxonomy_Code_6 varchar,
    Provider_License_Number_6 varchar,
    Provider_License_Number_State_Code_6 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_6 varchar,
    Healthcare_Provider_Taxonomy_Code_7 varchar,
    Provider_License_Number_7 varchar,
    Provider_License_Number_State_Code_7 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_7 varchar,
    Healthcare_Provider_Taxonomy_Code_8 varchar,
    Provider_License_Number_8 varchar,
    Provider_License_Number_State_Code_8 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_8 varchar,
    Healthcare_Provider_Taxonomy_Code_9 varchar,
    Provider_License_Number_9 varchar,
    Provider_License_Number_State_Code_9 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_9 varchar,
    Healthcare_Provider_Taxonomy_Code_10 varchar,
    Provider_License_Number_10 varchar,
    Provider_License_Number_State_Code_10 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_10 varchar,
    Healthcare_Provider_Taxonomy_Code_11 varchar,
    Provider_License_Number_11 varchar,
    Provider_License_Number_State_Code_11 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_11 varchar,
    Healthcare_Provider_Taxonomy_Code_12 varchar,
    Provider_License_Number_12 varchar,
    Provider_License_Number_State_Code_12 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_12 varchar,
    Healthcare_Provider_Taxonomy_Code_13 varchar,
    Provider_License_Number_13 varchar,
    Provider_License_Number_State_Code_13 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_13 varchar,
    Healthcare_Provider_Taxonomy_Code_14 varchar,
    Provider_License_Number_14 varchar,
    Provider_License_Number_State_Code_14 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_14 varchar,
    Healthcare_Provider_Taxonomy_Code_15 varchar,
    Provider_License_Number_15 varchar,
    Provider_License_Number_State_Code_15 varchar,
    Healthcare_Provider_Primary_Taxonomy_Switch_15 varchar,
    Other_Provider_Identifier_1 varchar,
    Other_Provider_Identifier_Type_Code_1 varchar,
    Other_Provider_Identifier_State_1 varchar,
    Other_Provider_Identifier_Issuer_1 varchar,
    Other_Provider_Identifier_2 varchar,
    Other_Provider_Identifier_Type_Code_2 varchar,
    Other_Provider_Identifier_State_2 varchar,
    Other_Provider_Identifier_Issuer_2 varchar,
    Other_Provider_Identifier_3 varchar,
    Other_Provider_Identifier_Type_Code_3 varchar,
    Other_Provider_Identifier_State_3 varchar,
    Other_Provider_Identifier_Issuer_3 varchar,
    Other_Provider_Identifier_4 varchar,
    Other_Provider_Identifier_Type_Code_4 varchar,
    Other_Provider_Identifier_State_4 varchar,
    Other_Provider_Identifier_Issuer_4 varchar,
    Other_Provider_Identifier_5 varchar,
    Other_Provider_Identifier_Type_Code_5 varchar,
    Other_Provider_Identifier_State_5 varchar,
    Other_Provider_Identifier_Issuer_5 varchar,
    Other_Provider_Identifier_6 varchar,
    Other_Provider_Identifier_Type_Code_6 varchar,
    Other_Provider_Identifier_State_6 varchar,
    Other_Provider_Identifier_Issuer_6 varchar,
    Other_Provider_Identifier_7 varchar,
    Other_Provider_Identifier_Type_Code_7 varchar,
    Other_Provider_Identifier_State_7 varchar,
    Other_Provider_Identifier_Issuer_7 varchar,
    Other_Provider_Identifier_8 varchar,
    Other_Provider_Identifier_Type_Code_8 varchar,
    Other_Provider_Identifier_State_8 varchar,
    Other_Provider_Identifier_Issuer_8 varchar,
    Other_Provider_Identifier_9 varchar,
    Other_Provider_Identifier_Type_Code_9 varchar,
    Other_Provider_Identifier_State_9 varchar,
    Other_Provider_Identifier_Issuer_9 varchar,
    Other_Provider_Identifier_10 varchar,
    Other_Provider_Identifier_Type_Code_10 varchar,
    Other_Provider_Identifier_State_10 varchar,
    Other_Provider_Identifier_Issuer_10 varchar,
    Other_Provider_Identifier_11 varchar,
    Other_Provider_Identifier_Type_Code_11 varchar,
    Other_Provider_Identifier_State_11 varchar,
    Other_Provider_Identifier_Issuer_11 varchar,
    Other_Provider_Identifier_12 varchar,
    Other_Provider_Identifier_Type_Code_12 varchar,
    Other_Provider_Identifier_State_12 varchar,
    Other_Provider_Identifier_Issuer_12 varchar,
    Other_Provider_Identifier_13 varchar,
    Other_Provider_Identifier_Type_Code_13 varchar,
    Other_Provider_Identifier_State_13 varchar,
    Other_Provider_Identifier_Issuer_13 varchar,
    Other_Provider_Identifier_14 varchar,
    Other_Provider_Identifier_Type_Code_14 varchar,
    Other_Provider_Identifier_State_14 varchar,
    Other_Provider_Identifier_Issuer_14 varchar,
    Other_Provider_Identifier_15 varchar,
    Other_Provider_Identifier_Type_Code_15 varchar,
    Other_Provider_Identifier_State_15 varchar,
    Other_Provider_Identifier_Issuer_15 varchar,
    Other_Provider_Identifier_16 varchar,
    Other_Provider_Identifier_Type_Code_16 varchar,
    Other_Provider_Identifier_State_16 varchar,
    Other_Provider_Identifier_Issuer_16 varchar,
    Other_Provider_Identifier_17 varchar,
    Other_Provider_Identifier_Type_Code_17 varchar,
    Other_Provider_Identifier_State_17 varchar,
    Other_Provider_Identifier_Issuer_17 varchar,
    Other_Provider_Identifier_18 varchar,
    Other_Provider_Identifier_Type_Code_18 varchar,
    Other_Provider_Identifier_State_18 varchar,
    Other_Provider_Identifier_Issuer_18 varchar,
    Other_Provider_Identifier_19 varchar,
    Other_Provider_Identifier_Type_Code_19 varchar,
    Other_Provider_Identifier_State_19 varchar,
    Other_Provider_Identifier_Issuer_19 varchar,
    Other_Provider_Identifier_20 varchar,
    Other_Provider_Identifier_Type_Code_20 varchar,
    Other_Provider_Identifier_State_20 varchar,
    Other_Provider_Identifier_Issuer_20 varchar,
    Other_Provider_Identifier_21 varchar,
    Other_Provider_Identifier_Type_Code_21 varchar,
    Other_Provider_Identifier_State_21 varchar,
    Other_Provider_Identifier_Issuer_21 varchar,
    Other_Provider_Identifier_22 varchar,
    Other_Provider_Identifier_Type_Code_22 varchar,
    Other_Provider_Identifier_State_22 varchar,
    Other_Provider_Identifier_Issuer_22 varchar,
    Other_Provider_Identifier_23 varchar,
    Other_Provider_Identifier_Type_Code_23 varchar,
    Other_Provider_Identifier_State_23 varchar,
    Other_Provider_Identifier_Issuer_23 varchar,
    Other_Provider_Identifier_24 varchar,
    Other_Provider_Identifier_Type_Code_24 varchar,
    Other_Provider_Identifier_State_24 varchar,
    Other_Provider_Identifier_Issuer_24 varchar,
    Other_Provider_Identifier_25 varchar,
    Other_Provider_Identifier_Type_Code_25 varchar,
    Other_Provider_Identifier_State_25 varchar,
    Other_Provider_Identifier_Issuer_25 varchar,
    Other_Provider_Identifier_26 varchar,
    Other_Provider_Identifier_Type_Code_26 varchar,
    Other_Provider_Identifier_State_26 varchar,
    Other_Provider_Identifier_Issuer_26 varchar,
    Other_Provider_Identifier_27 varchar,
    Other_Provider_Identifier_Type_Code_27 varchar,
    Other_Provider_Identifier_State_27 varchar,
    Other_Provider_Identifier_Issuer_27 varchar,
    Other_Provider_Identifier_28 varchar,
    Other_Provider_Identifier_Type_Code_28 varchar,
    Other_Provider_Identifier_State_28 varchar,
    Other_Provider_Identifier_Issuer_28 varchar,
    Other_Provider_Identifier_29 varchar,
    Other_Provider_Identifier_Type_Code_29 varchar,
    Other_Provider_Identifier_State_29 varchar,
    Other_Provider_Identifier_Issuer_29 varchar,
    Other_Provider_Identifier_30 varchar,
    Other_Provider_Identifier_Type_Code_30 varchar,
    Other_Provider_Identifier_State_30 varchar,
    Other_Provider_Identifier_Issuer_30 varchar,
    Other_Provider_Identifier_31 varchar,
    Other_Provider_Identifier_Type_Code_31 varchar,
    Other_Provider_Identifier_State_31 varchar,
    Other_Provider_Identifier_Issuer_31 varchar,
    Other_Provider_Identifier_32 varchar,
    Other_Provider_Identifier_Type_Code_32 varchar,
    Other_Provider_Identifier_State_32 varchar,
    Other_Provider_Identifier_Issuer_32 varchar,
    Other_Provider_Identifier_33 varchar,
    Other_Provider_Identifier_Type_Code_33 varchar,
    Other_Provider_Identifier_State_33 varchar,
    Other_Provider_Identifier_Issuer_33 varchar,
    Other_Provider_Identifier_34 varchar,
    Other_Provider_Identifier_Type_Code_34 varchar,
    Other_Provider_Identifier_State_34 varchar,
    Other_Provider_Identifier_Issuer_34 varchar,
    Other_Provider_Identifier_35 varchar,
    Other_Provider_Identifier_Type_Code_35 varchar,
    Other_Provider_Identifier_State_35 varchar,
    Other_Provider_Identifier_Issuer_35 varchar,
    Other_Provider_Identifier_36 varchar,
    Other_Provider_Identifier_Type_Code_36 varchar,
    Other_Provider_Identifier_State_36 varchar,
    Other_Provider_Identifier_Issuer_36 varchar,
    Other_Provider_Identifier_37 varchar,
    Other_Provider_Identifier_Type_Code_37 varchar,
    Other_Provider_Identifier_State_37 varchar,
    Other_Provider_Identifier_Issuer_37 varchar,
    Other_Provider_Identifier_38 varchar,
    Other_Provider_Identifier_Type_Code_38 varchar,
    Other_Provider_Identifier_State_38 varchar,
    Other_Provider_Identifier_Issuer_38 varchar,
    Other_Provider_Identifier_39 varchar,
    Other_Provider_Identifier_Type_Code_39 varchar,
    Other_Provider_Identifier_State_39 varchar,
    Other_Provider_Identifier_Issuer_39 varchar,
    Other_Provider_Identifier_40 varchar,
    Other_Provider_Identifier_Type_Code_40 varchar,
    Other_Provider_Identifier_State_40 varchar,
    Other_Provider_Identifier_Issuer_40 varchar,
    Other_Provider_Identifier_41 varchar,
    Other_Provider_Identifier_Type_Code_41 varchar,
    Other_Provider_Identifier_State_41 varchar,
    Other_Provider_Identifier_Issuer_41 varchar,
    Other_Provider_Identifier_42 varchar,
    Other_Provider_Identifier_Type_Code_42 varchar,
    Other_Provider_Identifier_State_42 varchar,
    Other_Provider_Identifier_Issuer_42 varchar,
    Other_Provider_Identifier_43 varchar,
    Other_Provider_Identifier_Type_Code_43 varchar,
    Other_Provider_Identifier_State_43 varchar,
    Other_Provider_Identifier_Issuer_43 varchar,
    Other_Provider_Identifier_44 varchar,
    Other_Provider_Identifier_Type_Code_44 varchar,
    Other_Provider_Identifier_State_44 varchar,
    Other_Provider_Identifier_Issuer_44 varchar,
    Other_Provider_Identifier_45 varchar,
    Other_Provider_Identifier_Type_Code_45 varchar,
    Other_Provider_Identifier_State_45 varchar,
    Other_Provider_Identifier_Issuer_45 varchar,
    Other_Provider_Identifier_46 varchar,
    Other_Provider_Identifier_Type_Code_46 varchar,
    Other_Provider_Identifier_State_46 varchar,
    Other_Provider_Identifier_Issuer_46 varchar,
    Other_Provider_Identifier_47 varchar,
    Other_Provider_Identifier_Type_Code_47 varchar,
    Other_Provider_Identifier_State_47 varchar,
    Other_Provider_Identifier_Issuer_47 varchar,
    Other_Provider_Identifier_48 varchar,
    Other_Provider_Identifier_Type_Code_48 varchar,
    Other_Provider_Identifier_State_48 varchar,
    Other_Provider_Identifier_Issuer_48 varchar,
    Other_Provider_Identifier_49 varchar,
    Other_Provider_Identifier_Type_Code_49 varchar,
    Other_Provider_Identifier_State_49 varchar,
    Other_Provider_Identifier_Issuer_49 varchar,
    Other_Provider_Identifier_50 varchar,
    Other_Provider_Identifier_Type_Code_50 varchar,
    Other_Provider_Identifier_State_50 varchar,
    Other_Provider_Identifier_Issuer_50 varchar,
    Is_Sole_Proprietor varchar,
    Is_Organization_Subpart varchar,
    Parent_Organization_LBN varchar,
    Parent_Organization_TIN varchar,
    Authorized_Official_Name_Prefix_Text varchar,
    Authorized_Official_Name_Suffix_Text varchar,
    Authorized_Official_Credential_Text varchar,
    Healthcare_Provider_Taxonomy_Group_1 varchar,
    Healthcare_Provider_Taxonomy_Group_2 varchar,
    Healthcare_Provider_Taxonomy_Group_3 varchar,
    Healthcare_Provider_Taxonomy_Group_4 varchar,
    Healthcare_Provider_Taxonomy_Group_5 varchar,
    Healthcare_Provider_Taxonomy_Group_6 varchar,
    Healthcare_Provider_Taxonomy_Group_7 varchar,
    Healthcare_Provider_Taxonomy_Group_8 varchar,
    Healthcare_Provider_Taxonomy_Group_9 varchar,
    Healthcare_Provider_Taxonomy_Group_10 varchar,
    Healthcare_Provider_Taxonomy_Group_11 varchar,
    Healthcare_Provider_Taxonomy_Group_12 varchar,
    Healthcare_Provider_Taxonomy_Group_13 varchar,
    Healthcare_Provider_Taxonomy_Group_14 varchar,
    Healthcare_Provider_Taxonomy_Group_15 varchar,
    Certification_Date varchar,
    current_month varchar
)
WITH (
    external_location = 's3://vr-timestamp/bi_sources/medicx/NPPES_NPI/monthly/npidata/',
    format = 'PARQUET',
    partitioned_by = ARRAY['current_month']
);