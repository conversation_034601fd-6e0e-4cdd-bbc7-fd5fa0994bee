CREATE TABLE IF NOT EXISTS s3.bronze_medicx.npi_endpoint(
    NPI varchar,
    Endpoint_Type varchar,
    Endpoint_Type_Description varchar,
    Endpoint varchar,
    Affiliation varchar,
    Endpoint_Description varchar,
    Affiliation_Legal_Business_Name varchar,
    Use_Code varchar,
    Use_Description varchar,
    Other_Use_Description varchar,
    Content_Type varchar,
    Content_Description varchar,
    Other_Content_Description varchar,
    Affiliation_Address_Line_One varchar,
    Affiliation_Address_Line_Two varchar,
    Affiliation_Address_City varchar,
    Affiliation_Address_State varchar,
    Affiliation_Address_Country varchar,
    Affiliation_Address_Postal_Code  varchar,
    current_month varchar
)
WITH (
    external_location = 's3://vr-timestamp/bi_sources/medicx/NPPES_NPI/monthly/endpoint/',
    format = 'PARQUET',
    partitioned_by = ARRAY['current_month']
);