CREATE TABLE  IF NOT EXISTS olympus.silver_medicx.npi_monthly(
    npi varchar,
    provider_first_line_business_mailing_address varchar,
    provider_business_mailing_address_city_name varchar,
    provider_business_mailing_address_state_name varchar,
    provider_business_mailing_address_postal_code varchar,
    provider_first_line_business_practice_location_address varchar,
    provider_business_practice_location_address_city_name varchar,
    provider_business_practice_location_address_state_name varchar,
    provider_business_practice_location_address_postal_code varchar,
    provider_ethashv1 varchar,
    provider_ethashv2 varchar,
    provider_rdi varchar,
    provider_matchcode varchar,
    practice_ethashv1 varchar,
    practice_ethashv2 varchar,
    practice_rdi varchar,
    practice_matchcode varchar,
    last_inserted TIMESTAMP,
    last_updated TIMESTAMP 
);