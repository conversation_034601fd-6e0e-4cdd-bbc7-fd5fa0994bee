MERGE INTO olympus.silver_medicx.npi_monthly AS target
USING (
    SELECT DISTINCT 
        core.npi, 
        core.provider_first_line_business_mailing_address,
        core.provider_business_mailing_address_city_name,
        core.provider_business_mailing_address_state_name,
        core.provider_business_mailing_address_postal_code,
        core.provider_first_line_business_practice_location_address,
        core.provider_business_practice_location_address_city_name,
        core.provider_business_practice_location_address_state_name,
        core.provider_business_practice_location_address_postal_code,
        provider.ethashv1 AS provider_ethashv1, 
        provider.ethashv2 AS provider_ethashv2,
        provider.rdi AS provider_rdi,
        provider.matchcode AS provider_matchcode,
        practice.ethashv1 AS practice_ethashv1,
        practice.ethashv2 AS practice_ethashv2,
        practice.rdi AS practice_rdi,
        practice.matchcode AS practice_matchcode
    FROM s3.bronze_medicx.npi_data_monthly core
    INNER JOIN olympus.silver_medicx.provider_business_address_bridge provider
        ON core.npi = provider.npi
    INNER JOIN olympus.silver_medicx.provider_practice_address_bridge practice
        ON core.npi = practice.npi
) AS source
    ON (
    target.npi = source.npi
    AND target.provider_first_line_business_mailing_address = source.provider_first_line_business_mailing_address
    AND target.provider_business_mailing_address_city_name = source.provider_business_mailing_address_city_name
    AND target.provider_business_mailing_address_state_name = source.provider_business_mailing_address_state_name
    AND target.provider_business_mailing_address_postal_code = source.provider_business_mailing_address_postal_code
    AND target.provider_first_line_business_practice_location_address = source.provider_first_line_business_practice_location_address
    AND target.provider_business_practice_location_address_city_name = source.provider_business_practice_location_address_city_name
    AND target.provider_business_practice_location_address_state_name = source.provider_business_practice_location_address_state_name
    AND target.provider_business_practice_location_address_postal_code = source.provider_business_practice_location_address_postal_code
)
WHEN MATCHED  
    THEN UPDATE SET 
        provider_first_line_business_mailing_address = source.provider_first_line_business_mailing_address,
        provider_business_mailing_address_city_name = source.provider_business_mailing_address_city_name,
        provider_business_mailing_address_state_name = source.provider_business_mailing_address_state_name,
        provider_business_mailing_address_postal_code = source.provider_business_mailing_address_postal_code,
        provider_first_line_business_practice_location_address = source.provider_first_line_business_practice_location_address,
        provider_business_practice_location_address_city_name = source.provider_business_practice_location_address_city_name,
        provider_business_practice_location_address_state_name = source.provider_business_practice_location_address_state_name,
        provider_business_practice_location_address_postal_code = source.provider_business_practice_location_address_postal_code,
        provider_ethashv1 = source.provider_ethashv1,
        provider_ethashv2 = source.provider_ethashv2,
        practice_ethashv1 = source.practice_ethashv1,
        practice_ethashv2 = source.practice_ethashv2
WHEN NOT MATCHED THEN
    INSERT (
        npi, 
        provider_first_line_business_mailing_address,
        provider_business_mailing_address_city_name,
        provider_business_mailing_address_state_name,
        provider_business_mailing_address_postal_code,
        provider_first_line_business_practice_location_address,
        provider_business_practice_location_address_city_name,
        provider_business_practice_location_address_state_name,
        provider_business_practice_location_address_postal_code,
        provider_ethashv1, 
        provider_ethashv2,
        provider_rdi,
        provider_matchcode,
        practice_ethashv1,
        practice_ethashv2,
        practice_rdi,
        practice_matchcode
    ) VALUES (
        source.npi, 
        source.provider_first_line_business_mailing_address,
        source.provider_business_mailing_address_city_name,
        source.provider_business_mailing_address_state_name,
        source.provider_business_mailing_address_postal_code,
        source.provider_first_line_business_practice_location_address,
        source.provider_business_practice_location_address_city_name,
        source.provider_business_practice_location_address_state_name,
        source.provider_business_practice_location_address_postal_code,
        source.provider_ethashv1, 
        source.provider_ethashv2,
        source.provider_rdi,
        source.provider_matchcode,
        source.practice_ethashv1,
        source.practice_ethashv2,
        source.practice_rdi,
        source.practice_matchcode
    );
