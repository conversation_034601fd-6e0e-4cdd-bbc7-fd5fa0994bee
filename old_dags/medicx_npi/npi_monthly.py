from etdag import ETDAG
from airflow.decorators import task
import os
import zipfile
import boto3
import re
import tempfile
import pandas as pd
from datetime import datetime
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator 
from airflow.providers.trino.hooks.trino import TrinoHook
import urllib.request
from starburst_geocoder_operator import StarburstGeocoderOperator


REFRESH_OLYMPUS_SQL = """
MERGE INTO olympus.silver_medicx.npi_monthly AS target
USING (
    SELECT DISTINCT 
        core.npi, 
        core.provider_first_line_business_mailing_address,
        core.provider_business_mailing_address_city_name,
        core.provider_business_mailing_address_state_name,
        core.provider_business_mailing_address_postal_code,
        core.provider_first_line_business_practice_location_address,
        core.provider_business_practice_location_address_city_name,
        core.provider_business_practice_location_address_state_name,
        core.provider_business_practice_location_address_postal_code,
        provider.ethashv1 AS provider_ethashv1, 
        provider.ethashv2 AS provider_ethashv2,
        provider.rdi AS provider_rdi,
        provider.matchcode AS provider_matchcode,
        practice.ethashv1 AS practice_ethashv1,
        practice.ethashv2 AS practice_ethashv2,
        practice.rdi AS practice_rdi,
        practice.matchcode AS practice_matchcode
    FROM s3.bronze_medicx.npi_data core
    INNER JOIN olympus.silver_medicx.provider_business_address_bridge provider
        ON core.npi = provider.npi
    INNER JOIN olympus.silver_medicx.provider_practice_address_bridge practice
        ON core.npi = practice.npi
) AS source
    ON (
    target.npi = source.npi
)
WHEN MATCHED  
    AND target.provider_first_line_business_mailing_address = source.provider_first_line_business_mailing_address
    AND target.provider_business_mailing_address_city_name = source.provider_business_mailing_address_city_name
    AND target.provider_business_mailing_address_state_name = source.provider_business_mailing_address_state_name
    AND target.provider_business_mailing_address_postal_code = source.provider_business_mailing_address_postal_code
    AND target.provider_first_line_business_practice_location_address = source.provider_first_line_business_practice_location_address
    AND target.provider_business_practice_location_address_city_name = source.provider_business_practice_location_address_city_name
    AND target.provider_business_practice_location_address_state_name = source.provider_business_practice_location_address_state_name
    AND target.provider_business_practice_location_address_postal_code = source.provider_business_practice_location_address_postal_code
    THEN UPDATE SET 
        provider_first_line_business_mailing_address = source.provider_first_line_business_mailing_address,
        provider_business_mailing_address_city_name = source.provider_business_mailing_address_city_name,
        provider_business_mailing_address_state_name = source.provider_business_mailing_address_state_name,
        provider_business_mailing_address_postal_code = source.provider_business_mailing_address_postal_code,
        provider_first_line_business_practice_location_address = source.provider_first_line_business_practice_location_address,
        provider_business_practice_location_address_city_name = source.provider_business_practice_location_address_city_name,
        provider_business_practice_location_address_state_name = source.provider_business_practice_location_address_state_name,
        provider_business_practice_location_address_postal_code = source.provider_business_practice_location_address_postal_code,
        provider_ethashv1 = source.provider_ethashv1,
        provider_ethashv2 = source.provider_ethashv2,
        practice_ethashv1 = source.practice_ethashv1,
        practice_ethashv2 = source.practice_ethashv2,
        last_inserted = target.last_inserted,
        last_updated = current_timestamp
WHEN NOT MATCHED THEN
    INSERT (
        npi, 
        provider_first_line_business_mailing_address,
        provider_business_mailing_address_city_name,
        provider_business_mailing_address_state_name,
        provider_business_mailing_address_postal_code,
        provider_first_line_business_practice_location_address,
        provider_business_practice_location_address_city_name,
        provider_business_practice_location_address_state_name,
        provider_business_practice_location_address_postal_code,
        provider_ethashv1, 
        provider_ethashv2,
        provider_rdi,
        provider_matchcode,
        practice_ethashv1,
        practice_ethashv2,
        practice_rdi,
        practice_matchcode,
        last_inserted,
        last_updated 
    ) VALUES (
        source.npi, 
        source.provider_first_line_business_mailing_address,
        source.provider_business_mailing_address_city_name,
        source.provider_business_mailing_address_state_name,
        source.provider_business_mailing_address_postal_code,
        source.provider_first_line_business_practice_location_address,
        source.provider_business_practice_location_address_city_name,
        source.provider_business_practice_location_address_state_name,
        source.provider_business_practice_location_address_postal_code,
        source.provider_ethashv1, 
        source.provider_ethashv2,
        source.provider_rdi,
        source.provider_matchcode,
        source.practice_ethashv1,
        source.practice_ethashv2,
        source.practice_rdi,
        source.practice_matchcode,
        current_timestamp,
        NULL
    )
"""

default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'start_date': datetime(2024, 6, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}


with ETDAG(
    dag_id="medicxs_npi_monthly_process",
    description="Monthly Update of NPI (National Provider) Data",
    schedule_interval="@monthly",
    default_args=default_args,
    catchup=False,
    tags=["team:DND", "application:medicx"],
) as dag:
    @task
    def download_unzip_process_upload():
        # Constants
        S3_BUCKET_NAME = "vr-timestamp"
        S3_PREFIX = "bi_sources/medicx/NPPES_NPI/monthly/"
        CMS_BASE_URL = "https://download.cms.gov/nppes/"

        now = datetime.now()
        month_year = now.strftime("%B_%Y")  # e.g., "June_2024"
        prefix_month_date = now.strftime("%m-%Y")

        url = f"{CMS_BASE_URL}NPPES_Data_Dissemination_{month_year}.zip"

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_zip_path = os.path.join(temp_dir, "npi_download.zip")

            print(f"Downloading {url} to {temp_zip_path}")
            urllib.request.urlretrieve(url, temp_zip_path)

            print(f"Unzipping contents to {temp_dir}")
            with zipfile.ZipFile(temp_zip_path, "r") as zip_ref:
                zip_ref.extractall(temp_dir)

            unzipped_files = [
                os.path.join(temp_dir, file_name)
                for file_name in os.listdir(temp_dir)
                if file_name.endswith(".csv")
            ]

            if not unzipped_files:
                raise ValueError(f"No valid CSV files found in {temp_dir}")

            s3_client = boto3.client("s3")
            for file_path in unzipped_files:
                match = re.match(r"(.+)_pfile_\d{8}-\d{8}\.csv", os.path.basename(file_path))
                if match:
                    prefix = match.group(1)
                    new_file_name = f"{prefix}_pfile.csv"
                    temp_csv_path = os.path.join(temp_dir, new_file_name)
                    os.rename(file_path, temp_csv_path)
                    print(f"Renamed {file_path} to {temp_csv_path}")

                    process_and_upload_to_s3(temp_csv_path, prefix, prefix_month_date, temp_dir, s3_client,
                                             S3_BUCKET_NAME, S3_PREFIX)


    def process_and_upload_to_s3(file_path, prefix, prefix_month_date, temp_dir, s3_client, bucket_name, s3_prefix):
        """Read a CSV file in chunks, clean the data, and upload to S3 as Parquet."""

        for index, chunk in enumerate(pd.read_csv(file_path, dtype=str, chunksize=1_000_000)):
            if chunk.empty:
                raise ValueError(f"Dataframe chunk {index} is empty. Failing the task.")

            chunk.columns = (
                chunk.columns.str.replace("-", "")
                .str.replace(" ", "_")
                .str.replace(r"[().]", "", regex=True)
                .str.replace(".", "")
            )

            chunk = chunk.fillna("NULL").replace("", "NULL")
            chunk["current_month"] = prefix_month_date

            parquet_file_path = os.path.join(temp_dir, f"{prefix}_{index}_pfile.parquet")
            chunk.to_parquet(parquet_file_path, index=False)
            print(f"Saved chunk {index} to {parquet_file_path}")

            # Upload to S3
            s3_folder_path = f"{s3_prefix}{prefix}/current_month={prefix_month_date}/"
            s3_key = f"{s3_folder_path}{prefix}_{index}_pfile.parquet"
            s3_client.upload_file(parquet_file_path, bucket_name, s3_key)
            print(f"Uploaded {parquet_file_path} to s3://{bucket_name}/{s3_key}")

    @task
    def refresh_olympus_table(sql_statement):
        trino_hook = TrinoHook(trino_conn_id="starburst")
        trino_hook.run(sql_statement)
        print(f"Merge statement have successfully executed")


    sb_geocoder_business_provider = StarburstGeocoderOperator(
        task_id='sb_geocoder_business_provider',
        source_table_name='"s3"."bronze_medicx"."npi_data"',
        source_row_identifier_column_name="npi",
        address1_column_name="provider_first_line_business_mailing_address",
        zipcode_column_name="Provider_Business_Mailing_Address_Postal_Code",
        bridge_table_name='"olympus"."silver_medicx"."provider_business_address_bridge"',
        source_where_clause=f"WHERE current_month = '{datetime.now().strftime('%m-%Y')}'",
        geocoder_columns=[
            "etHashV1",
            "etHashV2",
            "addressLine",
            "city",
            "state",
            "zipcode",
            "rdi",
            "matchCode",
        ],
        bridge_record_ttl_days=25
    )

    sb_geocoder_practice_provider = StarburstGeocoderOperator(
        task_id='sb_geocoder_practice_provider',
        source_table_name='"s3"."bronze_medicx"."npi_data"',
        source_row_identifier_column_name="npi",
        address1_column_name="provider_first_line_business_practice_location_address",
        zipcode_column_name="Provider_Business_Practice_Location_Address_Postal_Code",
        bridge_table_name='"olympus"."silver_medicx"."provider_practice_address_bridge"',
        source_where_clause=f"WHERE current_month = '{datetime.now().strftime('%m-%Y')}'",
        geocoder_columns=[
            "etHashV1",
            "etHashV2",
            "addressLine",
            "city",
            "state",
            "zipcode",
            "rdi",
            "matchCode",
        ],
    )

    file_processing = download_unzip_process_upload()
    refresh_olympus_tbl = refresh_olympus_table(REFRESH_OLYMPUS_SQL)

    MEDICX_NPI = ["npi_data", "npi_endpoint", "npi_othername", "pl_pfile"]
    update_bronze_stmts = []
    for data in MEDICX_NPI:
        update_bronze_stmts.append(f"CALL s3.system.sync_partition_metadata('bronze_medicx', '{data}', 'ADD')")

    update_bronze = SQLExecuteQueryOperator(
        task_id=f"update_partitions",
        conn_id="starburst",
        sql=update_bronze_stmts,
        handler=list,
    )

    file_processing >> update_bronze >> sb_geocoder_business_provider >> sb_geocoder_practice_provider >> refresh_olympus_tbl