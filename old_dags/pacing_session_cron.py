from etdag import ETDAG
from pygene.base_api import NextGenBaseAPI
from airflow.operators.python_operator import PythonOperator
from airflow.exceptions import AirflowFailException
from airflow.models import Variable
from airflow.utils.dates import days_ago
import requests
import json

def run_pacing_session(pacing_session):
    env = Variable.get("environment")
    if env == "dev":
        base_url = "https://bi-api.k8s.dev.eltoro.com"
    if env == "prod":
        base_url = "https://bi-api.k8s.eltoro.com"
    # Retrieve credentials
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    base_api = NextGenBaseAPI(client_id, client_secret, env=env)
    url = f"{base_url}/api/v1/pacing-stats/rerun?pacing_session={pacing_session}"
    # Set headers with the initial access token
    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, timeout=10, headers=headers)
        # Check if token needs refresh
        if base_api._resp_handler(response):
            print("Refreshing token and retrying...")
            base_api.refresh_token()  # Assuming there's a method to refresh the token
            headers["Authorization"] = f"Bearer {base_api.access_token}"
            response = requests.post(url, timeout=10, headers=headers)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        raise AirflowFailException(f"Request to {url} failed: {e}")
    return


def create_pacing_cron(cron, pacing_session):
    dag_id = f"pacing_session_cron_{pacing_session}"
    with ETDAG(
        dag_id=dag_id,
        description="Hits an API to create pacing session",
        start_date=days_ago(2),
        default_args={
            "owner": "Rorie Lizenby",
            "depends_on_past": False,
            "email_on_failure": True,
            "email_on_retry": False,
            "retries": 0,
            "max_active_runs": 1,
        },
        schedule_interval=cron,
        catchup=False,
        tags=['app:pacing', 'team:DND'],
    ) as dag:

        PythonOperator(
            task_id='make_api_request',
            python_callable=run_pacing_session,
            op_kwargs={'pacing_session': pacing_session},
            dag=dag,
        )

# Create DAG instances
config_1 = {'pacing_session': 'morning', 'cron': "0 11 * * *"}
config_2 = {'pacing_session': 'afternoon', 'cron': "32 20 * * *"}

dag1 = create_pacing_cron(**config_1)
dag2 = create_pacing_cron(**config_2)
