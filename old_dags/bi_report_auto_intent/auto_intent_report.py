from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from airflow.operators.bash import Ba<PERSON><PERSON>perator
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.models.param import Param
from etdag import ETDAG
import pandas as pd
import time
import boto3
import json
import os
import gzip
import shutil
from copy import deepcopy
import s3fs
from airflow.operators.python_operator import PythonOperator
from typing import Dict, List


# Default arguments for the DAG
default_args = {
    "owner": "Tim Hoang",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
}

# Configuration for auto intent jobs
auto_intent_config = [
    {
        "name": "foreign",
        "bucket": "vr-timestamp",
        "temp_prefix": "bi_sources/tim_test/auto_intent/foreign/",
        "output_file": "auto_intent_foreign.csv.gz",
        "local_download_dir": "/tmp/",
        "sql_statements": [
            {
                "drop_temp_table": "DROP TABLE IF EXISTS s3.dev_data_analytics.temp_auto_table_foreign"
            },
            {
                "create_temp_table": """
                    create table s3.dev_data_analytics.temp_auto_table_foreign
                    WITH(
                        FORMAT = 'CSV',
                        external_location = 's3://vr-timestamp/bi_sources/tim_test/auto_intent/foreign/',
                        csv_separator = ',',
                        skip_header_line_count = 1
                    )
                    AS
                    SELECT 
                        ethash_v1,
                        ethash_v2,
                        address_line,
                        city,
                        state,
                        zipcode,
                        segmentid,
                        brand,    
                        category,    
                        parent_category,
                        sub_category,
                        topic,
                        path,    
                        cast(event_date as varchar) as event_date,
                        cast(mv_refresh_date as varchar) as mv_refresh_date
                    FROM s3.bronze_auto_intender.auto_intent_daily_4eyes 
                    WHERE sub_category = 'Foreign Brand'
                    AND state in ('TX','NC','FL','KY')
                    WITH DATA
                """
            },
        ],
    },
    {
        "name": "domestic",
        "bucket": "vr-timestamp",
        "temp_prefix": "bi_sources/tim_test/auto_intent/domestic/",
        "output_file": "auto_intent_domestic.csv.gz",
        "local_download_dir": "/tmp/",
        "sql_statements": [
            {
                "drop_temp_table": "DROP TABLE IF EXISTS s3.dev_data_analytics.temp_auto_table_domestic"
            },
            {
                "create_temp_table": """
                    create table s3.dev_data_analytics.temp_auto_table_domestic
                    WITH(
                        FORMAT = 'CSV',
                        external_location = 's3://vr-timestamp/bi_sources/tim_test/auto_intent/domestic/',
                        csv_separator = ',',
                        skip_header_line_count = 1
                    )
                    AS
                    SELECT 
                        ethash_v1,
                        ethash_v2,
                        address_line,
                        city,
                        state,
                        zipcode,
                        segmentid,
                        brand,    
                        category,    
                        parent_category,
                        sub_category,
                        topic,
                        path,    
                        cast(event_date as varchar) as event_date,
                        cast(mv_refresh_date as varchar) as mv_refresh_date
                    FROM s3.bronze_auto_intender.auto_intent_daily_4eyes 
                    WHERE sub_category != 'Foreign Brand'
                    AND state in ('TX','NC','FL','KY')
                    WITH DATA
                """
            },
        ],
    },
]


def delete_s3_objects(s3_client, bucket_name: str, prefix: str):
    paginator = s3_client.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix)
    objects_to_delete = []

    for page in pages:
        objects_to_delete.extend(
            [{"Key": obj["Key"]} for obj in page.get("Contents", [])]
        )

    if objects_to_delete:
        s3_client.delete_objects(
            Bucket=bucket_name, Delete={"Objects": objects_to_delete}
        )


def execute_sql_statements(trino_hook: TrinoHook, sql_statements: List[Dict[str, str]]):
    for statement in sql_statements:
        for key, sql in statement.items():
            trino_hook.run(sql)
            time.sleep(10 if key == "drop_temp_table" else 300)


def download_and_combine_files(
    s3_client, bucket_name: str, prefix: str, output_path: str
):
    paginator = s3_client.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

    print(
        f"Starting to download and combine files from S3 bucket {bucket_name} with prefix {prefix}"
    )
    with gzip.open(output_path, "wb") as output_gz:
        for page in page_iterator:
            for obj in page.get("Contents", []):
                if obj["Key"].endswith(".gz"):
                    local_file_path = os.path.join(
                        "/tmp/", os.path.basename(obj["Key"])
                    )

                    # Log the file being downloaded
                    print(f"Downloading file: {obj['Key']} to {local_file_path}")
                    s3_client.download_file(bucket_name, obj["Key"], local_file_path)

                    # Log the start of file extraction and combination
                    print(f"Combining file: {local_file_path} into {output_path}")
                    with gzip.open(local_file_path, "rb") as input_gz:
                        shutil.copyfileobj(input_gz, output_gz)

                    # Log the deletion of the temporary local file
                    print(f"Deleting local file: {local_file_path}")
                    os.remove(local_file_path)

    # Final log after all files are combined
    print(f"All files combined successfully into {output_path}")


def process_job(job):
    s3_client = boto3.client("s3")
    trino_hook = TrinoHook(trino_conn_id="trino_conn")

    delete_s3_objects(s3_client, job["bucket"], job["temp_prefix"])
    execute_sql_statements(trino_hook, job["sql_statements"])

    output_path = os.path.join(job["local_download_dir"], job["output_file"])
    os.makedirs(job["local_download_dir"], exist_ok=True)

    download_and_combine_files(
        s3_client, job["bucket"], job["temp_prefix"], output_path
    )

    final_s3_path = f"bi_sources/auto_tables/{job['output_file']}"
    s3_client.upload_file(output_path, job["bucket"], final_s3_path)
    os.remove(output_path)

    print(
        f"Successfully combined files into {job['output_file']} for job {job['name']}"
    )


@task
def read_jobs_from_s3(bucket_name: str, file_key: str):
    s3_client = boto3.client("s3")
    response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
    return json.loads(response["Body"].read().decode("utf-8"))


def fetch_and_write_to_s3(job_config: Dict):
    df_chunks = TrinoHook(trino_conn_id="trino_conn").get_pandas_df_by_chunks(
        sql=job_config["trino_query"], chunksize=100_000
    )

    for i, df_chunk in enumerate(df_chunks):
        process_and_upload_data(df_chunk, job_config, i + 1)


def process_and_upload_data(df: pd.DataFrame, job_config: Dict, chunk_number: int):
    s3_fs = s3fs.S3FileSystem(
        config_kwargs={"connect_timeout": 600, "read_timeout": 600}
    )
    mode, header = ("a", False) if chunk_number > 1 else ("w", True)
    compression = "gzip" if job_config["compress_to_gzip"] else "infer"

    s3_locations = deepcopy(job_config["s3_locations"])
    if job_config["LargeDataSet"]:
        s3_locations = [
            {
                "s3_bucket": loc["s3_bucket"],
                "s3_key": f"{loc['s3_key'].rsplit('.', 1)[0]}_{chunk_number}.{loc['s3_key'].rsplit('.', 1)[1]}",
            }
            for loc in s3_locations
        ]

    if job_config["compress_to_gzip"]:
        s3_locations = [
            {"s3_bucket": loc["s3_bucket"], "s3_key": f"{loc['s3_key']}.gz"}
            for loc in s3_locations
        ]

    for loc in s3_locations:
        s3_url = f"s3://{loc['s3_bucket']}/{loc['s3_key']}"
        print(f"Uploading chunk {chunk_number} to {s3_url}")
        with s3_fs.open(s3_url, mode) as file:
            df.to_csv(
                file,
                index=False,
                encoding="utf-8",
                compression=compression,
                header=header,
            )


with ETDAG(
    dag_id="auto_intent_bi_report",
    default_args=default_args,
    description="Dynamically Generate auto intent for BI team",
    schedule_interval="0 10 * * 2-5", # From Tuesday to Friday at 10:00 (Airflow UTC)
    catchup=False,
    concurrency=1,
    max_active_runs=1,
    start_date=days_ago(2),
    tags=["starburst", "bi-report", "auto intent"],
) as dag:

    for job in auto_intent_config:
        PythonOperator(
            task_id=f'process_{job["name"]}',
            python_callable=process_job,
            op_args=[job],
        )
