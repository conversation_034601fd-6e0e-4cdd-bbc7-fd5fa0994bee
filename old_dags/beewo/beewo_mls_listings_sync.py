import logging
import pendulum
import pandas.io.common

from typing import Type

from airflow.providers.common.sql.hooks.sql import DbApiHook
from airflow.decorators import dag, task
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook

import sqlalchemy.engine.base

logger = logging.getLogger("airflow.task")


def get_trino_sa_engine_from_conn_id(trino_conn_id: str) -> sqlalchemy.engine.base.Engine:
    import sqlalchemy as sa
    tr = TrinoHook(trino_conn_id)
    uri = tr.get_uri()
    if "https://" in uri:
        uri = uri.replace("https://", "")
    if "jwt__token" in uri:
        uri = uri.replace("jwt__token", "access_token")
    engine = sa.create_engine(uri)
    return engine


def stream_sql_to_csv_file(h: Type[DbApiHook], sql: str, path: str, fs_conn_id: str, chunksize: int = 10000):
    logger.info(f"Exporting the results of executing '[{h.conn_name_attr}]{sql}' to '[{fs_conn_id}]{path}'")
    with pandas.io.common.get_handle(path, "w", compression="infer", storage_options={"session": S3Hook(fs_conn_id).get_session(deferrable=True)}) as handles:
        header = True
        records = 0
        for chunk_df in h.get_pandas_df_by_chunks(sql, chunksize=chunksize):
            chunk_df.to_csv(handles.handle, encoding="utf-8", mode="a", index=False, header=header)
            header = False
            records += chunksize
            logger.debug(f"Wrote {records} records")
    logger.info(f"Exported {records} records to '{path}'")


@dag(
    dag_id="beewo_mls_listings_sync",
    description="Syncs MLS Listings from starburst to Beewo S3",
    default_args={
        "owner": "Loki Jacobsen",
        "email": ["<EMAIL>"],
        "email_on_failure": True,
        "email_on_retry": False,
    },
    schedule_interval="@daily",
    start_date=pendulum.datetime(2024, 3, 1),
    catchup=False
)
def beewo_mls_listings_sync():
    @task
    def export_listings_v1_1(ds: str):
        """
        This task is responsible for syncing MLS Listings from Beewo to S3
        """

        tr = TrinoHook(trino_conn_id="starburst")

        stream_sql_to_csv_file(
            tr,"""
                SELECT
                    listing_uid
                    , listingid
                    , mls_service_name
                    , mls_service_code
                    , data_source
                    , unparsedaddress
                    , situs_city
                    , state
                    , postalcode
                    , subdivisionname
                    , listprice
                    , buildingareatotal
                    , bedrooms_total
                    , bathroomstotalinteger
                    , yearbuilt
                    , created
                    , updated
                    , statuscode
                    , propertysubtype
                    , poolprivateyn
                    , listagentname
                    , listagentmlsid
                    , listofficename
                    , listofficemlsid
                    , standardstatus
                    , onmarketdate
                    , offmarketdate
                    , ethash
                    , wkb
                    , device_count
                    , hh_count
                    , file_transfer_date                
                FROM s3.beewo.mls_listings_with_observation_counts ORDER BY file_transfer_date DESC, updated DESC LIMIT 1000000
            """,
            f"s3://beewo-et-incoming/mls_listings/v1.1/full/{ds}.csv.gz","beewo_s3")


    export_listings_v1_1()


dag_object = beewo_mls_listings_sync()


if __name__ == "__main__":
    import os
    # required to prevent wierd ab_user errors
    from airflow.providers.fab.auth_manager.models import User  # noqa
    from airflow.models.dagrun import DagRunNote  # noqa
    logger.setLevel(logging.DEBUG)
    dag_object.test(
        execution_date=pendulum.datetime(2024, 10, 20),
        conn_file_path="local_connections.yaml",
        variable_file_path="local_variables.json"
    )
