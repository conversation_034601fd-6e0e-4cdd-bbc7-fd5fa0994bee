## Field Mappings using RESO Data Dictionary v1.7
- [Google Docs Link](https://docs.google.com/spreadsheets/d/1SZ0b6T4_lz6ti6qB2Je7NSz_9iNOaV_v9dbfhPwWgXA/edit#gid=799978943)
- [GitHub Link](https://github.com/RESOStandards/transport/blob/dd-1.7-lookup-resource/data-dictionary.md)

| CL FIELD NAME                         | RESO FIELD NAME | VALID VALUES                                            | DESCRIPTION                                                                                |
|---------------------------------------|-----------------|---------------------------------------------------------|--------------------------------------------------------------------------------------------|
| LISTING ID                            |                 |                                                         | Unique identifier for the listed property. Example: 2846797                                |
| LISTING TRANSACTION TYPE CODE DERIVED |                 | S = Sale, R = Rent                                      | CoreLogic generated identifier that determines if listed property is for sale or for rent. |
| LISTING STATUS CODE STANDARDIZED      |                 | A=Active, CG=Contingent, IA=Inactive, P=Pending, S=Sold | CoreLogic generated identifier that determines the status of the listing.                  |
| LISTING STATUS DESCRIPTION            |                 | Closed, cancelled, expired                              | Describes listing status. Value is from the Multiple List Service (MLS).                   |
| LISTING ADDRESS STREET ADDRESS        |                 | 	                                                    | Full or complete street address (number, street name, direction suffix/prefix, street suffix) for listed property. Includes unit number if available. Does not include city, state, or postal code (ZIP Code). Example: 4506 Doverbrook Road |
| LISTING ADDRESS CITY                  |                 |                                                         | Abbreviation for state, commonwealth, district or province location for listed property. Example: AZ |
| PROPERTY SUB TYPE DESCRIPTION         |                 | Multiple Listing Service (MLS) property sub type description. Examples: Borders common area, mountain view(s)
TOTAL BATHS	Total number of individual bathrooms in a listed unit or property. This is the sum of Full Baths and Partial Baths fields if at least one of each is present. Examples: 3 full + 1 Half = 3.5
ORIGINAL LISTING DATE AND TIME 	First recording of Listing.  Format: YYYY-MM-DD hh:mm:ss.mss
LAST LISTING DATE AND TIME 	Last date and time the property was actively observed on market.  Format: YYYY-MM-DD hh:mm:ss.mss.
OFF MARKET DATE AND TIME 	Listing no longer observed on market date and time or date and time the listing ended. Format: YYYY-MM-DD hh:mm:ss.mss.
CLOSED DATE	Listing close date for the property (Sold). Format: YYYY-MM-DD hh:mm:ss.mss
LISTING ADDRESS STATE	Abbreviation for state, commonwealth, district or province location for listed property. Example: AZ
LISTING ADDRESS ZIP CODE	Five-digit postal code (U.S. ZIP Code) portion of the street or mailing address for listed property. Example: 85302
CURRENT LISTING PRICE	Property list price at the time the record was last queried. A query occurs when new information is received from the Multiple Listing Service to determine there are any price changes. Example: 224000
LISTING AGENT NAME	Listing agent's name. Examples: John M Smith, J Smith
LISTING OFFICE NAME	Listing office's local unique name. Example: Florida Best Buy Realty, LLC
CLIP	Unique identification number assigned to each property.
FIPS CODE	Federal Information Processing Standards codes used nationally to numerically identify a specific county or political jurisdiction.
SITUS STATE	The two-letter USPS postal abbreviation associated with the state / protectorates / commonwealth (e.g., CA, VI).
SITUS CITY	The city associated with the property address.
SITUS ZIP CODE	Code assigned by the USPS. This is populated by various source files and other proprietary and non-proprietary processes. Data may be the 5-digit zip or 9-digit Zip+4 (e.g., 00501 or 954630042).
SITUS STREET ADDRESS	Full Situs address (not including City/St/Zip) (e.g. 123 N Main St)
SITUS UNIT NUMBER	The unit or suite number of the property address (e.g., 649 Lake Shore Dr #1400).
YEAR BUILT	Year the property was built. This can be a future year for new construction. Format: YYYY
LIVING AREA SQUARE FEET STANDARDIZED	CoreLogic standardized square footage (sq. ft.) of listed unit or building. Multiple Listing Service (MLS) has multiple fields that convey sq. ft. CoreLogic creates information in this field that is more accurate. It usually is the total sq. ft. of the living area. Example: 1821
ACTIVE AD COUNT	Number of active ads observed
LOCAL LISTING ID	Listing ID used by the local MLS Board