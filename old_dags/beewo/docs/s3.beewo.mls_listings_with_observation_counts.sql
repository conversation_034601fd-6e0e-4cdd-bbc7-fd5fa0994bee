CREATE MATERIALIZED VIEW "s3"."beewo"."mls_listings_with_observation_counts"
COMMENT '{
  "version": "v20250527"
  "description": "MLS listings for beewo with household and device counts"
  "author: "<PERSON> <<EMAIL>"
}'
WITH (
   cron = '30 2 * * *',
   grace_period = '10.00m',
   max_import_duration = '1.00h',
   run_as_invoker = true
) AS
WITH
  unq AS (
   SELECT
     listing_id
   , listing_transaction_type_code_derived
   , listing_status_code_standardized
   , listing_status_description
   , listing_address_street_address
   , listing_address_city
   , property_sub_type_description
   , total_baths
   , original_listing_date_and_time
   , last_listing_date_and_time
   , off_market_date_and_time
   , close_date
   , listing_address_state
   , listing_address_zip_code
   , current_listing_price
   , listing_agent_name
   , listing_office_name
   , clip
   , fips_code
   , situs_state
   , situs_city
   , situs_zip_code
   , situs_street_address
   , situs_unit_number
   , year_built
   , living_area_square_feet_standardized
   , active_ad_count
   , local_listing_id
   , listing_agent_identifier
   , buyer_agent_full_name
   , buyer_agent_identifier
   , listing_service_name
   , geocoder_ethashv1
   , geocoder_geometrywkb
   , file_transfer_date
   FROM
     "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode"
    WHERE
     file_transfer_date = (SELECT max(file_transfer_date) as max_ftd FROM "olympus"."bronze_corelogic"."mls_listings_sale_w_geocode")
)
, mls_listings AS (
   SELECT
     max(file_transfer_date) file_transfer_date
   , max_by(clip, file_transfer_date) clip
   , listing_id
   , max_by(local_listing_id, file_transfer_date) local_listing_id
   , max_by(situs_street_address, file_transfer_date) situs_street_address
   , max_by(situs_city, file_transfer_date) situs_city
   , max_by(situs_state, file_transfer_date) situs_state
   , max_by(situs_zip_code, file_transfer_date) situs_zip_code
   , max_by(current_listing_price, file_transfer_date) current_listing_price
   , max_by(living_area_square_feet_standardized, file_transfer_date) living_area_square_feet_standardized
   , max_by(total_baths, file_transfer_date) total_baths
   , max_by(year_built, file_transfer_date) year_built
   , min(original_listing_date_and_time) original_listing_date_and_time
   , max(last_listing_date_and_time) last_listing_date_and_time
   , max_by(listing_status_code_standardized, file_transfer_date) listing_status_code_standardized
   , max_by(property_sub_type_description, file_transfer_date) property_sub_type_description
   , max_by(listing_agent_name, file_transfer_date) listing_agent_name
   , max_by(listing_agent_identifier, file_transfer_date) listing_agent_identifier
   , max_by(buyer_agent_full_name, file_transfer_date) buyer_agent_full_name
   , max_by(buyer_agent_identifier, file_transfer_date) buyer_agent_identifier
   , max_by(listing_office_name, file_transfer_date) listing_office_name
   , max_by(listing_status_description, file_transfer_date) listing_status_description
   , max_by(off_market_date_and_time, file_transfer_date) off_market_date_and_time
   , max_by(listing_service_name, file_transfer_date) listing_service_name
   , max_by(geocoder_ethashv1, file_transfer_date) geocoder_ethashv1
   , max_by(geocoder_geometrywkb, file_transfer_date) geocoder_geometrywkb
   FROM
     unq
   WHERE ((listing_transaction_type_code_derived = 'S') AND (clip IS NOT NULL) AND (original_listing_date_and_time IS NOT NULL) AND (last_listing_date_and_time IS NOT NULL) AND (local_listing_id IS NOT NULL))
   GROUP BY listing_id
)
SELECT
  mls_listings.listing_id listing_uid
, mls_listings.local_listing_id listingid
, COALESCE(mls_listings.listing_service_name, 'Unknown') mls_service_name
, 'UNK' mls_service_code
, 'eltoro' data_source
, mls_listings.situs_street_address unparsedaddress
, mls_listings.situs_city situs_city
, mls_listings.situs_state state
, mls_listings.situs_zip_code postalcode
, prop.subdivision_name subdivisionname
, mls_listings.current_listing_price listprice
, mls_listings.living_area_square_feet_standardized buildingareatotal
, prop.bedrooms_all_buildings bedrooms_total
, ROUND(COALESCE(TRY_CAST(mls_listings.total_baths AS DECIMAL), 0)) bathroomstotalinteger
, mls_listings.year_built yearbuilt
, mls_listings.original_listing_date_and_time created
, mls_listings.last_listing_date_and_time updated
, mls_listings.listing_status_code_standardized statuscode
, mls_listings.property_sub_type_description propertysubtype
, CAST(null AS VARCHAR) poolprivateyn
, mls_listings.listing_agent_name listagentname
, mls_listings.listing_agent_identifier listagentmlsid
, mls_listings.buyer_agent_full_name buyagentname
, mls_listings.buyer_agent_identifier buyagentmlsid
, mls_listings.listing_office_name listofficename
, CAST(null AS VARCHAR) listofficemlsid
, mls_listings.listing_status_description standardstatus
, date(CAST(mls_listings.original_listing_date_and_time AS timestamp)) OnMarketDate
, date(CAST(mls_listings.off_market_date_and_time AS timestamp)) OffMarketDate
, mls_listings.geocoder_ethashv1 ethash
, mls_listings.geocoder_geometrywkb wkb
, obvs.device_count device_count
, obvs.hh_count hh_count
, mls_listings.file_transfer_date file_transfer_date
FROM
  ((mls_listings
LEFT JOIN olympus.silver_corelogic.property_basic2 prop ON (mls_listings.clip = prop.clip))
LEFT JOIN(
SELECT
   listing_ethash ethash,
   count(distinct dl.deviceid) device_count,
   count(distinct ethash) hh_count
FROM "s3"."gold_real_estate_intender"."device_listing_agg_filtered" dl
JOIN "s3"."gold_real_estate_intender"."device_ethash_latest" de ON dl.deviceid = de.deviceid
GROUP BY listing_ethash
) obvs ON (mls_listings.geocoder_ethashv1 = obvs.ethash))
WHERE ((mls_listings.geocoder_ethashv1 IS NOT NULL) AND (mls_listings.total_baths IS NOT NULL))
ORDER BY mls_listings.file_transfer_date DESC
;
