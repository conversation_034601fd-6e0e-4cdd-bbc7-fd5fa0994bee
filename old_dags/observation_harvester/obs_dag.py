from etdag import ETDAG
from datetime import datetime
from operators.timestamp_report_operator import TimestampReportOperator
from airflow.operators.dummy_operator import DummyOperator

default_args = {"owner": "BP"}
hardware_buckets = [
    "TJDiRXYn7FJ5dpYon",
    "6orcWwDy7qbHEwTrW",
    "L5Ej2M3s3eCXZKpxW",
    "XhjCTxfibHYd3mEde",
]
with ETDAG(
    dag_id="hardware_obs_harvester",
    catchup=False,
    default_args=default_args,
    is_paused_upon_creation=True,
    schedule_interval="0 22 * * *",  # Fires every day at 10 PM UTC
    start_date=datetime(2025, 5, 12),
    max_active_runs=1,
    tags=["observations", "hardware"],
) as dag:

    hardware_ts_instance = TimestampReportOperator.partial(
        task_id="run_hardware_reports",
        operation="create",
        start="{{ (execution_date - macros.timedelta(days=7)).strftime('%Y-%m-%d') }}",
        end="{{ (execution_date - macros.timedelta(days=4)).strftime('%Y-%m-%d') }}",
        request_types=[
            TimestampReportOperator.REQ_TYPE_ADDRESS_TO_DEVICE,
            TimestampReportOperator.REQ_TYPE_OBSERVATIONS,
        ],
        reduce_to_date=True,
        wait_for_job_to_finish=False,
        max_active_tis_per_dag=1,
    ).expand(bucket_id=hardware_buckets)

    hardware_ts_instance
