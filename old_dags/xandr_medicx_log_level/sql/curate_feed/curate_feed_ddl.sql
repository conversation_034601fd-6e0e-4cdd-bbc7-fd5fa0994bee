CREATE TABLE "s3"."bronze_medicx_xandr"."xandr_curate_feed" (
       auction_id_64 bigint ,
       date_time bigint  ,
       user_tz_offset int ,
       media_type int ,
       event_type varchar ,
       user_id_64 bigint ,
       hashed_user_id_64 varchar ,
       ip_address varchar ,
       ip_address_trunc varchar,
       country varchar,
       region varchar,
       dma int,
       city int,
       postal_code varchar,
       latitude varchar,
       latitude_trunc varchar,
       longitude varchar,
       longitude_trunc varchar,
       device_unique_id varchar,
       device_type int,
       tc_string varchar,
       curated_deal_id int,
       gross_revenue_dollars double,
       curator_margin double,
       total_tech_fees_dollars double,
       total_cost_dollars double,
       net_media_cost_dollars double,
       seller_memeber_id int,
       publisher_id int,
       site_id int,
       site_domain varchar,
       tag_id int,
       application_id varchar,
       mobile_app_instance_id int,
       buyer_member_id int,
       creative_id int,
       brand_id int,
       seller_deal_id int,
       view_result int,
       view_non_measurable_reason int,
       supply_type int,
       creative_width int,
       creative_height int,
       partition_time_millis timestamp(3),
       operation_system int,
       browser int,
       language int,
       device_id int,
        extended_ids array(ROW(id_type int, id_value varchar))
        -- DND-2585
        curated_deal_code varchar,
        split_id int,
        external_campaign_id varchar,
        external_bidrequest_id bigint,
        external_bidrequest_imp_id bigint,
        postal_code_ext_id int

      )
    WITH (
       external_location = 's3://medicx-apnx-reporting/feeds/curator_feed/2023/09/21/15/20230921173612/',
       format = 'AVRO',
      avro_schema_url = 's3://et-datalake-medicx-xandr/bronze/medicx_curator_schema.avsc'
    );



    create table if not exists olympus.bronze_medicx_xandr.xandr_curate_feed(
        auction_id_64 bigint ,
       date_time bigint  ,
       user_tz_offset int ,
       media_type int ,
       event_type varchar ,
       user_id_64 bigint ,
       hashed_user_id_64 varbinary ,
       ip_address varchar ,
       ip_address_trunc varchar,
       country varchar,
       region varchar,
       dma int,
       city int,
       postal_code varchar,
       latitude varchar,
       latitude_trunc varchar,
       longitude varchar,
       longitude_trunc varchar,
       device_unique_id varchar,
       device_type int,
       tc_string varchar,
       curated_deal_id int,
       gross_revenue_dollars double,
       curator_margin double,
       total_tech_fees_dollars double,
       total_cost_dollars double,
       net_media_cost_dollars double,
       seller_memeber_id int,
       publisher_id int,
       site_id int,
       site_domain varchar,
       tag_id int,
       application_id varchar,
       mobile_app_instance_id int,
       buyer_member_id int,
       creative_id int,
       brand_id int,
       seller_deal_id int,
       view_result int,
       view_non_measurable_reason int,
       supply_type int,
       creative_width int,
       creative_height int,
       partition_time_millis timestamp(6),
       operation_system int,
       browser int,
       language int,
       device_id int,
       extended_ids array(ROW(id_type int, id_value varchar))
    -- DND-2585
        curated_deal_code varchar,
        split_id int,
        external_campaign_id varchar,
        external_bidrequest_id bigint,
        external_bidrequest_imp_id bigint,
        postal_code_ext_id int

    )WITH (
   format = 'PARQUET',
   format_version = 1,
   location = 's3a://et-datalake-medicx-xandr/bronze//curator_feed_temp',
   partitioning = ARRAY['day(partition_time_millis)']
);

insert into olympus.bronze_medicx_xandr.xandr_curate_feed
select * from "s3"."bronze_medicx_xandr"."xandr_curate_feed";

