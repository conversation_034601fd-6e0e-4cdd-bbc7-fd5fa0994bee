from airflow.decorators import task, dag
import boto3
import logging
from etdag import ETDAG
from datetime import datetime
from airflow.providers.trino.hooks.trino import TrinoHook
import old_dags.xandr_medicx_log_level.medicx_log_level_data_config as config

docs = """
DAG Name: medicx_xandr_log_level_load

Description:
This DAG processes log-level data from an S3 bucket and loads it into a Starburst (Trino) database. It performs the following steps:
1. Retrieves a date parameter (either backfill or logical execution date) to define the time window for data extraction.
2. Fetches the existing log hours that have already been inserted for the specified date from Starburst to avoid redundant insertions.
3. Queries the S3 bucket for log paths that match the specified date.
4. Filters the log paths, removing those that have already been inserted into Starburst, based on the existing log hours.
5. Inserts the unprocessed log data into the Starburst table using an external Trino query.

The DAG utilizes Trino and S3 for data handling, and includes custom retry and logging mechanisms to ensure robust data ingestion.

On Failure:
 - Clear tasks on failure.  No harm will come if you do start a new run, but it will run for "yesterday" which may not be what you want.
 - Contact dag owner if error can not be easily diagnosed

Schedule: 
Runs daily at 6:25 AM (UTC) for each day of the week (Monday to Sunday).

Tags:
- application:medicx
- team:DND
"""


# Function to insert logs into the Trino table
def insert_logs(s3_paths: list):
    tr = TrinoHook(trino_conn_id="starburst")
    DROP_TBL_STMNT = str(config.DROP_TABLE_CURATE_FEED)
    CREATE_TBL_STMNT = str(config.CREATE_TABLE_STATMENT_CURATE_FEED).replace(
        "s3_path", f"{s3_paths}"
    )
    INSERT_TBL_STMNT = str(config.INSERT_STATEMENT_CURATE_FEED)

    tr.run(DROP_TBL_STMNT)
    tr.run(CREATE_TBL_STMNT)
    tr.run(INSERT_TBL_STMNT)


# Default arguments
default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'start_date': datetime(2024, 8, 25),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}


# DAG definition
with ETDAG(
    dag_id="medicx_xandr_log_level_load",
    description="Process to pull log level data from S3 and load it into Startburst",
    schedule_interval="25 6 * * 0-6",
    catchup=False,
    default_args=default_args,
    tags=["application:medicx", "team:DND"],
) as dag:
    dag.doc_md = docs

    @task
    def get_airflow_param(**kwargs) -> str:
        backfill_date = kwargs["params"].get("backfill_date_param", None)
        logical_date = '2024-09-12'  #kwargs["data_interval_start"].strftime("%Y-%m-%d")

        if backfill_date and len(str(backfill_date)) == 10:
            logging.info(f'Backfill date provided: {backfill_date}')
            return backfill_date
        else:
            logging.info(f'Using logical date: {logical_date}')
            return logical_date

    @task(retries=1)
    def get_existing_hours_for_partition(date: str) -> list:
        log_query = config.EXISTING_HOURS_STATEMENT_CURATE_FEED
        tr = TrinoHook(trino_conn_id="starburst")
        hours = tr.get_records(log_query.replace("p_date", date))

        return [str(h[0]).zfill(2) for h in hours]

    @task
    def get_log_paths_for_date(date: str) -> list:
        s3 = boto3.resource("s3")
        bucket_str = "medicx-apnx-reporting"
        prefix = f"feeds/curator_feed/{date[:4]}/{date[5:7]}/{date[8:10]}/"
        logging.info(f'Fetching logs from S3 with prefix: {prefix}')

        paths = []
        bucket = s3.Bucket(bucket_str)
        for obj in bucket.objects.filter(Prefix=prefix):
            key = obj.key
            if key.endswith(".avro"):
                path = "/".join(key.split("/")[:-1])
                paths.append(path)

        logging.info(f'Log paths found: {paths}')
        return paths

    @task
    def filter_already_inserted_from_path_list(paths: list, hours_inserted: list) -> list:
        uninserted_log_paths = [path for path in paths if path.split("/")[5] not in hours_inserted]
        logging.info(f'Uninserted log paths: {uninserted_log_paths}')
        return uninserted_log_paths

    @task(retries=1)
    def insert_logs_data(date: str, paths_uninserted: list):
        if paths_uninserted:
            for path in paths_uninserted:
                logging.info(f'Inserting logs for date {date} at path {path}')
                insert_logs(path)
        else:
            logging.info(f'No logs to insert for date {date}')

    date_value = get_airflow_param()
    hours_inserted = get_existing_hours_for_partition(date_value)
    paths = get_log_paths_for_date(date_value)
    paths_uninserted = filter_already_inserted_from_path_list(paths, hours_inserted)

    date_value >> paths
    hours_inserted >> paths_uninserted >> insert_logs_data(date_value, paths_uninserted)


