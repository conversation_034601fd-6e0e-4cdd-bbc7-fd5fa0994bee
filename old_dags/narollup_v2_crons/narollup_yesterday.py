from airflow.providers.amazon.aws.hooks.step_function import <PERSON><PERSON>un<PERSON>Hook
from airflow.utils.dates import days_ago
from etdag import ETDAG
import uuid
from airflow.decorators import task
from datetime import timedelta

unique_id = str(uuid.uuid4())


def Xandr_narollup_v2(name, start_hour, minute, env):
    dag_id = f"xandr_{env}_narollup_yesterday_{name}"
    with ETDAG(
        dag_id=dag_id,
        description="Crons for Narollup yesterday",
        start_date=days_ago(2),
        default_args={
            "owner": "BP",
            "depends_on_past": False,
            "email_on_retry": False,
            "retries": 2,
            "retry_delay": timedelta(seconds=10),
        },
        schedule_interval=f"{minute} {start_hour} * * *",
        catchup=False,
        tags=["reporting", "naRollUp", "team:DND"],
    ) as dag:

        @task(task_id="run_step_function")
        def kickoff():
            step_function_hook = StepFunctionHook(aws_conn_id="s3_conn")
            step_function_hook.start_execution(
                state_machine_arn=f"arn:aws:states:us-east-1:************:stateMachine:awseast-{env}-reporting-data-migration",
                name=f"{dag_id}-{unique_id}",
                state_machine_input={
                    "reportType": name,
                    "shouldMigrate": True,
                    "runDatesArray": None,
                    "runToday": False,
                    "runYesterday": True,
                    "isRerunReady": False,
                    "downloadFile": True,
                },
            )

        kickoff()
        return dag


# config_1 = {
#     "name": "video",
#     "minute": 45,
#     "env": "dev",
#     "start_hour": 4,
# }
# config_2 = {
#     "name": "network",
#     "minute": 15,
#     "env": "dev",
#     "start_hour": 4,
# }
config_3 = {
    "name": "video",
    "minute": 45,
    "env": "prod",
    "start_hour": 4,
}
config_4 = {
    "name": "network",
    "minute": 15,
    "env": "prod",
    "start_hour": 4,
}

# dag1 = Xandr_narollup_v2(**config_1)
# dag2 = Xandr_narollup_v2(**config_2)
dag3 = Xandr_narollup_v2(**config_3)
dag4 = Xandr_narollup_v2(**config_4)
