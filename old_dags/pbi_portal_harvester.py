from etdag import ETDAG
from datetime import timedelta, datetime
from airflow.decorators import task
import pandas as pd
from airflow.models import Variable
import json
import requests
import s3fs

"""_summary_

Purpose:
Auto Update S3 - powerbi/portal/ Folder by triggering Lambda

The code creates csv files from Power BI Portal BackOffice APIs, like, Users, Groups, Reports, and Report Access.
The code also uploads these CSV files to AWS S3.

Power BI Portal:

Links:
BackOffice: https://admin.powerbiportal.com/Account/Login?ReturnUrl=%2F
FrontOffice: https://app.powerbiportal.com/reports
PBI Portal BackOffice API: https://admin.powerbiportal.com/docs/swagger/index.html
"""


default_args = {"owner": "bp", "retries": 0, "retry_delay": timedelta(minutes=1)}
creds = json.loads(Variable.get("powerbi_portal"))

api_key = creds["api_key"]
environment_key = creds["environment_key"]
main_url = "https://admin.powerbiportal.com/api"
headers = {"X-API-KEY": api_key, "X-ENVIRONMENT-KEY": environment_key}


@task
def get_reports_csv():
    pg = 1
    reports = {}
    final_report_dict = {}
    reports_df = pd.DataFrame()
    while True:
        response = requests.request(
            "GET",
            f"{main_url}/Reports/WithPagination?pageSize=100&pageNumber={pg}",
            headers=headers,
        )
        reports = response.json()
        report_items = reports["items"]
        reports_items_df = pd.DataFrame(report_items)

        reports_df = pd.concat([reports_df, reports_items_df], ignore_index=True)

        report_id_dict = {}
        for report in report_items:
            report_id_dict[report["name"]] = report["reportId"]
            final_report_dict.update(report_id_dict)

        # Check if there is another page
        if not reports["hasNextPage"]:
            break
        pg += 1

    print("get_reports_csv")
    reports_df.to_csv(
        "s3://vr-timestamp/powerbi/portal/reports/reports.csv", index=False
    )
    return final_report_dict


@task
def get_report_access_csv(final_report_dict):
    pg = 1
    group_access = []
    user_access = []
    while True:
        response = requests.request(
            "GET",
            f"{main_url}/ReportsAccess/WithPagination?pageSize=100&pageNumber={pg}",
            headers=headers,
        )
        ra_res = response.json()

        # This will effectively overwrite the integer report id in this dataset with the uuid id, solving that problem
        ra = ra_res["items"]
        for access_record in ra:
            access_record["reportId"] = final_report_dict[access_record["reportName"]]

        for access_record in ra:
            if access_record["entityTypeDisplay"] == "User":
                user_access.append(access_record)
            else:
                group_access.append(access_record)

        if not ra_res["hasNextPage"]:
            break
        pg += 1

    group_access_df = pd.DataFrame(group_access)
    group_access_df.to_csv(
        "s3://vr-timestamp/powerbi/portal/group-access/group-access.csv", index=False
    )

    user_access_df = pd.DataFrame(user_access)
    user_access_df.to_csv(
        "s3://vr-timestamp/powerbi/portal/user-access/user-access.csv", index=False
    )
    print("get_report_access_csv")

    return


@task
def get_groups_csv():
    pg = 1
    group_ids = []
    groups_df = pd.DataFrame()
    while True:
        response = requests.request(
            "GET",
            f"{main_url}/Groups/WithPagination?pageSize=5&pageNumber={pg}",
            headers=headers,
        )
        groups = response.json()
        items = groups["items"]
        items_df = pd.DataFrame(items)
        ids = [str(g["id"]) for g in items]
        group_ids.extend(ids)
        groups_df = pd.concat([groups_df, items_df], ignore_index=True)

        # Check if there is another page
        if not groups["hasNextPage"]:
            break

        pg += 1

    groups_df.to_csv("s3://vr-timestamp/powerbi/portal/groups/groups.csv", index=False)
    print("get_groups_csv")

    return group_ids


@task
def get_user_group_csv(group_ids):
    group_user_associations = []

    for group_id in group_ids:
        group_user_url = f"{main_url}/Groups/{group_id}/users"
        response = requests.request("GET", group_user_url, headers=headers)
        user_dict = response.json()
        for user_id in user_dict["userListId"]:
            temp_dict = {"group_id": group_id, "user_id": user_id}
            group_user_associations.append(temp_dict)

    df = pd.DataFrame(group_user_associations)
    df.to_csv("s3://vr-timestamp/powerbi/portal/user-group/user-group.csv", index=False)
    print("get_user_group_csv")

    return


@task
def get_users_csv():
    pg = 1

    users_df = pd.DataFrame()
    while True:
        response = requests.request(
            "GET",
            f"{main_url}/Users/<USER>",
            headers=headers,
        )
        users = response.json()
        user_items = users["items"]
        users_items_df = pd.DataFrame(user_items)
        users_items_df.pop("accessCode")
        users_df = pd.concat([users_df, users_items_df], ignore_index=True)

        if not users["hasNextPage"]:
            break

        pg += 1
    users_df.to_csv("s3://vr-timestamp/powerbi/portal/users/users.csv", index=False)
    print("get_users_csv")


with ETDAG(
    dag_id="powerbi_harvester",
    schedule="0 11 * * *",  # every day at 11 am
    default_args=default_args,
    catchup=False,
    description="gets user and group access for powerbi",
    start_date=datetime(2024, 9, 3),
) as dag:

    get_reports = get_reports_csv()
    get_report_access = get_report_access_csv(get_reports)
    get_groups = get_groups_csv()
    get_user_group = get_user_group_csv(get_groups)
    get_users = get_users_csv()

get_reports >> get_report_access >> get_groups >> get_user_group >> get_users
