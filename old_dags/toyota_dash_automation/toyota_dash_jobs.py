from typing import TypedDict
from airflow.models import Variable

class S3Loc(TypedDict):
    s3_bucket: str  # vr-timestamp
    s3_key: str  # bi_sources/toyota_dashboard_datasets/auto_bucklocs.csv

class Job(TypedDict):
    trino_query: str  # "SELECT * FROM s3.gold_auto_intender.locations_new_and_used",
    s3_location: S3Loc
    chunk: bool  # False
    gzip_compress: bool # False

env_var = Variable.get("environment", "dev") #default value dev
if env_var == "prod":
    s3_key_prefix = "bi_sources/toyota_dashboard_datasets/"
else:
    s3_key_prefix = "bi_sources/toyota_dashboard_datasets/dev/"


jobs = {
    "traffic": Job(
        trino_query='SELECT * FROM "s3"."dev_prototyping"."toyota_traffic_hybrid";', 
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}toyota_traffic_hybrid.csv"),
        chunk=False,
        gzip_compress=False
    ),
    "locations": Job(
        trino_query='SELECT * FROM "s3"."dev_prototyping"."toyota_locations";',
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}toyota_locations_new.csv"),
        chunk=False,
        gzip_compress=False
    ),
    "observations": Job(
        trino_query='SELECT * FROM "s3"."dev_prototyping"."toyota_observations_hybrid";',
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}toyota_observations_hybrid.csv"),
        chunk=True,
        gzip_compress=True
    ),
    "stats": Job(
        trino_query='select * from s3.dev_prototyping.toyota_stats;',
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}toyota_stats.csv"),
        chunk=False,
        gzip_compress=False
    ),
}
