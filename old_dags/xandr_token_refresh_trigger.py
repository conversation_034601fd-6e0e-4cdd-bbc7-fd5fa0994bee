from etdag import ETDAG
from airflow.utils.dates import days_ago
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.operators.lambda_function import (
    LambdaInvokeFunctionOperator,
)
from datetime import timedelta


with ETDAG(
    dag_id="xandr_token_refresh",
    description="Trigger AppNexus token refresh Lambda function every 2 hours",
    schedule_interval="0 */2 * * *",  # Run every 2 hours
    start_date=days_ago(1),
    catchup=False,
    default_args={
        "retries": 3,  # Add retries for Lambda function calls
        "retry_delay": timedelta(minutes=2),  # Quick retry for token refresh
        "retry_exponential_backoff": True,  # Handle rate limiting
        "max_retry_delay": timedelta(minutes=10),  # Cap maximum delay
    },
) as dag:

    # Task to invoke Lambda function
    invoke_lambda = LambdaInvokeFunctionOperator(
        task_id="invoke_appnexus_token_regen",
        function_name="arn:aws:lambda:us-east-1:498598553520:function:appnexus-token-regen",
        aws_conn_id="aws_default",
        payload="{}",
    )