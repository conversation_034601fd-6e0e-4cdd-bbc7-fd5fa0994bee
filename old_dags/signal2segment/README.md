# Signal 2 Segment

## How it works
This DAG parses configuration files provided by the client to set up and process audience data. The configuration files specify the type of prospecting audience the client wants, the query arguments that will be passed to the quote lambda, the name of the audience, the organization ID (`org_id`), and the desired destination for the output.

### Workflow Overview:
1. **Parsing the Config File:** 
   - The DAG reads a configuration file from the source bucket: `vr-timestamp/prospecting/configs/{client name}/{config.json}`.
   - It extracts key information such as the `audience_type`, `query_args`, `audience_name_prefix`, `org_id`, and the destination specified in the `destinations` section of the config.
   
   We currently support four audience types:
   - `AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET`: For prospects in the automobile market.
   - `AUDIENCE_TYPE_PROSPECTS_BY_INTENT`: For prospects identified by their intent.
   - `AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS`: For prospects likely to sell their homes.
   - `AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET`: For prospects in the housing market.

  
2. **Creating the Audience:** 
   - Based on the `query_args` provided, the DAG creates an audience. An example of query arguments might look like this:
     ```json
     {
       "zipcodes": ["03257", "47150", "02653"],
       "topics": ["4eyes_103240", "4eyes_103239"],
       "interval": 30
     }
     ```
   - The audience will be named based on the provided `audience_name_prefix`, such as `"people_looking_for_harry_potter_wands"`.
   
3. **Storing for Records:**
   - Once the audience is created, the DAG stores the hashed audience file in an S3 bucket for our records. The location for this is: 
     `vr-timestamp/prospecting/hashed-files/{org_id}/{audience_type}/{file}`.

4. **Processing the Destination:**
   - After storing the audience, the DAG processes the destination specified in the `destinations` section of the config. Depending on the configuration, the audience may be deposited in one of the following:
     - Another S3 bucket
     - An SFTP server
     - The NextGen portal
     - The V2 portal
   - The DAG will follow the instructions provided in the destination configuration and deposit the audience accordingly.

This entire process happens either nightly (for automated runs) or manually when the DAG is triggered via the Airflow UI.

## How to run it
This DAG operates in two modes:

1. **Automated Nightly Run:** Every night, the DAG automatically pulls configurations from an S3 bucket. Based on these configurations, it creates new audiences and drops them into a designated location. This location could be an S3 bucket, an SFTP server, the NextGen portal, or the V2 portal. If configured, it will also remove the audience from the previous day.

2. **Manual Trigger:** You can manually trigger the DAG through the Airflow UI. To do this, go to the Airflow dashboard, start the DAG, and you will be prompted to provide a specific configuration. Once provided, the DAG will run using only the configuration you’ve entered—it will not search the S3 bucket for other configs.


## Example Configurations
A configuration file can contain a single config with multiple destinations or multiple configs in one file. 

When setting up the config:
- If the destination is an S3 bucket, you must provide the client’s S3 key and path. Required roles may also need to be set up.
- For an SFTP destination, you must provide the SFTP credentials, which need to be added to Airflow before the transfer can work. You also need to specify the desired SFTP path.
- If the destination is NextGen, you must provide a valid order line ID, and you can specify whether to remove the audience from the previous run.

### Single Config with Multiple Destinations
Below is an example of a configuration that specifies multiple destinations:

```json
{
  "org_id": "7GJnjokWuRgTPuDTW",           
  "audience_type": "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET",
  "query_args": "{\"dealer_zipcode\": [\"03257\"], \"seen_count\": 2}",
  "data_source": "DATA_SOURCE_ONSPOT",
  "audience_name_prefix": "auto_seen_2_times_s2s",
  "destinations": [
    {
      "type": "sftp",
      "sftp_id": "eltoro_sftp_id",
      "sftp_path": "/incoming/R&D/devtesting/",
      "interval": "daily"
    },
    {
      "type": "order_line",
      "portal": "nextgen",
      "interval": "daily",
      "conf": {
        "order_line_id": "crg6fiu6ji4c73faof7g",
        "is_exclude": false,
        "detach_previous_audience": true
      }
    }
  ]
}
```

### Mulitple Configs with Multiple Destinations
Below is an example of a configuration that specifies multiple configs:

```json
[
  {
    "org_id": "7GJnjokWuRgTPuDTW",
    "audience_type": "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET",
    "query_args": "{\"dealer_zipcode\": [\"03257\"], \"seen_count\": 2}",
    "data_source": "DATA_SOURCE_ONSPOT",
    "audience_name_prefix": "people_seen_on_lot_twice_s2s",
    "destinations": [
      {
        "type": "sftp",
        "sftp_id": "eltoro_sftp_id",
        "sftp_path": "/incoming/R&D/devtesting/",
        "interval": "daily"
      },
      {
        "type": "s3",
        "bucket_name": "vr-timestamp",
        "bucket_key": "prospecting/uploaded-test-files/",
        "interval": "daily"
      }
    ]
  },
  {
    "org_id": "7GJnjokWuRgTPuDTW",
    "audience_type": "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
    "query_args": "{\"zipcodes\": [\"03257\",\"47150\",\"02653\"], \"topics\": [\"4eyes_103240\",\"4eyes_103239\"], \"interval\": 30}",
    "data_source": "DATA_SOURCE_ONSPOT",
    "audience_name_prefix": "test_intent_with_s2s",
    "destinations": [
      {
        "type": "order_line",
        "portal": "nextgen",
        "interval": "daily",
        "conf": {
          "order_line_id": "crg6fiu6ji4c73faof7g",
          "is_exclude": false,
          "detach_previous_audience": false
        }
      },
      {
        "type": "order_line",
        "portal": "v2",
        "interval": "daily",
        "conf": {
          "order_line_id": "f6c7RTEdbFtXqDiZ21",
          "order_line_name": "Stews new OL",
          "is_exclude": false,
          "detach_previous_audience": false,
          "type": "l2l"
        }
      }
    ]
  }
]
```
