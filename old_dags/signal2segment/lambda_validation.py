from airflow.providers.amazon.aws.operators.lambda_function import (
    LambdaInvokeFunctionOperator,
)
from datetime import datetime
from airflow.providers.slack.operators.slack import SlackAPIPostOperator

from airflow.exceptions import AirflowFailException
import json
from airflow.decorators import task
from datetime import timedelta


################  this will make a call to the quoteprospect lambda to validate the query args
query_map = {
    "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET": "d6c4dd02-7e57-11ee-b962-0242ac120002",
    "AUDIENCE_TYPE_PROSPECTS_BY_INTENT": "479f18c0-5ebf-448f-b7df-d3d3da978e54",
    "AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS": "eff6f0ab-2149-4496-ad12-96d74bb7d672",
    "AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET": "0813e754-daf0-4cc9-858e-db22e2701feb",
    "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V2": "e5168f2e-2a96-4a80-8b32-5f18653cbae9",
    "AUDIENCE_TYPE_PROSPECTS_BY_AUTOMOBILE_INTENT": "4f2cf7be-4fc1-4992-8663-22ccf114e86d",
    "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V3": "8d88d0c4-df8b-418d-b0a3-2a88d6d7a2ad",
}


@task(
    max_active_tis_per_dag=5,
    retries=3,
    retry_delay=timedelta(seconds=60),
)
def query_validation(config):
    print(query_map[config["audience_type"]])
    bad_configs = {}
    good_configs = []
    lambda_response = LambdaInvokeFunctionOperator(
        task_id="invoke_lambda_task",
        function_name="arn:aws:lambda:us-east-1:498598553520:function:prod-quoteprospect",  ##### probably switch to prod
        invocation_type="RequestResponse",
        log_type="Tail",
        aws_conn_id="aws_default",
        region_name="us-east-1",
        payload=json.dumps(
            {
                "query_id": query_map[config["audience_type"]],
                "query_args": config["query_args"],
                "limit": 3,
            }
        ),
    ).execute(context={})
    lambda_response = json.loads(lambda_response)
    print(lambda_response)
    if "error" in lambda_response:
        bad_configs[config["org_id"]] = lambda_response["error"]
    else:
        good_configs.append(config)

    return {"good_configs": good_configs, "bad_configs": bad_configs}


@task(
    retries=3,
    retry_delay=timedelta(seconds=15),
)
def collect_good_configs(output):
    all_good_configs = []

    if output["good_configs"]:
        for config in output["good_configs"]:
            all_good_configs.append(config)
    return all_good_configs


##### if bad configs if will fail this task to notify of the bad config and send a slack message with the error
@task(
    retries=3,
    retry_delay=timedelta(seconds=15),
)
def handle_bad_configs(output):
    bad_configs = {}
    print(output)

    if output["bad_configs"]:
        bad_configs.update(output["bad_configs"])
    print(bad_configs)
    if bad_configs:

        slack_message = (
            "\n"
            "🚨 *Bad Configurations Detected* 🚨\n"
            "The following configurations failed validation:\n\n"
        )
        for org_id, error in bad_configs.items():
            slack_message += f"• Org ID: {org_id} - Error: {error}\n"

        slack_alert = SlackAPIPostOperator(
            task_id="slack_alert",
            username="Intent Config Validation",
            slack_conn_id="bi-report-slacker-token",
            text=slack_message,
            channel="C07PGKAHU80",
        )
        slack_alert.execute(context={})

        raise AirflowFailException("Bad configurations detected")
