{
    "campaignStartTime": "2024-09-04T00:00:00",   //do I need to handle this
    "campaignEndTime": "2024-10-01T23:59:00",     //do I need to handle this
    "rollingTimeFrame": 40,       //not sure how to handle this
    "orgID": "cCjowb5HerFhBJiF5",
    "jobID": "ndkRmAdFLbjWwvtu5",       // this is the campaign ID 
    "targetFile": {               ///////////////////////////////////// this should be all replaced by s3_url
      "targetFileBucket": "location-to-lead",
      "targetFileKey": "prod/orgs/cCjowb5HerFhBJiF5/ndkRmAdFLbjWwvtu5/targets/targets.csv",
      "wkbColumnIndex": 2,
      "groupColumnIndex": 1,
      "bucketLocationIdIndex": 0,
      "headers": true
    },
    "segments": [
      {
        "name": "Conquest-Truck",
        "orderLines": [
          {
            "orgID": "",
            "orderLineID": "rFMK5Dz5Ef95Ny2Mb",
            "isExclude": false
          },
          {
            "orgID": "",
            "orderLineID": "sPfbxw4jdpTrhNgiK",
            "isExclude": true
          }
        ],
        "type": "ANY_LOCATION",
        "attachBuckets": true,
        "greaterThanEqualTo": 2,    ////////// not sure what all this does
        "addressFilter": {
          "op": "AND",
          "s3Bucket": "eltoro-yf",
          "s3Key": "l2l_aoi_group_lists/garage/garage_bodytype_TRUCK.csv",
          "ethashIndex": 0,
          "headers": false
        }
      },
      {
        "name": "Conquest-Generic",   //////////////// OL name
        "orderLines": [
          {
            "orgID": "",
            "orderLineID": "sPfbxw4jdpTrhNgiK",
            "isExclude": false
          }
        ],
        "type": "ANY_LOCATION",
        "attachBuckets": true,
        "greaterThanEqualTo": 2
      },
      {
        "name": "GeoAlert-1",
        "orderLines": [
          {
            "orgID": "",
            "orderLineID": "K4HBx7TzWmJJ9iatX",
            "isExclude": false
          }
        ],
        "type": "ANY_LOCATION",
        "attachBuckets": true,
        "equalTo": 1,
        "addressFilter": {
          "op": "AND",
          "s3Bucket": "eltoro-yf",
          "s3Key": "l2l_aoi_group_lists/auto_alert/dms/5453",
          "ethashIndex": 0,
          "headers": false
        }
      },
      {
        "name": "GeoAlert-2",
        "orderLines": [
          {
            "orgID": "",
            "orderLineID": "LHFPYkbqkvCbLJznX",
            "isExclude": false
          }
        ],
        "type": "ANY_LOCATION",
        "attachBuckets": true,
        "greaterThanEqualTo": 2,
        "addressFilter": {
          "op": "AND",
          "s3Bucket": "eltoro-yf",
          "s3Key": "l2l_aoi_group_lists/auto_alert/dms/5453",
          "ethashIndex": 0,
          "headers": false
        }
      }
    ]
  }