from urllib.parse import urlparse
from airflow.decorators import task
from datetime import datetime, timedelta, date
from airflow.exceptions import AirflowFailException
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import json
from airflow.models import Variable
from airflow.providers.amazon.aws.hooks.step_function import StepFunctionHook
from pygene.audience_api import NextGenAudienceAPI, AudienceType
from pygene.campaign_api import NextGenCampaignAPI
import json
from airflow.models import Variable
from datetime import datetime, timedelta, date

env = Variable.get("environment")
pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
CLIENT_ID = pygene_creds["client_id"]
CLIENT_SECRET = pygene_creds["client_secret"]


@task(
    retries=3,
    retry_delay=timedelta(seconds=15),
)
def attach_audinces_to_order_lines(output, **kwargs):
    today = kwargs["data_interval_end"].date()

    campaign_api = NextGenCampaignAPI(
        client_id=CLIENT_ID,
        client_secret=CLIENT_SECRET,
        org_id=output["config"]["org_id"],
        env=env,
    )

    def get_old_audience_ids(ol_id):
        order_line = campaign_api.get_order_line(ol_id)
        old_audiences = [audience.id for audience in order_line.audiences]
        if old_audiences:
            print(f"Found old audiences for orderline {ol_id}: {old_audiences}")
            return old_audiences
        else:
            print(f"No audiences found for orderline {ol_id}")
            return []

    for dest in output["config"]["destinations"]:
        ol_id = dest["order_line_id"]
        ol_name = output["config"]["audience_name_prefix"]
        exclude = dest["is_exclude"]
        audience_id = output["audience_ids"]

        old_audience_ids = get_old_audience_ids(ol_id)

        if not old_audience_ids:
            print(f"No old audiences found for orderline {ol_id}.")

        if audience_id in old_audience_ids:
            print(
                f"Audience {audience_id} is already attached to orderline {ol_id}. Skipping."
            )
            continue

        print(f"Attempting to attach audience {audience_id} to orderline {ol_id}.")
        try:
            campaign_api.batch_add_audiences(ol_id, [audience_id], exclude)
            print(f"Successfully attached audience {audience_id} to orderline {ol_id}.")
        except Exception as e:
            if "504" in str(e):
                print(
                    f"504 Gateway Timeout when attaching audience {audience_id} to orderline {ol_id}"
                )
                raise AirflowFailException(
                    f"504 Gateway Timeout when attaching audience {audience_id} to orderline {ol_id}"
                )
            elif "500" in str(e):
                print(
                    f"500 Internal Server Error when attaching audience {audience_id}. Retrying..."
                )
                raise AirflowFailException(
                    f"500 Internal Server Error when attaching audience {audience_id}."
                )
            else:
                print(f"Unexpected error: {e}. Skipping this audience.")
                continue

        if dest["detach_previous_audience"]:
            print(
                f"Detaching previous audiences: {old_audience_ids} from orderline {ol_id}"
            )
            if old_audience_ids:
                campaign_api.batch_remove_audiences(ol_id, old_audience_ids)
                print(f"Detached {old_audience_ids} from orderline {ol_id}")
            else:
                print(f"The ol does not have an audience with the name {ol_name}")
