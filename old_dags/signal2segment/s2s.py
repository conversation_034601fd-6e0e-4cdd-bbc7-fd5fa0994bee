from airflow.utils.task_group import TaskGroup
from datetime import datetime, timedelta
from airflow.decorators import task, task_group
from etdag import ETDAG
from datetime import datetime
from airflow.models.param import Param
from airflow.models import Variable
from airflow.exceptions import AirflowSkipException, AirflowException
from old_dags.signal2segment.destinations import attach_audinces_to_order_lines
from old_dags.signal2segment.lambda_validation import *

"""
    DAG: `signal_2_segment`
    
    Schedule: 2am EDT daily
    
    Owner:  Bryan Price
    
    ## Summary
    Looks for active s2s configs in s3 and will parse them to create
    a new prospecting audience that will transfered to a desired location
    it will run every night or can be triggered manually with a config input
    
    ## Time Dependent
    should be run once daily
    
    ## Failures
    In the event of a failure this Dag can be rerun manually by clearing the task.
    
    ## Escalation
    If rerunning the Dag <NAME_EMAIL>.
    
    ## Dependencies
    atleast pygene 1.0.34.
    
    ## Results
    * A new prospecting audience created and delivered to desired location
    

"""


default_args = {"owner": "bp"}


#### Will take in an adhoc config request through the api, if no configs present, it will run on a cron that pulls configs from s3
@task
def get_configs(**context):
    config = context["dag_run"].conf.get("config", {})

    print(f"the configs {config}")
    return config


#### WIll create prospecting audiences and store them in an s3 bucket to de delivered to the proper destination
@task.virtualenv(
    max_active_tis_per_dag=10,
    requirements="requirements.txt",
    system_site_packages=True,
    retries=3,
    retry_delay=timedelta(seconds=60),
    pip_install_options=["--upgrade"],
)
def run_prospect_task(config, logical_date):
    from pygene.audience_api import (
        NextGenAudienceAPI,
        Audience,
        AudienceType,
        Automation,
        AutomationIntervalUnit,
        AutomationType,
    )
    from pygene.target_api import NextGenTargetAPI
    from airflow.models import Variable
    from airflow.exceptions import AirflowFailException
    import json

    env = Variable.get("environment")
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    config = config[0]
    today = logical_date.date()

    def __parse_audience_type(audience_type):
        ad_type = audience_type.split("_")[2:]

        parsed = "_".join(ad_type)
        return parsed

    target_api = NextGenTargetAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=config["org_id"],
        env=env,
    )
    target = target_api.upload_fileless_targets()
    print(f"target id is {target.id}")

    audience_api = NextGenAudienceAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=config["org_id"],
        env=env,
    )

    print("Generating audience")
    aud_type_str = __parse_audience_type(config["audience_type"])
    aud_type = AudienceType[aud_type_str]

    audience = Audience(
        {
            "target_id": target.id,
            "type": aud_type,
            "name": config["audience_name_prefix"],
            "org_id": config["org_id"],
            "data_source": config["data_source"],
            "audiences": [
                Audience(
                    {
                        "type": AudienceType.ADDRESS,
                        "name": f"{config['audience_name_prefix']}-{today}",
                        "data_source": config["data_source"],
                    },
                )
            ],
            "automations": [
                Automation(
                    {
                        "enabled": True,
                        "interval_value": config["automation"]["interval_value"],
                        "interval_unit": AutomationIntervalUnit[
                            config["automation"]["interval_unit"]
                        ],
                        "type": AutomationType[config["automation"]["type"]],
                    },
                )
            ],
        }
    )
    try:
        aud_id = audience_api.create_any_audience(
            poll=True, generate=True, audience=audience, query_args=config["query_args"]
        )

        for s in aud_id.sub_audiences:
            print(f"sub audience id is {s['id']}")
            sub_audience_id = s["id"]

            return {"audience_ids": sub_audience_id, "config": config}

    except Exception as e:
        error_message = f"Error generating audience: {str(e)}"
        print(error_message)
        raise AirflowFailException(error_message)


#### Process where files/audiences need to be delivered


with ETDAG(
    dag_id="signal_2_segment",
    schedule=None,
    default_args=default_args,
    catchup=False,
    description="l21",
    start_date=datetime(2024, 8, 5),
    et_failure_msg=False,
    params={
        "config": Param(
            default=[],
            type="array",
            description="List of configurations to process",
            schema={
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "org_id": {"type": "string"},
                        "audience_type": {"type": "string"},
                        "query_args": {"type": "string"},
                        "data_source": {"type": "string"},
                        "audience_name_prefix": {"type": "string"},
                        "destinations": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "order_line_id": {"type": "string"},
                                    "is_exclude": {"type": "boolean"},
                                    "detach_previous_audience": {"type": "boolean"},
                                },
                                "required": ["order_line_id"],
                            },
                        },
                    },
                    "required": [
                        "org_id",
                        "audience_type",
                        "query_args",
                        "data_source",
                        "audience_name_prefix",
                        "destinations",
                    ],
                },
            },
        ),
    },
) as dag:
    configs = get_configs()

    @task_group("processing_tasks")
    def processing_tasks(configs):
        list_of_configs = query_validation(config=configs)
        all_good_configs = collect_good_configs(output=list_of_configs)
        handle_bad_configs(output=list_of_configs)

        config = run_prospect_task(config=all_good_configs)
        attach_audinces_to_order_lines(output=config)

    processing_tasks.expand(configs=configs)
