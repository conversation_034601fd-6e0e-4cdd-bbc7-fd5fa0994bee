import airflow
import airflow.hooks.S3_hook
import requests
from airflow.exceptions import AirflowException
from airflow.models import Variable
from etdag import ETDAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.providers.amazon.aws.operators.s3 import (
    S3DeleteObjectsOperator,
)
from datetime import datetime, timedelta
from os import path
from zipfile import ZipFile
import tarfile
from old_dags.slack_alert import task_slack_alert
import os


dag_directory = os.path.dirname(os.path.abspath(__file__))
readme_file = os.path.join(dag_directory, "README.md")
with open(readme_file, "r") as f:
    readme_content = f.read()


default_args = {
    "owner": "Tim H",
    "depends_on_past": False,
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 3,  # Add retries for external API calls
    "retry_delay": timedelta(minutes=5),  # Wait before retrying downloads
    "retry_exponential_backoff": True,  # Handle rate limiting
    "max_retry_delay": timedelta(minutes=20),  # Cap maximum delay
}


now = datetime.now()
month_ago = now - timedelta(days=29)
bucket = Variable.get("maxmind_bucket", default_var="eltoro-data-sources")
key = "maxmind/" + now.strftime("%Y%m%d")
delete_prefix = "maxmind/" + month_ago.strftime("%Y%m%d") + "/"
mm_secret = Variable.get("mm_secret")


def get_file(**kwargs):
    file_path = kwargs["file_path"]
    try:
        filedata = requests.get(
            f"https://download.maxmind.com/geoip/databases/{kwargs['edition_id']}/download",
            params={
                # "edition_id": kwargs["edition_id"],
                # "license_key": kwargs["license_key"],
                "suffix": kwargs["file_suffix"],
            },
            auth=("92735", kwargs["license_key"]),
        )
    except Exception as e:
        print(str(e))
    file_type = kwargs["file_type"]
    res_name = kwargs["res_name"]
    res_suffix = kwargs["file_suffix"]
    file_dir = kwargs["file_dir"]
    with open(file_path, "wb") as f:
        for chunk in filedata.iter_content(chunk_size=128):
            f.write(chunk)
    files = unzip_file(res_name, file_type, res_suffix, file_dir)
    upload_files(files)


def unzip_tar(file_path, file_type):
    file_names = []
    tar = tarfile.open(file_path, mode="r:gz")
    for tinfo in tar.getmembers():
        if file_type in tinfo.name:
            name = tinfo.name
            tar.extract(tinfo, path=file_dir)
            if path.exists(file_dir + name):
                file_names.append(name)
            else:
                raise AirflowException("Error unarchiving file " + file_path)
    return file_names


def unzip_file(res_name, file_type, res_suffix, file_dir):
    file_path = file_dir + res_name + "." + res_suffix
    file_names = []
    if res_suffix == "tar.gz":
        file_names = unzip_tar(file_path, file_type)
    else:
        zip = ZipFile(file_path)
        for zinfo in zip.infolist():
            if file_type in zinfo.filename:
                name = zinfo.filename
                zip.extract(zinfo, path=file_dir)
                if path.exists(file_dir + name):
                    file_names.append(name)
                else:
                    raise AirflowException("Error unarchiving file " + file_path)
    return file_names


def upload_files(files):
    hook = airflow.providers.amazon.aws.hooks.s3.S3Hook("s3_conn")
    for file in files:
        name = file.split("/")[1]
        file_path = file_dir + file
        file_key = key + "/" + name
        if path.exists(file_path):
            hook.load_file(file_path, file_key, bucket, replace=True)
        else:
            raise AirflowException("File not found " + file_path)


with ETDAG(
    dag_id="maxmind",
    description="Makes an API call to Maxmind, decompresses and moves data to S3",
    on_failure_callback=task_slack_alert,
    default_args=default_args,
    schedule_interval="@daily",
    start_date=airflow.utils.dates.days_ago(2),
    dagrun_timeout=timedelta(minutes=30),
    tags=["maxmind"],
) as dag:

    resources = [
        ("GeoIP2-City", "GEOIP2-City", "tar.gz", "mmdb"),
        ("GeoIP2-City-CSV", "GEOIP2-City-CSV", "zip", "csv"),
        ("GeoIP2-Country", "GEOIP2-Country", "tar.gz", "mmdb"),
        ("GeoIP2-Country-CSV", "GEOIP2-Country-CSV", "zip", "csv"),
        ("GeoIP2-Connection-Type-CSV", "GEOIP2-Connection-Type-CSV", "zip", "csv"),
        ("GeoIP2-Connection-Type", "GEOIP2-Connection-Type", "tar.gz", "mmdb"),
        ("GeoLite2-ASN-CSV", "GeoLite2-ASN-CSV", "zip", "csv"),
        ("GeoIP2-ISP", "GeoIP2-ISP", "tar.gz", "mmdb"),
        ("GeoIP2-ISP-CSV", "GeoIP2-ISP-CSVP", "zip", "csv"),
    ]

    dag.doc_md = readme_content

    delete_task = S3DeleteObjectsOperator(
        task_id="delete_30_days_ago",
        bucket=bucket,
        prefix=delete_prefix,
        aws_conn_id="s3_conn",
    )

    download_tasks = DummyOperator(task_id="maxmind_start", retries=2)
    delete_task >> download_tasks

    for res in resources:
        file_dir = "/tmp/"
        res_name = res[1]
        res_suffix = res[2]
        file_type = res[3]
        get_task = PythonOperator(
            task_id="get-" + res[0],
            provide_context=True,
            python_callable=get_file,
            op_kwargs={
                "edition_id": res[0],
                "license_key": mm_secret,
                "file_suffix": res_suffix,
                "file_path": file_dir + res_name + "." + res_suffix,
                "res_name": res_name,
                "file_dir": file_dir,
                "file_type": file_type,
            },
        )

        download_tasks >> get_task
