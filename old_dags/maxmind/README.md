# Airflow Process
DAG Name: maxmind <br>
Schedule Interval: Run Daily <br>
owner: <PERSON>.

# Background Info:
The maxmind dag imports geo data files from the [MaxMind API](https://dev.maxmind.com/) to the S3 bucket eltoro-moria/maxmind. See the [documentation](https://eltorocorp.atlassian.net/wiki/spaces/BD/pages/329908281/Maxmind+db+info) for more info.


# Failure Scenario:
Failure is unexpected and will be troublehsoot based on Data Service member that is on call.

# Source Data:
        f"https://download.maxmind.com/geoip/databases/{kwargs['edition_id']}/download",


        ("GeoIP2-City", "GEOIP2-City", "tar.gz", "mmdb"),
        ("GeoIP2-City-CSV", "GEOIP2-City-CSV", "zip", "csv"),
        ("GeoIP2-Country", "GEOIP2-Country", "tar.gz", "mmdb"),
        ("GeoIP2-Country-CSV", "GEOIP2-Country-CSV", "zip", "csv"),
        ("GeoIP2-Connection-Type-CSV", "GEOIP2-Connection-Type-CSV", "zip", "csv"),
        ("GeoIP2-Connection-Type", "GEOIP2-Connection-Type", "tar.gz", "mmdb"),
        ("GeoLite2-ASN-CSV", "GeoLite2-ASN-CSV", "zip", "csv"),

# Final Destination:
s3://eltoro-data-sources/maxmind/

# Esclation Path:
Contact Data Service regarding to any issue with delivering the files <br>
Contact Rorie for access/Credential 
