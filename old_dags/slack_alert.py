from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator


def task_slack_alert(context):
    log_url = f"""<https://kibana.diagonalley.eltoro.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-24h,to:now))&_a=(columns:!(log),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'313898a0-df81-11eb-8407-e763b6f094d2',key:kubernetes.annotations.dag_id,negate:!f,params:(query:{context.get('task_instance').dag_id}),type:phrase),query:(match_phrase:(kubernetes.annotations.dag_id:{context.get('task_instance').dag_id}))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'313898a0-df81-11eb-8407-e763b6f094d2',key:kubernetes.namespace_name,negate:!f,params:(query:airflow),type:phrase),query:(match_phrase:(kubernetes.namespace_name:airflow))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'313898a0-df81-11eb-8407-e763b6f094d2',key:kubernetes.pod_name,negate:!f,params:(query:jkrealestateimporter.a6cf9ff63433467585d2ac5b9a3e3aa3),type:phrase),query:(match_phrase:(kubernetes.pod_name:jkrealestateimporter.a6cf9ff63433467585d2ac5b9a3e3aa3)))),index:'313898a0-df81-11eb-8407-e763b6f094d2',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))|Log URL>"""
    slack_msg = f"""
            :red_circle: Airflow Error: {context.get('exception') or context.get('reason')}
            *Task*: {context.get('task_instance').task_id}
            *DAG*: {context.get('task_instance').dag_id}
            *Execution Time*: {context.get('execution_date')}
            *Dag URL*: https://airflow.diagonalley.eltoro.com/tree?dag_id={context.get('task_instance').dag_id}
            *Log Url*: {log_url}
            """
    failed_alert = SlackWebhookOperator(
        task_id="slack_failed_alert",
        slack_webhook_conn_id="slack",
        message=slack_msg,
        channel="#dev-bigdata-notifications",
        username="webhookbot",
        icon_emoji=":bigdata-airflow:",
    )
    return failed_alert.execute(context)
