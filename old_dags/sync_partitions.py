from airflow.decorators import task
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from datetime import timedelta
import logging

logging.basicConfig()
logger = logging.getLogger(__name__)


default_args = {
    "owner": "<PERSON> Morton",
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}
with ETDAG(
    dag_id="sync_partitions_processor",
    description="Call sync_partition_metadata on various tables in Starburst",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 */6 * * *",
    catchup=False,
    tags=["ts_reports", "sync_partitions"],
) as dag:
    # Add any schemas you want to run sync_partitions on for all tables here in the format of:
    # (catalog, schema)
    schemas = [
        ("s3", "bronze_ts_reports"),
        ("s3", "bronze_eltoro_audiences"),
        ("s3", "bronze_real_estate"),
    ]

    @task
    def sync_tables(catalog, schema, table_names):
        processed_tables = []
        for tn in table_names:
            query = f"CALL {catalog}.system.sync_partition_metadata('{schema}', '{tn[0]}', 'ADD')",

            logger.debug("______________________________")
            logger.debug(query)
            logger.debug("______________________________")

            try:
                SQLExecuteQueryOperator(
                    task_id=f"update_{schema}_partitions",
                    conn_id="starburst",
                    sql=query,
                ).execute(dict({}))
            except Exception as e:
                dag.log.error(e)

    for catalog, schema in schemas:
        table_names = SQLExecuteQueryOperator(
            task_id=f"query_{schema}_tables",
            conn_id="starburst",
            sql=f"SHOW TABLES FROM {catalog}.{schema}",
            handler=list,
        )

        processed_tables = sync_tables(catalog, schema, table_names.output)


        table_names >> processed_tables

if __name__ == "__main__":

    logger.setLevel(logging.DEBUG)
    run = dag.test(
        conn_file_path="new_mover/connections.json",
        variable_file_path="new_mover/variables.json",
        run_conf={
            "start_date": None,
        },
        # mark_success_pattern="wait_for_.*|send_email",
    )
