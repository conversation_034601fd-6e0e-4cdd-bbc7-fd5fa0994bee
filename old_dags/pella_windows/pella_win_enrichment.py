from airflow.decorators import task
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from etdag import ETDAG
import pandas as pd
from datetime import datetime, timedelta



default_args = {
    'owner': '<PERSON><PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': datetime(2025, 5, 6),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}


def get_dataset_from_trino() -> pd.DataFrame:
    query = "select * from s3.dev_prototyping.pella_windows_intent"
    tr = TrinoHook(trino_conn_id="starburst")
    results_df = tr.get_pandas_df(sql=query, dtype=str)
    return results_df


with ETDAG(
    dag_id="pellawindows_enrichment",
    description="Provides enrichment weekly, queried from starburst, delivered to sftp",
    start_date=datetime(2025,5,6),
    default_args=default_args,
    schedule_interval="0 6 * * 1",
    catchup=False,
    tags=["application:PellaWindows", "team:DND"],
) as dag:
    BUCKET_NAME = "vr-timestamp"
    today = datetime.today() - timedelta(days=0)
    date_st = today.date().strftime("%Y%m%d")
    date_st_hyphen = today.date().strftime("%Y-%m-%d")
    filename = f"PellaWindows_Intent_Results_{date_st}.csv"
    S3_PATH = f"bi_sources/realestate/pella/enrichment/transfer_date={date_st_hyphen}/{filename}"

    @task
    def get_enrichment():
        df = get_dataset_from_trino()
        df.to_csv(f"s3://{BUCKET_NAME}/{S3_PATH}", index=False)

    deliver_dataset = get_enrichment()

    s3_to_sftp_cl = S3ToSFTPOperator(
        task_id="s3_to_sftp_cl",
        s3_bucket=BUCKET_NAME,
        s3_key=S3_PATH,
        sftp_path=f"/incoming/PellaWindows/{filename}",
        sftp_conn_id="pella_sftp",
        aws_conn_id="s3_conn",
    )

    deliver_dataset >> s3_to_sftp_cl
