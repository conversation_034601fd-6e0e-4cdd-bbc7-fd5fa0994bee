CREATE TABLE IF NOT EXISTS s3.bronze_et_xandr.multi_buyer_seller_deal_metrics (
   hour varchar,
   buyer_member_name varchar,
   buyer_seat_name varchar,
   buyer_seat_code varchar,
   deal_id varchar,
   deal_name varchar,
   deal_buyer_type varchar,
   package_id varchar,
   start_date varchar,
   imps_matched varchar,
   bid_requests varchar,
   imps_won varchar,
   seller_revenue varchar,
   average_net_bid varchar,
   average_biased_bid varchar,
   average_floor_price varchar,
   bid_rate varchar,
   ineligible_bid_rate varchar,
   gross_win_rate varchar,
   net_win_rate varchar,
   ask_price varchar,
   reject_count varchar,
   final_bids varchar,
   day date
)
WITH (
   external_location = 's3://et-datalake-campaign-reporting-prod/external/xandr/multi_buyer_seller_deal_metrics',
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)