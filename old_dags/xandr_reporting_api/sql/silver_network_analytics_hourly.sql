CREATE TABLE IF NOT EXISTS s3.silver_et_xandr.network_analytics_hourly (
   hour varchar,
   month date,
   line_item_id varchar,
   line_item_name varchar,
   line_item_code varchar,
   campaign_id varchar,
   campaign_code varchar,
   campaign_name varchar,
   advertiser_id varchar,
   advertiser_code varchar,
   advertiser_name varchar,
   "line_item.booked_impressions_budget_daily" integer,
   size varchar,
   imps integer,
   imp_requests integer,
   imps_blank integer,
   imps_psa integer,
   imps_psa_error integer,
   imps_default_error integer,
   imps_default_bidder integer,
   imps_kept integer,
   imps_resold integer,
   imps_rtb integer,
   clicks integer,
   click_thru_pct varchar,
   external_click integer,
   cost double,
   revenue double,
   booked_revenue double,
   booked_revenue_adv_curr double,
   reseller_revenue double,
   profit double,
   commissions double,
   cpm double,
   post_click_convs integer,
   post_click_revenue double,
   post_view_convs integer,
   post_view_revenue double,
   total_convs integer,
   convs_per_mm double,
   convs_rate double,
   ctr double,
   rpm double,
   total_network_rpm double,
   total_publisher_rpm double,
   sold_network_rpm double,
   sold_publisher_rpm double,
   media_cost_pub_curr double,
   serving_fees double,
   view_measured_imps integer,
   imps_viewed integer,
   view_rate double,
   view_measurement_rate double,
   cpvm double,
   revenue_selling_currency double,
   booked_revenue_buying_currency double,
   booked_revenue_selling_currency double,
   reseller_revenue_buying_currency double,
   reseller_revenue_selling_currency double,
   cost_buying_currency double,
   cost_selling_currency double,
   profit_buying_currency double,
   profit_selling_currency double,
   total_network_rpm_buying_currency double,
   total_network_rpm_selling_currency double,
   cpm_buying_currency double,
   cpm_selling_currency double,
   rpm_buying_currency double,
   rpm_selling_currency double,
   sold_network_rpm_buying_currency double,
   sold_network_rpm_selling_currency double,
   serving_fees_buying_currency double,
   serving_fees_selling_currency double,
   data_costs_buying_currency double,
   data_costs_selling_currency double,
   insertion_order_id varchar,
   insertion_order_name varchar,
   "insertion_order.type" varchar,
   creative_id varchar,
   creative_name varchar,
   creative_code varchar,
   total_cost_ecpm double,
   pixel_id varchar,
   pixel_name varchar,
   "flight.start_date" date,
   deal_name varchar,
   deal_id varchar,
   video_skips integer,
   video_starts integer,
   video_25_pcts integer,
   video_50_pcts integer,
   video_75_pcts integer,
   video_completions integer,
   video_served integer,
   video_errors integer,
   video_completion_rate double,
   booked_revenue_ecpm double,
   split_id varchar,
   split_name varchar,
   day date
)
WITH (
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)