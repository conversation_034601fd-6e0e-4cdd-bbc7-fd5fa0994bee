CREATE TABLE IF NOT EXISTS s3.bronze_et_xandr.network_site_domain_performance (
   line_item_id varchar,
   site_domain varchar,
   deal_id varchar,
   top_level_category_name varchar,
   mobile_application_name varchar,
   mobile_application_id varchar,
   imps varchar,
   clicks varchar,
   day date
)
WITH (
   external_location = 's3://et-datalake-campaign-reporting-prod/external/xandr/network_site_domain_performance',
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)