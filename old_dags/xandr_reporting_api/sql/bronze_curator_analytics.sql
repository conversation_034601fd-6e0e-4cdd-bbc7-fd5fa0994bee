CREATE TABLE IF NOT EXISTS s3.bronze_et_xandr.curator_analytics (
   hour varchar,
   curated_deal_advertiser_id varchar,
   curated_deal_advertiser_name varchar,
   curated_deal_id varchar,
   curated_deal_insertion_order_id varchar,
   curated_deal_line_item_id varchar,
   curated_deal_insertion_order_name varchar,
   curated_deal_line_item_name varchar,
   curated_deal_name varchar,
   curator_member_id varchar,
   curator_member_name varchar,
   media_type varchar,
   size varchar,
   curator_margin varchar,
   curator_net_media_cost varchar,
   curator_revenue varchar,
   curator_tech_fees varchar,
   curator_total_cost varchar,
   imps varchar,
   viewdef_viewed_imps varchar,
   viewdef_view_rate varchar,
   viewed_imps varchar,
   day date
)
WITH (
   external_location = 's3://et-datalake-campaign-reporting-prod/external/xandr/curator_analytics',
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)