CREATE TABLE IF NOT EXISTS s3.bronze_et_xandr.network_analytics_hourly (
    "hour" varchar,
    "month" varchar,
    "line_item_id" varchar,
    "line_item_name" varchar,
    "line_item_code" varchar,
    "campaign_id" varchar,
    "campaign_code" varchar,
    "campaign_name" varchar,
    "advertiser_id" varchar,
    "advertiser_code" varchar,
    "advertiser_name" varchar,
    "line_item.booked_impressions_budget_daily" varchar,
    "size" varchar,
    "imps" varchar,
    "imp_requests" varchar,
    "imps_blank" varchar,
    "imps_psa" varchar,
    "imps_psa_error" varchar,
    "imps_default_error" varchar,
    "imps_default_bidder" varchar,
    "imps_kept" varchar,
    "imps_resold" varchar,
    "imps_rtb" varchar,
    "clicks" varchar,
    "click_thru_pct" varchar,
    "external_click" varchar,
    "cost" varchar,
    "revenue" varchar,
    "booked_revenue" varchar,
    "booked_revenue_adv_curr" varchar,
    "reseller_revenue" varchar,
    "profit" varchar,
    "commissions" varchar,
    "cpm" varchar,
    "post_click_convs" varchar,
    "post_click_revenue" varchar,
    "post_view_convs" varchar,
    "post_view_revenue" varchar,
    "total_convs" varchar,
    "convs_per_mm" varchar,
    "convs_rate" varchar,
    "ctr" varchar,
    "rpm" varchar,
    "total_network_rpm" varchar,
    "total_publisher_rpm" varchar,
    "sold_network_rpm" varchar,
    "sold_publisher_rpm" varchar,
    "media_cost_pub_curr" varchar,
    "serving_fees" varchar,
    "view_measured_imps" varchar,
    "imps_viewed" varchar,
    "view_rate" varchar,
    "view_measurement_rate" varchar,
    "cpvm" varchar,
    "revenue_selling_currency" varchar,
    "booked_revenue_buying_currency" varchar,
    "booked_revenue_selling_currency" varchar,
    "reseller_revenue_buying_currency" varchar,
    "reseller_revenue_selling_currency" varchar,
    "cost_buying_currency" varchar,
    "cost_selling_currency" varchar,
    "profit_buying_currency" varchar,
    "profit_selling_currency" varchar,
    "total_network_rpm_buying_currency" varchar,
    "total_network_rpm_selling_currency" varchar,
    "cpm_buying_currency" varchar,
    "cpm_selling_currency" varchar,
    "rpm_buying_currency" varchar,
    "rpm_selling_currency" varchar,
    "sold_network_rpm_buying_currency" varchar,
    "sold_network_rpm_selling_currency" varchar,
    "serving_fees_buying_currency" varchar,
    "serving_fees_selling_currency" varchar,
    "data_costs_buying_currency" varchar,
    "data_costs_selling_currency" varchar,
    "insertion_order_id" varchar,
    "insertion_order_name" varchar,
    "insertion_order.type" varchar,
    "creative_id" varchar,
    "creative_name" varchar,
    "creative_code" varchar,
    "total_cost_ecpm" varchar,
    "pixel_id" varchar,
    "pixel_name" varchar,
    "flight.start_date" varchar,
    "deal_name" varchar,
    "deal_id" varchar,
    "video_skips" varchar,
    "video_starts" varchar,
    "video_25_pcts" varchar,
    "video_50_pcts" varchar,
    "video_75_pcts" varchar,
    "video_completions" varchar,
    "video_served" varchar,
    "video_errors" varchar,
    "video_completion_rate" varchar,
    "booked_revenue_ecpm" varchar,
    "split_id" varchar,
    "split_name" varchar,
    day date
)
WITH (
   external_location = 's3://et-datalake-campaign-reporting-prod/external/xandr/network_analytics_hourly',
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)