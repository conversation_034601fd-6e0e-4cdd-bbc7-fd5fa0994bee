CREATE TABLE IF NOT EXISTS s3.bronze_et_xandr.video_analytics (
    advertiser_name varchar,
    advertiser_code varchar,
    insertion_order_name varchar,
    insertion_order_code varchar,
    line_item_name varchar,
    line_item_code varchar,
    line_item_id varchar,
    creative_id varchar,
    creative_name varchar,
    creative_code varchar,
    "flight.start_date" varchar,
    "flight.end_date" varchar,
    seller_member_name varchar,
    seller_member_id varchar,
    publisher_name varchar,
    publisher_id varchar,
    deal_name varchar,
    deal_id varchar,
    imps varchar,
    clicks varchar,
    view_rate varchar,
    view_measurement_rate varchar,
    video_skips varchar,
    video_starts varchar,
    video_25_pcts varchar,
    video_50_pcts varchar,
    video_75_pcts varchar,
    video_completions varchar,
    video_errors varchar,
    booked_revenue_ecpm varchar,
    day date
)
WITH (
   external_location = 's3://et-datalake-campaign-reporting-prod/external/xandr/video_analytics',
   format = 'PARQUET',
   partitioned_by = ARRAY['day']
)