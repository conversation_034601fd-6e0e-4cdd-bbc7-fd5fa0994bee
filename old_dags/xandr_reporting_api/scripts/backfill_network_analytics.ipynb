{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-02-15T19:49:26.033857055Z", "start_time": "2024-02-15T19:49:25.965473113Z"}, "collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "video_path = \"s3a://eltoro-apnx-reporting/video_analytics/na_yesterday\"\n", "na_path = \"s3a://eltoro-apnx-reporting/na_yesterday\"\n", "\n", "new_na_path = \"s3://et-datalake-campaign-reporting-dev/external/xandr/network_analytics_hourly\""]}, {"cell_type": "code", "execution_count": 2, "id": "a8c5b7ed06acc5cd", "metadata": {"ExecuteTime": {"end_time": "2024-02-15T19:49:26.034141373Z", "start_time": "2024-02-15T19:49:26.007910688Z"}, "collapsed": false}, "outputs": [], "source": ["columns = [\n", "    \"hour\",\n", "    \"month\",\n", "    \"day\",\n", "    \"line_item_id\",\n", "    \"line_item_name\",\n", "    \"line_item_code\",\n", "    \"campaign_id\",\n", "    \"campaign_code\",\n", "    \"campaign_name\",\n", "    \"advertiser_id\",\n", "    \"advertiser_code\",\n", "    \"advertiser_name\",\n", "    \"line_item.booked_impressions_budget_daily\",\n", "    \"size\",\n", "    \"imps\",\n", "    \"imp_requests\",\n", "    \"imps_blank\",\n", "    \"imps_psa\",\n", "    \"imps_psa_error\",\n", "    \"imps_default_error\",\n", "    \"imps_default_bidder\",\n", "    \"imps_kept\",\n", "    \"imps_resold\",\n", "    \"imps_rtb\",\n", "    \"clicks\",\n", "    \"click_thru_pct\",\n", "    \"external_click\",\n", "    \"cost\",\n", "    \"revenue\",\n", "    \"booked_revenue\",\n", "    \"booked_revenue_adv_curr\",\n", "    \"reseller_revenue\",\n", "    \"profit\",\n", "    \"commissions\",\n", "    \"cpm\",\n", "    \"post_click_convs\",\n", "    \"post_click_revenue\",\n", "    \"post_view_convs\",\n", "    \"post_view_revenue\",\n", "    \"total_convs\",\n", "    \"convs_per_mm\",\n", "    \"convs_rate\",\n", "    \"ctr\",\n", "    \"rpm\",\n", "    \"total_network_rpm\",\n", "    \"total_publisher_rpm\",\n", "    \"sold_network_rpm\",\n", "    \"sold_publisher_rpm\",\n", "    \"media_cost_pub_curr\",\n", "    \"serving_fees\",\n", "    \"view_measured_imps\",\n", "    \"imps_viewed\",\n", "    \"view_rate\",\n", "    \"view_measurement_rate\",\n", "    \"cpvm\",\n", "    \"revenue_selling_currency\",\n", "    \"booked_revenue_buying_currency\",\n", "    \"booked_revenue_selling_currency\",\n", "    \"reseller_revenue_buying_currency\",\n", "    \"reseller_revenue_selling_currency\",\n", "    \"cost_buying_currency\",\n", "    \"cost_selling_currency\",\n", "    \"profit_buying_currency\",\n", "    \"profit_selling_currency\",\n", "    \"total_network_rpm_buying_currency\",\n", "    \"total_network_rpm_selling_currency\",\n", "    \"cpm_buying_currency\",\n", "    \"cpm_selling_currency\",\n", "    \"rpm_buying_currency\",\n", "    \"rpm_selling_currency\",\n", "    \"sold_network_rpm_buying_currency\",\n", "    \"sold_network_rpm_selling_currency\",\n", "    \"serving_fees_buying_currency\",\n", "    \"serving_fees_selling_currency\",\n", "    \"data_costs_buying_currency\",\n", "    \"data_costs_selling_currency\",\n", "    \"insertion_order_id\",\n", "    \"insertion_order_name\",\n", "    \"insertion_order.type\",\n", "    \"creative_id\",\n", "    \"creative_name\",\n", "    \"creative_code\",\n", "    \"total_cost_ecpm\",\n", "    \"pixel_id\",\n", "    \"pixel_name\",\n", "    \"flight.start_date\",\n", "    \"deal_name\",\n", "    \"deal_id\",\n", "    \"video_skips\",\n", "    \"video_starts\",\n", "    \"video_25_pcts\",\n", "    \"video_50_pcts\",\n", "    \"video_75_pcts\",\n", "    \"video_completions\",\n", "    \"video_served\",\n", "    \"video_errors\",\n", "    \"video_completion_rate\",\n", "    \"booked_revenue_ecpm\",\n", "    \"split_id\",\n", "    \"split_name\"\n", "]"]}, {"cell_type": "code", "execution_count": 3, "id": "c37c8c9a70003415", "metadata": {"ExecuteTime": {"end_time": "2024-02-15T19:49:26.034323805Z", "start_time": "2024-02-15T19:49:26.008038614Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dates between 2024-03-01 and 2024-02-29\n", "['20240229']\n"]}], "source": ["from datetime import date, timedelta\n", "\n", "start_dt = date(2024, 2, 29)\n", "end_dt = date(2024, 2, 29)\n", "delta = <PERSON>elta(days=1)\n", "dates = []\n", "while start_dt <= end_dt:\n", "    dates.append(start_dt.strftime(\"%Y%m%d\"))\n", "    start_dt += delta\n", "    \n", "print('Dates between', start_dt, 'and', end_dt)\n", "print(dates)    "]}, {"cell_type": "code", "execution_count": 5, "id": "5de466df244f7082", "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20240229\n"]}, {"ename": "KeyError", "evalue": "'deal_id'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 90\u001b[0m\n\u001b[1;32m     85\u001b[0m merged_df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimps\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mwhere(\n\u001b[1;32m     86\u001b[0m     merged_df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimps_va\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39misna(), merged_df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimps_na\u001b[39m\u001b[38;5;124m\"\u001b[39m], merged_df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimps_va\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m     87\u001b[0m )\n\u001b[1;32m     89\u001b[0m \u001b[38;5;66;03m# Group by deal_id and deal_name and sum the 'imps' column\u001b[39;00m\n\u001b[0;32m---> 90\u001b[0m result_df \u001b[38;5;241m=\u001b[39m \u001b[43mmerged_df\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgroupby\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdeal_id\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdeal_name\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimps\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39msum()\u001b[38;5;241m.\u001b[39mreset_index()\n\u001b[1;32m     92\u001b[0m \u001b[38;5;28mprint\u001b[39m(result_df\u001b[38;5;241m.\u001b[39mhead(\u001b[38;5;241m10\u001b[39m))\n\u001b[1;32m     94\u001b[0m \u001b[38;5;66;03m# Further processing\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/frame.py:8402\u001b[0m, in \u001b[0;36mDataFrame.groupby\u001b[0;34m(self, by, axis, level, as_index, sort, group_keys, squeeze, observed, dropna)\u001b[0m\n\u001b[1;32m   8399\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYou have to supply one of \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mby\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m and \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlevel\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   8400\u001b[0m axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_axis_number(axis)\n\u001b[0;32m-> 8402\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDataFrameGroupBy\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   8403\u001b[0m \u001b[43m    \u001b[49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8404\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeys\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mby\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8405\u001b[0m \u001b[43m    \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8406\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8407\u001b[0m \u001b[43m    \u001b[49m\u001b[43mas_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mas_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8408\u001b[0m \u001b[43m    \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8409\u001b[0m \u001b[43m    \u001b[49m\u001b[43mgroup_keys\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgroup_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8410\u001b[0m \u001b[43m    \u001b[49m\u001b[43msqueeze\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msqueeze\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8411\u001b[0m \u001b[43m    \u001b[49m\u001b[43mobserved\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobserved\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8412\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdropna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdropna\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   8413\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/groupby/groupby.py:965\u001b[0m, in \u001b[0;36mGroupBy.__init__\u001b[0;34m(self, obj, keys, axis, level, grouper, exclusions, selection, as_index, sort, group_keys, squeeze, observed, mutated, dropna)\u001b[0m\n\u001b[1;32m    962\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m grouper \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    963\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgroupby\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgrouper\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_grouper\n\u001b[0;32m--> 965\u001b[0m     grouper, exclusions, obj \u001b[38;5;241m=\u001b[39m \u001b[43mget_grouper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    966\u001b[0m \u001b[43m        \u001b[49m\u001b[43mobj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    967\u001b[0m \u001b[43m        \u001b[49m\u001b[43mkeys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    968\u001b[0m \u001b[43m        \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    969\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    970\u001b[0m \u001b[43m        \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    971\u001b[0m \u001b[43m        \u001b[49m\u001b[43mobserved\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobserved\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    972\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmutated\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmutated\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    973\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdropna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdropna\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    974\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    976\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj \u001b[38;5;241m=\u001b[39m obj\n\u001b[1;32m    977\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;241m=\u001b[39m obj\u001b[38;5;241m.\u001b[39m_get_axis_number(axis)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/groupby/grouper.py:888\u001b[0m, in \u001b[0;36mget_grouper\u001b[0;34m(obj, key, axis, level, sort, observed, mutated, validate, dropna)\u001b[0m\n\u001b[1;32m    886\u001b[0m         in_axis, level, gpr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m, gpr, \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    887\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 888\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(gpr)\n\u001b[1;32m    889\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(gpr, Grouper) \u001b[38;5;129;01mand\u001b[39;00m gpr\u001b[38;5;241m.\u001b[39mkey \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    890\u001b[0m     \u001b[38;5;66;03m# Add key to exclusions\u001b[39;00m\n\u001b[1;32m    891\u001b[0m     exclusions\u001b[38;5;241m.\u001b[39madd(gpr\u001b[38;5;241m.\u001b[39mkey)\n", "\u001b[0;31m<PERSON><PERSON>Error\u001b[0m: 'deal_id'"]}], "source": ["for date in dates:\n", "    print(date)\n", "\n", "    # Load network analytics data\n", "    df_na = pd.read_csv(f\"{na_path}/{date}-network_analytics.csv.gz\", dtype=str)\n", "    df_na = df_na[df_na[\"cost\"] != \"cost\"]\n", "\n", "    # Load network video analytics data\n", "    df_va = pd.read_csv(\n", "        f\"{video_path}/{date}-network_video_analytics.csv.gz\", dtype=str\n", "    )\n", "\n", "    # Rename columns in network video analytics data\n", "    renamed_columns = {\n", "        \"imps\": \"imps\",\n", "        \"25_pcts\": \"video_25_pcts\",\n", "        \"50_pcts\": \"video_50_pcts\",\n", "        \"75_pcts\": \"video_75_pcts\",\n", "        \"completions\": \"video_completions\",\n", "        \"starts\": \"video_starts\",\n", "        \"skips\": \"video_skips\",\n", "        \"served\": \"video_served\",\n", "        \"errors\": \"video_errors\",\n", "    }\n", "    df_va = df_va.rename(columns=renamed_columns)\n", "    df_va = df_va[\n", "        [\n", "            \"hour\",\n", "            \"creative_id\",\n", "            \"advertiser_id\",\n", "            \"line_item_id\",\n", "            \"insertion_order_id\",\n", "            \"video_25_pcts\",\n", "            \"video_50_pcts\",\n", "            \"video_75_pcts\",\n", "            \"video_completions\",\n", "            \"video_starts\",\n", "            \"video_errors\",\n", "            \"video_served\",\n", "            \"video_skips\",\n", "            \"deal_name\",\n", "            \"imps\",\n", "        ]\n", "    ]\n", "\n", "    # Convert columns to appropriate data types\n", "    for key, value in renamed_columns.items():\n", "        df_va[value] = df_va[value].astype(int)\n", "\n", "    # Group by certain columns and sum the rest\n", "    df_va = (\n", "        df_va.groupby(\n", "            [\n", "                \"hour\",\n", "                \"creative_id\",\n", "                \"advertiser_id\",\n", "                \"line_item_id\",\n", "                \"insertion_order_id\",\n", "                \"deal_name\",\n", "            ]\n", "        )\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "\n", "    # Calculate video completion rate\n", "    df_va[\"video_completion_rate\"] = (\n", "        df_va[\"video_completions\"].astype(float) / df_va[\"imps\"].astype(float)\n", "    ).<PERSON>na(0)\n", "    df_va[\"video_completion_rate\"].replace([np.inf, -np.inf], 0, inplace=True)\n", "\n", "    # Merge the dataframes\n", "    merged_df = df_na.merge(\n", "        df_va,\n", "        how=\"left\",\n", "        on=[\n", "            \"hour\",\n", "            \"creative_id\",\n", "            \"advertiser_id\",\n", "            \"line_item_id\",\n", "            \"insertion_order_id\",\n", "        ],\n", "        suffixes=(\"_na\", \"_va\"),\n", "    )\n", "    merged_df[\"imps\"] = np.where(\n", "        merged_df[\"imps_va\"].isna(), merged_df[\"imps_na\"], merged_df[\"imps_va\"]\n", "    )\n", "\n", "    # Further processing\n", "    merged_df.fillna(\"0\", inplace=True)\n", "    for c in [\n", "        \"video_25_pcts\",\n", "        \"video_50_pcts\",\n", "        \"video_75_pcts\",\n", "        \"video_completions\",\n", "        \"video_starts\",\n", "        \"video_skips\",\n", "        \"video_served\",\n", "        \"video_errors\",\n", "    ]:\n", "        merged_df[c] = merged_df[c].astype(int)\n", "\n", "    missing_columns = set(columns) - set(merged_df.columns)\n", "    for column in missing_columns:\n", "        merged_df[column] = \"\"\n", "    merged_df[\"line_item.booked_impressions_budget_daily\"] = \"0\"\n", "    merged_df[\"flight.start_date\"] = \"1970-01-01\"\n", "\n", "    merged_df.drop(columns=list(set(merged_df.columns) - set(columns)), inplace=True)\n", "\n", "    merged_df = merged_df.astype(str)\n", "\n", "    pd.to_datetime(merged_df[\"day\"], format=\"%Y-%m-%d\")\n", "    log_dates = list(merged_df[\"day\"].unique())\n", "    if len(log_dates) != 1:\n", "        print(log_dates)\n", "        raise Exception(\"There should only be one\")\n", "\n", "    for log_date in log_dates:\n", "        df_temp = merged_df[merged_df[\"day\"] == log_date]\n", "        df_temp.drop(columns=[\"day\"], inplace=True)\n", "\n", "        print(df_temp.columns.tolist())\n", "        df_temp.groupby([\"deal_id\", \"deal_name\"])[\"imps\"].sum().reset_index()\n", "\n", "        file_path = f\"external/xandr/network_analytics_hourly/day={log_date}/network_analytics_hourly_{log_date}.parquet\"\n", "        print(file_path)\n", "        if df_temp.shape[0] > 1:\n", "            df_temp.to_parquet(\n", "                f\"s3://et-datalake-campaign-reporting-dev/{file_path}\",\n", "                index=False,\n", "            )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}