import boto3
import awswrangler as wr

s3 = boto3.client('s3')


def initiate_restore(bucket, key):
    response = s3.restore_object(
        Bucket=bucket,
        Key=key,
        RestoreRequest={
            'Days': 60,
            'GlacierJobParameters': {
                'Tier': 'Bulk',
            },
        },
    )
    print(response)


objects = wr.s3.list_objects('s3://eltoro-apnx-reporting/na_yesterday')
#objects = ['s3://eltoro-apnx-reporting/na_yesterday/20231214-network_analytics.csv.gz']
for obj in objects:
    bucket = "eltoro-apnx-reporting"
    key = obj.replace("s3://eltoro-apnx-reporting/", '')
    initiate_restore(bucket, key)