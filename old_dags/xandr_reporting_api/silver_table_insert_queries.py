NETWORK_ANALYTICS_HOURLY = [
    """
    DELETE FROM "s3"."silver_et_xandr"."network_analytics_hourly"
    WHERE "day" = current_date or "day" = (current_date - interval '1' day)
    """,
    """
INSERT INTO s3.silver_et_xandr.network_analytics_hourly
WITH 
    missing_hours AS (
        SELECT "day", array_agg("hour") hours
        FROM s3.bronze_et_xandr.network_analytics_hourly b WHERE "hour" not in (
            SELECT distinct CONCAT(CAST("day" AS VARCHAR), ' ', "hour") 
            FROM s3.silver_et_xandr.network_analytics_hourly) group by "day"
        ),
    bronze AS (
        SELECT * FROM s3.bronze_et_xandr.network_analytics_hourly WHERE day = current_date or day = (current_date - interval '1' day)
    )    
SELECT
   SUBSTRING(hour, 12, 5) "hour",
   date(month) "month",
   line_item_id,
   line_item_name,
   line_item_code,
   campaign_id,
   campaign_code,
   campaign_name,
   advertiser_id,
   advertiser_code,
   advertiser_name,
   TRY_CAST("line_item.booked_impressions_budget_daily" AS INTEGER) "line_item.booked_impressions_budget_daily",
   size,
   CAST(imps AS double ) imps,
   CAST(imp_requests AS INTEGER) imp_requests,
   CAST(imps_blank AS INTEGER) imps_blank,
   CAST(imps_psa AS INTEGER) imps_psa,
   CAST(imps_psa_error AS INTEGER) imps_psa_error,
   CAST(imps_default_error AS INTEGER) imps_default_error,
   CAST(imps_default_bidder AS INTEGER) imps_default_bidder,
   CAST(imps_kept AS INTEGER) imps_kept,
   CAST(imps_resold AS INTEGER) imps_resold,
   CAST(imps_rtb AS INTEGER) imps_rtb,
   CAST(clicks AS INTEGER) clicks,
   click_thru_pct,
   CAST(external_click AS INTEGER) external_click,
   CAST(cost AS DOUBLE) cost,
   CAST(revenue AS DOUBLE) revenue,
   CAST(booked_revenue AS DOUBLE) booked_revenue,
   CAST(booked_revenue_adv_curr AS DOUBLE) booked_revenue_adv_curr,
   CAST(reseller_revenue AS DOUBLE) reseller_revenue,
   CAST(profit AS DOUBLE) profit,
   CAST(commissions AS DOUBLE) commissions,
   CAST(cpm AS DOUBLE) cpm,
   CAST(post_click_convs AS INTEGER) post_click_convs,
   CAST(post_click_revenue AS DOUBLE) post_click_revenue,
   CAST(post_view_convs AS INTEGER) post_view_convs,
   CAST(post_view_revenue AS DOUBLE) post_view_revenue,
   CAST(total_convs AS INTEGER) total_convs,
   CAST(convs_per_mm AS DOUBLE) convs_per_mm,
   CAST(convs_rate AS DOUBLE) convs_rate,
   CAST(ctr AS DOUBLE) ctr,
   CAST(rpm AS DOUBLE) rpm,
   CAST(total_network_rpm AS DOUBLE) total_network_rpm,
   CAST(total_publisher_rpm AS DOUBLE) total_publisher_rpm,
   CAST(sold_network_rpm AS DOUBLE) sold_network_rpm,
   CAST(sold_publisher_rpm AS DOUBLE) sold_publisher_rpm,
   CAST(media_cost_pub_curr AS DOUBLE) media_cost_pub_curr,
   CAST(serving_fees AS DOUBLE) serving_fees,
   CAST(view_measured_imps AS INTEGER) view_measured_imps,
   CAST(imps_viewed AS INTEGER) imps_viewed,
   CAST(view_rate AS DOUBLE) view_rate,
   CAST(view_measurement_rate AS DOUBLE) view_measurement_rate,
   CAST(cpvm AS DOUBLE) cpvm,
   CAST(revenue_selling_currency AS DOUBLE) revenue_selling_currency,
   CAST(booked_revenue_buying_currency AS DOUBLE) booked_revenue_buying_currency,
   CAST(booked_revenue_selling_currency AS DOUBLE) booked_revenue_selling_currency,
   CAST(reseller_revenue_buying_currency AS DOUBLE) reseller_revenue_buying_currency,
   CAST(reseller_revenue_selling_currency AS DOUBLE) reseller_revenue_selling_currency,
   CAST(cost_buying_currency AS DOUBLE) cost_buying_currency,
   CAST(cost_selling_currency AS DOUBLE) cost_selling_currency,
   CAST(profit_buying_currency AS DOUBLE) profit_buying_currency,
   CAST(profit_selling_currency AS DOUBLE) profit_selling_currency,
   CAST(total_network_rpm_buying_currency AS DOUBLE) total_network_rpm_buying_currency,
   CAST(total_network_rpm_selling_currency AS DOUBLE) total_network_rpm_selling_currency,
   CAST(cpm_buying_currency AS DOUBLE) cpm_buying_currency,
   CAST(cpm_selling_currency AS DOUBLE) cpm_selling_currency,
   CAST(rpm_buying_currency AS DOUBLE) rpm_buying_currency,
   CAST(rpm_selling_currency AS DOUBLE) rpm_selling_currency,
   CAST(sold_network_rpm_buying_currency AS DOUBLE) sold_network_rpm_buying_currency,
   CAST(sold_network_rpm_selling_currency AS DOUBLE) sold_network_rpm_selling_currency,
   CAST(serving_fees_buying_currency AS DOUBLE) serving_fees_buying_currency,
   CAST(serving_fees_selling_currency AS DOUBLE) serving_fees_selling_currency,
   CAST(data_costs_buying_currency AS DOUBLE) data_costs_buying_currency,
   CAST(data_costs_selling_currency AS DOUBLE) data_costs_selling_currency,
   insertion_order_id,
   insertion_order_name,
   "insertion_order.type",
   creative_id,
   creative_name,
   creative_code,
   TRY_CAST(total_cost_ecpm AS DOUBLE) total_cost_ecpm,
   pixel_id,
   pixel_name,
   TRY_CAST(SUBSTRING("flight.start_date", 1, 10) AS DATE) "flight.start_date",
   deal_name,
   deal_id,
   CAST(video_skips AS INTEGER) video_skips,
   CAST(video_starts AS INTEGER) video_starts,
   CAST(video_25_pcts AS INTEGER) video_25_pcts,
   CAST(video_50_pcts AS INTEGER) video_50_pcts,
   CAST(video_75_pcts AS INTEGER) video_75_pcts,
   CAST(video_completions AS INTEGER) video_completions,
   CAST(video_served AS INTEGER) video_served,
   CAST(video_errors AS INTEGER) video_errors,
   CAST(video_completion_rate AS DOUBLE) video_completion_rate,
   CAST(booked_revenue_ecpm AS DOUBLE) booked_revenue_ecpm,
   split_id,
   split_name,
   b.day
from bronze b
JOIN missing_hours m on (m."day" = b."day")
where m."day" = b."day" and contains(m."hours", b."hour")
""",
]
