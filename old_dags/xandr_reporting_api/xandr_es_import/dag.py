from etdag import ETDAG
from datetime import datetime, timedelta
from old_dags.xandr_reporting_api.xandr_es_import.elasticsearch_insert import (
    query_and_upsert_reporting_stats_v2,
)
from airflow.decorators import task
from airflow.operators.python import get_current_context
from airflow.models.param import Param

docs = """
# Xandr Elasticsearch Import DAG

This DAG upserts Xandr hourly reporting statistics into Elasticsearch for analytics and reporting.

## Purpose

- Imports and aggregates Xandr reporting data from Starburst/Trino.
- Upserts the processed data into Elasticsearch for downstream analytics and dashboards.
- Can be triggered automatically by upstream DAGs (e.g., daily or yesterday reporting) or manually for backfill and recovery.

## Usage

- **Automatic Trigger:**  
  This DAG is triggered by the `xandr_reporting_daily` and `xandr_reporting_yesterday` DAGs after their successful completion, passing the relevant reporting date(s).
- **Manual Trigger:**  
  You can trigger this DAG manually from the Airflow UI or CLI, specifying the date(s) to process and optionally a backfill window.

## Parameters

- `unique_days` (string or list, optional):  
  The date(s) (YYYY-MM-DD) to process.  
  - If a string, a single date is processed.
  - If a comma-separated string, multiple dates are processed.
  - If omitted, defaults to the DAG run date.

- `backfill_days` (integer, optional):  
  If set to N > 0, the DAG will process N days backwards starting from the first date in `unique_days`.  
  For example, `unique_days: "2025-06-18", backfill_days: 3` will process 2025-06-18, 2025-06-17, and 2025-06-16.

## Examples

**Trigger from another DAG:**
```python
TriggerDagRunOperator(
    task_id="trigger_es_import",
    trigger_dag_id="xandr_elasticsearch_import",
    conf={"unique_days": "{{ data_interval_end | ds }}"},
)
```

**Manual trigger with backfill (UI or CLI):**
```json
{
  "unique_days": "2025-06-18",
  "backfill_days": 3
}
```

## Tags

- application:reporting
- team:DND

"""

default_args = {
    "owner": "Bryan Price",
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 0,
}


@task
def upsert_into_es():
    ctx = get_current_context()
    conf = ctx.get("dag_run").conf if ctx.get("dag_run") else {}
    params = ctx.get("params", {})
    merged = {**params, **conf}

    unique_days = merged.get("unique_days")
    backfill_days = merged.get("backfill_days")

    # Normalize unique_days to a list
    if not unique_days:
        unique_days = [ctx["ds"]]  # fallback: execution date (today)
    elif isinstance(unique_days, str):
        unique_days = [unique_days]

    # Optional rolling backfill
    if backfill_days:
        end = datetime.strptime(unique_days[0], "%Y-%m-%d")
        unique_days = [
            (end - timedelta(days=i)).strftime("%Y-%m-%d")
            for i in range(int(backfill_days))
        ]

    query_and_upsert_reporting_stats_v2(unique_days)


with ETDAG(
    dag_id="xandr_elasticsearch_import",
    description="Upserts Xandr hourly stats into ES",
    start_date=datetime(2025, 5, 30),
    schedule_interval=None,
    default_args=default_args,
    catchup=False,
    params={
        "unique_days": Param(
            default="",
            type="string",
            description="Comma-separated YYYY-MM-DD list; leave blank to use the run date",
        ),
        "backfill_days": Param(
            default=0,
            type="integer",
            description="If >0, generate N days backwards starting from unique_days[0]",
        ),
    },
    tags=["application:reporting", "team:DND"],
) as dag:
    upsert_into_es()
