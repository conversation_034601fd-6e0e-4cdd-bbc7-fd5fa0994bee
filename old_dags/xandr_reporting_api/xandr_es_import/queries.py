reporting_api_query = """
SELECT
  CONCAT(CAST(nah.day AS VARCHAR), 'T', nah.hour, ':00') AS day,
  "advertiser_id" org_id_appnexus,
  "advertiser_code" org_id,
  "advertiser_name" org_name,
  CAST("line_item_id" AS INTEGER) campaign_id_appnexus,
  ol.campaign_id campaign_id,
  ol.campaign_name campaign_name,
  "line_item_name" order_line_name,
  "line_item_code" order_line_id,
  "line_item_id" order_line_id_appnexus,
  CAST("creative_id" AS INTEGER) creative_id_appnexus,
  "creative_name",
  "creative_code" creative_id,
  "size", 
  sum("imps") imps,
  sum(imp_requests) imp_requests,
  sum(imps_blank) imps_blank,
  sum(imps_psa) imps_psa,
  sum(imps_psa_error) imps_psa_error,
  sum(imps_default_error) imps_default_error,
  sum(imps_default_bidder) imps_default_bidder,
  sum(imps_kept) imps_kept,
  sum(imps_resold) imps_resold,
  sum(imps_rtb) imps_rtb,
  0 external_impression,
  sum(clicks) clicks,
  sum(external_click) external_click,
  sum(cost) cost,
  0 cost_including_fees,
  sum(revenue) revenue,
  0 revenue_including_fees,
  sum(booked_revenue) booked_revenue,
  sum("booked_revenue_adv_curr") booked_revenue_adv_curr,
  avg("booked_revenue_ecpm") "booked_revenue_ecpm",
  sum(reseller_revenue) reseller_revenue,
  sum(profit) profit,
  0 profit_including_fees,
  sum(commissions) commissions,
  sum("post_click_convs") "post_click_convs",
  sum("post_click_revenue") "post_click_revenue",
  sum("post_view_convs") "post_view_convs",
  sum("post_view_revenue") "post_view_revenue",
  sum("total_convs") "conversions",
  sum("media_cost_pub_curr") "media_cost_pub_curr",
  sum("serving_fees") "serving_fees",
  sum("imps_viewed") "imps_viewed",
  sum("view_measured_imps") "view_measured_imps",
  0 data_costs,
  sum("video_75_pcts") pcts_75,
  sum("video_50_pcts") pcts_50,
  sum("video_skips") skips,
  sum("video_starts") starts,
  sum("video_completions") completions,
  sum("video_25_pcts") pcts_25,
  sum("imps") served,
  sum("video_errors") errors,
  1 bidder_source,
  0 ol_target_type,
  0 ol_creative_type
FROM
  "s3"."silver_et_xandr"."network_analytics_hourly" nah
JOIN "s3"."silver_platform_services"."order_lines" ol ON nah.line_item_code = ol.order_line_id 
WHERE :where_clause 
GROUP BY nah.day, nah.hour, "line_item_id", "line_item_name", "line_item_code", ol.campaign_id, "campaign_code", ol.campaign_name, "advertiser_id", "advertiser_code", "advertiser_name",  "size", "insertion_order_id", "insertion_order_name", "insertion_order.type", "creative_id", "creative_name", "creative_code"
ORDER BY "line_item_id" ASC, day DESC, "creative_id" DESC 
"""


narollupv2 = {
    "columns": [
        "day",
        "org_id_appnexus",
        "org_id",
        "org_name",
        "campaign_id_appnexus",
        "campaign_id",
        "campaign_name",
        "order_line_id",
        "order_line_name",
        "order_line_id_appnexus",
        "creative_id",
        "creative_name",
        "creative_id_appnexus",
        "size",
        "imps",
        "imp_requests",
        "imps_blank",
        "imps_psa",
        "imps_psa_error",
        "imps_default_error",
        "imps_default_bidder",
        "imps_kept",
        "imps_resold",
        "imps_rtb",
        "external_impression",
        "clicks",
        "external_click",
        "cost",
        "cost_including_fees",
        "revenue",
        "revenue_including_fees",
        "booked_revenue",
        "booked_revenue_adv_curr",
        "booked_revenue_ecpm",
        "reseller_revenue",
        "profit",
        "profit_including_fees",
        "commissions",
        "post_click_convs",
        "post_click_revenue",
        "post_view_convs",
        "post_view_revenue",
        "conversions",
        "media_cost_pub_curr",
        "serving_fees",
        "imps_viewed",
        "view_measured_imps",
        "data_costs",
        "pcts_75",
        "pcts_50",
        "skips",
        "starts",
        "completions",
        "pcts_25",
        "served",
        "errors",
        "bidder_source",
        "ol_target_type",
        "ol_creative_type",
    ]
}
