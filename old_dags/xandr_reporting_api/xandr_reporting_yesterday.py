from etdag import ETDAG
from slack_sdk import WebhookClient
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.xandr_reporting_api.xandr_reporting_operator import XandrAPIOperator
from old_dags.xandr_reporting_api.xandr_reporting_config import reporting_configs
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.operators.empty import EmptyOperator
from airflow.utils.trigger_rule import TriggerRule

docs = """
# Overview
Connect to Xandr API (https://docs.xandr.com/bundle/xandr-api/page/reference.html) to load the following feed to Starburst.

## Instructions for handling Xandr Password expirations.  
* Both passwords used in this ETL should be configured to the MS group account, <EMAIL>.
* If a password expires, get the appropriate username and manually head over to https://curate.xandr.com/account-recovery or invest possibly if appropriate.  I was unable to find a link any other way.
* Request a password reset on the user and wait for the reset <NAME_EMAIL>.  My experience is that it lands in spam 9/10 times
* Perform a password reset and update the variables in airflow appropriately.  
"""

default_args = {
    "owner": "Rorie Lizenby",
    "depends_on_past": False,
    "start_date": datetime(2024, 9, 12),
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}


def query_and_notify():
    trino_hook = TrinoHook(trino_conn_id="starburst")
    stmt = """
        SELECT day, count(distinct hour) hours 
        from s3.silver_et_xandr.network_analytics_hourly 
        where day != current_date
        group by day
        having count(distinct hour) != 24
        order by 1 desc
        """
    df = trino_hook.get_pandas_df(stmt)
    if df.shape[0] != 0:
        msg = """
        Airflow Dag xandr_reporting_yesterday: Date found in olympus.silver_et_xandr.network_analytics_hourly
        that does not contain the expected 24 hrs of data.  Please Investigate!!!
        """
        url = "*****************************************************************************"
        webhook = WebhookClient(url=url)
        webhook.send(text=msg)

    return "Task completed successfully."


with ETDAG(
    dag_id="xandr_reporting_yesterday",
    description="Extract and load xandr reporting api. Necessary redundancy to get any updates past midnight",
    schedule_interval="15 6 * * 1-7",  # Run at 6:15 UTC
    catchup=True,
    tags=["starburst", "xandr", "app:campaign_reporting", "team:DND"],
    concurrency=1,
) as dag:
    dag.doc_md = docs

    trigger_es_import = TriggerDagRunOperator(
        task_id="trigger_es_import_from_yesterday",
        trigger_dag_id="xandr_elasticsearch_import",
        conf={
            "unique_days": "{{ (data_interval_end - macros.timedelta(days=1)) | ds }}",
            "source": "yesterday",
        },
        wait_for_completion=False,
    )
    # added to ensure the elasticsearch import is triggered only once all tasks are complete
    wait_for_all_notify = EmptyOperator(
        task_id="wait_for_all_notify_tasks", trigger_rule=TriggerRule.ALL_SUCCESS
    )

    final_tasks = []

    for reporting in reporting_configs:
        report_interval = "yesterday"
        if reporting["name"] == "pixel_fired":
            report_interval = "lifetime"

        api_report = XandrAPIOperator(
            reporting_config=reporting,
            task_id=f"get_{reporting['name']}",
            interval=report_interval,
            data_interval_end="{{ data_interval_end }}",
            max_active_tis_per_dag=1,
            retries=3,
        )
        if reporting["name"] != "pixel_fired":
            update_partitions = SQLExecuteQueryOperator(
                task_id=f"update_{reporting['name']}_partitions",
                conn_id="trino_conn",
                sql=f"CALL s3.system.sync_partition_metadata('{reporting['bronze_schema_name']}', '{reporting['bronze_table_name']}', 'ADD')",
                handler=list,
            )

        if "silver_insert_query" in reporting:
            update_silver = SQLExecuteQueryOperator(
                task_id=f"update_silver_{reporting['name']}",
                conn_id="trino_conn",
                sql=reporting["silver_insert_query"],
                handler=list,
            )
            api_report >> update_partitions >> update_silver
            final_tasks.append(update_silver)  # Add the final task to the list
        else:
            api_report >> update_partitions
            final_tasks.append(update_partitions)  # Add the final task to the list

    # Create the query_and_notify task using PythonOperator
    query_and_notify_task = PythonOperator(
        task_id="query_and_notify", python_callable=query_and_notify
    )

    # Set dependencies to ensure query_and_notify runs after all final tasks
    for task in final_tasks:
        task >> query_and_notify_task >> wait_for_all_notify

    wait_for_all_notify >> trigger_es_import
