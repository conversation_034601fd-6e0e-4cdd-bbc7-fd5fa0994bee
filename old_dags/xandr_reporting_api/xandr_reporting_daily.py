from etdag import ETDAG
from datetime import datetime, timedelta
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from old_dags.xandr_reporting_api.xandr_reporting_operator import Xandr<PERSON>IOperator
from old_dags.xandr_reporting_api.xandr_reporting_config import reporting_configs

docs = """
# Overview
Connect to Xandr API (https://docs.xandr.com/bundle/xandr-api/page/reference.html) to load the following feed to Starburst.
"""

default_args = {
    "owner": "<PERSON><PERSON>e <PERSON>",
    "depends_on_past": False,
    "start_date": datetime(2024, 9, 12),
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}


with ETDAG(
    dag_id="xandr_reporting_daily",
    description="Extract and load xandr reporting api",
    schedule_interval="40 4-23 * * *",  # Run every hour (excluding 20,21,22 hour) from Monday to Sunday
    catchup=False,
    default_args=default_args,
    tags=["starburst", "xandr", "app:campaign_reporting", "team:DND"],
    concurrency=1,
) as dag:
    dag.doc_md = docs

    trigger_es_import = TriggerDagRunOperator(
        task_id="trigger_es_import_from_daily",
        trigger_dag_id="xandr_elasticsearch_import",
        conf={"unique_days": "{{ data_interval_end | ds }}", "source": "daily"},
        wait_for_completion=False,
    )

    reports_to_run_hourly = [
        "network_analytics_hourly",
    ]

    reporting_configs = [
        r for r in reporting_configs if r["name"] in reports_to_run_hourly
    ]
    for reporting in reporting_configs:
        api_report = XandrAPIOperator(
            reporting_config=reporting,
            task_id=f"get_{reporting['name']}",
            interval="today",
            data_interval_end="{{ data_interval_end }}",
            max_active_tis_per_dag=1,
        )

        update_partitions = SQLExecuteQueryOperator(
            task_id=f"update_partitions_{reporting['name']}",
            conn_id="starburst",
            sql=f"CALL s3.system.sync_partition_metadata('{reporting['bronze_schema_name']}', '{reporting['bronze_table_name']}', 'ADD')",
            handler=list,
        )

        if "silver_insert_query" in reporting:
            update_silver = SQLExecuteQueryOperator(
                task_id=f"update_silver_{reporting['name']}",
                conn_id="starburst",
                sql=reporting["silver_insert_query"],
                handler=list,
            )
            api_report >> update_partitions >> update_silver

    update_silver >> trigger_es_import
