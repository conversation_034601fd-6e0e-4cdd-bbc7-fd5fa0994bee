# Airflow Process
DAG Name: xandr_reporting_api </br>
Schedule Interval: Hourly </br>
owner: Tim

## Resources
Your primary resource in understanding these dags and referencing appropriate time intervals for each reporty type is the [Xandr Reporting Documentation](https://docs.xandr.com/bundle/xandr-api/page/report-service.html) 
Dags within this repo path have been constructed to maintain tables derived from Xandr Reports.  It should not include any ETL merging to ElToro data.

## Dags
### xandr_reporting_config
* The config is used by both dags in this path.  If a particular dag should not run a particular report, it must be controlled by the logic in the dag.
* The config contains a pointer to the appropriate credentials, the exact json to request the report and reference to any additional tables these dags may maintain.
* The reporting time interval is the only values in the config that should be modified by the dag, potentially switching the value between `yesterday` and `today`

### xandr_reporting_today
* Some reports are required by the business to fetch data early and frequently.  This dag runs hourly. 

### xandr_reporting_yesterday
* Other reports do not need frequency or recency and can be run at some point past midnight to get the previous days data. 
* `today` reports are also run in this dag to ensure all of `yesterday` data is retrieved.

## Tasks
### Get {Report Name}
This task uses the custom Xandr Reporting Operator to request and retrieve a xandr report, as defined in the config.  Use caution if making changes to the custom operator, to ensure it doesn't break a specific report.  Within this task, after the data is retrieved it is then uploaded to s3 in a "day" partitioned scheme (one exception).
### Update {Report Name} Partitions
This is just a sync_partition Starburst request on the appropriate bronze table, as defined in the config
### Update {Report Name} Silver Table
This is a INSERT query (if introduced for the specific report) that does an appropriate INSERT into the silver table defined in the config.  These queries are a good resource if any adhoc work or maintenance is required on the table.

## Additional Notes
* The xandr_reporting_operator is designed to only work appropriately with "full day" time intervals.  When running locally, it is acceptable to use something other than `today` or `yesterday`. But do not use something like `last_2_hours`
