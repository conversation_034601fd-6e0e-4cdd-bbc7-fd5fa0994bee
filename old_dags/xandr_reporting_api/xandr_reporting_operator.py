from io import StringIO
import json
import time
import pandas as pd
import requests
import pendulum
from urllib.parse import unquote
from airflow.models import Variable
from airflow.models.baseoperator import BaseOperator
from airflow.utils.decorators import apply_defaults
from airflow.exceptions import AirflowException

from pandas.errors import EmptyDataError

class XandrAPIOperator(BaseOperator):
    template_fields = ('data_interval_end',)

    @apply_defaults
    def __init__(
        self,
        *,
        reporting_config: dict,
        interval: str,  # acceptable values: today, yesterday, lifetime
        data_interval_end: str,
        aws_conn_id: str = "aws_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.report_name = reporting_config["name"]
        self.report_config = reporting_config["request_config"]
        self.interval = interval
        self.data_interval_end = data_interval_end
        self.xandr_api_var = json.loads(Variable.get(reporting_config["api_cred_var"]))
        self.env = Variable.get("environment")
        self.max_page_size = reporting_config.get("max_page_size", 1_000_000)
        self.current_page = 0
        self.all_records_received = False
        self.aws_conn_id = aws_conn_id
        self.s3_campaign_reporting_bucket = f"et-datalake-campaign-reporting-{Variable.get('environment')}"
        self.xandr_api_user = self.xandr_api_var["user"]
        self.xandr_api_pw = self.xandr_api_var["password"]
        self.xandr_auth_url = "https://api.appnexus.com/auth"
        self.xandr_endpoint_url = "https://api.appnexus.com/report"
        self.xandr_download_url = "https://api.appnexus.com/report-download"

    def prepare_start_and_end_dates(self):
        if self.interval == 'today':
            self.report_config['report']['start_date'] = self.data_interval_end.start_of("day").format("YYYY-MM-DD HH:00:00")
            self.report_config['report']['end_date'] = self.data_interval_end.start_of("hour").format("YYYY-MM-DD HH:00:00")

        if self.interval == 'yesterday':
            self.report_config['report']['start_date'] = self.data_interval_end.subtract(days=1).start_of("day").format("YYYY-MM-DD HH:00:00")
            self.report_config['report']['end_date'] = self.data_interval_end.start_of("day").format("YYYY-MM-DD HH:00:00")

        if self.interval == 'lifetime':
            self.report_config['report']["report_interval"] = "lifetime"

    def _get_token(self):
        credentials = {
            "auth": {
                "username": self.xandr_api_user,
                "password": self.xandr_api_pw,
            }
        }
        response = requests.post(self.xandr_auth_url, json=credentials)
        if response.status_code != 200:
            raise AirflowException(f"Failed to retrieve token: {response.text}")
        res = response.json()
        return res.get("response", {}).get("token")

    def _get_report_data(self, report_config) -> pd.DataFrame:
        self.log.info(f"Requesting report with config: {report_config}")
        headers = {"Authorization": self._get_token()}
        rep_status = ""
        xandr_data = requests.post(
            url=self.xandr_endpoint_url,
            headers=headers,
            data=json.dumps(report_config),
        )
        if xandr_data.status_code != 200:
            raise AirflowException(f"Failed to initiate report: {xandr_data.text}")
        xandr_data = xandr_data.json()
        report_id = xandr_data["response"].get("report_id")
        status = xandr_data["response"].get("status")
        if status != "OK":
            raise AirflowException(f"Report initiation failed with status: {status}")
        time.sleep(3)

        report_status_url = f"{self.xandr_endpoint_url}?id={report_id}"

        while rep_status != "OK":
            self.log.info("Waiting for report to be ready.................")
            time.sleep(5)
            report_status = requests.get(url=report_status_url, headers=headers)
            if report_status.status_code != 200:
                raise AirflowException(f"Failed to get report status: {report_status.text}")
            report_status = report_status.json()
            rep_status = report_status["response"].get("status")
            if rep_status != "OK":
                self.log.info(f"Current report status: {rep_status}")
        time.sleep(30)

        report_url = f"{self.xandr_download_url}?id={report_id}"
        self.log.info(f"Downloading report from {report_url}")
        report_data = requests.get(url=report_url, headers=headers)
        if report_data.status_code != 200:
            raise AirflowException(f"Failed to download report: {report_data.text}")

        report_content = report_data.content
        while not report_content or len(report_content.strip()) == 0:
            self.log.info("Report content is empty. Waiting 30 seconds before retrying download...")
            time.sleep(30)
            report_data = requests.get(url=report_url, headers=headers)
            if report_data.status_code != 200:
                raise AirflowException(f"Failed to download report: {report_data.text}")
            report_content = report_data.content


        unquoted_data = unquote(report_content.decode("utf-8"))
        try:
            df = pd.read_csv(StringIO(unquoted_data), dtype=str)
        except EmptyDataError:
            raise AirflowException("Downloaded report is empty or invalid.")

        self.log.info(f"Dataframe shape: {df.shape}")
        if df.empty or len(df.columns) < len(report_config['report']['columns']):
            raise AirflowException(f"Data returned from API is incomplete or empty: DataFrame shape is {df.shape}")

        return df

    def execute(self, context):
        """
        Use the Saved report Service
        Step 1 : POST request (JSON report request)
        Step 2 : GET report status (If report is successfully created get "report id")
        Step 3 : GET request (Get the report data using the report id)
        """
        self.data_interval_end = pendulum.parse(self.data_interval_end)
        self.prepare_start_and_end_dates()

        df = pd.DataFrame()

        while not self.all_records_received:
            report_config = self.report_config
            report_config['report']["offset"] = self.current_page * self.max_page_size
            report_config['report']["num_elements"] = self.max_page_size
            try:
                df_page = self._get_report_data(report_config)
            except AirflowException as e:
                self.log.error(f"Failed to retrieve data page: {str(e)}")
                raise

            self.log.info(f"Retrieved {len(df_page)} rows in this page.")
            df = pd.concat([df, df_page], ignore_index=True)
            if len(df_page) < self.max_page_size:
                self.all_records_received = True
            else:
                self.current_page += 1

        if df.empty:
            raise AirflowException("No data received after retrieving all pages.")

        # Handle reports without day column
        if self.report_name in ['pixel_fired']:
            file_path = f"external/xandr/{self.report_name}/{self.report_name}.parquet"
            df.to_parquet(
                f"s3://{self.s3_campaign_reporting_bucket}/{file_path}",
                index=False,
                engine="pyarrow",
            )
            return

        # Handle all other reports that can be partitioned by day
        pd.to_datetime(df["day"], format="%Y-%m-%d")
        dates = list(df['day'].unique())
        for date in dates:
            df_temp = df[df['day'] == date]
            df_temp.drop(columns=['day'], inplace=True)
            file_path = f"external/xandr/{self.report_name}/day={date}/{self.report_name}_{date}.parquet"
            if df_temp.shape[0] > 1:
                df_temp.to_parquet(
                    f"s3://{self.s3_campaign_reporting_bucket}/{file_path}",
                    index=False,
                    engine="pyarrow",
                )
        return
