from etdag import ETDAG
import boto3
import base64
import requests
import pandas as pd
from airflow.models import Variable
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator


docs = """
# Xandr Creative Audit Legacy DAG

## Overview

This DAG is designed to replace the legacy code responsible for auditing creatives from the Xandr platform. It runs daily and retrieves audit data for Xandr creatives for the most recent 4 day timeframe. This 4 day interval is replaced in S3, no "day" partitions maintained. The retrieved data is stored in parquet format on AWS S3.

## On Failure
 - This dag can be rerun all you want with no consequences. Or clear task.  Either will produce the same result. 
"""

default_args = {
    'owner': '<PERSON><PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': datetime(2024, 9, 12),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}

def get_xandr_token():
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
    )
    response = client.get_secret_value(SecretId="apnxs-token")

    if 'SecretString' in response:
        secret = response['SecretString']
    else:
        secret = base64.b64decode(response['SecretBinary'])
    return secret

def get_creative_audit_data():
    headers = {"Authorization": get_xandr_token()}
    min_date = (datetime.now() - timedelta(4)).strftime('%Y-%m-%d')
    max_date = (datetime.now() + timedelta(1)).strftime('%Y-%m-%d')
    i = 0
    record_count = 1
    df = pd.DataFrame()
    data_list = []
    creative_records = []
    while i < record_count:
        xandr_url = 'https://api.appnexus.com/creative?audit_stats=true&show_category_name=true&min_last_modified={}&' \
                    'max_last_modified={}&num_elements=100&start_element={}&sort=id.asc'.format(min_date, max_date, i)
        xandr_data = requests.get(url=xandr_url, headers=headers).json()
        for record in xandr_data:
            record_count = xandr_data[record]['count']
            data_list.append(xandr_data)
            i = i + 100
            for one_iter in data_list:
                for creatives in one_iter['response']['creatives']:
                    creative_record = {
                        "audit_status": creatives.get('audit_status'),
                        "is_expired": creatives.get('is_expired'),
                        "creative_id": creatives.get('code'),
                        "state": creatives.get('state'),
                        "last_modified": creatives.get('last_modified'),
                        "created_on": creatives.get('created_on'),
                        "appnexus_id": creatives.get('id'),
                        "creative_name": creatives.get('name'),
                        "audit_feedback": creatives.get('audit_feedback'),
                        "category_name": creatives['brand']['category_name']
                    }
                    creative_records.append(creative_record)
                df_temp = pd.DataFrame(creative_records)
                df = pd.concat([df, df_temp])
        print(i)
    df = df.drop_duplicates()

    file_path = f"external/xandr/creative_audit/creative_audit.parquet"
    if df.shape[0] > 1:
        df.to_parquet(
            f"s3://et-datalake-campaign-reporting-{Variable.get('environment')}/{file_path}",
            index=False,
            engine="pyarrow",
        )


with ETDAG(
    dag_id="xandr_creative_audit_legacy",
    description="Replacement for Legacy Xandr Creative Audit code.  Only needed for portal V2",
    schedule_interval="35 4 * * 1-7",  # Run at 4:15 A.M hours
    catchup=False,
    tags=["legacy", "xandr", "app:campaign_reporting", "team:DND"],
    concurrency=1,
) as dag:
    dag.doc_md = docs

    get_creative_audit = PythonOperator(
        task_id='get_creative_audit',
        retries=1,
        python_callable=get_creative_audit_data
    )

