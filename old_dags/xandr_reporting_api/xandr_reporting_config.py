import old_dags.xandr_reporting_api.silver_table_insert_queries as silver_insert

reporting_configs = [
    {
        "name": "network_site_domain_performance",
        "api_cred_var": "xandr_invest_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "network_site_domain_performance",
        "request_config": {
            "report": {
                "report_type": "network_site_domain_performance",
                "columns": [
                    "day",
                    "line_item_id",
                    "site_domain",
                    "deal_id",
                    "top_level_category_name",
                    "mobile_application_name",
                    "mobile_application_id",
                    "imps",
                    "clicks",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "buyer_segment_performance",
        "api_cred_var": "xandr_invest_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "buyer_segment_performance",
        "request_config": {
            "report": {
                "report_type": "buyer_segment_performance",
                "columns": [
                    "day",
                    "line_item_id",
                    "imps",
                    "segment_id",
                    "segment_name",
                    "segment_code",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "network_analytics_hourly",
        "api_cred_var": "xandr_invest_cred",
        "max_page_size": 75_000,
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "network_analytics_hourly",
        "silver_insert_query": silver_insert.NETWORK_ANALYTICS_HOURLY,
        "request_config": {
            "report": {
                "report_type": "network_analytics",
                "columns": [
                    "hour",
                    "month",
                    "day",
                    "line_item_id",
                    "line_item_name",
                    "line_item_code",
                    "campaign_id",
                    "campaign_code",
                    "campaign_name",
                    "advertiser_id",
                    "advertiser_code",
                    "advertiser_name",
                    "line_item.booked_impressions_budget_daily",
                    "size",
                    "imps",
                    "imp_requests",
                    "imps_blank",
                    "imps_psa",
                    "imps_psa_error",
                    "imps_default_error",
                    "imps_default_bidder",
                    "imps_kept",
                    "imps_resold",
                    "imps_rtb",
                    "clicks",
                    "click_thru_pct",
                    "external_click",
                    "cost",
                    "revenue",
                    "booked_revenue",
                    "booked_revenue_adv_curr",
                    "reseller_revenue",
                    "profit",
                    "commissions",
                    "cpm",
                    "post_click_convs",
                    "post_click_revenue",
                    "post_view_convs",
                    "post_view_revenue",
                    "total_convs",
                    "convs_per_mm",
                    "convs_rate",
                    "ctr",
                    "rpm",
                    "total_network_rpm",
                    "total_publisher_rpm",
                    "sold_network_rpm",
                    "sold_publisher_rpm",
                    "media_cost_pub_curr",
                    "serving_fees",
                    "view_measured_imps",
                    "imps_viewed",
                    "view_rate",
                    "view_measurement_rate",
                    "cpvm",
                    "revenue_selling_currency",
                    "booked_revenue_buying_currency",
                    "booked_revenue_selling_currency",
                    "reseller_revenue_buying_currency",
                    "reseller_revenue_selling_currency",
                    "cost_buying_currency",
                    "cost_selling_currency",
                    "profit_buying_currency",
                    "profit_selling_currency",
                    "total_network_rpm_buying_currency",
                    "total_network_rpm_selling_currency",
                    "cpm_buying_currency",
                    "cpm_selling_currency",
                    "rpm_buying_currency",
                    "rpm_selling_currency",
                    "sold_network_rpm_buying_currency",
                    "sold_network_rpm_selling_currency",
                    "serving_fees_buying_currency",
                    "serving_fees_selling_currency",
                    "data_costs_buying_currency",
                    "data_costs_selling_currency",
                    "insertion_order_id",
                    "insertion_order_name",
                    "insertion_order.type",
                    "creative_id",
                    "creative_name",
                    "creative_code",
                    "total_cost_ecpm",
                    "pixel_id",
                    "pixel_name",
                    "flight.start_date",
                    "deal_name",
                    "deal_id",
                    "video_skips",
                    "video_starts",
                    "video_25_pcts",
                    "video_50_pcts",
                    "video_75_pcts",
                    "video_completions",
                    "video_served",
                    "video_errors",
                    "video_completion_rate",
                    "booked_revenue_ecpm",
                    "split_id",
                    "split_name",
                ],
                "orders": [
                    "hour",
                    "month",
                    "day",
                    "line_item_id",
                    "line_item_name",
                    "line_item_code",
                    "campaign_id",
                    "campaign_code",
                    "campaign_name",
                    "advertiser_id",
                    "advertiser_code",
                    "advertiser_name",
                    "line_item.booked_impressions_budget_daily",
                    "size",
                    "insertion_order_id",
                    "insertion_order_name",
                    "insertion_order.type",
                    "creative_id",
                    "creative_name",
                    "creative_code",
                    "pixel_id",
                    "pixel_name",
                    "flight.start_date",
                    "deal_name",
                    "deal_id",
                    "split_id",
                    "split_name"
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "network_device_analytics",
        "api_cred_var": "xandr_invest_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "network_device_analytics",
        "request_config": {
            "report": {
                "report_type": "network_device_analytics",
                "columns": [
                    "line_item_id",
                    "day",
                    "imps",
                    "clicks",
                    "device_type",
                    "media_type",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "pixel_fired",
        "api_cred_var": "xandr_invest_cred",
        "request_config": {
            "report": {
                "report_type": "pixel_fired",
                "columns": ["pixel_id", "last_fired"],
                "orders": ["pixel_id", "last_fired"],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "video_analytics",
        "api_cred_var": "xandr_invest_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "video_analytics",
        "request_config": {
            "report": {
                "report_type": "network_analytics",
                "timezone": "UTC",
                "use_cache": False,
                "columns": [
                    "day",
                    "advertiser_name",
                    "advertiser_code",
                    "insertion_order_name",
                    "insertion_order_code",
                    "line_item_name",
                    "line_item_code",
                    "line_item_id",
                    "creative_id",
                    "creative_name",
                    "creative_code",
                    "flight.start_date",
                    "flight.end_date",
                    "seller_member_name",
                    "seller_member_id",
                    "publisher_name",
                    "publisher_id",
                    "deal_name",
                    "deal_id",
                    "imps",
                    "clicks",
                    "view_rate",
                    "view_measurement_rate",
                    "video_skips",
                    "video_starts",
                    "video_25_pcts",
                    "video_50_pcts",
                    "video_75_pcts",
                    "video_completions",
                    "video_errors",
                    "booked_revenue_ecpm",
                    "total_cost_ecpm",
                    "ctr"
                ],
                "orders": [
                    "day",
                    "advertiser_name",
                    "advertiser_code",
                    "insertion_order_name",
                    "insertion_order_code",
                    "line_item_name",
                    "line_item_code",
                    "line_item_id",
                    "creative_id",
                    "creative_name",
                    "creative_code",
                    "flight.start_date",
                    "flight.end_date",
                    "seller_member_name",
                    "seller_member_id",
                    "publisher_name",
                    "publisher_id",
                    "deal_name",
                    "deal_id"
                ],
                "filters": [{"media_type_id": 64}]
            }
        },
    },
    {
        "name": "multi_buyer_seller_deal_metrics",
        "api_cred_var": "xandr_curate_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "bronze_table_name": "multi_buyer_seller_deal_metrics",
        "request_config": {
            "report": {
                "report_type": "multi_buyer_seller_deal_metrics",
                "columns": [
                    "hour",
                    "buyer_member_name",
                    "buyer_seat_name",
                    "buyer_seat_code",
                    "deal_id",
                    "deal_name",
                    "deal_buyer_type",
                    "package_id",
                    "start_date",
                    "imps_matched",
                    "bid_requests",
                    "imps_won",
                    "seller_revenue",
                    "average_net_bid",
                    "average_biased_bid",
                    "average_floor_price",
                    "bid_rate",
                    "ineligible_bid_rate",
                    "gross_win_rate",
                    "net_win_rate",
                    "ask_price",
                    "reject_count",
                    "final_bids",
                    "day",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "curator_analytics",
        "api_cred_var": "xandr_curate_cred",
        "bronze_schema_name": "bronze_et_xandr",
        "max_page_size": 200_000,
        "bronze_table_name": "curator_analytics",
        "request_config": {
            "report": {
                "report_type": "curator_analytics",
                "columns": [
                    "hour",
                    "curated_deal_advertiser_id",
                    "curated_deal_advertiser_name",
                    "curated_deal_id",
                    "curated_deal_insertion_order_id",
                    "curated_deal_line_item_id",
                    "curated_deal_insertion_order_name",
                    "curated_deal_line_item_name",
                    "curated_deal_name",
                    "curator_member_id",
                    "curator_member_name",
                    "media_type",
                    "size",
                    "curator_margin",
                    "curator_net_media_cost",
                    "curator_revenue",
                    "curator_tech_fees",
                    "curator_total_cost",
                    "imps",
                    "viewdef_viewed_imps",
                    "viewdef_view_rate",
                    "viewed_imps",
                    "day",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
    {
        "name": "medicx_curator_segment_performance",
        "api_cred_var": "medicx_xandr_curate_cred",
        "bronze_schema_name": "bronze_medicx",
        "max_page_size": 200_000,
        "bronze_table_name": "curator_segment_performance",
        "request_config": {
            "report": {
                "report_type": "curator_segment_performance",
                "columns": [
                    "day",
                    "curated_deal_id",
                    "segment_id",
                    "segment_name",
                    "targeted_impressions",
                ],
                "timezone": "UTC",
                "use_cache": False,
            }
        },
    },
]
