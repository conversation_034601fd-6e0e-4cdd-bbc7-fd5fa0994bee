from airflow.models import <PERSON><PERSON>perator
from airflow.utils.decorators import apply_defaults
from airflow.utils.db import provide_session
from airflow.models import DagRun, TaskInstance
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pytz

class AlertIfSkippedOperator(BaseOperator):
    """
    Custom Airflow operator to alert via Slack if a specified task has not been successfully executed
    within the past specified number of days.

    This operator checks if a specific task (`inspected_task_id`) has successfully run in any of the
    DAG runs that occurred within a timeframe of twice the specified number of days. If the task
    has not run successfully in this timeframe, an alert message is sent to a specified Slack channel.

    The operator constructs a Slack message with the following details:
    - DAG ID
    - Execution date
    - URL to the DAG in Airflow
    - Task ID
    - Custom alert message

    Args:
        inspected_task_id (str): The task ID to inspect for successful execution.
        days (int): Number of days to check for the task's successful execution.
        msg (str): Custom message to include in the Slack alert.
        slack_channel_id (str, optional): The Slack channel ID where the alert will be sent. If not provided,
                                           the default Slack webhook connection is used.

    Attributes:
        inspected_task_id (str): The task ID being inspected.
        days (int): Number of days to check.
        msg (str): Custom alert message.
        slack_channel_id (str, optional): Slack channel ID for sending alerts.

    Methods:
        execute(context, session=None): Checks the task's execution status and sends an alert to Slack if
                                        the task has not run successfully within the specified timeframe.
    """
    @apply_defaults
    def __init__(self, inspected_task_id, days, msg, slack_channel_id=None, *args, **kwargs):
        super(AlertIfSkippedOperator, self).__init__(*args, **kwargs)
        self.inspected_task_id = inspected_task_id
        self.days = days
        self.msg = msg
        self.slack_channel_id = slack_channel_id

    @provide_session
    def execute(self, context, session=None):
        dag_id = context['dag'].dag_id
        utc = pytz.UTC
        end_date = utc.localize(datetime.utcnow())
        start_date = end_date - timedelta(days=self.days)

        # Get interval X2 to be able to confirm that the previous one had run
        dag_runs = session.query(DagRun).filter(
            DagRun.dag_id == dag_id,
            DagRun.execution_date.between(start_date - timedelta(days=self.days), end_date)
        ).all()

        # This clause is in here to prevent alert during the first period the dag is in production
        if min([dr.execution_date for dr in dag_runs]) > (end_date - timedelta(days=self.days)):
            print("Not a long enough timeframe present yet, no action taken")
            return

        # Check if task has run in all DAG runs
        task_ran = False
        for dag_run in dag_runs:
            task_instances = session.query(TaskInstance).filter(
                TaskInstance.dag_id == dag_id,
                TaskInstance.task_id == self.inspected_task_id,
                TaskInstance.execution_date == dag_run.execution_date
            ).all()

            for ti in task_instances:
                print(ti.state)
                if ti.state == 'success' :
                    task_ran = True
                    break

        if task_ran:
            self.log.info(f"The task {self.inspected_task_id} has run at some point in the last {self.days} days.")
            return

        else:
            slack_msg = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f":large_yellow_circle: Warning: {context.get('task_instance').dag_id}",
                        "emoji": True,
                    },
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Execution Time*: {context.get('execution_date')}",
                    },
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Dag URL*: https://airflow.k8s.eltoro.com/dags/{context.get('task_instance').dag_id}/grid",
                    },
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Task*: {self.task_id}",
                    },
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Message*: {self.msg}",
                    },
                },

            ]

            warning_alert = SlackWebhookOperator(
                task_id="slack_success_alert",
                slack_webhook_conn_id=f"slack_alert_conn_{Variable.get('environment')}",
                blocks=slack_msg,
            )
            warning_alert.execute(context)
            self.log.info(f"Alert sent to Slack channel {self.slack_channel_id}.")

