from etdag import ETDAG
from airflow.decorators import task
from datetime import datetime, timedelta
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.providers.amazon.aws.hooks.step_function import StepFunctionHook
from airflow.providers.amazon.aws.operators.step_function import (
    StepFunctionStartExecutionOperator,
)
from airflow.operators.python import get_current_context
from airflow.providers.amazon.aws.sensors.step_function import (
    StepFunctionExecutionSensor,
)
import json
from airflow.models import Variable
from airflow.decorators import task_group
from airflow.operators.trigger_dagrun import TriggerDagRunOperator


docs = """
    DAG: `new_mover_executor`
    Schedule: 10pm EDT daily
    Owner: Panama
    
    ## Summary
    * Checks whitehat's sftp for new mover file
    * If the file is present then it executes the new mover step functions
    * If the file is not present then nothing happens
    
    ## Time Dependent
    Dag runs are time dependent as the the new mover file is tagged with the current date

    ## Failures
    In the event of a failure try to clear the task that failed. Would not recommend doing a whole new dag run

    ## Escalation
    If rerunning the Dag <NAME_EMAIL> or <EMAIL>

    ## Dependencies
    The souce data comes from a whitehat sft server.
   
   ## Results
    * New Mover runs
"""

with ETDAG(
    dag_id="new_mover_executor",
    catchup=False,
    default_args={
        "owner": "Panama",
        "retries": 3,
        "retry_delay": timedelta(minutes=30),
    },
    is_paused_upon_creation=True,
    schedule_interval="0 3 * * *",  # 10 PM everyday
    start_date=datetime(2023, 10, 22),
    max_active_runs=1,
    tags=["newmover"],
    extra_slack_conn_ids_to_message=["slack_new_mover_prod"],
) as dag:
    search_date_string = (
        "{{ (execution_date - macros.timedelta(days=1)).strftime('%Y%m%d') }}"
    )

    @task.short_circuit
    def check_for_new_mover_files(search_date_string):
        """
        Will search for files in the whitehat server with the search_date_string, eg 20210203.
        If it finds 3 files it means that all new mover files are in the server.
        If it finds 0 files it means that the files for the day are just not there.
        If it finds any other number it errors out, the task retries later,
        by then hopefully the right number of files has been uploaded
        """
        print(f"searching for: {search_date_string}")
        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        files = sftp_hook.list_directory("/TestACH/")
        count_of_nm_files = len(
            [s for s in files if (search_date_string in s) and s.endswith(".zip")]
        )
        if count_of_nm_files >= 3:
            return True
        elif count_of_nm_files == 0:
            return False
        else:
            raise Exception(
                f"Wrong number of new mover files found: {count_of_nm_files}"
            )

    check_for_new_mover_files_instance = check_for_new_mover_files(search_date_string)

    trigger_other_dag = TriggerDagRunOperator(
        task_id="trigger_new_mover_dag",
        trigger_dag_id="new_movers",
        wait_for_completion=False,
    )

    check_for_new_mover_files_instance >> trigger_other_dag
