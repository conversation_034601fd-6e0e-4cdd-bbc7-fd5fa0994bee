import base64
import hashlib
import json
import logging
import tempfile
import zipfile
from dataclasses import dataclass
from datetime import datetime
from datetime import datetime as dt
from datetime import timed<PERSON>ta
from tempfile import NamedTemporaryFile

import boto3
import pandas as pd
import requests
from airflow.decorators import task
from airflow.models import Variable
from airflow.models.dag import DAG
from airflow.models.param import Param
from airflow.models.xcom_arg import XComArg
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.sftp.hooks.sftp import SFTPHook

logging.basicConfig()
logger = logging.getLogger(__name__)

docs = """
    DAG: `new_mover_executor`
    Schedule: 10pm EDT daily
    Owner: <PERSON> Morton

    ## Summary
    * Checks ACHco-op's sftp for new mover file.
    * If the file is present then it executes the new mover step functions.
    * If the file is not present then nothing happens.

    ## Configuration Parameters
    * `file_limit` - This will limit the number of lines that get uploaded to quote.
    * `mode` - The mode to run the DAG in. The default is "live" which will run the DAG
        in production mode. The "test" mode will run the DAG in test mode. Test mode uses
        the nextgen prod endpoint to get real data. This can be used to test the DAG using
        production data in the "dev" environment which allows for comparing the results
        without affecting production data for semgentor, mongo, and starburst.

    ## Time Dependent
    Dag runs are time dependent as the the new mover file is tagged with the current date.

    ## Failures
    In the event of a failure clear the task that failed.

    ## Escalation
    If rerunning the Dag <NAME_EMAIL>.

    ## Dependencies
    The souce data comes from a whitehat sft server.

   ## Results
    * The annotated results are stored in `s3.dev_data_analytics.new_movers` and
        `olympus.dev_data_analytics.new_mover` Starburst tables. The files for the
        external table are located at `s3://et-data-staging/external/new_movers`.
    * The segments are uploaded as sqlite database files to;
      * `s3://newmover/prod/segments/bulk_segments.db`
      * `s3://newmover/prod/segments/bulk_segments_renters.db`
      * `s3://newmover/prod/segments/bulk_segments_owners.db`
    * The segmentor is notified that the segments are ready for processing.
    * The matches are uploaded to the ACHco-op sftp server to path `/TestACH/Matchbacks`.
    * An email is sent to ACHco-op product owner with the results of the process.
    * The following V2 Mongo `movers` database collections get fully replaced.
      * cnm_matches
      * pending_matches
      * pre_matches
      * cnm_zipcodes
      * pending_zipcodes
      * pre_zipcodes
"""

params = {
    "file_limit": Param(type=["null", "integer"], default=None),
    "search_date": Param(type=["null", "integer"], default=None),
    "mode": Param(type=["null", "string"], default="live"),
}

# ---------------------
# Utils
# ---------------------

def get_secret_value(secret: str):
    session = boto3.session.Session()
    client = session.client(
        service_name="secretsmanager",
        region_name="us-east-1",
    )
    response = client.get_secret_value(SecretId=secret)

    if "SecretString" in response:
        value = response["SecretString"]
    else:
        value = base64.b64decode(response["SecretBinary"])

    secret_value = json.loads(value)

    return secret_value


with DAG(
    dag_id="load_new_movers_files",
    description="Load new mover files to S3",
    schedule=None,
    catchup=False,
    default_args={
        "owner": "Clay Morton",
        "retries": 0,
        "retry_delay": timedelta(minutes=30),
    },
    render_template_as_native_obj=True,
    is_paused_upon_creation=True,
    start_date=datetime(2024, 11, 7),
    max_active_runs=1,
    params=params,
    tags=["new_movers"],
) as dag:

    # ---------------------
    # Configs
    # ---------------------

    @dataclass
    class Configs:
        def __init__(self, **kwargs):
            # Accept dict as input.
            for k, v in kwargs.items():
                setattr(self, k, v)

        env: str
        search_date: int
        file_types: list[str]
        segments: dict
        segmentor_endpoints: list[str]
        segmentor_secret: str
        remote_file_names: list[str]
        remote_file_paths: list[str]
        staging_file_paths: list[str]
        annotated_file_paths: list[str]
        selected_file_paths: list[str]
        matches_file_paths: list[str]
        column_headers: list[dict]
        new_mover_tables: dict
        mongo_url: str
        org_id: str
        quote_env: str

    @task()
    def generate_configs(
        search_date: int,
        mode: str = "live",
        **kwargs,
    ) -> XComArg:
        """
        Generates configuration settings for the New Mover datasets.

        The `config` object contains variables that facilitate all actions on the datasets.
        1. The datasets as defined by the source files; `file_types`.
        2. The file paths used during loading and transforming the data.
        3. The column header declarations used by the quote api.
        4. The starburst tables and the queries to manage them.
        5. The `segment_codes` used to generate the segmentor data.

        The `file_types` list is used to populate the file path variables which dictates
        an order of operations that must be abided by and so the order of the `segment_codes`
        must follow the same order.

        Args:
            search_date (int): The date to search for files.
            mode (str): If set to "test" then quote will occur in the prod environment so that
                real data can be used to asses the result of the New Mover processes.
            **kwargs: Additional parameters.

        Returns:
            XComArg: The configuration settings.
        """
        # Override the search date if it is passed in as a user defined parameter.
        test_search_date: int | None = kwargs["params"].get("search_date", None)
        if test_search_date is not None:
            search_date: int = test_search_date
        logger.debug(f"Search Date :: {search_date} --->")

        # ACHco-op SFTP server file names/paths.
        file_types = ["cnm", "premover", "pending"]
        remote_file_names = [f"{search_date}-{ft}.zip" for ft in file_types]
        remote_file_paths = [
            f"/TestACH/{search_date}-{ft}.zip" for ft in file_types
        ]

        # These are the specified columns for all of new mover files coming from ACHco-op
        # SFTP server. We want to preserve them all so that the annotated file produced
        # by platform api quote process will have all of the orginal columns.
        # [TODO] Swap column headers for previous address dataset.
        column_headers = [
            {"index": 0, "value": "id", "type": "keep"},
            {"index": 1, "value": "zip", "type": "zip"},
            {"index": 2, "value": "full_address", "type": "address1"},
            {"index": 3, "value": "house_number", "type": "keep"},
            {"index": 4, "value": "street_name", "type": "keep"},
            {"index": 5, "value": "street_suffix", "type": "keep"},
            {"index": 6, "value": "city", "type": "city"},
            {"index": 7, "value": "st", "type": "state"},
            {"index": 8, "value": "plus4", "type": "zip4"},
            {"index": 9, "value": "dwellingTypeCode", "type": "keep"},
            {"index": 10, "value": "homeOwnerFlag", "type": "keep"},
            {"index": 11, "value": "income", "type": "keep"},
            {"index": 12, "value": "date", "type": "keep"},
        ]
        previous_address_column_headers = [
            {"index": 0, "value": "id", "type": "keep"},
            {"index": 1, "value": "zip", "type": "zip"},
            {"index": 2, "value": "full_address", "type": "address1"},
            {"index": 3, "value": "house_number", "type": "keep"},
            {"index": 4, "value": "street_name", "type": "keep"},
            {"index": 5, "value": "street_suffix", "type": "keep"},
            {"index": 6, "value": "city", "type": "city"},
            {"index": 7, "value": "st", "type": "state"},
            {"index": 8, "value": "plus4", "type": "zip4"},
            {"index": 9, "value": "date", "type": "keep"},
            {"index": 10, "value": "prevadd_zip", "type": "keep"},
            {"index": 11, "value": "prevadd_full_address", "type": "keep"},
            {"index": 12, "value": "prevadd_house_number", "type": "keep"},
            {"index": 13, "value": "prevadd_street_name", "type": "keep"},
            {"index": 14, "value": "prevadd_street_suffix", "type": "keep"},
            {"index": 15, "value": "prevadd_city", "type": "keep"},
            {"index": 16, "value": "prevadd_st", "type": "keep"},
            {"index": 17, "value": "prevadd_plus4", "type": "keep"},
            {"index": 18, "value": "source_code", "type": "keep"},
            {"index": 19, "value": "Hflag", "type": "keep"},
        ]

        # Segmentor configs --->
        env = Variable.get("environment")
        segments = {
            "bucket": "newmover",
            "segment_keys": {
                "all_movers": f"{env}/segments/bulk_segments.db",
                "renters": f"{env}/segments/bulk_segments_renters.db",
                "owners": f"{env}/segments/bulk_segments_owners.db",
            },
            "checksum_keys": {
                "all_movers": f"{env}/segments/bulk_segments.db.md5sum",
                "renters": f"{env}/segments/bulk_segments_renters.db.md5sum",
                "owners": f"{env}/segments/bulk_segments_owners.db.md5sum",
            },
            "codes": {
                "cnm": "113",
                "premover": "111",
                "pending": "112",
            },
        }

        # Environment specific configs --->
        # Override the environment if in test mode.
        if mode == "test":
            env = "dev"
        bucket = ""
        external_schema = ""
        bronze_schema = ""
        matches_dir = ""
        mongo_url = ""
        segmentor_endpoints = []
        segmentor_secret = ""
        org_id = ""
        quote_env = env
        if env == "dev":
            bucket = "et-data-dev"
            external_schema = "dev_data_analytics"
            bronze_schema = "dev_data_analytics"
            matches_dir = "dev/Matchbacks"
            mongo_url = "mongodb://10.0.2.168:27017"
            segmentor_endpoints = [
                "http://awseast-dev-a-deployingress01.dev.middleearth.eltoro.com:8087/v1/segmentor/newmover/notify",
                "https://viant-deploy-ingress.k8s.dev.eltoro.com/v1/segmentor/newmover/notify",
            ]
            segmentor_secret = "bidder_deploy_ingress_auth_key_dev"
            org_id = "crrgefqmoj5s73bmec4g"
        if env == "prod":
            bucket = "et-data-staging"
            external_schema = "external_new_movers"
            bronze_schema = "bronze_new_movers"
            matches_dir = "Matchbacks"
            mongo_url = "mongodb-2.middleearth.eltoro.com:27000"
            segmentor_endpoints = [
                "http://awseast-prod-a-deployingress01.prod.middleearth.eltoro.com:8087/v1/segmentor/newmover/notify",
                "https://viant-deploy-ingress.k8s.eltoro.com/v1/segmentor/newmover/notify",
            ]
            segmentor_secret = "BIDDER_AUTH_KEY"
            org_id = "csch7v3kv6sc73cditg0"
        if mode == "test":
            org_id = "csch7v3kv6sc73cditg0"
            quote_env = "prod"

        # File path configs --->
        # `transfer_date` is formatted with dashes; ex. 2024-10-08.
        transfer_date = str(dt.date(dt.strptime(str(search_date), "%Y%m%d")))
        staging_file_paths = [
            f"s3://{bucket}/staging/new_movers/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}.csv"
            for ft in file_types
        ]
        annotated_file_paths = [
            f"s3://{bucket}/external/new_movers/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}-annotated.csv"
            for ft in file_types
        ]
        selected_file_paths = [
            f"s3://{bucket}/external/new_movers_selected/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}-selected.csv"
            for ft in file_types
        ]
        matches_file_paths = [
            f"/TestACH/{matches_dir}/{search_date}-{ft}-matches.json"
            for ft in file_types
        ]

        # Table configs.
        new_mover_tables = {
            "new_movers_external_table": {
                "schema": external_schema,
                "location": f"{bucket}/external/new_movers",
            },
            "new_movers_bronze_table": {
                "schema": bronze_schema,
                "location": f"{bucket}/warehouse/bronze_eltoro_audiences.db/new_movers",
            },
        }

        return {
            "env": env,
            "search_date": search_date,
            "file_types": file_types,
            "segments": segments,
            "segmentor_endpoints": segmentor_endpoints,
            "segmentor_secret": segmentor_secret,
            "remote_file_names": remote_file_names,
            "remote_file_paths": remote_file_paths,
            "staging_file_paths": staging_file_paths,
            "annotated_file_paths": annotated_file_paths,
            "selected_file_paths": selected_file_paths,
            "matches_file_paths": matches_file_paths,
            "column_headers": column_headers,
            "new_mover_tables": new_mover_tables,
            "mongo_url": mongo_url,
            "org_id": org_id,
            "quote_env": quote_env,
        }

    # ---------------------
    # Tasks
    # ---------------------

    @task.short_circuit()
    def check_new_mover_files(configs: XComArg) -> bool:
        """
        Check New Mover files with the search_date, e.g., 20210203.

        If the files matching the search_date aren't found this task will short circuit
        and the subsequent tasks will not run.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - remote_file_names (list[str]): List of remote file names to check on
                    the SFTP server.

        Returns:
            bool: True if the required files are found on the SFTP server, False otherwise.
        """
        remote_file_names = Configs(**configs).remote_file_names

        logger.info(f"Searching for: {remote_file_names}")

        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        files = sftp_hook.list_directory("/TestACH/")

        logger.debug(f"Files on server: \n {files}")

        logger.debug(f"Remote file names: {remote_file_names}")

        if set(remote_file_names).issubset(files):
            return True
        else:
            logger.info(f"Latest New Mover files not found.")
            return False

    @task()
    def stage_new_mover_files(configs: XComArg, **kwargs):
        """
        Get recent New Mover files from the ACHco-op FTP server and convert them to CSV
        formatted files.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - remote_file_paths (list[str]): List of remote file paths to download from
                    the SFTP server.
                - staging_file_paths (list[str]): List of local staging file paths to save
                    the downloaded files.
            **kwargs: Additional parameters, including `file_limit`.

        Returns:
            None
        """
        conf = Configs(**configs)
        remote_file_paths = conf.remote_file_paths
        staging_file_paths = conf.staging_file_paths

        tmp_dir = tempfile.TemporaryDirectory()

        logger.debug("***************************************************")
        logger.debug("download_new_mover_files: --->>")
        logger.debug(remote_file_paths)
        logger.debug("***************************************************")

        file_limit = kwargs["params"].get("file_limit", None)
        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        for remote_path, staging_path in zip(
            remote_file_paths, staging_file_paths
        ):
            with NamedTemporaryFile(suffix=".zip") as temp_file:
                sftp_hook.retrieve_file(
                    remote_full_path=remote_path, local_full_path=temp_file.name
                )
                with zipfile.ZipFile(temp_file.name, "r") as zip_ref:
                    # Extract from zip file.
                    zip_ref.extractall(tmp_dir.name)

                    dest_path = f"{tmp_dir.name}/{zip_ref.namelist()[0]}"
                    logger.debug(
                        f"Unzipped {dest_path}  ------------------------>"
                    )

            # If json file convert to csv file and upload to S3.
            try:
                # Read json file.
                df = pd.read_json(dest_path, dtype="string").iloc[:file_limit]
                df.to_csv(staging_path, index=False)

            except Exception as e:
                logger.debug("------------------------------------------>")
                logger.debug(f"Error -- {e}")
                logger.debug(f"Unzipped path -- {dest_path}")
                logger.debug("------------------------------------------>")
                if file_limit is not None:
                    df = pd.read_csv(dest_path, dtype="string").iloc[
                        :file_limit
                    ]
                    # Upload to S3.
                    df.to_csv(staging_path, index=False)

        tmp_dir.cleanup()

    search_date = (
        "{{ (execution_date - macros.timedelta(days=1)).strftime('%Y%m%d') }}"
    )
    mode = "{{ dag_run.conf['mode'] }}"

    configs = generate_configs(search_date, mode)

    new_files_check = check_new_mover_files(configs)

    stage_files = stage_new_mover_files(configs)

    configs >> new_files_check >> stage_files
