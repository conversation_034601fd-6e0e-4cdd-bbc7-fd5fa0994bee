# New Mover Dag

New Mover is a product that creates audiences from people who have recently moved. This is a great way to reach people who are in the market for new products and services.

On a weekly basis ACHco-op uploads three sets of New Mover files to their SFTP server. 
* `premover` - People who are planning to move.
* `cnm` - People who have completed their move.
* `pending` - People who have moved but are not yet in their new home.

These files are sent to Platform services quote API to get quotes for the new movers. The quotes are then used to create audiences and segments for targeted marketing.

## Tasks
1. **check_new_mover_files**
    - **Summary**: This task connects to the SFTP server and verifies if the expected new mover files are available. If the files are not found, the DAG run is short-circuited to prevent further execution.

2. **staging_new_mover_files**
    - **Summary**: This task retrieves the new mover files from the SFTP server, extracts them if they are in ZIP format, and converts them to CSV files for further processing.

3. **quote_file_group**
    - **Summary**: This task group reads the CSV file in chunks, uploads each chunk to the target API, and creates an audience. The annotated and selected files are then downloaded for the chunks to be consolidated. The consolidated files are then stored in S3.

4. **load_starburst_tables**
    - **Summary**: This task runs SQL queries to create external and bronze tables in Starburst, syncs metadata, and inserts data into the bronze tables, ensuring that the new mover data is properly stored and accessible.

5. **generate_segments**
    - **Summary**: This task builds a SQLite database for every IP matched with a quote, assigns segment codes to zip codes, and uploads the database to S3, facilitating targeted marketing efforts.

6. **notify_segmentors**
    - **Summary**: This task notifies the Segmentor service that new New Mover segments are available so they can use the new data when bidding.

7. **update_v2_mongo_collections**
    - **Summary**: This task replaces existing MongoDB collections with new data, ensuring that the collections are dropped and rebuilt atomically to avoid inconsistencies and maintain data integrity.

8. **upload_matches_to_ach_coop**
    - **Summary**: This task converts matched records to JSON format and uploads them to the ACH co-op SFTP server, ensuring that the matched data is shared with the appropriate stakeholders.

9. **email_ach_coop**
    - **Summary**: This task compiles statistics for each state and sends an email summarizing the results, providing stakeholders with insights into the new mover data.

## DAG Flow

The tasks are executed in the following order:

1. `check_new_mover_files`
2. `stage_new_mover_files`
3. `quote_file_group`
    1. `quote_cnm_file`
    2. `quote_pre_file`
    3. `quote_pending_file`
4. `load_starburst_tables`, `generate_segments`
5. `update_v2_mongo_collections`
6. `upload_matches_to_ach_coop`
7. `email_ach_coop`

## Testing

This Dag can be run from the command line for testing purposes. Make sure the environment variable `environment` is set to `dev`.

If the `mode` attribute is set to "test" this will enable using production quote and therefore will return actual geocoded data.

### Virtual Environment
You will need to setup a virtual environment with the following dependencies.

``` text
fsspec
s3fs
boto3
botocore
apache-airflow==2.10.0
apache-airflow-providers-amazon
apache-airflow-providers-common-compat
apache-airflow-providers-common-io
apache-airflow-providers-common-sql
apache-airflow-providers-fab
apache-airflow-providers-ftp
apache-airflow-providers-http
apache-airflow-providers-imap
apache-airflow-providers-postgres
apache-airflow-providers-sftp
apache-airflow-providers-slack
apache-airflow-providers-smtp
apache-airflow-providers-sqlite
apache-airflow-providers-ssh
apache-airflow-providers-trino
astronomer-providers
backoff
black
duckdb
trino
virtualenv
pymongo==3.13.0
```

### Variables and Connections

In the `new_mover` directory create a `variables.json` file with the following content.

```json
{
  "environment": "dev"
}
```

You will also need to create a `connections.json` file with the following content in the same directory.

``` json
{
  "new_mover_whitehat_sftp": {
    "conn_type": "sftp",
    "description": "",
    "login": "ACH",
    "password": "<password>",
    "host": "sftp.achcoop.com",
    "port": 22,
    "schema": "",
    "extra": { "no_host_key_check": true }
  },
  "starburst": {
    "conn_type": "trino",
    "login": "<your-login-name>",
    "host": "starburst.k8s.eltoro.com",
    "port": 443,
    "extra": {
      "protocol": "https",
      "auth": "jwt",
      "jwt__token": "<your-starburst-session-token>" 
    }
  }
}
```
Ask someone on the Analytics Engineering team for the password for the `new_mover_whitehat_sftp` server.

You can visit the url `https://starburst.k8s.eltoro.com/ui/token` in a browser to get the current Starburst session token. This will need to be updated everytime your ui session expires.

### Running Tests

From the root of this repo run the following command;
`PYTHONPATH="." python new_mover/new_mover_dag.py`.

## Verification Locations for New Mover Dag

To verify the results of running the New Mover Dag, check the following locations based on the environment (`dev` or `prod`):

### Dev Mode

#### S3
- **Staging Files**:
  - `s3://et-data-dev/staging/new_movers/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm.csv`
  - `s3://et-data-dev/staging/new_movers/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover.csv`
  - `s3://et-data-dev/staging/new_movers/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending.csv`
- **Annotated Files**:
  - `s3://et-data-dev/external/new_movers/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm-annotated.csv`
  - `s3://et-data-dev/external/new_movers/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover-annotated.csv`
  - `s3://et-data-dev/external/new_movers/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending-annotated.csv`
- **Selected Files**:
  - `s3://et-data-dev/external/new_movers_selected/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm-selected.csv`
  - `s3://et-data-dev/external/new_movers_selected/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover-selected.csv`
  - `s3://et-data-dev/external/new_movers_selected/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending-selected.csv`
- **Segments**:
  - `s3://newmover/dev/segments/bulk_segments.db`
  - `s3://newmover/dev/segments/bulk_segments_renters.db`
  - `s3://newmover/dev/segments/bulk_segments_owners.db`
  - `s3://newmover/dev/segments/bulk_segments.db.mdbsum`
  - `s3://newmover/dev/segments/bulk_segments_renters.db.mdbsum`
  - `s3://newmover/dev/segments/bulk_segments_owners.db.mdbsum`
   
#### SFTP
- **Matches Files**:
  - `/TestACH/dev/Matchbacks/<search_date>-cnm-matches.json`
  - `/TestACH/dev/Matchbacks/<search_date>-premover-matches.json`
  - `/TestACH/dev/Matchbacks/<search_date>-pending-matches.json`

#### Starburst Tables
- **External**:
  - `dev_data_analytics.new_movers.external_table`
- **Bronze**:
  - `dev_data_analytics.new_movers.bronze_table`

#### MongoDB Collections
- **Collections**:
  - `mongodb://**********:27017/movers/cnm_matches`
  - `mongodb://**********:27017/movers/pending_matches`
  - `mongodb://**********:27017/movers/pre_matches`
  - `mongodb://**********:27017/movers/cnm_zipcodes`
  - `mongodb://**********:27017/movers/pending_zipcodes`
  - `mongodb://**********:27017/movers/pre_zipcodes`

### Prod Mode

#### S3
- **Staging Files**:
  - `s3://et-data-staging/staging/new_movers/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm.csv`
  - `s3://et-data-staging/staging/new_movers/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover.csv`
  - `s3://et-data-staging/staging/new_movers/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending.csv`
- **Annotated Files**:
  - `s3://et-data-staging/external/new_movers/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm-annotated.csv`
  - `s3://et-data-staging/external/new_movers/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover-annotated.csv`
  - `s3://et-data-staging/external/new_movers/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending-annotated.csv`
- **Selected Files**:
  - `s3://et-data-staging/external/new_movers_selected/mover_type=cnm/file_transfer_date=<transfer_date>/<search_date>-cnm-selected.csv`
  - `s3://et-data-staging/external/new_movers_selected/mover_type=premover/file_transfer_date=<transfer_date>/<search_date>-premover-selected.csv`
  - `s3://et-data-staging/external/new_movers_selected/mover_type=pending/file_transfer_date=<transfer_date>/<search_date>-pending-selected.csv`

Replace `<search_date>` and `<transfer_date>`  with the actual search date when verifying the results. This documentation will help anyone reading the README to know exactly where to check for results based on the environment. `search_date` is the numeric representation of the date in the format `YYYYMMDD` and `transfer_date` is the numeric representation of the date in the format `YYYY-MM-DD`. 

- **Segments**:
  - `s3://newmover/prod/segments/bulk_segments.db`
  - `s3://newmover/prod/segments/bulk_segments_renters.db`
  - `s3://newmover/prod/segments/bulk_segments_owners.db`
  - `s3://newmover/prod/segments/bulk_segments.db.mdbsum`
  - `s3://newmover/prod/segments/bulk_segments_renters.db.mdbsum`
  - `s3://newmover/prod/segments/bulk_segments_owners.db.mdbsum`
      
#### SFTP
- **Matchback Files**:
  - `/TestACH/Matchbacks/<search_date>-cnm-matches.json`
  - `/TestACH/Matchbacks/<search_date>-premover-matches.json`
  - `/TestACH/Matchbacks/<search_date>-pending-matches.json`

#### Starburst Tables
- **External**:
  - `external_new_movers.new_movers_external_table`
- **Bronze**:
  - `bronze_new_movers.new_movers_bronze_table`

#### MongoDB Collections
- **Collections**:
  - `mongodb-2.middleearth.eltoro.com:27000/movers/cnm_matches`
  - `mongodb-2.middleearth.eltoro.com:27000/movers/pending_matches`
  - `mongodb-2.middleearth.eltoro.com:27000/movers/pre_matches`
  - `mongodb-2.middleearth.eltoro.com:27000/movers/cnm_zipcodes`
  - `mongodb-2.middleearth.eltoro.com:27000/movers/pending_zipcodes`
  - `mongodb-2.middleearth.eltoro.com:27000/movers/pre_zipcodes`
