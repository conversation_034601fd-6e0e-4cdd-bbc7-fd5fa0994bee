import base64
import hashlib
import json
import logging
import tempfile
import zipfile
from datetime import datetime
from datetime import datetime as dt
from datetime import timed<PERSON>ta
from tempfile import NamedTemporaryFile
from typing import Any
import ipaddress
from ipaddress import IPv4Address, IPv6Address
import struct

import boto3
import pandas as pd
import requests
from botocore.client import Config
from airflow.decorators import task
from airflow.models import Variable
from airflow.models.dag import DAG
from airflow.models.param import Param
from airflow.models.xcom_arg import XComArg
from airflow.providers.smtp.hooks.smtp import SmtpHook
from airflow.operators.empty import EmptyOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.providers.trino.hooks.trino import TrinoHook

from old_dags.quote_file_group import quote_file
from sqlalchemy import create_engine

from old_dags.new_mover.sql.create_table_queries import (
    create_external_new_movers_table,
    create_external_ips_table,
    create_bronze_new_movers_table,
    create_bronze_zip_stats_table,
    create_bronze_segments_table,
)
from old_dags.new_mover.sql.load_table_queries import (
    sync_external_new_movers,
    sync_external_ips,
    insert_bronze_new_movers,
    insert_bronze_zip_stats,
    insert_bronze_segments,
)

logging.basicConfig()
logger = logging.getLogger(__name__)

docs = """
    DAG: `new_mover_executor`
    Schedule: 10pm EDT daily
    Owner: Clay Morton

    ## Summary
    * Checks ACHco-op's sftp server for new mover files.
    * Task `check_new_mover_files` will look for the latest files, if the entire group
        is found then the subsequent tasks are run.

    ## Configuration Parameters
    * `file_limit` - This will limit the number of lines that get uploaded to quote.
    * `search_date` - This will override the current date to search for files.
    * `mode` - The mode to run the DAG in. The default is "live" which will run the DAG
        in production mode. The "test" mode will run the DAG in test mode. Test mode uses
        the nextgen prod endpoint to get real data. This can be used to test the DAG using
        production data in the "dev" environment which allows for comparing the results
        without affecting production data for semgentor, mongo, and starburst.

    ## Time Dependent
    Dag runs are time dependent as the the new mover file is tagged with the current date.

    ## Failures
    In the event of a failure clear the task that failed.

    ## Escalation
    If rerunning the Dag <NAME_EMAIL>.

    ## Dependencies
    The souce data comes from a whitehat sft server.

   ## Results
    * The annotated results are stored in `s3.dev_data_analytics.new_movers` and
        `olympus.dev_data_analytics.new_mover` Starburst tables. The files for the
        external table are located at `s3://et-data-staging/external/new_movers`.
    * The segments are uploaded as sqlite database files to;
      * `s3://newmover/prod/segments/bulk_segments.db`
    * The segmentor is notified that the segments are ready for processing.
    * The matches are uploaded to the ACHco-op sftp server to path `/TestACH/Matchbacks`.
    * An email is sent to ACHco-op product owner with the results of the process.
"""

params = {
    "file_limit": Param(type=["null", "integer"], default=None),
    "search_date": Param(type=["null", "integer"], default=None),
    "mode": Param(type=["null", "string"], default="live"),
}

# ---------------------
# Utils
# ---------------------


def get_secret_value(secret: str):
    session = boto3.session.Session()
    client = session.client(
        service_name="secretsmanager",
        region_name="us-east-1",
    )
    response = client.get_secret_value(SecretId=secret)

    if "SecretString" in response:
        value = response["SecretString"]
    else:
        value = base64.b64decode(response["SecretBinary"])

    secret_value = json.loads(value)

    return secret_value


with DAG(
    dag_id="new_movers",
    catchup=False,
    default_args={
        "owner": "Clay Morton",
        "retries": 0,
        "retry_delay": timedelta(minutes=30),
    },
    render_template_as_native_obj=True,
    is_paused_upon_creation=True,
    start_date=datetime(2024, 9, 22),
    schedule=None,
    max_active_runs=1,
    params=params,
    tags=["new_movers"],
) as dag:

    # ---------------------
    # Configs
    # ---------------------

    @task()
    def generate_configs(
        search_date: int,
        mode: str = "live",
        **kwargs,
    ) -> XComArg:
        """
        Generates configuration settings for the New Mover datasets.

        The `config` object contains variables that facilitate all actions on the datasets.
        1. The datasets as defined by the source files; `file_types`.
        2. The file paths used during loading and transforming the data.
        3. The column header declarations used by the quote api.
        4. The starburst tables and the queries to manage them.
        5. The `segment_codes` used to generate the segmentor data.

        The `file_types` list is used to populate the file path variables which dictates
        an order of operations that must be abided by and so the order of the `segment_codes`
        must follow the same order.

        Args:
            search_date (int): The date to search for files.
            mode (str): If set to "test" then quote will occur in the prod environment so that
                real data can be used to asses the result of the New Mover processes.
            **kwargs: Additional parameters.

        Returns:
            XComArg: The configuration settings.
        """
        # Override the search date if it is passed in as a user defined parameter.
        test_search_date: int | None = kwargs["params"].get("search_date", None)
        if test_search_date is not None:
            search_date: int = test_search_date
        logger.debug(f"Search Date :: {search_date} --->")

        # ACHco-op SFTP server file names/paths.
        file_types = ["cnm", "premover", "pending"]
        remote_file_names = [f"{search_date}-{ft}.zip" for ft in file_types]
        remote_file_paths = {
            ft: f"/TestACH/{search_date}-{ft}.zip" for ft in file_types
        }
        # These are the specified columns for all of new mover files coming from ACHco-op
        # SFTP server. We want to preserve them all so that the annotated file produced
        # by platform api quote process will have all of the orginal columns.
        premover_pending_column_renames = [
            "id",
            "zip",
            "full_address",
            "house_number",
            "street_name",
            "street_suffix",
            "city",
            "state",
            "zip4",
            "dwelling_type_code",
            "home_owner_flag",
            "income",
            "date",
        ]
        cnm_column_renames = [
            "id",
            "zip",
            "full_address",
            "house_number",
            "street_name",
            "street_suffix",
            "city",
            "state",
            "zip4",
            "date",
            "prevadd_zip",
            "prevadd_full_address",
            "prevadd_house_number",
            "prevadd_street_name",
            "prevadd_street_suffix",
            "prevadd_city",
            "prevadd_state",
            "prevadd_zip4",
            "source_code",
            "home_owner_flag",
        ]
        premover_pending_column_headers = [
            {"index": 0, "value": "id", "type": "keep"},
            {"index": 1, "value": "zip", "type": "zip"},
            {"index": 2, "value": "full_address", "type": "address1"},
            {"index": 3, "value": "house_number", "type": "keep"},
            {"index": 4, "value": "street_name", "type": "keep"},
            {"index": 5, "value": "street_suffix", "type": "keep"},
            {"index": 6, "value": "city", "type": "city"},
            {"index": 7, "value": "state", "type": "state"},
            {"index": 8, "value": "zip4", "type": "zip4"},
            {"index": 10, "value": "home_owner_flag", "type": "keep"},
            {"index": 12, "value": "date", "type": "keep"},
        ]
        cnm_column_headers = [
            {"index": 0, "value": "id", "type": "keep"},
            {"index": 1, "value": "zip", "type": "zip"},
            {"index": 2, "value": "full_address", "type": "address1"},
            {"index": 3, "value": "house_number", "type": "keep"},
            {"index": 4, "value": "street_name", "type": "keep"},
            {"index": 5, "value": "street_suffix", "type": "keep"},
            {"index": 6, "value": "city", "type": "city"},
            {"index": 7, "value": "state", "type": "state"},
            {"index": 8, "value": "zip4", "type": "zip4"},
            {"index": 19, "value": "home_owner_flag", "type": "keep"},
            {"index": 9, "value": "date", "type": "keep"},
            {"index": 10, "value": "prevadd_zip", "type": "keep"},
            {"index": 11, "value": "prevadd_full_address", "type": "keep"},
            {"index": 12, "value": "prevadd_house_number", "type": "keep"},
            {"index": 13, "value": "prevadd_street_name", "type": "keep"},
            {"index": 14, "value": "prevadd_street_suffix", "type": "keep"},
            {"index": 15, "value": "prevadd_city", "type": "keep"},
            {"index": 16, "value": "prevadd_state", "type": "keep"},
            {"index": 17, "value": "prevadd_zip4", "type": "keep"},
            {"index": 18, "value": "source_code", "type": "keep"},
        ]
        columns = {
            "cnm": {
                "headers": cnm_column_headers,
                "renames": cnm_column_renames,
            },
            "premover": {
                "headers": premover_pending_column_headers,
                "renames": premover_pending_column_renames,
            },
            "pending": {
                "headers": premover_pending_column_headers,
                "renames": premover_pending_column_renames,
            },
        }

        # Segmentor configs --->
        env = Variable.get("environment")

        # Environment specific configs --->
        # Override the environment if in test mode.
        if mode == "test":
            env = "dev"

        segments = {
            "bucket": "newmover",
            "segment_key": f"{env}/segments/bulk_segments.db",
            "checksum_key": f"{env}/segments/bulk_segments.db.md5sum",
            "archive_prefix": f"{env}/toSegmentors",
            "codes": {
                "cnm": "112",
                "premover": "111",
                "pending": "113",
                "owners": "114",
                "renters": "115",
            },
        }

        bucket = ""
        external_schema = ""
        bronze_schema = ""
        matches_dir = ""
        mongo_url = ""
        segmentor_endpoints = []
        segmentor_secret = ""
        org_id = ""
        quote_env = env
        if env == "dev":
            bucket = "et-data-dev"
            external_schema = "dev_data_analytics"
            bronze_schema = "dev_data_analytics"
            matches_dir = "dev/Matchbacks"
            mongo_url = "mongodb://10.0.2.168:27017"
            segmentor_endpoints = [
                "http://awseast-dev-a-deployingress01.dev.middleearth.eltoro.com:8087/v1/segmentor/newmover/notify",
            ]
            segmentor_secret = "bidder_deploy_ingress_auth_key_dev"
            org_id = "crrgefqmoj5s73bmec4g"
        if env == "prod":
            bucket = "et-data-staging"
            external_schema = "external_new_movers"
            bronze_schema = "bronze_new_movers"
            matches_dir = "Matchbacks"
            mongo_url = "mongodb-0.middleearth.eltoro.com:27000"
            segmentor_endpoints = [
                "http://awseast-prod-a-deployingress01.prod.middleearth.eltoro.com:8087/v1/segmentor/newmover/notify",
            ]
            segmentor_secret = "BIDDER_AUTH_KEY"
            org_id = "csch7v3kv6sc73cditg0"
        # Test mode will use prod quote so that the actual data can be verified.
        if mode == "test":
            org_id = "csch7v3kv6sc73cditg0"
            quote_env = "prod"

        # File path configs --->
        # `transfer_date` is formatted with dashes; ex. 2024-10-08.
        transfer_date = str(dt.date(dt.strptime(str(search_date), "%Y%m%d")))
        staging_file_paths = {
            ft: f"s3://{bucket}/staging/new_movers/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}.csv"
            for ft in file_types
        }
        annotated_file_paths = {
            ft: f"s3://{bucket}/external/new_movers/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}-annotated.csv"
            for ft in file_types
        }
        selected_file_paths = {
            ft: f"s3://{bucket}/external/new_movers_selected/mover_type={ft}/file_transfer_date={transfer_date}/{transfer_date}-{ft}-selected.csv"
            for ft in file_types
        }
        matches_file_paths = {
            ft: f"/TestACH/{matches_dir}/{search_date}-{ft}-matches.json"
            for ft in file_types
        }

        # Table configs.
        new_mover_tables = {
            "new_movers_external_table": {
                "schema": external_schema,
                "location": f"{bucket}/external/new_movers",
            },
            "ips_external_table": {
                "schema": external_schema,
                "location": f"{bucket}/external/new_movers_selected",
            },
            "new_movers_bronze_table": {
                "schema": bronze_schema,
                "location": f"{bucket}/warehouse/bronze_eltoro_audiences.db/new_movers",
            },
            "zip_stats_bronze_table": {
                "schema": bronze_schema,
                "location": f"{bucket}/warehouse/bronze_eltoro_audiences.db/new_movers_zips_stats",
            },
            "segments_bronze_table": {
                "schema": bronze_schema,
                "location": f"{bucket}/warehouse/bronze_eltoro_audiences.db/new_movers_segments",
            },
        }

        return {
            "env": env,
            "search_date": search_date,
            "file_types": file_types,
            "segments": segments,
            "segmentor_endpoints": segmentor_endpoints,
            "segmentor_secret": segmentor_secret,
            "remote_file_names": remote_file_names,
            "remote_file_paths": remote_file_paths,
            "staging_file_paths": staging_file_paths,
            "annotated_file_paths": annotated_file_paths,
            "selected_file_paths": selected_file_paths,
            "matches_file_paths": matches_file_paths,
            "columns": columns,
            "new_mover_tables": new_mover_tables,
            "mongo_url": mongo_url,
            "org_id": org_id,
            "quote_env": quote_env,
        }

    # ---------------------
    # Tasks
    # ---------------------

    @task.short_circuit()
    def check_new_mover_files(configs: dict[str, Any]) -> bool:
        """
        Check New Mover files with the search_date, e.g., 20210203.

        If the files matching the search_date aren't found this task will short circuit
        and the subsequent tasks will not run.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - remote_file_names (list[str]): List of remote file names to check on
                    the SFTP server.

        Returns:
            bool: True if the required files are found on the SFTP server, False otherwise.
        """
        remote_file_names = configs["remote_file_names"]

        logger.info(f"Searching for: {remote_file_names}")

        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        files = sftp_hook.list_directory("/TestACH/")

        logger.debug(f"Files on server: \n {files}")

        logger.debug(f"Remote file names: {remote_file_names}")

        if set(remote_file_names).issubset(files):
            return True
        else:
            logger.info(f"Latest New Mover files not found.")
            return False

    @task()
    def stage_new_mover_files(configs: dict[str, Any], **kwargs):
        """
        Get recent New Mover files from the ACHco-op FTP server and convert them to CSV
        formatted files.

        Args:
            configs (dict[str,Any]): Configuration settings for the New Mover datasets.
                - file_types (list[str]): List of New Mover dataset names.
                - remote_file_paths (dict[str, str]): Dict of remote file paths to download from
                    the SFTP server with file type as the key.
                - staging_file_paths (dict[str, str]): Dict of local staging file paths to save
                    the downloaded files, with file type as key.
                - column_headers (dict[str,dict[str,str|int]]): Dict of column specifications formatted
                    for quote service, but will be used hear to transform the column names.
            **kwargs: Additional parameters, including `file_limit`.

        Returns:
            None
        """
        remote_file_paths: dict[str, str] = configs["remote_file_paths"]
        staging_file_paths: dict[str, str] = configs["staging_file_paths"]
        file_types: list[str] = configs["file_types"]
        segments_bucket = configs["segments"]["bucket"]
        columns: dict[str, dict[str, str | int]] = configs["columns"]
        search_date = configs["search_date"]
        env = configs["env"]

        s3_hook = S3Hook(aws_conn_id="aws_default")

        tmp_dir = tempfile.TemporaryDirectory()

        logger.info("***************************************************")
        logger.info("download_new_mover_files: --->>")
        logger.info(remote_file_paths.keys())
        logger.info("***************************************************")

        file_limit = kwargs["params"].get("file_limit", None)
        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        for ft in file_types:
            with NamedTemporaryFile(suffix=".zip") as temp_file:
                sftp_hook.retrieve_file(
                    remote_full_path=remote_file_paths[ft],
                    local_full_path=temp_file.name,
                )

                # Upload copy of zip file to S3.
                backup_key = f"{env}/fromClient/{search_date}/{ft.upper()}.zip"
                s3_hook.load_file(
                    filename=temp_file.name,
                    key=backup_key,
                    bucket_name=segments_bucket,
                    replace=True,
                )

                with zipfile.ZipFile(temp_file.name, "r") as zip_ref:
                    # Extract from zip file.
                    zip_ref.extractall(tmp_dir.name)

                    dest_path = f"{tmp_dir.name}/{zip_ref.namelist()[0]}"
                    logger.debug(
                        f"Unzipped {dest_path}  ------------------------>"
                    )

                    # Read json file.
                    df = pd.read_json(dest_path, dtype="string").iloc[
                        :file_limit
                    ]
                    df.columns = columns[ft]["renames"]
                    df.to_csv(staging_file_paths[ft], index=False)

        tmp_dir.cleanup()

    @task()
    def load_starburst_tables(configs: dict[str, Any]):
        """
        Create and load New Mover tables in Starburst.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - new_mover_tables (dict): Configs to be used in creating starburst tables; includes the
                    location and schema information.
                - search_date (int): The date specified in the source New Mover dataset files. This is
                    used to form the `file_transfer_date`, which is one of the partition columns.

        Returns:
            None
        """
        new_mover_tables = configs["new_mover_tables"]
        search_date = configs["search_date"]

        logger.info("***************************************************")
        logger.info("Load Starburst Tables")
        logger.info("***************************************************")

        transfer_date = str(dt.date(dt.strptime(str(search_date), "%Y%m%d")))

        # Run all queries to create tables, sync metada for external hive tables,
        # and insert data into bronze iceberg tables.
        tables = new_mover_tables
        queries = [
            create_external_new_movers_table.format(
                tables["new_movers_external_table"]["schema"],
                tables["new_movers_external_table"]["location"],
            ),
            create_bronze_new_movers_table.format(
                tables["new_movers_bronze_table"]["schema"],
                tables["new_movers_bronze_table"]["location"],
            ),
            create_external_ips_table.format(
                tables["ips_external_table"]["schema"],
                tables["ips_external_table"]["location"],
            ),
            create_bronze_zip_stats_table.format(
                tables["zip_stats_bronze_table"]["schema"],
                tables["zip_stats_bronze_table"]["location"],
            ),
            create_bronze_segments_table.format(
                tables["segments_bronze_table"]["schema"],
                tables["segments_bronze_table"]["location"],
            ),
            sync_external_new_movers.format(
                tables["new_movers_external_table"]["schema"]
            ),
            sync_external_ips.format(
                tables["ips_external_table"]["schema"],
            ),
            insert_bronze_new_movers.format(
                tables["new_movers_bronze_table"]["schema"],
                tables["new_movers_external_table"]["schema"],
                tables["ips_external_table"]["schema"],
                transfer_date,
                transfer_date,
            ),
            insert_bronze_zip_stats.format(
                tables["zip_stats_bronze_table"]["schema"],
                tables["new_movers_bronze_table"]["schema"],
                transfer_date,
                tables["new_movers_bronze_table"]["schema"],
                transfer_date,
            ),
            insert_bronze_segments.format(
                tables["segments_bronze_table"]["schema"],
                tables["new_movers_bronze_table"]["schema"],
                transfer_date,
                tables["new_movers_bronze_table"]["schema"],
                transfer_date,
            ),
        ]
        tr = TrinoHook(trino_conn_id="starburst")
        for table in queries:
            tr.run(table)

    @task()
    def generate_segments(configs: dict[str, Any]):
        """
        Build and upload a SQLite database for every household IP address with associated segment codes.

        The segments are assigned based on the following rules:
        * Premover Base Segment: '111' + zip
        * CNM Base Segment: '112' + zip
        * Pending Base Segment: '113' + zip
        * CNM Owner Segment: '114' + zip (if home_owner_flag = 'H')
        * CNM Renter Segment: '115' + zip (if home_owner_flag != 'H')

        Args:
            configs (dict[str, Any]): Configuration settings containing:
                - segments (dict): Segment configurations including bucket and segment keys
                - new_mover_tables (dict): Table configurations including schema for `segments` table
                - search_date (int): Date used to filter records

        Returns:
            list[str]: List of S3 paths where the segment databases and checksums are uploaded
                [
                    "s3://<bucket>/<key>/bulk_segments.db",
                    "s3://<bucket>/<key>/bulk_segments.db.md5sum"
                ]

        Note:
            - Creates SQLite database with 'segment_codes' table containing ip and segment columns
            - Archives previous segment files before uploading new ones
            - Generates and uploads MD5 checksum for verification
        """
        schema = configs["new_mover_tables"]["segments_bronze_table"]["schema"]
        segment_configs = configs["segments"]
        bucket = segment_configs["bucket"]
        segment_key = segment_configs["segment_key"]
        checksum_key = segment_configs["checksum_key"]
        search_date = configs["search_date"]
        transfer_date = str(dt.date(dt.strptime(str(search_date), "%Y%m%d")))

        s3_hook = S3Hook(
            aws_conn_id="aws_default", config=Config(read_timeout=1200)
        )
        s3_client = s3_hook.get_conn()
        trino_hook = TrinoHook(trino_conn_id="starburst")

        logger.info("***************************************************")
        logger.info("Generate Segments")
        logger.info("***************************************************")

        def encode_ip(ip_str: str):
            ip: IPv4Address | IPv6Address = ipaddress.ip_address(ip_str)

            if ip.version == 6:
                # For IPv6, check if it's an IPv4-mapped IPv6 address
                if ip.ipv4_mapped:
                    ip: Any | IPv6Address = ip.ipv4_mapped
                else:
                    raise ValueError(
                        "Only IPv4 and IPv4-mapped IPv6 addresses are supported."
                    )

            # Convert the IPv4 address to a 32-bit integer
            encoded = struct.unpack("!I", ip.packed)[0]

            return encoded

        query = f"""
        SELECT
            ip, segment_code_prefix segment
        FROM olympus.{schema}.segments
        WHERE file_transfer_date = DATE '{transfer_date}'
        """
        segments = trino_hook.get_pandas_df(query)

        logger.debug(segments.head())

        # Convert `ip` to integer.
        segments["ip"] = segments["ip"].apply(encode_ip)

        # Cast to Int64.
        segments["ip"] = pd.to_numeric(segments["ip"], errors="coerce").astype(
            "Int64"
        )
        segments["segment"] = pd.to_numeric(
            segments["segment"], errors="coerce"
        ).astype("Int64")

        logger.debug(segments.head())

        # Copy previous segments before replacing current segment file.
        s3_client.copy_object(
            CopySource={"Bucket": bucket, "Key": segment_key},
            Bucket=bucket,
            Key=f"{segment_configs['archive_prefix']}/{dt.now().strftime('%Y%m%d%H')}_bulk_segments.db",
        )

        # Replace New Mover segments in S3.
        result_paths = []
        # Write to sqllite db.
        with NamedTemporaryFile(
            suffix=".db"
        ) as sqlite_file, NamedTemporaryFile(suffix="md5sum") as checksum_file:
            engine = create_engine(f"sqlite:///{sqlite_file.name}")
            segments.to_sql("segment_codes", con=engine, index=False)

            # Write checksum.
            with open(sqlite_file.name, "rb") as f, open(
                checksum_file.name, "w"
            ) as out:
                md5 = hashlib.md5(f.read()).hexdigest()
                out.write(md5)

            # Upload New Mover segments sqlite database to S3.
            s3_hook.load_file(
                filename=sqlite_file.name,
                key=segment_key,
                bucket_name=bucket,
                replace=True,
            )
            # Upload New Mover segments checksum.
            s3_hook.load_file(
                filename=checksum_file.name,
                key=checksum_key,
                bucket_name=bucket,
                replace=True,
            )

        result_paths.append(f"s3://{bucket}/{segment_key}")
        result_paths.append(f"s3://{bucket}/{checksum_key}")

        return result_paths

    @task()
    def notify_segmentors(configs: dict[str, Any]):
        """
        Notify segmentors that the new mover segments are ready for processing.

        The response from the segmentor should be a json object with the following keys;
            - current_checksum: The md5sum of the segments file.
            - job_id: The job id of the segmentor.
            - result: The result of the segmentor job.

        The result should be "success" if the segmentor successfully processed the segments.
        And the checksum should match the checksum of the segments file.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - segmentor_endpoints (list[str]): List of endpoints to notify.
                - segmentor_secret (str): Secret key for authentication.
                - segments (dict): Segment configurations, including bucket and checksum keys.

        Returns:
            None
        """
        endpoints = configs["segmentor_endpoints"]
        secret = configs["segmentor_secret"]
        segments = configs["segments"]

        s3_hook = S3Hook(aws_conn_id="aws_default")
        s3_client = s3_hook.get_conn()
        bucket = segments["bucket"]

        token = get_secret_value(secret)[secret]
        checksum = ""
        # [TODO] Add other segments to list.
        checksum_key = segments["checksum_key"]
        # Get checksum.
        with NamedTemporaryFile(suffix="md5sum") as checksum_file:
            s3_client.download_file(
                Bucket=bucket,
                Key=checksum_key,
                Filename=checksum_file.name,
            )
            with open(checksum_file.name, "r") as f:
                checksum = f.read()
                logger.info(f"Checksum: {checksum}")

        for url in endpoints:
            logger.info(f"Notify segementors at url: {url}")
            res = requests.Response()
            try:
                res = requests.get(url, headers={"Auth-Key": token}, timeout=20)
            except requests.exceptions.ReadTimeout as e:
                logger.error(f"Read Timeout: {e}")
            except requests.exceptions.ConnectionError as e:
                logger.error(f"Connection Error: {e}")

            if res.status_code is not None:
                logger.info(res.json())
                res.raise_for_status()
                # assert res.json()["current_checksum"] == checksum



    @task()
    def upload_matches_to_ach_coop(configs: XComArg):
        """
        Upload list of ACHco-op IDs of records matched/quoted in JSON file to SFTP server for each dataset.

        File format `<current date string>-<dataset>-matches.json` ex: `20241008-cnm-matches.json`.

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - file_types (list[str]): List of file types.
                - matches_file_paths (dict[str, str]): Dict of matches file paths.
                - annotated_file_paths (dict[str, str]): Dict of annotated files produced by
                    the quoting process.

        Returns:
            None
        """
        file_types = configs["file_types"]
        matches_file_paths = configs["matches_file_paths"]
        annotated_file_paths = configs["annotated_file_paths"]
        bucket = configs["segments"]["bucket"]
        search_date = configs["search_date"]
        env = configs["env"]

        s3_hook = S3Hook(
            aws_conn_id="aws_default", config=Config(read_timeout=1200)
        )

        sftp_hook = SFTPHook(
            ssh_conn_id="new_mover_whitehat_sftp",
        )
        for ft in file_types:
            df = pd.read_csv(
                annotated_file_paths[ft],
                dtype="string",
                usecols=["id", "target"],
            )
            # Filter out unmatched targets.
            df = df[df.target == "t"]["id"]
            logger.debug(df.head())
            with NamedTemporaryFile(suffix="json") as temp_file:
                df.to_json(temp_file.name, index=False)
                # Copy backup to `newmover` bucket.
                backup_key = f"{env}/toClient/{search_date}/{ft.upper()}.zip"
                s3_hook.load_file(
                    filename=temp_file.name,
                    bucket_name=bucket,
                    key=backup_key,
                    replace=True,
                )

                sftp_hook.store_file(
                    remote_full_path=matches_file_paths[ft],
                    local_full_path=temp_file.name,
                )

    @task()
    def email_ach_coop(configs: dict[str, Any], **kwargs):
        """
        Send an email with statistics for every state.

        Stats for every state in the following format;
            Results:
            pending:    166265 /     244420 @ 68.02
            pre:    543639 /     796547 @ 68.25
            cnm:   2352373 /    3591526 @ 65.50
            WA
            cnm: 19432
            pending: 1613
            pre: 4995
            total: 26040
            AL
            cnm: 34820
            pending: 1975
            pre: 6818
            total: 43613
            ...

        Args:
            configs (XComArg): Configuration settings for the New Mover datasets.
                - file_types (list[str]): List of file types.
                - annotated_file_paths (dict[str, str]): Dict of annotated files produced by
                    the quoting process.
            **kwargs: Additional parameters.

        Returns:
            None
        """
        file_types = configs["file_types"]
        annotated_file_paths = configs["annotated_file_paths"]

        cnm = pd.DataFrame()
        pre = pd.DataFrame()
        pending = pd.DataFrame()
        logger.info('Begin reading CSV files')
        for ft in file_types:
            if ft == "cnm":
                cnm = pd.read_csv(
                    annotated_file_paths[ft],
                    dtype="string",
                    usecols=["state", "target"],
                )
            if ft == "premover":
                pre = pd.read_csv(
                    annotated_file_paths[ft],
                    dtype="string",
                    usecols=["state", "target"],
                )
            if ft == "pending":
                pending = pd.read_csv(
                    annotated_file_paths[ft],
                    dtype="string",
                    usecols=["state", "target"],
                )

        # Group by state and count the non-NA values
        cnm_counts = (
            cnm[cnm.target == "t"].groupby(
                by="state",
                ).target.count()
        )
        pending_counts = (
            pending[pending.target == "t"].groupby(
                by="state",
                ).target.count()
        )
        pre_counts = (
            pre[pre.target == "t"].groupby(
                by="state",
                ).target.count()
        )

        # Create a DataFrame with the counts for each state
        df = (
            pd.DataFrame(
                {
                    "cnm": cnm_counts,
                    "pending": pending_counts,
                    "pre": pre_counts,
                }
            )
            .fillna(0)
            .astype(int)
        )

        # Calculate the total count for each state
        df["total"] = df[["cnm", "pending", "pre"]].sum(axis=1)

        # Construct message
        message = f"""Results:
        Pending: {df.pending.sum()} / {pending.shape[0]} @ {(df.pending.sum()/pending.shape[0]):.2f}
        Pre:     {df.pre.sum()} / {pre.shape[0]} @ {(df.pre.sum()/pre.shape[0]):.2f}
        CNM:     {df.cnm.sum()} / {cnm.shape[0]} @ {(df.cnm.sum()/cnm.shape[0]):.2f}
        <br><br>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="text-align: center; width: 150px;">State</th>
                <th style="text-align: center; width: 150px;">CNM</th>
                <th style="text-align: center; width: 150px;">Pending</th>
                <th style="text-align: center; width: 150px;">Pre</th>
                <th style="text-align: center; width: 150px;">Total</th>
            </tr>"""
        
        # Iterate over the rows of the DataFrame and construct the table rows
        for row in df.itertuples():
            if row.Index in ["AE", "AA", "AP"]:
                continue
            state_stats = f"""
                    <tr>
                        <td style="text-align: center; font-weight: bold;">{row.Index}</td>
                        <td style="text-align: center;">{row.cnm}</td>
                        <td style="text-align: center;">{row.pending}</td>
                        <td style="text-align: center;">{row.pre}</td>
                        <td style="text-align: center;">{row.cnm + row.pending + row.pre}</td>
                    </tr>"""
            message += state_stats
        
        # Close the table tag
        message += "</table>"

        # Only send email if running in prod and not in test mode.
        if (
            Variable.get("environment") == "prod"
            and kwargs["params"]["mode"] != "test"
        ):
            # Getting the smtp connection
            smtp_hook = SmtpHook(smtp_conn_id="k8s_relay_smtp")
            smtp_hook.get_conn()

            logger.info("Sending email to ACH Coop")

            # Send email
            smtp_hook.send_email_smtp(
                from_email="<EMAIL>",
                to=["<EMAIL>", "<EMAIL>", "<EMAIL>"], # List of recipients
                subject="NewMover",
                html_content=message,
                mime_subtype="html",
            )

        logger.info(message)

    # ---------------------
    # Run time
    # ---------------------

    search_date = (
        "{{ (execution_date - macros.timedelta(days=2)).strftime('%Y%m%d') }}"
    )
    mode = "{{ dag_run.conf.get('mode', 'live') }}"

    configs = generate_configs(search_date, mode)

    new_files_check = check_new_mover_files(configs)

    stage_files = stage_new_mover_files(configs)

    quote_files = []
    for audience_type, ft, i in zip(
        ["postmover_address", "premover_address", "escrowmover_address"],
        ["cnm", "premover", "pending"],
        range(3),
    ):
        quote_task = quote_file.override(
            group_id=f"quote-{audience_type.split('_')[0]}-file",
        )(
            source_file_path=f"{{{{ ti.xcom_pull('generate_configs')['staging_file_paths']['{ft}'] }}}}",
            annotated_file_path=f"{{{{ ti.xcom_pull('generate_configs')['annotated_file_paths']['{ft}'] }}}}",
            selected_file_path=f"{{{{ ti.xcom_pull('generate_configs')['selected_file_paths']['{ft}'] }}}}",
            data_source="NEWMOVER",
            audience_type=audience_type.upper(),
            org_id="{{ ti.xcom_pull('generate_configs')['org_id'] }}",
            column_headers=f"{{{{ ti.xcom_pull('generate_configs')['columns']['{ft}']['headers'] }}}}",
            env="{{ ti.xcom_pull('generate_configs')['quote_env'] }}",
        )
        quote_files.append(quote_task)

    starburst_tables = load_starburst_tables(configs)

    segments = generate_segments(configs)

    segmentor_notify = notify_segmentors(configs)

    matchbacks = upload_matches_to_ach_coop(configs)

    send_email = email_ach_coop(configs)

    end = EmptyOperator(task_id="end")

    (
        new_files_check
        >> stage_files
        >> quote_files
        >> starburst_tables
        >> segments
        >> segmentor_notify
        >> matchbacks
        >> send_email
        >> end
    )

    # ---------------------
    # Test code
    # ---------------------


def decode_ip(encoded):
    # Pack the 32-bit integer into bytes using network byte order
    packed_ip = struct.pack("!I", encoded)

    # Convert the packed bytes into an IPv4Address object
    ip = ipaddress.IPv4Address(packed_ip)

    # Return the string representation of the IP address
    return str(ip)


if __name__ == "__main__":
    logger.setLevel(logging.DEBUG)

    run = dag.test(
        conn_file_path="new_mover/connections.json",
        variable_file_path="new_mover/variables.json",
        run_conf={
            "file_limit": 1000,
            "search_date": 20241203,
            "mode": "live",
            "start_date": None,
        },
        mark_success_pattern="wait_for_.*|end",
    )

    paths = run.get_task_instance("generate_segments").xcom_pull()

    assert len(paths) == 2
