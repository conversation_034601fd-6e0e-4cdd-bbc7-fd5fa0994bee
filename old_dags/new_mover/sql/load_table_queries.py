sync_external_new_movers = """
CALL s3.system.sync_partition_metadata('{}', 'new_movers', 'ADD')
"""

sync_external_ips = """
CALL s3.system.sync_partition_metadata('{}', 'ips', 'ADD')
"""

insert_bronze_new_movers = """
INSERT INTO olympus.{}.new_movers
SELECT
    target varchar,
    nm_ext.ethash varchar,
    ips.ips,
    id varchar,
    zip varchar,
    full_address varchar,
    house_number varchar,
    street_name varchar,
    street_suffix varchar,
    city varchar,
    state varchar,
    zip4 varchar,
    home_owner_flag varchar,
    date("date") date,
    prevadd_zip varchar,
    prevadd_full_address varchar,
    prevadd_house_number varchar,
    prevadd_street_name varchar,
    prevadd_street_suffix varchar,
    prevadd_city varchar,
    prevadd_state varchar,
    prevadd_zip4 varchar,
    source_code varchar,
    mover_type varchar,
    date(file_transfer_date) file_transfer_date,
    current_date as processed_date
FROM s3.{}.new_movers nm_ext
LEFT JOIN (
    SELECT
        ethash,
        ARRAY_DISTINCT(ARRAY_AGG(ip)) AS ips
    FROM s3.{}.ips
    WHERE file_transfer_date = '{}'
    GROUP BY ethash
) ips
ON nm_ext.ethash = ips.ethash
WHERE file_transfer_date = '{}'
"""

insert_bronze_zip_stats = """
INSERT INTO olympus.{}.zip_stats
SELECT
    nm.zip,
    count(distinct(t.ip)) AS ip_count,
    count(distinct(ethash)) AS ethash_count,
    nm.mover_type,
    case
        WHEN mover_type = 'cnm' THEN '112'
        WHEN mover_type = 'premover' THEN '111'
        WHEN mover_type = 'pending' THEN '113'
    END as segment_code_prefix,
    nm.file_transfer_date,
    current_date as processed_date
FROM
    olympus.{}.new_movers nm,
    unnest(nm.ips) AS t(ip)
WHERE
    nm.file_transfer_date = date('{}')
    AND target = 't'
GROUP BY
    nm.zip,
    nm.mover_type,
    nm.file_transfer_date
UNION ALL
SELECT
    nm.zip,
    count(distinct(t.ip)) AS ip_count,
    count(distinct(ethash)) AS ethash_count,
    nm.mover_type,
    case
        WHEN home_owner_flag = 'H' THEN '114'
        WHEN home_owner_flag != 'H' THEN '115'
    END AS segment_code_prefix,
    nm.file_transfer_date,
    current_date as processed_date
FROM
    (
        SELECT 
            zip, 
            ips, 
            target,
            ethash, 
            mover_type, 
            case
                WHEN home_owner_flag = 'H' then 'H'
                ELSE 'R'
            end home_owner_flag, 
            file_transfer_date 
        FROM olympus.{}.new_movers) nm,
    unnest(nm.ips) as t(ip)
WHERE
    nm.file_transfer_date = date('{}')
    AND target = 't'
    AND mover_type = 'cnm'
GROUP BY
    nm.zip,
    nm.mover_type,
    nm.home_owner_flag,
    nm.file_transfer_date
"""

insert_bronze_segments = """
INSERT into olympus.{}.segments
SELECT DISTINCT
    ip,
    zip,
    state,
    CASE
        WHEN mover_type = 'cnm' THEN '112' || zip
        WHEN mover_type = 'premover' THEN '111' || zip
        WHEN mover_type = 'pending' THEN '113' || zip
    END AS segment_code,
    file_transfer_date
    FROM (
        SELECT
            t.ip,
            zip,
            state,
            home_owner_flag,
            target,
            mover_type,
            file_transfer_date,
            row_number() over (partition by t.ip, zip, mover_type order by home_owner_flag desc) as rn
        FROM olympus.{}.new_movers
        CROSS JOIN unnest(ips) as t(ip)
        WHERE target = 't'
        AND file_transfer_date = date '{}'
    )
    WHERE rn = 1
UNION ALL
SELECT DISTINCT
    ip,
    zip,
    state,
    CASE
        WHEN mover_type = 'cnm' and home_owner_flag = 'H' THEN '114' || zip
        WHEN mover_type = 'cnm' and home_owner_flag != 'H' THEN '115' || zip
    END AS segment,
    file_transfer_date
    FROM (
        SELECT
            t.ip,
            zip,
            state,
            home_owner_flag,
            target,
            mover_type,
            file_transfer_date,
            row_number() over (partition by t.ip, zip, mover_type order by home_owner_flag desc) as rn
        FROM olympus.{}.new_movers
        CROSS JOIN unnest(ips) as t(ip)
        WHERE target = 't'
        AND mover_type = 'cnm'
        AND file_transfer_date = date '{}'
    )
    WHERE rn = 1
"""
