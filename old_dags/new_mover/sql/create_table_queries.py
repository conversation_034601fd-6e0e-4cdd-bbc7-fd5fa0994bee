create_external_new_movers_table = """
CREATE TABLE IF NOT EXISTS s3.{}.new_movers(
    target varchar,
    ethash varchar,
    id varchar,
    zip varchar,
    full_address varchar,
    house_number varchar,
    street_name varchar,
    street_suffix varchar,
    city varchar,
    state varchar,
    zip4 varchar,
    home_owner_flag varchar,
    "date" varchar,
    prevadd_zip varchar,
    prevadd_full_address varchar,
    prevadd_house_number varchar,
    prevadd_street_name varchar,
    prevadd_street_suffix varchar,
    prevadd_city varchar,
    prevadd_state varchar,
    prevadd_zip4 varchar,
    source_code varchar,
    mover_type varchar,
    file_transfer_date varchar
)
WITH (
    external_location = 's3a://{}',
    format = 'CSV',
    skip_header_line_count = 1,
    partitioned_by = ARRAY['mover_type','file_transfer_date']
)
"""

create_external_ips_table = """
create table if not exists s3.{}.ips(
    ip varchar,
    ethash varchar,
    mover_type varchar,
    file_transfer_date varchar
)
WITH (
    external_location = 's3a://{}',
    format = 'CSV',
    partitioned_by = ARRAY['mover_type','file_transfer_date']
)
"""

create_bronze_new_movers_table = """
CREATE TABLE if NOT EXISTS olympus.{}.new_movers(
    target varchar,
    ethash varchar,
    ips array<varchar>,
    id varchar,
    zip varchar,
    full_address varchar,
    house_number varchar,
    street_name varchar,
    street_suffix varchar,
    city varchar,
    state varchar,
    zip4 varchar,
    home_owner_flag varchar,
    "date" date,
    prevadd_zip varchar,
    prevadd_full_address varchar,
    prevadd_house_number varchar,
    prevadd_street_name varchar,
    prevadd_street_suffix varchar,
    prevadd_city varchar,
    prevadd_state varchar,
    prevadd_zip4 varchar,
    source_code varchar,
    mover_type varchar,
    file_transfer_date date,
    processed_date date
)
WITH (
    format = 'PARQUET',
    format_version = 2,
    partitioning = ARRAY['mover_type','processed_date'],
    sorted_by = ARRAY['zip ASC NULLS FIRST']
)
"""

create_bronze_zip_stats_table = """
CREATE TABLE if NOT EXISTS olympus.{}.zip_stats(
    zip varchar,
    ethash_count int,
    ip_count int,
    mover_type varchar,
    segment_code_prefix varchar,
    file_transfer_date date,
    processed_date date
)
WITH (
    format = 'PARQUET',
    format_version = 2,
    partitioning = ARRAY['file_transfer_date'],
    sorted_by = ARRAY['zip ASC NULLS FIRST']
)
"""

create_bronze_segments_table = """
CREATE TABLE if NOT EXISTS olympus.{}.segments(
    ip varchar,
    zip varchar,
    state varchar,
    segment_code_prefix varchar,
    file_transfer_date date
)
WITH (
    format = 'PARQUET',
    format_version = 2,
    partitioning = ARRAY['file_transfer_date'],
    sorted_by = ARRAY['zip ASC NULLS FIRST']
)
"""
