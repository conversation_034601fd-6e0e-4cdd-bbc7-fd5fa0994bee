CREATE_PREQUOTE_AUDIENCE_TABLE = """
CREATE TABLE IF NOT EXISTS s3.{SCHEMA}.{PREQUOTE_TABLE_NAME} (
   ethash varchar,
   streetaddress varchar,
   city varchar,
   state varchar,
   zipcode varchar,
   pti integer,
   pui integer,
   poi integer,
   tg integer,
   vti integer,
   voi integer,
   gc integer,
   nc integer,
   segment_group varchar,
   seg_id integer,
   date date,
   "group" varchar
)
WITH (
   external_location = 's3://vr-timestamp/bi_sources/toyota_2024/{ENV}/prequote',
   format = 'PARQUET',
   partitioned_by = ARRAY['date', 'group']
)
"""

CREATE_ANNOTATED_TABLE = """
CREATE TABLE IF NOT EXISTS s3.{SCHEMA}.{ANNOTATED_TABLE_NAME} (
   target varchar,
   ethash varchar,
   streetaddress varchar,
   zipcode varchar,
   date date,
   "group" varchar
)
WITH (
   external_location = 's3://vr-timestamp/bi_sources/toyota_2024/{ENV}/annotated_audience',
   format = 'CSV',
   partitioned_by = ARRAY['date', 'group'],
   skip_header_line_count = 1
)"""


CREATE_TABLE_DELIVERABLE = """
    CREATE TABLE IF NOT EXISTS s3.{SCHEMA}.{DELIVERABLE_TABLE_NAME} (
        target varchar,
       ethash varchar,
       streetaddress varchar,
       city varchar,
       state varchar,
       zipcode varchar,
       pti varchar,
       pui varchar,
       poi varchar,
       tg varchar,
       vti varchar,
       voi varchar,
       gc varchar,
       nc varchar,
       segment_group varchar,
       seg_id varchar,
       date date,
       "group" varchar
    )
    WITH (
       external_location = 's3://vr-timestamp/bi_sources/toyota_2024/{ENV}/deliverable',
       format = 'CSV',
       partitioned_by = ARRAY['date', 'group'],
       skip_header_line_count = 1
    )
"""