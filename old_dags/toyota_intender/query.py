audience_query = """
WITH obs AS (
    SELECT 
        visitor_ethash AS ethash,
        count(DISTINCT buckloc_id) loc_count,
        array_agg(DISTINCT tag) AS tags
    FROM "s3"."gold_auto_intender"."distinct_obs_90" obs
    JOIN s3.gold_auto_enrichment.fry_toyota_intender_locations USING(buckloc_id)
    WHERE "date" > (CURRENT_DATE - INTERVAL '40' DAY)
    GROUP BY 1
),
p_intenders AS(
SELECT 
    ethash,
    CASE
        WHEN CONTAINS(tags, 'USED_CARS') THEN 1
        ELSE 0
    END "PUI",
    CASE
        WHEN CONTAINS(tags, 'TOYOTA') THEN 1
        ELSE 0
    END "PTI",
    CASE
        WHEN CARDINALITY(array_remove(array_remove(tags, 'USED'), 'TOYOTA')) > 0 THEN 1
        ELSE 0
    END "POI"
FROM obs
WHERE loc_count > 1
),
g_intender AS (
    SELECT DISTINCT
    ethashv1 ethash,
    1 "TG"
    FROM "olympus"."bronze_garage"."sp_owner_model_pivot"
    JOIN "olympus"."bronze_garage"."sp_garage_address_bridge_tbl" bdg USING(polkid) WHERE make = 'Toyota'
),
gc_intender AS (
    SELECT ethash,
    1 "GC"
FROM olympus.silver_aristotle.hh_good_credit 
),
nc_intender AS (
    SELECT ethash,
    1 "NC" 
    FROM olympus.silver_aristotle.hh_no_credit 
),
-- Updated Toyota B2C intender using new topic mappings
vt_intender AS (
        SELECT DISTINCT
      ai.ethash_v1 ethash,
      1 "VTI"
        from "s3"."bronze_auto_intender"."auto_intent_daily_4eyes" ai
        where ai.segmentid  in (
    'b2c_186',  -- Toyota Prius (4eyes_100191)
    'b2c_189',  -- Toyota Corolla (4eyes_100194)
    'b2c_191',  -- Toyota Crown (4eyes_100196)
    'b2c_210',  -- Toyota Bz4x (4eyes_100215)
    'b2c_214',  -- Toyota 4runner (4eyes_100219)
    'b2c_238',  -- Toyota Rav4 (4eyes_100243)
    'b2c_247',  -- Toyota C-hr (4eyes_100252)
    'b2c_255',  -- Toyota Venza (4eyes_100261)
    'b2c_264',  -- Toyota Rav4 Prime (4eyes_100270)
    'b2c_285',  -- Toyota Rav4 N (4eyes_100297)
    'b2c_294',  -- Toyota Rav4 Trd Off-road (4eyes_100306)
    'b2c_303',  -- Toyota Rav4 Limited (4eyes_100315)
    'b2c_312',  -- Toyota 4runner Trd Pro (4eyes_100324)
    'b2c_321',  -- Toyota Rav (4eyes_100333)
    'b2c_326',  -- Toyota Tundra (4eyes_100338)
    'b2c_332',  -- Toyota Tacoma (4eyes_100344)
    'b2c_338',  -- Toyota T100 (4eyes_100350)
    'b2c_355',  -- Toyota Hilux (4eyes_100367)
    'b2c_467',  -- Toyota Sienna (4eyes_100480)
    'b2c_4746', -- Toyota 86 (4eyes_105014)
    'b2c_4747', -- Toyota Avalon (4eyes_105015)
    'b2c_4748', -- Toyota Camry (4eyes_105016)
    'b2c_4749', -- Toyota Corolla Hatchback (4eyes_105017)
    'b2c_4750', -- Toyota FJ Cruiser (4eyes_105018)
    'b2c_4751', -- Toyota Highlander (4eyes_105019)
    'b2c_4752', -- Toyota Land Cruiser (4eyes_105020)
    'b2c_4753', -- Toyota Matrix (4eyes_105021)
    'b2c_4754', -- Toyota Mirai (4eyes_105022)
    'b2c_4755', -- Toyota Prius Prime (4eyes_105023)
    'b2c_4756', -- Toyota Rav4 Hybrid (4eyes_105024)
    'b2c_4757', -- Toyota Sequoia (4eyes_105025)
    'b2c_4758', -- Toyota Supra (4eyes_105026)
    'b2c_4759', -- Toyota Yaris (4eyes_105027)
    'b2c_4760'  -- Toyota Yaris Hatchback (4eyes_105028)
    )  --Toyota B2C Topics
),
-- Updated Other Vehicle B2C intender using new topic mappings
-- Systematically mapped from original 4eyes IDs to new B2C IDs
-- Total: 473 automotive topics mapped to 578 B2C IDs
vo_intender AS (
        SELECT DISTINCT
      ai.ethash_v1 ethash,
      1 "VOI"
        from "s3"."bronze_auto_intender"."auto_intent_daily_4eyes" ai
        where ai.segmentid  IN (
    -- Automotive B2C mappings for vo_intender section:
   'b2c_168',  -- BMW (4eyes_100173)
    'b2c_447',  -- BMW (4eyes_100173)
    'b2c_168',  -- BMW (4eyes_100173)
    'b2c_447',  -- BMW (4eyes_100173)
    'b2c_173',  -- Lamborghini (4eyes_100178)
    'b2c_457',  -- Lamborghini (4eyes_100178)
    'b2c_173',  -- Lamborghini (4eyes_100178)
    'b2c_457',  -- Lamborghini (4eyes_100178)
    'b2c_174',  -- Ferrari (4eyes_100179)
    'b2c_4587',  -- Ferrari (4eyes_100179)
    'b2c_174',  -- Ferrari (4eyes_100179)
    'b2c_4587',  -- Ferrari (4eyes_100179)
    'b2c_175',  -- Land Rover (4eyes_100180)
    'b2c_460',  -- Land Rover (4eyes_100180)
    'b2c_175',  -- Land Rover (4eyes_100180)
    'b2c_460',  -- Land Rover (4eyes_100180)
    'b2c_177',  -- Jaguar (4eyes_100182)
    'b2c_459',  -- Jaguar (4eyes_100182)
    'b2c_177',  -- Jaguar (4eyes_100182)
    'b2c_459',  -- Jaguar (4eyes_100182)
    'b2c_179',  -- Aston Martin (4eyes_100184)
    'b2c_180',  -- Bently (4eyes_100185)
    'b2c_181',  -- Maserati (4eyes_100186)
    'b2c_458',  -- Maserati (4eyes_100186)
    'b2c_181',  -- Maserati (4eyes_100186)
    'b2c_458',  -- Maserati (4eyes_100186)
    'b2c_182',  -- Mclaren (4eyes_100187)
    'b2c_185',  -- Bugatti (4eyes_100190)
    'b2c_187',  -- Hyundai Sonata (4eyes_100192)
    'b2c_188',  -- Honda Accord (4eyes_100193)
    'b2c_190',  -- Hyundai Ioniq (4eyes_100195)
    'b2c_192',  -- Chevrolet Bolt (4eyes_100197)
    'b2c_195',  -- Mercedes Benz Eqs (4eyes_100200)
    'b2c_197',  -- BMW I7 (4eyes_100202)
    'b2c_198',  -- BMW I4 (4eyes_100203)
    'b2c_200',  -- Audi E-tron Gt (4eyes_100205)
    'b2c_201',  -- Porsche Taycan (4eyes_100206)
    'b2c_204',  -- Ford F-150 Lightning (4eyes_100209)
    'b2c_206',  -- Hyundai Kona (4eyes_100211)
    'b2c_251',  -- Hyundai Kona (4eyes_100211)
    'b2c_206',  -- Hyundai Kona (4eyes_100211)
    'b2c_251',  -- Hyundai Kona (4eyes_100211)
    'b2c_208',  -- Kia Nero Ev (4eyes_100213)
    'b2c_211',  -- Ford Explorer (4eyes_100216)
    'b2c_212',  -- Chevrolet Tahoe (4eyes_100217)
    'b2c_213',  -- Jeep Grand Cherokee (4eyes_100218)
    'b2c_215',  -- Honda Pilot (4eyes_100220)
    'b2c_217',  -- BMW X5 (4eyes_100222)
    'b2c_217',  -- BMW X5 (4eyes_100222)
    'b2c_220',  -- Audi Q7 (4eyes_100225)
    'b2c_222',  -- Infiniti Qx60 (4eyes_100227)
    'b2c_225',  -- Land Rover Discovery (4eyes_100230)
    'b2c_227',  -- Cadillac Escalade (4eyes_100232)
    'b2c_228',  -- GMC Yukon (4eyes_100233)
    'b2c_229',  -- Ford Expedition (4eyes_100234)
    'b2c_230',  -- Chevrolet Suburban (4eyes_100235)
    'b2c_231',  -- Dodge Durango (4eyes_100236)
    'b2c_232',  -- GMC Acadia (4eyes_100237)
    'b2c_233',  -- Buick Enclave (4eyes_100238)
    'b2c_234',  -- Hyundai Santa Fe (4eyes_100239)
    'b2c_235',  -- Kia Sorento (4eyes_100240)
    'b2c_237',  -- Honda CR-V (4eyes_100242)
    'b2c_239',  -- Ford Escape (4eyes_100244)
    'b2c_240',  -- Chevrolet Equinox (4eyes_100245)
    'b2c_241',  -- GMC Terrain (4eyes_100246)
    'b2c_242',  -- Buick Encore (4eyes_100247)
    'b2c_243',  -- Hyundai Tucson (4eyes_100248)
    'b2c_244',  -- Kia Sportage (4eyes_100249)
    'b2c_246',  -- Honda HR-V (4eyes_100251)
    'b2c_248',  -- Ford Edge (4eyes_100253)
    'b2c_249',  -- Chevrolet Blazer (4eyes_100254)
    'b2c_250',  -- Buick Envision (4eyes_100256)
    'b2c_206',  -- Hyundai Kona (4eyes_100257)
    'b2c_251',  -- Hyundai Kona (4eyes_100257)
    'b2c_206',  -- Hyundai Kona (4eyes_100257)
    'b2c_251',  -- Hyundai Kona (4eyes_100257)
    'b2c_252',  -- Kia Soul (4eyes_100258)
    'b2c_254',  -- Honda Passport (4eyes_100260)
    'b2c_256',  -- Ford Flex (4eyes_100262)
    'b2c_257',  -- Chevrolet Traverse (4eyes_100263)
    'b2c_258',  -- GMC Acadia Limited (4eyes_100264)
    'b2c_259',  -- Buick Encore Gx (4eyes_100265)
    'b2c_260',  -- Hyundai Palisade (4eyes_100266)
    'b2c_261',  -- Kia Telluride (4eyes_100267)
    'b2c_263',  -- Honda Pilot Hybrid (4eyes_100269)
    'b2c_265',  -- Ford Mustang Mach-e (4eyes_100271)
    'b2c_266',  -- Chevrolet Bolt Euv (4eyes_100272)
    'b2c_267',  -- GMC Hummer Ev (4eyes_100273)
    'b2c_268',  -- Buick Enspire (4eyes_100274)
    'b2c_269',  -- Hyundai Ioniq 5 (4eyes_100275)
    'b2c_270',  -- Kia Ev6 (4eyes_100276)
    'b2c_272',  -- Honda Ev-ster (4eyes_100278)
    'b2c_273',  -- Ford Maverick (4eyes_100280)
    'b2c_274',  -- Buick Encore Gx Sport Touring (4eyes_100283)
    'b2c_275',  -- Hyundai Santa Cruz (4eyes_100284)
    'b2c_276',  -- Kia Stonic (4eyes_100285)
    'b2c_278',  -- Honda Ridgeline (4eyes_100287)
    'b2c_279',  -- Chevrolet Silverado (4eyes_100290)
    'b2c_280',  -- Buick Avista (4eyes_100292)
    'b2c_281',  -- Hyundai Santa Fe N (4eyes_100293)
    'b2c_282',  -- Kia Seltos (4eyes_100294)
    'b2c_284',  -- Honda Hr-v N (4eyes_100296)
    'b2c_286',  -- Ford Bronco Sport (4eyes_100298)
    'b2c_287',  -- Chevrolet Trailblazer (4eyes_100299)
    'b2c_288',  -- GMC Granite (4eyes_100300)
    'b2c_289',  -- Buick Encore Gx Sport (4eyes_100301)
    'b2c_290',  -- Hyundai Venue (4eyes_100302)
    'b2c_291',  -- Kia Seltos Gt-line (4eyes_100303)
    'b2c_293',  -- Honda Hr-v Sport (4eyes_100305)
    'b2c_295',  -- Ford Bronco (4eyes_100307)
    'b2c_296',  -- Chevrolet Blazer Rs (4eyes_100308)
    'b2c_297',  -- GMC Acadia At4 (4eyes_100309)
    'b2c_298',  -- Buick Envision Avenir (4eyes_100310)
    'b2c_299',  -- Hyundai Santa Fe Calligraphy (4eyes_100311)
    'b2c_300',  -- Kia Sorento Gt-line (4eyes_100312)
    'b2c_302',  -- Honda Cr-v Touring (4eyes_100314)
    'b2c_304',  -- Ford Edge St (4eyes_100316)
    'b2c_305',  -- Chevrolet Equinox Premier (4eyes_100317)
    'b2c_306',  -- GMC Terrain Denali (4eyes_100318)
    'b2c_307',  -- Buick Enclave Avenir (4eyes_100319)
    'b2c_308',  -- Hyundai Palisade Calligraphy (4eyes_100320)
    'b2c_309',  -- Kia Telluride Gt-line (4eyes_100321)
    'b2c_311',  -- Honda Pilot Elite (4eyes_100323)
    'b2c_313',  -- Ford Explorer Platinum (4eyes_100325)
    'b2c_314',  -- Chevrolet Tahoe Rst (4eyes_100326)
    'b2c_315',  -- GMC Yukon Denali (4eyes_100327)
    'b2c_316',  -- Buick Enclave Black Label (4eyes_100328)
    'b2c_317',  -- Hyundai Santa Fe N Line (4eyes_100329)
    'b2c_318',  -- Kia Sorento N Line (4eyes_100330)
    'b2c_320',  -- Honda Cr-v N Line (4eyes_100332)
    'b2c_322',  -- Ford F-150 (4eyes_100334)
    'b2c_323',  -- Chevrolet Silverado 1500 (4eyes_100335)
    'b2c_324',  -- Ram 1500 (4eyes_100336)
    'b2c_325',  -- GMC Sierra 1500 (4eyes_100337)
    'b2c_327',  -- Nissan Titan (4eyes_100339)
    'b2c_328',  -- Ford F-250 (4eyes_100340)
    'b2c_329',  -- Chevrolet Silverado 2500 (4eyes_100341)
    'b2c_330',  -- Ram 2500 (4eyes_100342)
    'b2c_331',  -- GMC Sierra 2500 (4eyes_100343)
    'b2c_333',  -- Nissan Frontier (4eyes_100345)
    'b2c_334',  -- Ford F-350 (4eyes_100346)
    'b2c_335',  -- Chevrolet Silverado 3500 (4eyes_100347)
    'b2c_336',  -- Ram 3500 (4eyes_100348)
    'b2c_337',  -- GMC Sierra 3500 (4eyes_100349)
    'b2c_339',  -- Nissan Nv (4eyes_100351)
    'b2c_340',  -- Ford F-450 (4eyes_100352)
    'b2c_341',  -- Chevrolet Silverado 4500 (4eyes_100353)
    'b2c_342',  -- Ram 4500 (4eyes_100354)
    'b2c_344',  -- Ford F-550 (4eyes_100356)
    'b2c_345',  -- Chevrolet Silverado 5500 (4eyes_100357)
    'b2c_346',  -- Ram 5500 (4eyes_100358)
    'b2c_348',  -- Ford F-650 (4eyes_100360)
    'b2c_349',  -- Chevrolet Silverado 6500 (4eyes_100361)
    'b2c_350',  -- Ram 6500 (4eyes_100362)
    'b2c_352',  -- Ford Ranger (4eyes_100364)
    'b2c_353',  -- Chevrolet Colorado (4eyes_100365)
    'b2c_356',  -- Nissan Navara (4eyes_100368)
    'b2c_357',  -- Ford Transit (4eyes_100369)
    'b2c_359',  -- Ford E-series (4eyes_100372)
    'b2c_360',  -- Chevrolet Express Cutaway (4eyes_100373)
    'b2c_362',  -- Ford Super Duty (4eyes_100375)
    'b2c_363',  -- Chevrolet Kodiak (4eyes_100376)
    'b2c_365',  -- Ford F-750 (4eyes_100378)
    'b2c_366',  -- Chevrolet Low Cab Forward (4eyes_100379)
    'b2c_368',  -- Ford F-650/f-750 Super Duty (4eyes_100381)
    'b2c_369',  -- Chevrolet Kodiak C4500 (4eyes_100382)
    'b2c_371',  -- Ford F-53 (4eyes_100384)
    'b2c_372',  -- Chevrolet P30 (4eyes_100385)
    'b2c_374',  -- Ford F-59 (4eyes_100387)
    'b2c_375',  -- Chevrolet C60 (4eyes_100388)
    'b2c_377',  -- Ford F-600 (4eyes_100390)
    'b2c_378',  -- Chevrolet C70 (4eyes_100391)
    'b2c_380',  -- Ford F-700 (4eyes_100393)
    'b2c_381',  -- Chevrolet C80 (4eyes_100394)
    'b2c_383',  -- Ford F-800 (4eyes_100396)
    'b2c_384',  -- Chevrolet C90 (4eyes_100397)
    'b2c_386',  -- Ford F-900 (4eyes_100399)
    'b2c_387',  -- Chevrolet C100 (4eyes_100400)
    'b2c_389',  -- Ford F-1000 (4eyes_100402)
    'b2c_390',  -- Chevrolet C120 (4eyes_100403)
    'b2c_392',  -- Ford F-1100 (4eyes_100405)
    'b2c_393',  -- Chevrolet C130 (4eyes_100406)
    'b2c_395',  -- Ford F-1200 (4eyes_100408)
    'b2c_396',  -- Chevrolet C140 (4eyes_100409)
    'b2c_398',  -- Ford F-1300 (4eyes_100411)
    'b2c_399',  -- Chevrolet C150 (4eyes_100412)
    'b2c_401',  -- Ford F-1400 (4eyes_100414)
    'b2c_402',  -- Chevrolet C160 (4eyes_100415)
    'b2c_404',  -- Ford F-1500 (4eyes_100417)
    'b2c_405',  -- Chevrolet C170 (4eyes_100418)
    'b2c_407',  -- Ford F-1600 (4eyes_100420)
    'b2c_408',  -- Chevrolet C180 (4eyes_100421)
    'b2c_410',  -- Ford F-1700 (4eyes_100423)
    'b2c_411',  -- Chevrolet C190 (4eyes_100424)
    'b2c_413',  -- Ford F-1800 (4eyes_100426)
    'b2c_414',  -- Chevrolet C200 (4eyes_100427)
    'b2c_416',  -- Ford F-1900 (4eyes_100429)
    'b2c_417',  -- Chevrolet C210 (4eyes_100430)
    'b2c_419',  -- Ford F-2000 (4eyes_100432)
    'b2c_420',  -- Chevrolet C220 (4eyes_100433)
    'b2c_422',  -- Ford F-2100 (4eyes_100435)
    'b2c_423',  -- Chevrolet C230 (4eyes_100436)
    'b2c_425',  -- Ford F-2200 (4eyes_100438)
    'b2c_426',  -- Chevrolet C240 (4eyes_100439)
    'b2c_428',  -- Ford F-2300 (4eyes_100441)
    'b2c_429',  -- Chevrolet C250 (4eyes_100442)
    'b2c_432',  -- Chevrolet (4eyes_100445)
    'b2c_442',  -- Honda (4eyes_100455)
    'b2c_3417',  -- Honda (4eyes_100455)
    'b2c_168',  -- BMW (4eyes_100460)
    'b2c_447',  -- BMW (4eyes_100460)
    'b2c_169',  -- Audi (4eyes_100462)
    'b2c_449',  -- Audi (4eyes_100462)
    'b2c_173',  -- Lamborghini (4eyes_100470)
    'b2c_457',  -- Lamborghini (4eyes_100470)
    'b2c_181',  -- Maserati (4eyes_100471)
    'b2c_458',  -- Maserati (4eyes_100471)
    'b2c_177',  -- Jaguar (4eyes_100472)
    'b2c_459',  -- Jaguar (4eyes_100472)
    'b2c_175',  -- Land Rover (4eyes_100473)
    'b2c_460',  -- Land Rover (4eyes_100473)
    'b2c_464',  -- Suzuki (4eyes_100477)
    'b2c_466',  -- Honda Odyssey (4eyes_100479)
    'b2c_469',  -- Dodge Grand Caravan (4eyes_100482)
    'b2c_471',  -- Volkswagen Routan (4eyes_100484)
    'b2c_472',  -- Nissan Quest (4eyes_100485)
    'b2c_475',  -- Chevrolet Express (4eyes_100488)
    'b2c_476',  -- Ferrari 488 Gtb (4eyes_100489)
    'b2c_477',  -- Lamborghini Huracan (4eyes_100490)
    'b2c_478',  -- Porsche 911 Turbo S (4eyes_100491)
    'b2c_479',  -- Bugatti Chiron (4eyes_100492)
    'b2c_480',  -- Mclaren 720s (4eyes_100493)
    'b2c_481',  -- Audi R8 V10 Plus (4eyes_100494)
    'b2c_483',  -- Chevrolet Corvette C8 (4eyes_100496)
    'b2c_484',  -- Aston Martin Vantage (4eyes_100497)
    'b2c_485',  -- Ford Gt (4eyes_100498)
    'b2c_486',  -- Nissan Gt-r (4eyes_100499)
    'b2c_487',  -- Porsche 718 Cayman S (4eyes_100500)
    'b2c_488',  -- Jaguar F-type R (4eyes_100501)
    'b2c_489',  -- Dodge Viper (4eyes_100502)
    'b2c_490',  -- Acura Nsx (4eyes_100503)
    'b2c_491',  -- BMW M4 (4eyes_100504)
    'b2c_492',  -- Chevrolet Camaro Zl1 (4eyes_100505)
    'b2c_494',  -- Mclaren 570s (4eyes_100507)
    'b2c_496',  -- Porsche Panamera Turbo (4eyes_100509)
    'b2c_497',  -- Audi Rs5 (4eyes_100510)
    'b2c_498',  -- BMW M2 Competition (4eyes_100511)
    'b2c_500',  -- Honda Civic Type R (4eyes_100513)
    'b2c_501',  -- Jaguar F-type Svr (4eyes_100514)
    'b2c_503',  -- Land Rover Range Rover Sport Svr (4eyes_100516)
    'b2c_504',  -- Lexus Lc 500 (4eyes_100517)
    'b2c_505',  -- Mclaren 650s (4eyes_100518)
    'b2c_507',  -- Porsche 911 Gt3 (4eyes_100520)
    'b2c_508',  -- Audi R8 (4eyes_100521)
    'b2c_509',  -- BMW M5 (4eyes_100522)
    'b2c_510',  -- Chevrolet Corvette Z06 (4eyes_100523)
    'b2c_511',  -- Dodge Challenger Srt Hellcat (4eyes_100524)
    'b2c_512',  -- Ford Mustang Gt (4eyes_100525)
    'b2c_513',  -- Honda S2000 (4eyes_100526)
    'b2c_514',  -- Jaguar Xk (4eyes_100527)
    'b2c_516',  -- Land Rover Range Rover Sport (4eyes_100529)
    'b2c_517',  -- Lexus Rc F (4eyes_100530)
    'b2c_518',  -- Mclaren 570gt (4eyes_100531)
    'b2c_520',  -- Porsche 911 Carrera S (4eyes_100533)
    'b2c_521',  -- Audi S5 (4eyes_100534)
    'b2c_522',  -- BMW M3 (4eyes_100535)
    'b2c_523',  -- Chevrolet Camaro Ss (4eyes_100536)
    'b2c_525',  -- Honda S660 (4eyes_100538)
    'b2c_526',  -- Jaguar Xf R (4eyes_100539)
    'b2c_528',  -- Land Rover Range Rover Evoque (4eyes_100541)
    'b2c_529',  -- Lexus Rc (4eyes_100542)
    'b2c_530',  -- Mclaren 540c (4eyes_100543)
    'b2c_532',  -- Porsche 911 (4eyes_100545)
    'b2c_533',  -- Audi Rs7 (4eyes_100546)
    'b2c_534',  -- BMW M6 (4eyes_100547)
    'b2c_535',  -- Chevrolet Camaro Z28 (4eyes_100548)
    'b2c_537',  -- Honda S1000 (4eyes_100550)
    'b2c_538',  -- Jaguar Xe S (4eyes_100551)
    'b2c_540',  -- Land Rover Range Rover (4eyes_100553)
    'b2c_541',  -- Lexus Ls (4eyes_100554)
    'b2c_542',  -- Mclaren 12c (4eyes_100555)
    'b2c_544',  -- Porsche 911 Gt2 (4eyes_100557)
    'b2c_4510',  -- Acura ILX (4eyes_104755)
    'b2c_4511',  -- Acura MDX (4eyes_104756)
    'b2c_4512',  -- Acura RDX (4eyes_104757)
    'b2c_4513',  -- Acura RLX (4eyes_104758)
    'b2c_4514',  -- Acura TLX (4eyes_104759)
    'b2c_4515',  -- Alfa Romeo 4C (4eyes_104760)
    'b2c_4516',  -- Alfa Romeo Giulia (4eyes_104761)
    'b2c_4517',  -- Alfa Romeo Stelvio (4eyes_104762)
    'b2c_4518',  -- Aston Martin DB11 (4eyes_104763)
    'b2c_4519',  -- Aston Martin DBS Superleggera (4eyes_104764)
    'b2c_4520',  -- Aston Martin Rapide S (4eyes_104765)
    'b2c_4521',  -- Aston Martin Valkyrie (4eyes_104766)
    'b2c_4522',  -- Aston Martin Vanquish (4eyes_104767)
    'b2c_4523',  -- Aston Martin Vanquish Zagato (4eyes_104768)
    'b2c_4539',  -- Bentley Bentayga (4eyes_104785)
    'b2c_4540',  -- Bentley Continental (4eyes_104786)
    'b2c_4541',  -- Bentley Flying Spur (4eyes_104787)
    'b2c_4542',  -- Bentley Mulsanne (4eyes_104788)
    'b2c_4543',  -- BMW 2 Series (4eyes_104789)
    'b2c_4544',  -- BMW 3 Series (4eyes_104790)
    'b2c_4545',  -- BMW 4 Series (4eyes_104791)
    'b2c_4546',  -- BMW 5 Series (4eyes_104792)
    'b2c_4546',  -- BMW 5 Series (4eyes_104792)
    'b2c_4547',  -- BMW 6 Series (4eyes_104793)
    'b2c_4548',  -- BMW 8 Series (4eyes_104794)
    'b2c_4549',  -- BMW X1 (4eyes_104795)
    'b2c_4550',  -- BMW X2 (4eyes_104796)
    'b2c_4551',  -- BMW X3 (4eyes_104797)
    'b2c_4552',  -- BMW X4 (4eyes_104798)
    'b2c_4553',  -- BMW X6 (4eyes_104799)
    'b2c_4554',  -- BMW X7 (4eyes_104800)
    'b2c_4555',  -- BMW Z4 (4eyes_104801)
    'b2c_4557',  -- Buick Cascada (4eyes_104805)
    'b2c_4558',  -- Buick LaCrosse (4eyes_104806)
    'b2c_4559',  -- Buick Regal (4eyes_104807)
    'b2c_4560',  -- Buick Regal TourX (4eyes_104808)
    'b2c_4561',  -- Buick Verano (4eyes_104809)
    'b2c_4562',  -- Cadillac ATS (4eyes_104810)
    'b2c_4563',  -- Cadillac CT4 (4eyes_104811)
    'b2c_4564',  -- Cadillac CT5 (4eyes_104812)
    'b2c_4565',  -- Cadillac CT6 (4eyes_104813)
    'b2c_4566',  -- Cadillac CTS (4eyes_104814)
    'b2c_4567',  -- Cadillac XT4 (4eyes_104816)
    'b2c_4568',  -- Cadillac XT5 (4eyes_104817)
    'b2c_4569',  -- Cadillac XT6 (4eyes_104818)
    'b2c_4570',  -- Cadillac XTS (4eyes_104819)
    'b2c_4571',  -- Chevrolet Camaro (4eyes_104820)
    'b2c_4572',  -- Chevrolet Corvette (4eyes_104821)
    'b2c_4573',  -- Chevrolet Cruze (4eyes_104822)
    'b2c_4574',  -- Chevrolet Impala (4eyes_104823)
    'b2c_4575',  -- Chevrolet Malibu (4eyes_104824)
    'b2c_4576',  -- Chevrolet Sonic (4eyes_104825)
    'b2c_4577',  -- Chevrolet Spark (4eyes_104826)
    'b2c_4578',  -- Chevrolet SS (4eyes_104827)
    'b2c_4579',  -- Chevrolet Trax (4eyes_104828)
    'b2c_4580',  -- Chevrolet Volt (4eyes_104829)
    'b2c_4584',  -- Dodge Charger (4eyes_104833)
    'b2c_4585',  -- Dodge Dart (4eyes_104834)
    'b2c_4586',  -- Dodge Journey (4eyes_104835)
    'b2c_174',  -- Ferrari (4eyes_104836)
    'b2c_4587',  -- Ferrari (4eyes_104836)
    'b2c_4588',  -- Ferrari 488 Pista (4eyes_104837)
    'b2c_4589',  -- Ferrari 488 Spider (4eyes_104838)
    'b2c_4590',  -- Ferrari 812 Superfast (4eyes_104839)
    'b2c_4591',  -- Ferrari F12berlinetta (4eyes_104840)
    'b2c_4592',  -- Ferrari F12tdf (4eyes_104841)
    'b2c_4593',  -- Ferrari F8 Tributo (4eyes_104842)
    'b2c_4594',  -- Ferrari GTC4Lusso (4eyes_104843)
    'b2c_4594',  -- Ferrari GTC4Lusso (4eyes_104843)
    'b2c_4595',  -- Ferrari LaFerrari (4eyes_104844)
    'b2c_4596',  -- Ferrari Monza (4eyes_104845)
    'b2c_4597',  -- Ferrari Portofino (4eyes_104846)
    'b2c_4601',  -- Fiat 500E (4eyes_104850)
    'b2c_4603',  -- Fiat 500X (4eyes_104852)
    'b2c_4611',  -- Genesis G70 (4eyes_104860)
    'b2c_4612',  -- Genesis G80 (4eyes_104861)
    'b2c_4613',  -- Genesis G90 (4eyes_104862)
    'b2c_4615',  -- Honda Clarity (4eyes_104865)
    'b2c_4616',  -- Honda CR-Z (4eyes_104867)
    'b2c_4617',  -- Honda Element (4eyes_104868)
    'b2c_4618',  -- Honda Fit (4eyes_104869)
    'b2c_4619',  -- Honda Insight (4eyes_104871)
    'b2c_4620',  -- Hyundai Accent (4eyes_104875)
    'b2c_4621',  -- Hyundai Elantra (4eyes_104876)
    'b2c_4622',  -- Hyundai Genesis (4eyes_104877)
    'b2c_4623',  -- Hyundai Nexo (4eyes_104880)
    'b2c_4624',  -- Hyundai Veloster (4eyes_104885)
    'b2c_4625',  -- Infiniti EX37 (4eyes_104888)
    'b2c_4626',  -- Infiniti FX37 (4eyes_104889)
    'b2c_4627',  -- Infiniti G37 (4eyes_104890)
    'b2c_4628',  -- Infiniti JX35 (4eyes_104891)
    'b2c_4629',  -- Infiniti M37 (4eyes_104892)
    'b2c_4630',  -- Infiniti Q40 (4eyes_104893)
    'b2c_4631',  -- Infiniti Q60 (4eyes_104894)
    'b2c_4632',  -- Infiniti Q70 (4eyes_104895)
    'b2c_4633',  -- Infiniti Q70L (4eyes_104896)
    'b2c_4634',  -- Infiniti QX30 (4eyes_104897)
    'b2c_4635',  -- Infiniti QX50 (4eyes_104898)
    'b2c_4636',  -- Infiniti QX55 (4eyes_104899)
    'b2c_4637',  -- Infiniti QX70 (4eyes_104900)
    'b2c_4638',  -- Infiniti QX80 (4eyes_104901)
    'b2c_4639',  -- Jaguar E-Pace (4eyes_104902)
    'b2c_4640',  -- Jaguar F-Pace (4eyes_104903)
    'b2c_4641',  -- Jaguar F-Type (4eyes_104904)
    'b2c_4642',  -- Jaguar I-Pace (4eyes_104905)
    'b2c_4643',  -- Jaguar XJ (4eyes_104906)
    'b2c_4644',  -- Jeep Cherokee (4eyes_104907)
    'b2c_4645',  -- Jeep Compass (4eyes_104908)
    'b2c_4646',  -- Jeep Gladiator (4eyes_104909)
    'b2c_4647',  -- Jeep Grand Wagoneer (4eyes_104910)
    'b2c_4648',  -- Jeep Patriot (4eyes_104911)
    'b2c_4649',  -- Jeep Renegade (4eyes_104912)
    'b2c_4650',  -- Jeep Wrangler (4eyes_104913)
    'b2c_4651',  -- Kia Cadenza (4eyes_104914)
    'b2c_4652',  -- Kia Forte (4eyes_104915)
    'b2c_4653',  -- Kia K5 (4eyes_104916)
    'b2c_4654',  -- Kia K900 (4eyes_104917)
    'b2c_4655',  -- Kia Niro (4eyes_104918)
    'b2c_4656',  -- Kia Optima (4eyes_104919)
    'b2c_4657',  -- Kia Rio (4eyes_104920)
    'b2c_4658',  -- Kia Stinger (4eyes_104921)
    'b2c_4659',  -- Land Rover Defender (4eyes_104922)
    'b2c_4660',  -- Land Rover LR4 (4eyes_104923)
    'b2c_4661',  -- Land Rover Velar (4eyes_104924)
    'b2c_4662',  -- Lexus CT200 (4eyes_104925)
    'b2c_4663',  -- Lexus ES (4eyes_104926)
    'b2c_4664',  -- Lexus GS (4eyes_104927)
    'b2c_4665',  -- Lexus IS (4eyes_104928)
    'b2c_4666',  -- Lexus LX (4eyes_104929)
    'b2c_4667',  -- Lexus NX (4eyes_104930)
    'b2c_4668',  -- Lexus RX (4eyes_104931)
    'b2c_4669',  -- Lexus UX (4eyes_104932)
    'b2c_4670',  -- Lincoln Aviator (4eyes_104933)
    'b2c_4671',  -- Lincoln Continental (4eyes_104934)
    'b2c_4672',  -- Lincoln Corsair (4eyes_104935)
    'b2c_4673',  -- Lincoln MKC (4eyes_104936)
    'b2c_4674',  -- Lincoln MKS (4eyes_104937)
    'b2c_4675',  -- Lincoln MKT (4eyes_104938)
    'b2c_4676',  -- Lincoln MKX (4eyes_104939)
    'b2c_4677',  -- Lincoln MKZ (4eyes_104940)
    'b2c_4678',  -- Lincoln Nautilus (4eyes_104941)
    'b2c_4679',  -- Lincoln Navigator (4eyes_104942)
    'b2c_4680',  -- Maserati Levante (4eyes_104944)
    'b2c_4681',  -- Maserati Quattroporte (4eyes_104945)
    'b2c_4682',  -- Mazda CX-30 (4eyes_104947)
    'b2c_4683',  -- Mazda Mazda CX-3 (4eyes_104948)
    'b2c_4684',  -- Mazda Mazda CX-5 (4eyes_104949)
    'b2c_4685',  -- Mazda Mazda CX-9 (4eyes_104950)
    'b2c_4686',  -- Mazda Mazda MX-5 (4eyes_104951)
    'b2c_4687',  -- Mazda Mazda3 (4eyes_104952)
    'b2c_4688',  -- Mazda Mazda5 (4eyes_104953)
    'b2c_4689',  -- Mazda Mazda6 (4eyes_104954)
    'b2c_4690',  -- Mercedes AMG-GT (4eyes_104956)
    'b2c_4690',  -- Mercedes AMG-GT (4eyes_104956)
    'b2c_4691',  -- Mercedes SL Roadster (4eyes_104957)
    'b2c_4692',  -- Mercedes Benz A-Class (4eyes_104958)
    'b2c_4693',  -- Mercedes Benz B-Class (4eyes_104959)
    'b2c_4694',  -- Mercedes Benz C-Class (4eyes_104960)
    'b2c_4695',  -- Mercedes Benz CLA-Class (4eyes_104961)
    'b2c_4696',  -- Mercedes Benz CLS-Class (4eyes_104962)
    'b2c_4697',  -- Mercedes Benz G-Class (4eyes_104963)
    'b2c_4698',  -- Mercedes Benz GLA-Class (4eyes_104964)
    'b2c_4699',  -- Mercedes Benz GLB-Class (4eyes_104965)
    'b2c_4700',  -- Mercedes Benz GLC-Class (4eyes_104966)
    'b2c_4701',  -- Mercedes Benz GLS-Class (4eyes_104967)
    'b2c_4702',  -- Mercedes Benz Mercedes-Maybach (4eyes_104968)
    'b2c_4703',  -- Mercedes Benz S-Class (4eyes_104969)
    'b2c_4714',  -- Nissan 370Z (4eyes_104981)
    'b2c_4715',  -- Nissan Altima (4eyes_104982)
    'b2c_4716',  -- Nissan Armada (4eyes_104983)
    'b2c_4717',  -- Nissan Kicks (4eyes_104984)
    'b2c_4718',  -- Nissan Maxima (4eyes_104985)
    'b2c_4719',  -- Nissan Murano (4eyes_104986)
    'b2c_4720',  -- Nissan Rogue (4eyes_104987)
    'b2c_4721',  -- Nissan Rogue Sport (4eyes_104988)
    'b2c_4722',  -- Nissan Sentra (4eyes_104989)
    'b2c_4723',  -- Nissan Titan XD (4eyes_104990)
    'b2c_4724',  -- Nissan Versa (4eyes_104991)
    'b2c_4725',  -- Porsche Macan (4eyes_104992)
    'b2c_4726',  -- Porsche Panamera (4eyes_104993)
    'b2c_4735',  -- Subaru BRZ (4eyes_105003)
    'b2c_4736',  -- Subaru Crosstrek (4eyes_105004)
    'b2c_4737',  -- Subaru Crosstrek Hybrid (4eyes_105005)
    'b2c_4738',  -- Subaru Forester (4eyes_105006)
    'b2c_4739',  -- Subaru Impreza (4eyes_105007)
    'b2c_4740',  -- Subaru Legacy (4eyes_105008)
    'b2c_4741',  -- Subaru Outback (4eyes_105009)
    'b2c_4742',  -- Subaru WRX (4eyes_105010)
    'b2c_4761',  -- Volkswagen Arteon (4eyes_105029)
    'b2c_4762',  -- Volkswagen Atlas Cross Sport (4eyes_105030)
    'b2c_4763',  -- Volkswagen Beetle (4eyes_105031)
    'b2c_4764',  -- Volkswagen CC (4eyes_105032)
    'b2c_4765',  -- Volkswagen Golf (4eyes_105033)
    'b2c_4766',  -- Volkswagen Jetta (4eyes_105034)
    'b2c_4767',  -- Volkswagen Jetta GLI (4eyes_105035)
    'b2c_4768',  -- Volkswagen Passat (4eyes_105036)
    'b2c_4769',  -- Volkswagen Tiguan (4eyes_105037)
    'b2c_4770',  -- Volkswagen Touareg (4eyes_105038)
    'b2c_4771',  -- Volvo S60 (4eyes_105039)
    'b2c_4772',  -- Volvo S80 (4eyes_105040)
    'b2c_4773',  -- Volvo S90 (4eyes_105041)
    'b2c_4774',  -- Volvo V60 (4eyes_105042)
    'b2c_4775',  -- Volvo V90 (4eyes_105043)
    'b2c_4776',  -- Volvo XC40 (4eyes_105044)
    'b2c_4777',  -- Volvo XC60 (4eyes_105045)
    'b2c_4798',  -- Motorcycles (4eyes_105067)
    'b2c_6414',  -- Audi A7 (4eyes_121962)
    'b2c_6415',  -- Audi Q8 E-Tron (4eyes_121963)
    'b2c_6416',  -- Audi RS 7 (4eyes_121964)
    'b2c_6417',  -- Bentley (4eyes_121965)
    'b2c_6418',  -- BMW 7 Series (4eyes_121966)
    'b2c_6419',  -- BMW 2 (4eyes_121967)
    'b2c_6420',  -- BMW 3 (4eyes_121968)
    'b2c_6421',  -- Chevrolet Bolt EV (4eyes_121969)
    'b2c_6422',  -- Dodge Challenger (4eyes_121970)
    'b2c_6425',  -- GMC Sierra 2500HD (4eyes_121973)
    'b2c_6426',  -- GMC Sierra 3500HD (4eyes_121974)
    'b2c_6427',  -- Honda Civic (4eyes_121975)
    'b2c_6429',  -- Jaguar XE (4eyes_121977)
    'b2c_6430',  -- Jaguar XF (4eyes_121978)
    'b2c_6431',  -- Lexus LC (4eyes_121979)
    'b2c_6432',  -- Lotus (4eyes_121980)
    'b2c_6436',  -- Mercedes Benz E-Class (4eyes_121984)
    'b2c_6437',  -- Mercedes Benz GLE-Class (4eyes_121985)
    'b2c_6441',  -- Porsche 718 (4eyes_121989)
    'b2c_6442',  -- Porsche Cayman (4eyes_121990)
    'b2c_6443'  -- Rolls Royce (4eyes_121991)
    )
)
SELECT
   ethash,
   streetaddress,
   city,
   state,
   zipcode,
  "PTI",
  "PUI",
  "POI",
  "TG",
  "VTI",
  "VOI",
  "GC",
  "NC"
FROM p_intenders
FULL OUTER JOIN g_intender USING(ethash)
FULL OUTER JOIN vt_intender USING(ethash)
FULL OUTER JOIN vo_intender USING(ethash)
LEFT JOIN gc_intender USING(ethash)
LEFT JOIN nc_intender USING(ethash)
JOIN "s3"."silver_corelogic"."clip_ethash_bridge" bdg ON ethash = bdg.ethashv1 
WHERE state in (
    SELECT state 
    FROM "s3"."bronze_auto_intender"."fry_region_state_map" 
    WHERE region in ({region_list})
    )
{limit_clause}
  
"""

new_quote_query = """
    SELECT 
    pq.streetaddress,
    pq.zipcode
    from s3.{SCHEMA}.{PREQUOTE_TABLE_NAME} pq
    LEFT JOIN (
        SELECT * FROM s3.{SCHEMA}.{ANNOTATED_TABLE_NAME}
        WHERE date >= CURRENT_DATE - interval '{requote_days}' day AND date != CURRENT_DATE and "group" = '{group}'
        ) qh
    ON pq.ethash = qh.ethash
    WHERE pq.date = CURRENT_DATE AND qh.target IS NULL and pq."group" = '{group}'
"""

deliverable_query = """
    SELECT 
    COALESCE(target, 'f') AS target,
    pq.ethash,
    pq.streetaddress,
   city,
   state,
   pq.zipcode,
   pti ,
   pui ,
   poi ,
   tg ,
   vti ,
   voi ,
   gc ,
   nc ,
   segment_group ,
   seg_id
    from s3.{SCHEMA}.{PREQUOTE_TABLE_NAME} pq
    LEFT JOIN (
        SELECT * FROM s3.{SCHEMA}.{ANNOTATED_TABLE_NAME}
        WHERE date >= CURRENT_DATE - interval '{requote_days}' day and "group" = '{group}'
        ) qh
    ON pq.ethash = qh.ethash
    WHERE pq.date = CURRENT_DATE and pq."group" = '{group}'
"""

metrics_insert_query = """
    INSERT INTO "s3"."bronze_auto_intender".{env}_toyota_deliverable_metrics
    SELECT 
        tdy."date",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '2d' AND yst.segment_group IS NULL) AS "2d",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '5a' AND yst.segment_group IS NULL) AS "5a",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '4a' AND yst.segment_group IS NULL) AS "4a",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '3b' AND yst.segment_group IS NULL) AS "3b",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '2c' AND yst.segment_group IS NULL) AS "2c",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '4b' AND yst.segment_group IS NULL) AS "4b",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '2a' AND yst.segment_group IS NULL) AS "2a",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '5c' AND yst.segment_group IS NULL) AS "5c",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '1a' AND yst.segment_group IS NULL) AS "1a",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '2b' AND yst.segment_group IS NULL) AS "2b",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '1b' AND yst.segment_group IS NULL) AS "1b",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '3a' AND yst.segment_group IS NULL) AS "3a",
        count(tdy.ethash) FILTER (WHERE tdy.segment_group = '5b' AND yst.segment_group IS NULL) AS "5b",
        '{config_name}' AS "group",
        count(distinct tdy.ethash) AS total_delivered_rows 
    FROM (SELECT * from "s3"."bronze_auto_intender"."prod_toyota_deliverable" where "date" = DATE('{tdy}')) tdy
    LEFT JOIN (SELECT * from "s3"."bronze_auto_intender"."prod_toyota_deliverable" where "date" = DATE('{yst}')) yst ON tdy.ethash = yst.ethash
    WHERE tdy."group" = '{config_name}'
    GROUP BY tdy."date"
    ORDER BY tdy."date"
"""
