from collections import OrderedDict

segment_rules = OrderedDict({
    "3a": {
        "conditions": [
            {"seg_id": 1, "logic": {"PTI": 1, "VTI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 2, "logic": {"PTI": 1, "VTI": 1, "TG": 1, "NC": 1}},
            {"seg_id": 3, "logic": {"PTI": 1, "VOI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 4, "logic": {"PTI": 1, "VOI": 1, "TG": 1, "NC": 1}},
        ]
    },
    "2a": {
        "conditions": [
            {"seg_id": 25, "logic": {"PUI": 1, "VTI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 26, "logic": {"PUI": 1, "VTI": 1, "TG": 1, "NC": 1}},
            {"seg_id": 27, "logic": {"PUI": 1, "VOI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 28, "logic": {"PUI": 1, "VOI": 1, "TG": 1, "NC": 1}},
        ]
    },
    "1a": {
        "conditions": [
            {"seg_id": 13, "logic": {"POI": 1, "VTI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 14, "logic": {"POI": 1, "VTI": 1, "TG": 1, "NC": 1}},
            {"seg_id": 15, "logic": {"POI": 1, "VOI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 16, "logic": {"POI": 1, "VOI": 1, "TG": 1, "NC": 1}},
        ]
    },
    "4b": {
        "conditions": [
            {"seg_id": 5, "logic": {"PTI": 1, "VTI": 1, "GC": 1}},
            {"seg_id": 6, "logic": {"PTI": 1, "VTI": 1, "NC": 1}},
            {"seg_id": 7, "logic": {"PTI": 1, "VOI": 1, "GC": 1}},
            {"seg_id": 8, "logic": {"PTI": 1, "VOI": 1, "NC": 1}},
        ]
    },
    "3b": {
        "conditions": [
            {"seg_id": 9, "logic": {"PTI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 10, "logic": {"PTI": 1, "TG": 1, "NC": 1}},
        ],
    },
    "5b": {
        "conditions": [
            {"seg_id": 11, "logic": {"PTI": 1, "GC": 1}},
            {"seg_id": 12, "logic": {"PTI": 1, "NC": 1}},
        ],
    },
    "2d": {
        "conditions": [
            {"seg_id": 29, "logic": {"PUI": 1, "VTI": 1, "GC": 1}},
            {"seg_id": 30, "logic": {"PUI": 1, "VTI": 1, "NC": 1}},
            {"seg_id": 31, "logic": {"PUI": 1, "VOI": 1, "GC": 1}},
            {"seg_id": 32, "logic": {"PUI": 1, "VOI": 1, "NC": 1}},
        ]
    },
    "2b": {
        "conditions": [
            {"seg_id": 33, "logic": {"PUI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 34, "logic": {"PUI": 1, "TG": 1, "NC": 1}},
        ]
    },
    "5c": {
        "conditions": [
            {"seg_id": 35, "logic": {"PUI": 1, "GC": 1}},
            {"seg_id": 36, "logic": {"PUI": 1, "NC": 1}},
        ],
    },
    "4a": {
        "conditions": [
            {"seg_id": 17, "logic": {"POI": 1, "VTI": 1, "GC": 1}},
            {"seg_id": 18, "logic": {"POI": 1, "VTI": 1, "NC": 1}},
            {"seg_id": 19, "logic": {"POI": 1, "VOI": 1, "GC": 1}},
            {"seg_id": 20, "logic": {"POI": 1, "VOI": 1, "NC": 1}},
        ]
    },
    "1b": {
        "conditions": [
            {"seg_id": 21, "logic": {"POI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 22, "logic": {"POI": 1, "TG": 1, "NC": 1}},
        ]
    },
    "5a": {
        "conditions": [
            {"seg_id": 23, "logic": {"POI": 1, "GC": 1}},
            {"seg_id": 24, "logic": {"POI": 1, "NC": 1}},
        ],
    },
    "2c": {
        "conditions": [
            {"seg_id": 37, "logic": {"VTI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 38, "logic": {"VTI": 1, "TG": 1, "NC": 1}},
            {"seg_id": 41, "logic": {"VOI": 1, "TG": 1, "GC": 1}},
            {"seg_id": 42, "logic": {"VOI": 1, "TG": 1, "NC": 1}},
        ]
    },
})