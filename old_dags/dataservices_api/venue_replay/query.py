process_data = """
SELECT 
    COALESCE(atd.deviceid, addrs.deviceid) AS deviceid,
    COUNT(DISTINCT atd.date) AS days_seen,  
    COALESCE(addrs.address, '') AS address,
    COALESCE(addrs.city, '') AS city,
    COALESCE(addrs.state, '') AS state,
    COALESCE(addrs.zipcode, '') AS zipcode,
    COALESCE(addrs.zip4, '') AS zip4
FROM (
    
    SELECT DISTINCT
        date,
        deviceid
    FROM s3.bronze_ts_reports.{env}_os_response_observations atd
    CROSS JOIN UNNEST(SPLIT(atd.timestamp, ',')) AS b (observations)
    WHERE request_id = '{audience_id}'
) atd
FULL OUTER JOIN (
    
    SELECT DISTINCT
        address,
        city,
        state,
        zipcode,
        zip4,
        deviceid
    FROM s3.bronze_ts_reports.{env}_os_response_addresstodevice atd
    CROSS JOIN UNNEST(SPLIT(atd.devices, ',')) AS b (deviceid)
    WHERE request_id = '{audience_id}'
) addrs
ON atd.deviceid = addrs.deviceid
GROUP BY COALESCE(atd.deviceid, addrs.deviceid), addrs.address, addrs.city, addrs.state, addrs.zipcode, addrs.zip4
ORDER BY deviceid ASC, address ASC;

"""
