from airflow.decorators import task
from etdag import ETDAG
from datetime import datetime, timedelta
from airflow.models import Variable
import pandas as pd
from airflow.exceptions import AirflowFailException
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import tempfile
from urllib.parse import urlparse
import logging
from pygene.audience_api import NextGenAudienceAPI
import json


logger = logging.getLogger(__name__)


###### Checks inputs passed #########
@task()
def requestor_output_creator(**context):
    passed_values = context["dag_run"].conf or {}
    input_s3_uri = passed_values.get("input_s3_uri")
    output_s3_uri = passed_values.get("output_s3_uri")
    audience_id = passed_values.get("audience_id")
    org_id = passed_values.get("org_id")

    endpoint_configs = {
        "observations": {
            "endpoint": "/save/geoframe/all/observations",
            "request_type": "observations",
            "latlng_prefix": "obs",
        },
        "addresstodevice": {
            "endpoint": "/save/geoframe/household/devicesbyaddress",
            "request_type": "addresstodevice",
            "latlng_prefix": "hh",
        },
    }
    endpoint_list = [config["endpoint"] for config in endpoint_configs.values()]

    if not input_s3_uri or not output_s3_uri or not audience_id:
        error_message = f"Missing required parameters! "
        logger.error(error_message)
        context["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowFailException(
            f"Missing required parameters! "
            f"Received: input_s3_uri={input_s3_uri}, output_s3_uri={output_s3_uri}, audience_id={audience_id}"
        )

    return {
        "request_id": audience_id,
        "org_id": org_id,
        "prepared_features_s3_uri": input_s3_uri,
        "output_s3_uri": output_s3_uri,
        "endpoint_list": endpoint_list,
    }


##### Parse an s3 string ########
def parse_s3_uri(s3_uri: str):
    """Parse an S3 URI into bucket name and key."""
    try:
        parsed_s3_uri = urlparse(s3_uri)
        bucket_name = parsed_s3_uri.netloc
        s3_key = parsed_s3_uri.path.lstrip("/")

        if not bucket_name or not s3_key:
            raise ValueError("Invalid S3 URI format")

        return bucket_name, s3_key

    except Exception as e:
        error_message = f"Failed to parse S3 URI: {s3_uri}. Error: {e}"
        logger.error(error_message)
        raise AirflowFailException(error_message)


##### This will be triggered if a task fails and pass the error message ######
@task(trigger_rule="one_failed")
def handle_failure(**kwargs):
    ti = kwargs["ti"]

    failed_tasks = [
        "requestor_output_creator",
        "counting_task",
        "failure_task",
        "date_checker_and_upload",
    ]

    for task_id in failed_tasks:
        print(task_id)
        error_msg = ti.xcom_pull(task_ids=task_id, key="error_message")
        if error_msg:
            logger.error(f"Handling failure: {task_id} failed with error: {error_msg}")
            return f"{task_id} failed: {error_msg}"

    return "Unknown error occurred."


####### Task to check a date and cause a failure if needed ######
@task()
def date_checker_and_upload(requestor_output, **kwargs):
    df = pd.read_csv(requestor_output["prepared_features_s3_uri"], dtype=str)
    s3_bucket, s3_key = parse_s3_uri(requestor_output["output_s3_uri"])
    date_str = df["start"].iloc[0]
    # date_str = "2025-02-05T05:00:00Z"
    input_date = datetime.fromisoformat(date_str.replace("Z", "+00:00"))

    now = datetime.now(input_date.tzinfo)  # Use the timezone of the input.
    one_year_ago = now - timedelta(days=365)
    if input_date < one_year_ago:
        error_message = "Date is more than one year in the past."
        logger.error(error_message)
        kwargs["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowFailException(error_message)

    s3_hook = S3Hook(aws_conn_id="s3_conn")

    with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=True) as temp_file:
        df.to_csv(temp_file.name, index=False)
        temp_file.flush()

        try:
            s3_hook.load_file(
                filename=temp_file.name,
                key=s3_key,
                bucket_name=s3_bucket,
                replace=True,
            )
            logger.info(f"File uploaded to S3: s3://{s3_bucket}/{s3_key}")

        except Exception as s3_error:
            error_message = (
                f"Failed to upload file to S3: {s3_bucket}/{s3_key}. Error: {s3_error}"
            )

            logger.error(error_message)
            kwargs["ti"].xcom_push(key="error_message", value=error_message)
            raise AirflowFailException(error_message)

    return


#### just extra for testing ####
@task()
def failure_task(**kwargs):
    error_message = "This task is meant to fail."
    logger.error(error_message)
    kwargs["ti"].xcom_push(key="error_message", value=error_message)
    # raise AirflowFailException("This task is meant to fail.")


#### just extra for testing ####


@task()
def counting_task():
    return {"count": 1}


###### generate a vr audience and will pass the error if there is one ######
@task(
    retries=3,
    trigger_rule="all_done",  ######Ensure it doesnt fail do to upstream failures ######
)
def generate_vr_audience(requestor_output, error: str = None):
    env = "dev"
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    aud_id = requestor_output["request_id"]
    org_id = requestor_output["org_id"]

    # "{\"dag_error\": \"This was an error caused by this or that.\"}"
    audience_api = NextGenAudienceAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env=env,
    )
    query_args = json.dumps({"dag_error": f"{error}."})
    logger.info("Generating audience")
    logger.info(f"Audience ID is {aud_id}")
    try:

        if error is not None:
            logger.error(f"Error: {error}")
            audience_api._generate_audience(audience_id=aud_id, query_args=query_args)
        else:
            audience_api._generate_audience(aud_id)

    except Exception as e:
        error_message = f"Error generating audience: {str(e)}"
        logger.error(error_message)
        raise AirflowFailException(error_message)
    audience_api._poll_for_audience_status_ready(aud_id)


with ETDAG(
    dag_id="dev_venue_replay_job_workflow",
    catchup=False,
    default_args={"owner": "BP", "retries": 3},
    is_paused_upon_creation=True,
    start_date=datetime(2025, 1, 22),
    schedule_interval=None,
    max_active_runs=3,
    tags=["venue_replay, DND"],
    et_failure_msg=True,
    params={
        "audience_id": "",
        "org_id": "",
        "input_s3_uri": "",
        "output_s3_uri": "",
        # "audience_id": "cv3thhgmt4hc77vu63c0",
        # "org_id": "cn71masfdsoc73fdb2pg",
        # "input_s3_uri": "s3://targetuploads.eltoro.com/local/cv3thh6p80ks77p7c1cg/versions/1/quote/JOB_TYPE_VR/2025-03-05/cv3thhgmt4hc77vu63c0/geojson.gz",
        # "output_s3_uri": "s3://targetuploads.eltoro.com/local/vr-observations/quote/JOB_TYPE_VR/2025-03-05/cv3thhgmt4hc77vu63c0/ce11dae2-5de1-49d5-8be8-c74493a7df8f.csv",
    },
) as dag:

    counting_task_output = counting_task()
    failure_task_output = failure_task()

    requestor_output = requestor_output_creator()
    date_checker_and_upload_task = date_checker_and_upload(requestor_output)
    handle_failure_task = handle_failure()
    generate_vr_audience_task = generate_vr_audience(
        requestor_output, handle_failure_task
    )

#### Main workflow without failures ####
(
    requestor_output
    >> date_checker_and_upload_task
    >> counting_task_output
    >> failure_task_output
    >> generate_vr_audience_task
)

#### Workflow if failures ####
(
    [
        requestor_output,
        counting_task_output,
        failure_task_output,
        date_checker_and_upload_task,
    ]
    >> handle_failure_task
)

#### Esures generate audince runs whether failures or not, if there are failures it will pass an error###
handle_failure_task >> generate_vr_audience_task
