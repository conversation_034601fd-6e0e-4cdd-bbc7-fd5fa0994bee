from airflow.decorators import task
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.dataservices_api.venue_replay.query import process_data
import tempfile
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.exceptions import AirflowFailException, AirflowException
import logging
from urllib.parse import urlparse
from pygene.audience_api import NextGenAudienceAPI
import json
from airflow.models import Variable

logger = logging.getLogger(__name__)


def sync_addresstodevice(audience_id, env):
    """Sync partition metadata for address-to-device table in Trino."""
    try:
        logger.info(
            f"Syncing table: {env}_os_response_addresstodevice for job_id: {audience_id}"
        )

        trino_hook = TrinoHook(trino_conn_id="trino_conn")
        sync_query = f"CALL s3.system.sync_partition_metadata('bronze_ts_reports', '{env}_os_response_addresstodevice', 'ADD')"

        trino_hook.run(sync_query)
        logger.info(
            f"Sync successful for table: {env}_os_response_addresstodevice for job_id: {audience_id}"
        )
        return audience_id

    except Exception as e:
        error_message = f"Sync failed for {env}_os_response_addresstodevice, audience_id: {audience_id}. Error: {e}"
        logger.error(error_message)
        raise AirflowException(f"Sync failed: {e}")


def sync_observations(audience_id, env):
    """Sync partition metadata for observations table in Trino."""
    try:
        logger.info(
            f"Syncing table: {env}_os_response_observations for job_id: {audience_id}"
        )

        trino_hook = TrinoHook(trino_conn_id="trino_conn")
        sync_query = f"CALL s3.system.sync_partition_metadata('bronze_ts_reports', '{env}_os_response_observations', 'ADD')"

        trino_hook.run(sync_query)
        logger.info(
            f"Sync successful for table: {env}_os_response_observations for job_id: {audience_id}"
        )
        return audience_id

    except Exception as e:
        error_message = f"Sync failed for {env}_os_response_observations, audience_id: {audience_id}. Error: {e}"
        logger.error(error_message)
        raise AirflowException(f"Sync failed: {e}")


def parse_s3_uri(s3_uri: str):
    """Parse an S3 URI into bucket name and key."""
    try:
        parsed_s3_uri = urlparse(s3_uri)
        bucket_name = parsed_s3_uri.netloc
        s3_key = parsed_s3_uri.path.lstrip("/")

        if not bucket_name or not s3_key:
            raise ValueError("Invalid S3 URI format")

        return bucket_name, s3_key

    except Exception as e:
        logger.error(f"Failed to parse S3 URI: {s3_uri}. Error: {e}")
        raise AirflowException(f"Invalid S3 URI: {s3_uri}")


def aggregate_and_upload(requestor_output, env):
    """Aggregate data using Trino, process it into a DataFrame, and upload to S3."""
    try:
        audience_id = requestor_output.get("request_id")
        output_s3_uri = requestor_output.get("output_s3_uri")

        if not audience_id or not output_s3_uri:
            raise AirflowFailException("Missing required fields in requestor_output")

        logger.info("Aggregating data with Trino")
        s3_bucket, s3_key = parse_s3_uri(output_s3_uri)

        tr = TrinoHook(trino_conn_id="trino_conn")
        df = tr.get_pandas_df(sql=process_data.format(env=env, audience_id=audience_id))

        logger.info(f"Dataframe Shape: {df.shape}")

        s3_hook = S3Hook(aws_conn_id="s3_conn")

        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".csv", delete=True
        ) as temp_file:
            df.to_csv(temp_file.name, index=False)
            temp_file.flush()

            try:
                s3_hook.load_file(
                    filename=temp_file.name,
                    key=s3_key,
                    bucket_name=s3_bucket,
                    replace=True,
                )
                logger.info(f"File uploaded to S3: s3://{s3_bucket}/{s3_key}")

            except Exception as s3_error:
                error_message = f"Failed to upload file to S3: {s3_bucket}/{s3_key}. Error: {s3_error}"
                logger.error(error_message)
                raise AirflowException(f"S3 upload failed: {s3_error}")

    except Exception as e:
        error_message = f"Aggregation and upload failed. Error: {e}"
        logger.error(error_message)
        raise AirflowException(f"Aggregation failed: {e}")


def generate_vr_audience(requestor_output, env, error: str = None):
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    aud_id = requestor_output["request_id"]
    org_id = requestor_output["org_id"]

    audience_api = NextGenAudienceAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env=env,
    )

    logger.info("Generating audience")
    logger.info(f"Audience ID is {aud_id}")
    print(f"Error: {error}")

    query_args = {"dag_status": "failed" if error else "completed", "output_s3_uri": requestor_output["output_s3_uri"]}
    # if error:
    #     query_args["dag_error"] = error

    try:
        print(f"Query Args: {query_args}")
        audience_api._generate_audience(aud_id, json.dumps(query_args))

    except Exception as e:
        logger.error(f"Error generating audience {aud_id}: {str(e)}")
        raise AirflowException(f"Failed to generate audience: {str(e)}")


def poll_for_audience_status(requestor_output, env):
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    aud_id = requestor_output["request_id"]
    org_id = requestor_output["org_id"]

    audience_api = NextGenAudienceAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env=env,
    )

    try:
        audience_api._poll_for_audience_status_ready(aud_id)

    except Exception as e:
        raise AirflowException(f"Status Failed: {e}")
