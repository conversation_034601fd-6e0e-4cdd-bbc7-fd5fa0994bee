import base64
from typing import Dict, <PERSON>, List
from shapely.geometry import  Polygon
from shapely.validation import make_valid
from shapely.geometry import shape, MultiPolygon, mapping, GeometryCollection
from shapely.geometry.base import BaseGeometry
from shapely.wkb import dumps as geom_to_wkb
from shapely.wkb import loads as wkb_loads
from shapely.errors import GEOSException
from shapely.ops import transform

import json
from datetime import date
from typing import List
import pandas as pd


def geo_validation(features: List[Dict], properties: Dict, max_area=2880648.0, min_area=0.01) -> List[Dict]:
    """
    Validate and normalize geometries locally (replacement for the external geo-validator API).
    Filters out polygons that are too small, and partitions polygons that are too large.

    Args:
        features (List[Dict]): List of features with geometries.
        properties (Dict): Dictionary of additional properties to attach to each feature.
        max_area (float): Maximum area allowed for any polygon before partitioning.
        min_area (float): Minimum area allowed for any polygon.

    Returns:
        List[Dict]: Response similar to the original geo-validator API.
    """
    normalized_geometries = []

    import pyproj
    from shapely.ops import transform

    # Project to Web Mercator for accurate area calculation
    project = pyproj.Transformer.from_crs("EPSG:4326", "EPSG:3857", always_xy=True).transform

    # Iterate over each feature and normalize it
    for feature in features:
        try:
            geometry = shape(feature['geometry'])
            normalized_geom = normalize_geometry(geometry)

            if normalized_geom:
                # First, partition any large geometries
                partitioned_geom = partition_large_geometry(normalized_geom, max_area)
                
                # Then filter out polygons that are too small (but keep all partitioned pieces)
                valid_polygons = []
                for geom in partitioned_geom.geoms:
                    # Project to get accurate area measurement
                    projected_geom = transform(project, geom)
                    if projected_geom.area >= min_area:
                        valid_polygons.append(geom)

                # Only proceed if we have valid polygons after size filtering
                if valid_polygons:
                    final_multi = MultiPolygon(valid_polygons)
                    normalized_geometries.append(final_multi)
        except AttributeError:
            print(f"Invalid geometry: {feature['geometry']}")
        except Exception as e:
            print(f"Error processing feature: {e}")

    # Format the final response
    return format_response(normalized_geometries, properties)


def normalize_geometry(geometry: BaseGeometry) -> Union[None, MultiPolygon]:
    # Make sure the geometry is valid
    valid_geom = make_valid(geometry)
    if valid_geom.is_empty:
        return None

    # If the geometry is a GeometryCollection, extract valid polygonal parts
    if valid_geom.geom_type == 'GeometryCollection':
        polygons = []
        for geom in valid_geom.geoms:
            if geom.geom_type == 'Polygon':
                polygons.append(geom)
            elif geom.geom_type == 'MultiPolygon':
                # Flatten the multipolygon into individual polygons
                polygons.extend(list(geom.geoms))
        if polygons:
            return MultiPolygon(polygons)
        else:
            # No valid polygons found within the collection
            return None

    # Handle simple Polygon and MultiPolygon cases
    if valid_geom.geom_type == 'Polygon':
        return MultiPolygon([valid_geom])
    elif valid_geom.geom_type == 'MultiPolygon':
        return valid_geom
    elif valid_geom.geom_type == 'MultiLineString':
        return None

    # For any other geometry types, log and raise an error
    print(valid_geom.geom_type)
    return None


def partition_single_polygon(geometry: Polygon, max_area: float) -> List[Polygon]:
    """
    Recursively partition a polygon into smaller parts based on max_area.

    Args:
        geometry (Polygon): The polygon to partition.
        max_area (float): The maximum area allowed for any resulting polygon.

    Returns:
        List[Polygon]: A list of partitioned polygons.
    """
    if geometry.area <= max_area:
        return [geometry]

    minx, miny, maxx, maxy = geometry.bounds
    width = maxx - minx
    height = maxy - miny

    # Split horizontally if the width is greater than the height
    if width > height:
        midx = (minx + maxx) / 2
        left_half = Polygon([(minx, miny), (midx, miny), (midx, maxy), (minx, maxy), (minx, miny)])
        right_half = Polygon([(midx, miny), (maxx, miny), (maxx, maxy), (midx, maxy), (midx, miny)])
        return partition_single_polygon(left_half, max_area) + partition_single_polygon(right_half, max_area)

    # Split vertically otherwise
    else:
        midy = (miny + maxy) / 2
        bottom_half = Polygon([(minx, miny), (maxx, miny), (maxx, midy), (minx, midy), (minx, miny)])
        top_half = Polygon([(minx, midy), (maxx, midy), (maxx, maxy), (minx, maxy), (minx, midy)])
        return partition_single_polygon(bottom_half, max_area) + partition_single_polygon(top_half, max_area)


def partition_large_geometry(geometry: MultiPolygon, max_area: float) -> MultiPolygon:
    """
    Partition a large MultiPolygon into smaller polygons based on a maximum area constraint.
    Uses the same EPSG:3857 projection as validate_geometry_area for consistent area measurement.
    """
    import pyproj
    from shapely.ops import transform

    # Project to Web Mercator (EPSG:3857) for area calculation
    project = pyproj.Transformer.from_crs("EPSG:4326", "EPSG:3857", always_xy=True).transform
    project_back = pyproj.Transformer.from_crs("EPSG:3857", "EPSG:4326", always_xy=True).transform

    partitioned_polygons = []

    for geom in geometry.geoms:
        # Project to get accurate area
        projected_geom = transform(project, geom)

        if projected_geom.area > max_area:
            # Partition in projected space (EPSG:3857)
            partitioned = partition_single_polygon(projected_geom, max_area)

            # Project partitioned polygons back to EPSG:4326
            for part in partitioned:
                unprojected_part = transform(project_back, part)
                partitioned_polygons.append(unprojected_part)
        else:
            partitioned_polygons.append(geom)

    return MultiPolygon(partitioned_polygons)


def format_response(geometries: List[MultiPolygon], properties: Dict) -> List[Dict]:
    """
    Format the final response in the GeoJSON-like structure.
    """
    feature_collection = []

    for geom in geometries:
        feature = {
            "type": "Feature",
            "properties": properties,
            "geometry": mapping(geom)  # Convert geometry back to GeoJSON-like structure
        }
        feature_collection.append(feature)

    return  feature_collection


def geojson_to_wkb(geojson: Dict) -> Union[None, str]:
    """
    Normalize a GeoJSON geometry and return the WKB representation.

    Args:
        geojson (Dict): The GeoJSON geometry input.

    Returns:
        Union[None, str]: The WKB of the normalized geometry, encoded in base64.
    """
    geometry = shape(geojson)
    valid_geom = make_valid(geometry)

    if valid_geom.is_empty:
        return None

    if valid_geom.geom_type == 'Polygon':
        valid_geom = MultiPolygon([valid_geom])

    if valid_geom.geom_type != 'MultiPolygon':
        raise TypeError("Invalid geometry type. Only Polygon or MultiPolygon is supported.")

    wkb = geom_to_wkb(valid_geom)
    wkb_base64 = base64.b64encode(wkb).decode('utf-8')

    return wkb_base64


def wkb_to_geojson(wkb_base64: str) -> Union[None, Dict]:
    """
    Convert a WKB (encoded in base64) to a GeoJSON dictionary.

    Args:
        wkb_base64 (str): The WKB geometry, encoded in base64.

    Returns:
        Union[None, Dict]: The GeoJSON representation of the geometry, or None if invalid.
    """
    if not wkb_base64:
        return None

    try:
        # Decode the base64-encoded WKB
        wkb_bytes = base64.b64decode(wkb_base64)
        geometry = wkb_loads(wkb_bytes)
    except (TypeError, ValueError, GEOSException):
        return None

    # Normalize to MultiPolygon if necessary
    if isinstance(geometry, Polygon):
        geometry = MultiPolygon([geometry])

    if isinstance(geometry, MultiPolygon):
        geojson_dict = mapping(geometry)
        return geojson_dict
    else:
        raise TypeError("Invalid geometry type. Only Polygon or MultiPolygon is supported.")


def validate_geometry_area(geometry, min_threshold=0.01, max_threshold=2880648.0):
    import pyproj
    from shapely.ops import transform

    # Converts Lat/Lon to square meters
    # Onspt calculates in square meeters not degrees
    # Converts the geometry to Web Mercator (EPSG:3857) for area calculation

    project = pyproj.Transformer.from_crs(
        "EPSG:4326", "EPSG:3857", always_xy=True
    ).transform
    try:
        geom = shape(geometry)
        projected_geom = transform(project, geom)

        # Handle MultiPolygon by validating each polygon inside it
        if isinstance(projected_geom, MultiPolygon):
            for poly in projected_geom.geoms:
                area = poly.area
                if area < min_threshold or area > max_threshold:
                    print(
                        f"Sub-polygon area {area} m² is out of range ({min_threshold} - {max_threshold})"
                    )
                    return False
        else:
            area = projected_geom.area
            if area < min_threshold or area > max_threshold:
                print(
                    f"Polygon area {area} m² is out of range ({min_threshold} - {max_threshold})"
                )
                return False

        return True
    except Exception as e:
        print(f"Error checking area: {e}")
        return False


def partition(df: pd.DataFrame) -> pd.DataFrame:
    import ast

    print(f"In Partitioner: {len(df)}")

    new_features = []

    # Iterate over each row in the dataframe
    for row in df.to_dict('records'):
        # Use the 'features' column and the 'location' information
        features = ast.literal_eval(row['geojson'])
        location = row['location']

        validated_features = geo_validation(features, properties={})

        for feature in validated_features:
            new_features.append(
                {
                    "location": location,
                    "start": row["start"],
                    "end": row["end"],
                    "feature_no": len(new_features) + 1,
                    "features": feature["geometry"],
                }
            )

    # Convert new features to a DataFrame
    df = pd.DataFrame(new_features)

    print(f"Out of Partitioner: {len(df)}")

    return df


def utf8len(s: str) -> int:
    return len(s.encode('utf-8'))

def chunk_features_by_bytesize(features: list, limit=90000) -> list:
    max = limit
    new_feature_list = []
    feature_chunk = []
    chunk_size = 0
    for feature in features:
        if utf8len(json.dumps(feature)) + chunk_size < max:
            feature_chunk.append(feature)
            chunk_size += utf8len(json.dumps(feature))
        else:
            new_feature_list.append(feature_chunk)
            feature_chunk = [feature]
            chunk_size = utf8len(json.dumps(feature))
    new_feature_list.append(feature_chunk)
    return new_feature_list


def send_request_to_onspot(features: dict, endpoint: dict, credentials: dict):
    from awsrequests import AwsRequester

    req = AwsRequester(
        'us-east-1',
        credentials['access_key'],
        credentials['secret_key']
    )
    header = {"x-api-key": credentials['api_key']}
    r = req.post(
        credentials['endpoint'] + endpoint,
        json={"type": "FeatureCollection", "features": features},
        headers=header
    )
    response = json.loads(r.text)
    print(response)
    if "message" in response:
        raise Exception(response["message"])

    return


def format_features(df, response_s3_path: str) -> List[Dict]:
    from dateutil import parser

    features = []
    for row in df.to_dict("records"):
        # Parse start and end dates, handling both simple dates and full timestamps
        start_date = parser.parse(row["start"]).date()  # Convert to date only if you want to strip time
        end_date = parser.parse(row["end"]).date()

        feature = format_feature(
            row["features"]["coordinates"],
            row["location"],
            start_date,
            end_date,
            response_s3_path,
            f"{row['location']}_{str(row['feature_no'])}.csv"
        )
        features.append(feature)
    return features


def format_feature(
        coordinates: List,
        loc_id: str,
        start_date: date,
        end_date: date,
        output_s3_path: str,
        output_filename: str
) -> dict:
    feature = {
        "type": "Feature",
        "geometry": {
            "type": "MultiPolygon",
            "coordinates": coordinates,
        },
        "properties": {
            "name": loc_id,
            "hash": False,
            "start": start_date.isoformat(),
            "end": end_date.isoformat(),
            "callback": f"https://lwelk82noc.execute-api.us-east-1.amazonaws.com/prod/bi-callback",
            "organization": "eltoro",
            "outputProvider": "s3",
            "outputLocation": output_s3_path,
            "fileName": output_filename,
            "fileFormat": {
                "delimiter": "csv",
                "quoteEncapsulate": True,
                "compressionType": "gzip",
            },
        },
    }
    return feature
