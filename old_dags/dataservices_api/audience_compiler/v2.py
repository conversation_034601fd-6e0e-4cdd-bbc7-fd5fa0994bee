from typing import List
import pandas as pd
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from pymongo import MongoClient
from logging import getLogger
from sqlalchemy import text, bindparam
from datetime import  timedelta

logger = getLogger(__name__)

bucket_types = {
    22: "Venue Replay",
    12: "Address List",
    220: "Venue Replay Mapped Homes",
    221: "Venue Replay Mapped Devices",
    222: "Venue Replay Unmapped Devices",
    223: "Venue Replay Other Devices In Home",
    31: "Digital Movers",
    13: "Zip List",
    21: "Polygon",
    29: "Device List",
    11: "IP List"
}

def get_trino_connection():
    conn = ''
    return conn

def bind_params_to_query_template(template, query_args):
    for key, value in query_args.items():
        expanding = False
        if isinstance(value, list):
            expanding = True
        template = template.bindparams(
            bindparam(key=key, value=value, expanding=expanding)
        )
    return template


class Buckets:
    def __init__(self, bucket_id, ol_start, ol_end):
        client = MongoClient("mongodb-2.middleearth.eltoro.com:27000")
        db = client['prod']

        self.id = bucket_id
        self.ol_start = ol_start
        self.ol_end = ol_end
        bucket = db.buckets.find_one({'_id': self.id})
        self.type = bucket.get('type')
        self.name = bucket.get('name')
        self.target_type = bucket.get('conf', {}).get('target_type')
        self.created = bucket.get('created')
        self.updated = bucket.get('updated')
        self.status = bucket.get('status')
        self.last_etl = bucket.get('lastEtl')
        self.quote_requested = bucket.get('output', {}).get('quoteRequested')
        self.quoted = bucket.get('output', {}).get('quoted')
        self.segment_id = bucket.get('output', {}).get('segmentId')
        self.segment_code = bucket.get('output', {}).get('segmentCode', bucket.get('deploy', {}).get('id'))

        self.mover_subtype = bucket.get('conf', {}).get('subtype')
        self.mover_zips = bucket.get('conf', {}).get('zips')
        self.mover_states = bucket.get('conf', {}).get('states')

        self.target_df: pd.DataFrame = pd.DataFrame()

        file_type_to_attr = {
            1: 'original_s3_uri',
            2: 'append_s3_uri',
            3: 'cleaned_s3_uri',
            4: 'errors_s3_uri',
            7: 'devices_seen_in_vr_s3_uri',
            8: 'matchback_s3_uri'
        }

        # Initialize all URIs to None
        for attr in file_type_to_attr.values():
            setattr(self, attr, None)

        # Iterate through files and assign URIs based on type
        for file in bucket.get("files", []):
            file_type = file.get('type')
            if file_type in file_type_to_attr:
                s3_uri = f"s3://{file.get('bucket')}/{file.get('key')}"
                setattr(self, file_type_to_attr[file_type], s3_uri)

    def get_target_df(self):
        if self.type in [12, 13, 220]: # address, zip or VR addresses
            print("Getting address targets")
            self.get_address_file_targets()
        elif self.type == 31: # mover
            query_start = self.ol_start - timedelta(days=15)
            query_end = self.ol_end
            self.get_mover_targets(query_start, query_end)
        return

    def get_address_file_targets(self):
        s3_uri = self.append_s3_uri
        if s3_uri is None:
            s3_uri = self.matchback_s3_uri
        if s3_uri is None:
            return pd.DataFrame()
        logger.info(s3_uri)
        targets = pd.read_csv(s3_uri, dtype=str)
        print(f"Read {len(targets)} rows from {s3_uri}")

        targets.rename(columns={"ETHash": "ethashv1", "etHash": "ethashv1", "target": "matched"}, inplace=True)
        targets.sort_values(by=['matched'], ascending=False, inplace=True)
        targets.drop_duplicates(subset=['ethashv1'], inplace=True)
        targets['serve_start_date'] = max(self.ol_start, self.quote_requested).strftime('%Y-%m-%d')
        targets['serve_end_date'] = self.ol_end.strftime('%Y-%m-%d')
        targets = targets[['ethashv1', 'matched', 'serve_start_date', 'serve_end_date']]
        targets['ethashv1'] = targets['ethashv1'].str.slice(0, 6) + '0000' + targets['ethashv1'].str.slice(10)
        targets['matched'] = targets['matched'].map({'t': True, 'f': False})
        self.target_df = targets


    def get_mover_targets(self, min_date, max_date):
        trino_hook = TrinoHook(trino_conn_id="starburst")
        mover_type_map = {
            131: "DNM",
            132: "DPM",
            133: "DPME"
        }
        query_template = text("""
        SELECT DISTINCT
          ethash ethashv1,
         serve_start_date,
       serve_end_date
        FROM
          (
            SELECT
              ethash,
            date_trunc('day', min(processed_date)) serve_start_date,
            date_trunc('day', max(processed_date)) + interval '7' day serve_end_date
            FROM
              "s3"."bronze_eltoro_audiences"."movers"
            WHERE
              processed_date > DATE(:min_date)
              and processed_date < DATE(:max_date)
              and "zip" in :zipcodes
              AND mover_type = :mover_type
            GROUP BY ethash
            UNION
            SELECT DISTINCT
              ethash,
        date_trunc('day', min(processed_date)) serve_start_date,
        date_trunc('day', max(processed_date)) + interval '7' day serve_end_date
            FROM
              "olympus"."bronze_new_movers"."new_movers"
            WHERE
              target = 't'
              AND processed_date > DATE(:min_date)
              AND processed_date < DATE(:max_date)
              AND "zip" in :zipcodes
              AND mover_type = 
                CASE :mover_type
                    WHEN 'DPM' THEN 'premover'
                    WHEN 'DNM' THEN 'cnm'
                    WHEN 'DPME' THEN 'pending'
                END
            GROUP BY ethash    
          )
        """)
        template = bind_params_to_query_template(
            query_template,
            {
                "zipcodes": self.mover_zips,
                "mover_type": mover_type_map[self.mover_subtype],
                "min_date": min_date.strftime('%Y-%m-%d'),
                "max_date": max_date.strftime('%Y-%m-%d'),
            }
        )
        logger.info(str(template.compile(compile_kwargs={"literal_binds": True})))
        targets = trino_hook.get_pandas_df(
            str(template.compile(compile_kwargs={"literal_binds": True})),
        )
        # targets = pd.DataFrame(rows, columns=['ethashv1', 'serve_start_date', 'serve_end_date'])
        targets['matched'] = True
        targets['ethashv1'] = targets['ethashv1'].str.slice(0, 6) + '0000' + targets['ethashv1'].str.slice(10)
        self.target_df = targets


class OrderLine:
    def __init__(self, order_line_id):
        self.id = order_line_id
        self.attached_buckets = []
        self.detached_buckets = []
        self.exclude_buckets = []

        client = MongoClient("mongodb-2.middleearth.eltoro.com:27000")
        db = client['prod']
        ol = db.order_lines.find_one({"_id": order_line_id})
        self.org_id = ol.get("org", {}).get("_id")

        self.start = ol['start']
        self.end = ol['stop']

        self.ol_target_df: pd.DataFrame = pd.DataFrame()

        for bucket_dict in ol["buckets"]:
            bucket = Buckets(bucket_dict["_id"], self.start, self.end)
            if bucket_dict.get("exclude"):
                self.exclude_buckets.append(bucket)
            else:
                self.attached_buckets.append(bucket)
        for bucket_id in ol.get('bucketIdsDetached', []):
            bucket = Buckets(bucket_id, self.start, self.end)
            self.detached_buckets.append(bucket)


def update_bucket_df(bucket_df, bucket, order_line, currently_attached, excluded):
    bucket_df['audience_id'] = bucket.id
    bucket_df['audience_name'] = bucket.name
    bucket_df['audience_type'] = bucket.type
    bucket_df['ol_id'] = order_line.id
    bucket_df['currently_attached'] = currently_attached
    bucket_df['excluded'] = excluded
    bucket_df['deployed'] = True if bucket.segment_code is not None else False
    return bucket_df


def adjust_serving_dates(df):
    # This function will adjust serve_start_date and serve_end_date based on exclusions
    df['served'] = (df['matched'] & df['deployed'] & ~df['excluded'])

    # For ethashes that have both excluded and non-excluded periods, we adjust the serve_start_date/end_date
    grouped = df.groupby('ethashv1')
    for ethash, group in grouped:
        if group['excluded'].any():  # Check if any rows are marked as excluded
            non_excluded = group.loc[~group['excluded']]
            if not non_excluded.empty:
                min_non_excluded_start = non_excluded['serve_start_date'].min()
                max_non_excluded_end = non_excluded['serve_end_date'].max()

                # Modify the serve_start_date and serve_end_date for non-excluded rows
                df.loc[(df['ethashv1'] == ethash) & ~df['excluded'], 'serve_start_date'] = min_non_excluded_start
                df.loc[(df['ethashv1'] == ethash) & ~df['excluded'], 'serve_end_date'] = max_non_excluded_end
    return df


def get_v2_compiled_audiences(ol_ids: List[str]) -> (str, pd.DataFrame):
    order_lines = []
    for ol_id in ol_ids:
        order_lines.append(OrderLine(ol_id))

    for order_line in order_lines:
        dfs_to_concat = []

        if not order_line.attached_buckets:
            logger.info(f"No attached buckets to process for orderline {order_line.id}")
        else:
            # Process attached buckets
            for bucket in order_line.attached_buckets:
                print(f"Processing attached bucket {bucket.id}")
                bucket.get_target_df()
                updated_df = update_bucket_df(bucket.target_df, bucket, order_line, True, False)
                dfs_to_concat.append(updated_df)

        if not order_line.detached_buckets:
            logger.info(f"No detached_buckets to process for orderline {order_line.id}")
        else:
            # Process detached buckets
            for bucket in order_line.detached_buckets:
                print(f"Processing detached bucket {bucket.id}")
                bucket.get_target_df()
                updated_df = update_bucket_df(bucket.target_df, bucket, order_line, False, False)
                dfs_to_concat.append(updated_df)

        if not order_line.exclude_buckets:
            logger.info(f"No exclude_buckets to process for orderline {order_line.id}")
        else:
            # Process exclude buckets
            for bucket in order_line.exclude_buckets:
                print(f"Processing excluded bucket {bucket.id}")
                bucket.get_target_df()
                updated_df = update_bucket_df(bucket.target_df, bucket, order_line, True, True)
                dfs_to_concat.append(updated_df)

        print(dfs_to_concat)
        # Concatenate all DataFrames at once for efficiency
        order_line.ol_target_df = pd.concat(dfs_to_concat, ignore_index=True)

        # Adjust serve_start_date and serve_end_date based on exclusions
        order_line.ol_target_df = adjust_serving_dates(order_line.ol_target_df)

        # Apply the ethashv1 slicing after all concatenation
        order_line.ol_target_df['ethashv1_no_unit'] = order_line.ol_target_df['ethashv1'].str.slice(0, 50)


    complete_df = pd.DataFrame()
    for order_line in order_lines:
        complete_df = pd.concat([complete_df, order_line.ol_target_df])

    bool_columns = list(complete_df.select_dtypes(include=['bool']).columns)
    if 'matched' not in bool_columns:
        bool_columns.append('matched')
    for col in bool_columns:
        complete_df[col] = complete_df[col].map({True: 'true', False: 'false'})
    return order_lines[0].org_id, complete_df