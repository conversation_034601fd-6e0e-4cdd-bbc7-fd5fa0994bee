import time

from pygene.utils import safe_isoparse
from logging import getLogger
from enum import Enum, unique
from pygene.base_api import NextGenBaseAP<PERSON>
from typing import Dict
import base64
import json

logger = getLogger(__name__)


@unique
class JobStatus(Enum):
    UNKNOWN = 'JOB_STATUS_UNKNOWN'
    CREATED = 'JOB_STATUS_CREATED'
    RUNNING = 'JOB_STATUS_RUNNING'
    READY = 'JOB_STATUS_READY'
    COMPLETED = 'JOB_STATUS_COMPLETED'
    ERRORED = 'JOB_STATUS_ERRORED'

    @classmethod
    def _missing_(cls, value):
        return cls.<PERSON>KNOWN


@unique
class JobType(Enum):
    UNKNOWN = 'JOB_TYPE_UNKNOWN'

    MATCHBACK = "JOB_TYPE_MATCHBACK"

    @classmethod
    def _missing_(cls, value):
        return cls.<PERSON>KNOW<PERSON>


@unique
class JobFileType(Enum):
    UNKNOWN = 'JOB_FILE_TYPE_UNKNOWN'
    SALES_MATCHBACK = 'JOB_FILE_TYPE_SALES_MATCHBACK'
    ORDER_LINE_MATCHBACK = 'JOB_FILE_TYPE_ORDER_LINE_MATCHBACK'
    ETHASHED_SALES = 'JOB_FILE_TYPE_ETHASHED_SALES'
    COMPILED_AUDIENCE = 'JOB_FILE_TYPE_COMPILED_AUDIENCE'

    @classmethod
    def _missing_(cls, value):
        return cls.UNKNOWN


class JobResult:
    def __init__(self, result_string: str):
        result = json.loads(base64.b64decode(result_string))
        if result is not None:
            self.order_line_matchback_data_file_location = result.get('order_line_matchback_data_file_location')
            self.sales_matchback_data_file_location = result.get('sales_matchback_data_file_location')
            self.compiled_audience_file_location = result.get('compiled_audience_file_location')
            self.hashed_sales_file_location = result.get('hashed_sales_file_location')


class Job:
    def __init__(self, response: Dict):
        self.id = response.get('id')
        self.name = response.get('name')
        self.org_id = response.get('org_id')
        self.target_id = response.get('target_id')
        self.create_time = safe_isoparse(response.get('create_time'))
        self.update_time = safe_isoparse(response.get('update_time'))
        self.status = JobStatus(response.get('status'))
        self.type = JobType(response.get('type'))
        self.instance_id = response.get('instance_id')
        self.result = JobResult(response.get('result'))


class JobAPI(NextGenBaseAPI):
    def __init__(self, client_id, client_secret, org_id, env='prod'):
        super().__init__(client_id, client_secret, env=env)
        self.org_id = org_id

    def get_job(self, job_id: str) -> Job:
        response = self.get(
            endpoint=f'jobs/{job_id}?org_id={self.org_id}'
        )
        job = Job(response.json())
        return job

    def _poll_for_job_status_created(self, job_id: str):
        job = self.get_job(job_id)
        job.status = JobStatus.UNKNOWN
        while job.status != JobStatus.CREATED:
            job = self.get_job(job_id)
            if job.status == JobStatus.ERRORED:
                raise Exception(f"JOB {job.id} has errored")
            time.sleep(5)
        logger.info("Job is Created.  You can now run your enrichment.")

    def _poll_for_job_status_ready(self, job_id: str):
        job_status = ''
        job = self.get_job(job_id)
        while job.status not in (JobStatus.COMPLETED, JobStatus.READY):
            job = self.get_job(job_id)
            if job.status == JobStatus.ERRORED:
                raise Exception(f"JOB {job_status} has errored")
            time.sleep(5)
        logger.info("Job is Ready.")

    def _run_job(self, job_id: str, query_args: dict):
        response = self.post(
            endpoint="jobs:run",
            params={
                "job_id": job_id,
                "org_id": self.org_id,
                "query_args": json.dumps(query_args)
            },
            data={}
        )
        return

    def create_job(self, target_id: str, job_type: JobType, name: str, query_args: dict, poll: bool = True) -> Job:
        response = self.post(
            endpoint='jobs',
            params={
                "org_id": self.org_id
            },
            data={
                "type": job_type.value,
                "target_id": target_id,
                "name": name,
                "org_id": self.org_id
            }
        )
        job_id = response.json()['id']
        logger.info(f"Job {job_id} created.")
        self._poll_for_job_status_created(job_id)
        # TODO poll to make sure that the job is created before generating the job
        self._run_job(job_id, query_args=query_args)
        if poll:
            self._poll_for_job_status_ready(job_id)
        job = self.get_job(job_id)
        return job

    def download_file(self, job_id: str, file_type: JobFileType) -> bytes:
        response = self.get(
            endpoint=f'jobs:download',
            params={
                "job_id": job_id,
                "job_file_type": file_type.value,
                "org_id": self.org_id
            },
        )
        return response.content