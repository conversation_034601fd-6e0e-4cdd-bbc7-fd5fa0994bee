from typing import List
import pandas as pd
import numpy as np
import json
from airflow.models import Variable
from logging import getLogger
from old_dags.dataservices_api.audience_compiler.job_api import JobAP<PERSON>, JobType
from pygene.target_api import NextGenTargetAPI, TargetFileType
from pygene.campaign_api import NextGenCampaignAP<PERSON>
from pygene.audience_api import NextGenAudienceAPI
from datascience_implement_cache import DIC
from datascience_implement_cache.geocoder import Geocoder


logger = getLogger(__name__)

pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_prod"))
client_id = pygene_creds["client_id"]
client_secret = pygene_creds["client_secret"]
ds_org_id = "cpc7mv17m05t49u7srp0"

def safe_split_of_time_frames(df: pd.DataFrame) -> pd.DataFrame:
    df['serve_time_frames'] = df['serve_time_frames'].fillna('')

    # Perform the split operation, ensuring consistent output length
    split_result = df['serve_time_frames'].str.split(r'-(?=\d{4})', expand=True)

    # If split_result has fewer than two columns (because of empty input), add missing columns
    if split_result.shape[1] < 2:
        split_result = split_result.reindex(columns=range(2), fill_value='')

    # Replace any NaNs or missing values with empty strings
    split_result.replace(np.nan, '', inplace=True)

    # Assign the split results back to the DataFrame
    df[['serve_start_date', 'serve_end_date']] = split_result

    return df


def get_org_id_for_ol(order_line_id: str) -> str:
    campaign_api = NextGenCampaignAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=ds_org_id,
        env="prod"
    )
    order_line = campaign_api.get_order_line_admin(order_line_id)
    return order_line.org_id


def create_nextgen_mb_job(name: str, ol_ids: List[str]) ->(str, str):
    logger.info(f"Creating NextGen matchback job for order lines: {ol_ids}")
    columns = [
        {"index": 0, "type": "address1", "value": "address1"},
        {"index": 1, "type": "zip", "value": "zip"},
    ]

    org_id = get_org_id_for_ol(ol_ids[0])
    logger.info(f"Org ID for order line {ol_ids[0]} is {org_id}")
    target_api = NextGenTargetAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env="prod",
    )

    file_path = "/tmp/dummy_file.csv"

    target = target_api.upload_targets(
        local_file=str(file_path),
        header_columns=columns,
        file_type=TargetFileType.ADDRESS
    )

    job_api = JobAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env="prod"
    )

    query_args = {
        "order_line_ids": ol_ids,
        "quote_sales_file": False,
        "fuzzy": False,
    }

    job = job_api.create_job(
        target_id=target.id,
        name=name,
        job_type=JobType.MATCHBACK,
        query_args=query_args,
    )

    return org_id, job.result.compiled_audience_file_location


def nextgen_transform_audiences(df: pd.DataFrame, org_id: str) -> pd.DataFrame:
    columns = {
        "AudienceID": "audience_id",
        "OLID": "ol_id",
        "CurrentlyAttached": "currently_attached",
        "Deployed": "deployed",
        "Served": "served",
        "Excluded": "excluded",
        "ETHash": "ethashv1",
        "Matched": "matched",
        "ServeTimeFrames": "serve_time_frames"
    }
    df.rename(columns=columns, inplace=True)
    df = safe_split_of_time_frames(df)
    df.drop(columns=['serve_time_frames'], inplace=True)
    df['ethashv1'] = df['ethashv1'].str.slice(0, 6) + '0000' + df['ethashv1'].str.slice(10)
    df['ethashv1_no_unit'] = df['ethashv1'].str.slice(0, 50)

    # Add ethashV2 functionality using datascience_implement_cache
    if 'Address' in df.columns and 'Zip' in df.columns:
        logger.info("Adding ethashV2 using datascience_implement_cache geocoder")

        # Rename columns to match geocoder expectations
        df.rename(columns={'Address': 'address', 'Zip': 'zipcode'}, inplace=True)

        # Check if there's any valid address and zip data before geocoding
        valid_data = df[
            (df['address'].notna()) &
            (df['zipcode'].notna()) &
            (df['address'].astype(str).str.strip() != '') &
            (df['zipcode'].astype(str).str.strip() != '')
        ]

        if len(valid_data) > 0:
            logger.info(f"Found {len(valid_data)} rows with valid address/zip data for geocoding")

            # Initialize the geocoder
            dic = DIC("NextGenAudienceCompiler", "airflow")
            dc_geocoder = Geocoder(dic)

            # Get column indices for address and zipcode
            address_idx = df.columns.get_loc('address')
            zipcode_idx = df.columns.get_loc('zipcode')

            # Geocode the entire dataframe to get etHashV2
            geocoder_columns = ['etHashV2']
            df = dc_geocoder.geocode(df, address_idx, None, zipcode_idx, geocoder_columns, scrub=False)
            df.columns = [x.lower() for x in df.columns]

            # Create ethashv2_no_unit by cutting off the last 4 characters
            # Ensure ethashv2 is string type and handle null values
            if 'ethashv2' in df.columns:
                df['ethashv2'] = df['ethashv2'].astype(str)
                df['ethashv2_no_unit'] = df['ethashv2'].str.slice(0, -4)
                logger.info("Successfully added ethashV2 to dataframe")
            else:
                logger.warning("ethashv2 column not found after geocoding")
                df['ethashv2'] = None
                df['ethashv2_no_unit'] = None
        else:
            logger.warning("No valid address/zip data found - setting ethashv2 columns to null")
            df['ethashv2'] = None
            df['ethashv2_no_unit'] = None
    else:
        logger.warning("Address and/or Zip columns not found - setting ethashv2 columns to null")
        df['ethashv2'] = None
        df['ethashv2_no_unit'] = None

    audience_api = NextGenAudienceAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id=org_id,
        env="prod"
    )

    audience_ids = df['audience_id'].dropna().unique()
    print(audience_ids)
    for audience_id in audience_ids:
        audience = audience_api.get_audience(audience_id)
        df.loc[df['audience_id'] == audience_id, 'audience_name'] = audience.name
        df.loc[df['audience_id'] == audience_id, 'audience_type'] = audience.type

    return df