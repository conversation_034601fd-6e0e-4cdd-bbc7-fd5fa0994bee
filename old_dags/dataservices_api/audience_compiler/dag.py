import pandas as pd
from etdag import ETDAG
from airflow.providers.amazon.aws.operators.s3 import S3Hook
from airflow.decorators import task, task_group
from airflow.models.param import Param
from datetime import datetime
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup
from airflow.operators.dummy import DummyOperator
from old_dags.dataservices_api.audience_compiler.main import update_stats_for_full_dataset

s3_hook = S3Hook(aws_conn_id="s3_conn")
docs = """
## Compiled Audience Workflow DAG

This DAG is designed to orchestrate the compilation and updating of audience data for the Data Services API. It handles the retrieval of audience configurations from a PostgreSQL database, processes these configurations through a set of dynamically generated tasks, and finally updates statistics and consolidates results into a single CSV file stored in S3.

### Workflow:
1. **Start**: The workflow initiates with a dummy start task.
2. **Prepare Input Data**: Fetches the audience data configurations from the 'compiled_audiences' table in PostgreSQL based on a provided `request_id`. Each configuration specifies details like platform, organization ID, and order line IDs which guide the compilation process.
3. **Compile Audiences**: For each configuration retrieved, a new task is dynamically generated to compile audience data. This compilation is performed in isolated Python virtual environments to ensure dependency integrity.
4. **Compile and Update**: After compilation, all resulting CSV files are aggregated from S3, and comprehensive statistics are updated. The final dataset is written back to an S3 bucket in a consolidated CSV format.

### Parameters:
- `request_id`: An optional integer that specifies which compiled audience configuration to process. If not provided, a default ID will be used.

### Environment Variables:
- `ENV`: Specifies the deployment environment to direct the S3 paths appropriately for staging, production, etc.

### Execution:
- This DAG does not follow a schedule (`schedule_interval=None`) and is meant to be triggered manually or by external events.
- Concurrency is set to 2 to allow multiple instances of the DAG to run simultaneously without overwhelming resources.
- Catchup is disabled to prevent historical backfilling upon deployment or reactivation.

### Notes:
- Each task within the 'Compile Audiences' step runs in a separate virtual environment, managed by Airflow's virtualenv feature, to isolate and resolve specific package dependencies.
- The final step consolidates all individual results into a master file which is then stored in a designated S3 bucket, making it accessible for further analysis or reporting.
"""

default_args = {
    "owner": "rorie.lizenby",
    "start_date": datetime(2023, 1, 1),
}

params = {
    "request_id": Param(type=["null", "integer"], default=None)
}

with (ETDAG(
        dag_id="compiled_audience_workflow",
        default_args=default_args,
        params=params,
        schedule_interval=None,
        max_active_runs=2,
        catchup=False,
        tags=["datasevices-api", "team:DND"],
) as dag):
    dag.doc_md = docs
    ENV = Variable.get("environment")

    @task.virtualenv(
        requirements=["pymongo==3.13.0", "pygene==1.0.51"],
        system_site_packages=True,
        venv_cache_path="/tmp/airflow_venvs"
    )
    def prepare_input_data(request_id: str) -> list:
        from pygene.campaign_api import NextGenCampaignAPI
        from old_dags.dataservices_api.audience_compiler.main import (
            get_platform_for_order_lines,
            do_pg_update
        )
        from pymongo import MongoClient
        from airflow.models import Variable
        from airflow.providers.postgres.hooks.postgres import PostgresHook
        from psycopg2.extras import RealDictCursor

        pg_hook = PostgresHook(postgres_conn_id="dataservices_pg_conn")
        conn = pg_hook.get_conn()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        sql = "SELECT * FROM compiled_audiences WHERE id = %s"
        cursor.execute(sql, (request_id,))
        row = cursor.fetchone()
        if not row:
            raise ValueError(f"No job found with request_id: {request_id}")

        order_line_ids = row['order_line_ids']
        ol_map = get_platform_for_order_lines(order_line_ids)

        sql = "UPDATE compiled_audiences SET status = 'RUNNING' WHERE id = %s"
        do_pg_update(sql, (request_id,))

        configs = []
        index = 0  # Initialize the index counter

        for platform, org_ol_list in ol_map.items():
            for org_id, ol_ids in org_ol_list.items():
                for i in range(0, len(ol_ids), 10):
                    chunk = ol_ids[i:i + 10]  # Get the current chunk of up to 10 ol_ids
                    configs.append({
                        "request_id": request_id,
                        "platform": platform,
                        "org_id": org_id,
                        "ol_ids": chunk,
                        "index": index
                    })
                    index += 1
        return configs

    @task.virtualenv(
        requirements=["pymongo==3.13.0", "pygene"],
        system_site_packages=True,
        venv_cache_path="/tmp/airflow_venvs"
    )
    def compile_audiences(config: dict):
        from old_dags.dataservices_api.audience_compiler.main import (
            compile_audiences,
            write_dummy_file,
        )

        platform = config["platform"]
        if platform == 'NextGen':
            write_dummy_file()
        compile_audiences(config=config)

    @task()
    def compile_and_update(request_id, _output):
        dfs_to_concat = []
        keys = s3_hook.list_keys(
            bucket_name="vr-timestamp",
            prefix=f"ds_api/{ENV}/compiled_audiences_temp/{request_id}/"
        )
        print(keys)
        for key in keys:
            df = pd.read_csv(f"s3://vr-timestamp/{key}", dtype=str)
            dfs_to_concat.append(df)

        df = pd.concat(dfs_to_concat)
        s3_uri = f"s3://vr-timestamp/ds_api/{ENV}/compiled_audiences/id={request_id}/{request_id}.csv"
        update_stats_for_full_dataset(request_id, s3_uri, df)
        df.to_csv(s3_uri, index=False)

    configs = prepare_input_data("{{ dag_run.conf.get('request_id', 'default_request_id') }}")
    audience_groups = compile_audiences.expand(config=configs)
    completed = compile_and_update("{{ dag_run.conf.get('request_id', 'default_request_id') }}", audience_groups)

    configs >> audience_groups >> completed
