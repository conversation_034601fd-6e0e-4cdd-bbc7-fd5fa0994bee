import csv
import pandas as pd
from airflow.models import Variable
import json
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pymongo import MongoClient
from pygene.campaign_api import NextGenCampaignAPI
from old_dags.dataservices_api.audience_compiler.nextgen import create_nextgen_mb_job, nextgen_transform_audiences
from old_dags.dataservices_api.audience_compiler.v2 import get_v2_compiled_audiences

ENV = Variable.get("environment")

def compile_audiences(config) -> None:
    request_id = config['request_id']
    platform = config['platform']
    org_id = config['org_id']
    ol_ids = config['ol_ids']
    index = config['index']

    print(f"Processing {platform} org {org_id} with order lines {ol_ids}")
    if platform == 'NextGen':
        org_id, s3_uri = create_nextgen_mb_job(
            name="temp_name",
            ol_ids=ol_ids
        )
        df = pd.read_csv(f"s3://{s3_uri}", dtype=str)
        print("Read CSV")
        df = nextgen_transform_audiences(df, org_id)
    elif platform == 'V2':
        org_id, df = get_v2_compiled_audiences(ol_ids)
    df = clean_df(df)
    s3_uri = f"s3://vr-timestamp/ds_api/{ENV}/compiled_audiences_temp/{request_id}/{platform}_{org_id}_{index}.csv"
    df.to_csv(s3_uri, index=False)

    for ol_id in ol_ids:
        update_stats_for_order_line(request_id, ol_id, df)

# --------------- helpers --------------
def do_pg_update(sql, args):
    pg_hook = PostgresHook(postgres_conn_id="dataservices_pg_conn")
    conn = pg_hook.get_conn()
    cursor = conn.cursor()
    cursor.execute(sql, args)
    conn.commit()
    cursor.close()
    conn.close()

def write_dummy_file():
    data = [
        ["address1", "zip"],
        ["1515 E Main St", "47150"]
    ]

    # Writing to a temporary CSV file
    with open('/tmp/dummy_file.csv', 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerows(data)


def get_platform_for_order_lines(order_line_ids) -> dict:
    client = MongoClient("mongodb-2.middleearth.eltoro.com:27000")
    db = client['prod']
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_prod"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]

    ol_map = {
        "NextGen": {},
        "V2": {}
    }
    campaign_api = NextGenCampaignAPI(
        client_id=client_id,
        client_secret=client_secret,
        org_id="cpc7mv17m05t49u7srp0",
        env="prod"
    )

    for order_line_id in order_line_ids:
        ol = db.order_lines.find_one({"_id": order_line_id})

        if ol is not None:
            current_org_id = ol['orgId']
            platform = 'V2'
        else:
            ol = campaign_api.get_order_line_admin(order_line_id=order_line_id)
            current_org_id = ol.org_id
            platform = 'NextGen'

        if current_org_id not in ol_map[platform]:
            ol_map[platform][current_org_id] = [order_line_id]
        else:
            ol_map[platform][current_org_id].append(order_line_id)

    return ol_map


def update_stats_for_order_line(ca_id: str, ol_id: str, df: pd.DataFrame) -> None:
    """
    We will only consider unexcluded targets for these stats
    """
    print(df.ol_id.unique())
    print(df.excluded.unique())
    print(df.audience_id.unique())
    df_ol = df[(df['ol_id'] == ol_id) & (df['excluded'] == 'false')]
    total_rows = len(df_ol)
    total_targets = len(df_ol[df_ol['matched'] == 'true'])
    total_unique_targets = df_ol[df_ol['matched'] == 'true']['ethashv1'].nunique()
    total_audiences = df_ol['audience_id'].nunique()
    print(f"Updating stats for order line {ol_id} with {total_rows} rows, {total_targets} targets, {total_unique_targets} unique targets, {total_audiences} audiences")
    sql = """
        UPDATE order_line_audiences 
        SET total_rows = %s, total_targets = %s, total_unique_targets = %s, total_audiences = %s, updated_at = now() 
        WHERE order_line_id = %s and compiled_audiences_id = %s
        """
    do_pg_update(sql, (total_rows, total_targets, total_unique_targets, total_audiences, ol_id, ca_id))
    return


def update_stats_for_full_dataset(ca_id: str, s3_uri: str, df: pd.DataFrame) -> None:
    """
    We will only consider unexcluded targets for these stats
    """
    df = df[(df['excluded'] == 'false')]
    total_rows = len(df)
    total_targets = len(df[df['matched'] == 'true'])
    total_unique_targets = df[df['matched'] == 'true']['ethashv1'].nunique()

    sql = """
        UPDATE compiled_audiences
        SET 
            total_rows = %s, 
            total_targets = %s, 
            total_unique_targets = %s, 
            status = 'COMPLETE', 
            updated_at = now(), 
            location = %s 
        WHERE id = %s
        """
    do_pg_update(sql, (total_rows, total_targets, total_unique_targets, s3_uri, ca_id))
    return


def clean_df(df: pd.DataFrame) -> pd.DataFrame:
    final_column_list = [
        'audience_id',
        'audience_name',
        'audience_type',
        'ol_id',
        'currently_attached',
        'deployed',
        'served',
        'matched',
        'serve_start_date',
        'serve_end_date',
        'excluded',
        'ethashv1',
        'ethashv1_no_unit',
        'ethashv2',
        'ethashv2_no_unit'
    ]

    # If the DataFrame is empty or doesn't have required columns
    if df.empty or not all(col in df.columns for col in final_column_list):
        # Return empty DataFrame with correct columns
        return pd.DataFrame(columns=final_column_list)

    # If we have data, proceed with column selection
    return df[final_column_list]
