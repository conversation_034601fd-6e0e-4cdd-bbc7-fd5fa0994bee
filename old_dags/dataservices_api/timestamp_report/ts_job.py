import json
from psycopg2.extras import RealDictCursor
from airflow.models import Variable
from urllib.parse import urlparse
from datetime import datetime, UTC
from airflow.providers.postgres.hooks.postgres import PostgresHook
from datascience_implement_cache import DIC
from datascience_implement_cache.geocoder import Geocoder


class TimestampJob:
    def __init__(self, request_id: str):
        self.env = Variable.get("environment")
        self.dic = DIC('TSReports', request_id.replace("-", ""), env=self.env)
        self.geocoder = Geocoder(self.dic)
        self.request_id = request_id
        self.response_bucket = f"onspot-eltoro-vr-prod"
        self.endpoint_configs = {
            "observations": {
                "endpoint": "/save/geoframe/all/observations",
                "request_type": "observations",
                "latlng_prefix": "obs"
            },
            "addresstodevice": {
                "endpoint": "/save/geoframe/household/devicesbyaddress",
                "request_type": "addresstodevice",
                "latlng_prefix": "hh"
            }
        }
        self.endpoint_map = []
        self.report_keys = []
        pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{self.env}"))
        self.nextgen_client_id = pygene_creds["client_id"]
        self.nextgen_client_secret = pygene_creds["client_secret"]

        self.pg_hook = PostgresHook(postgres_conn_id="dataservices_pg_conn")

        # Get a connection and create a cursor that returns dict-like rows
        conn = self.pg_hook.get_conn()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        sql = "SELECT * FROM timestamp_report WHERE id = %s"
        cursor.execute(sql, (request_id,))
        row = cursor.fetchone()
        if not row:
            raise ValueError(f"No job found with request_id: {request_id}")

        print(row)
        self.location_source_type = row['location_source_type']
        self.bucket_id = row['bucket_id']
        self.request_types = row['request_types']
        self.data_sources = row['data_sources']
        self.request_id = request_id
        self.report_name = row['report_name']
        self.created = row['created_at']
        self.email = row['email']
        self.start = row['start']
        self.end = row['end']
        self.state = row['state']
        self.geocode = row['geocode']
        self.reduce_to_date = row['reduce_to_date']
        self.is_parquet_written = row['is_parquet_written']

        self.request_types.sort(reverse=True) #It is required that 'observations' be processed before 'addresstodevice` if both present
        for request_type in self.request_types:
            self.endpoint_map.append(self.endpoint_configs[request_type])

        result_dirs = [e['request_type'] for e in self.endpoint_map]
        self.response_path_keys = [f"inbound/ts-reports/{self.env}/{request_id}/{d}" for d in result_dirs]
        self.geocoder_fields = ['etHashV1', 'latitude', 'longitude']
        self.result_bucket = "vr-timestamp"
        self.result_path = f"timestamp-report/{self.env}/{self.report_name}"

    def parse_s3_uri(self, s3_uri: str) -> (str, str):
        parsed_s3_uri = urlparse(s3_uri)
        bucket_name = parsed_s3_uri.netloc
        s3_key = parsed_s3_uri.path.lstrip('/')
        return bucket_name, s3_key

    def update_job(self, column: str, value) -> None:
        """Update a single column in the timestamp_report table."""
        conn = self.pg_hook.get_conn()
        cursor = conn.cursor()
        sql = f"""
        UPDATE timestamp_report 
        SET "{column}" = %s, "updated_at" = %s
        WHERE id = %s
        """
        cursor.execute(sql, (value, datetime.now(UTC), self.request_id))
        conn.commit()
        cursor.close()
        conn.close()
