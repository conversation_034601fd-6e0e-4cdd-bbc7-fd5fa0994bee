from etdag import ETDAG
from airflow.decorators import task
from airflow.models.param import Param
from airflow.models import Variable
from old_dags.dataservices_api.onspot_task_group import create_onspot_request_task_group
from datetime import datetime, timedelta
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from starburst_geocoder_operator import StarburstGeocoderOperator, supported_geocoder_columns
import logging

logger = logging.getLogger(__name__)


docs = """
# Timestamp Job Workflow DAG

## Overview
This DAG is responsible for executing the **timestamp job workflow**, which processes geolocation data 
by making external API requests, monitoring responses, and generating structured outputs in CSV and Parquet formats. 

## Execution & Triggering
- This DAG **must only be triggered by the `dataservices-api`**.
- It does **not** run on a schedule and must be triggered manually via an API request.
- The DAG is **idempotent** and supports retries at any stage without causing inconsistencies.

## Concurrency & Parallelism
- The DAG allows **up to 2 concurrent runs**, ensuring controlled execution while supporting multiple requests.
- Tasks are structured to leverage **parallel execution** where applicable, such as request partitioning and response processing.

## Stages of Execution
1. **Prepare Input:** Formats and uploads request data to S3.
2. **Send Requests & Monitor Responses:** Sends OnSpot API requests and waits for results.
3. **Process Responses:** Aggregates response data, performs necessary transformations, and updates the job status.
4. **Write Parquet:** Converts processed data into Parquet format and uploads it to S3.

## Retry & Failure Handling
- Each stage is **safe to retry** independently, allowing partial reprocessing without impacting overall data integrity.
- The DAG **does not enforce failure propagation** between tasks—ensuring isolated retries if required.
- **Enhanced retry configuration**: Tasks have specific retry settings based on their function:
  - API-dependent tasks (send_requests, monitor_responses): 5 retries with longer delays
  - Data processing tasks (prepare_input, process_responses): 3 retries with moderate delays
  - Database operations (add_response_partitions): 3 retries with quick turnaround
  - Error checking tasks: 2 retries for quick recovery
- **Exponential backoff** is enabled at the DAG level to handle external API dependencies gracefully.

"""

default_args = {
    "owner": "rorie.lizenby",
    "start_date": datetime(2023, 1, 1),
    "retries": 4,  # Add retries for external API dependencies
    "retry_delay": timedelta(minutes=5),  # Initial delay
    "retry_exponential_backoff": True,  # Exponential backoff for API calls
    "max_retry_delay": timedelta(minutes=30),  # Cap maximum delay
}

params = {
    "request_id": Param(type=["null", "integer"], default=None)
}

with (ETDAG(
        dag_id="timestamp_job_workflow",
        default_args=default_args,
        params=params,
        schedule_interval=None,
        max_active_runs=2,
        catchup=False,
        tags=["datasevices-api", "team:DND"],
) as dag):
    dag.doc_md = docs
    ENV = Variable.get("environment")

    # -------------------------------------------
    # Task 1: Prepare Input Data & Upload to S3
    # -------------------------------------------
    @task.virtualenv(
        requirements=["datascience-implement-cache", "pymongo==3.13.0", "shapely", "pygene"],
        system_site_packages=True,
        venv_cache_path="/tmp/airflow_venvs",
        retries=3,  # Data preparation task - moderate retries
        retry_delay=timedelta(minutes=3),  # Quick retry for data prep
    )
    def prepare_input(request_id: str) -> dict:
        """
        Prepares input data by instantiating the `TimestampJob` and `Requestor` objects,
        standardizing the prepared CSV, and placing it in S3 for further processing.

        Args:
            request_id (str): Unique identifier for the timestamp job.

        Returns:
            dict: Contains:
                - `prepared_features_s3_uri` (str): S3 URI where the standardized CSV is stored.
                - `endpoint_list` (list): List of API endpoints the requests will be sent to.
        """
        from old_dags.dataservices_api.timestamp_report.ts_job import TimestampJob
        from old_dags.dataservices_api.timestamp_report.prepare_input import Requestor

        ts_job = TimestampJob(request_id)
        requestor = Requestor(ts_job)
        requestor.prepare_features()

        endpoint_list = [e['endpoint'] for e in ts_job.endpoint_map]

        return {
            "prepared_features_s3_uri": requestor.prepared_features_s3_uri,
            "endpoint_list": endpoint_list,
            "secret_name": "prod/bi/onspot-api",
        }


    requestor_output = prepare_input("{{ dag_run.conf.get('request_id', 'default_request_id') }}")

    # -----------------------------------------------------------
    # Task 2: Send Requests & Monitor for Responses (Task Group)
    # -----------------------------------------------------------
    onspot_request_task_group_output = create_onspot_request_task_group(
        request_id="{{ dag_run.conf.get('request_id', 'default_request_id') }}",
        requestor_output=requestor_output,
    )

    # -----------------------------------------------------------
    # Task 3: Sync Partitions in Starburst
    # -----------------------------------------------------------
    add_response_partitions = SQLExecuteQueryOperator(
        task_id="add_response_partitions",
        conn_id="starburst",
        sql=[
            f"CALL s3.system.sync_partition_metadata('bronze_ts_reports', '{ENV}_os_response_addresstodevice', 'ADD')",
            f"CALL s3.system.sync_partition_metadata('bronze_ts_reports', '{ENV}_os_response_observations', 'ADD')"
        ],
        split_statements=True,
        return_last=False,
        retries=3,  # Database operation - moderate retries
        retry_delay=timedelta(minutes=2),  # Quick retry for DB operations
    )

    # -----------------------------------------------------------
    # Task 4: Geocode Addresses
    # -----------------------------------------------------------
    sb_geocoder = StarburstGeocoderOperator(
        task_id="geocode_onspot_addresses",
        source_table_name=f'"s3"."bronze_ts_reports"."{ENV}_os_response_addresstodevice"',
        source_row_identifier_column_name="CONCAT(address, '_', zipcode)",
        source_row_alias_column_name="address_pk",
        address1_column_name="address",
        zipcode_column_name="zipcode",
        bridge_table_name='"olympus"."bronze_eltoro_audiences"."onspot_address_ethash_bridge"',
        geocoder_columns=[
            'addressLine',
            'city',
            'state',
            'zipcode',
            'etHashV1',
            'etHashV2',
            'latitude',
            'longitude',
            'matchCode'
        ],
        source_where_clause= """
            request_id = '{{ dag_run.conf.get('request_id') }}'
        """,
        retries=2
    )


    # -----------------------------------------------------------
    # Task 5: Process Responses & Generate Reports
    # -----------------------------------------------------------

    @task.virtualenv(
        requirements=["datascience-implement-cache"],
        system_site_packages=True,
        venv_cache_path="/tmp/airflow_venvs",
        retries=3,  # Response processing task - moderate retries
        retry_delay=timedelta(minutes=5),  # Moderate delay for processing
    )
    def process_responses(request_id: str, onspot_request_task_group_output: dict) -> None:
        """
        Processes the responses received from OnSpot, compiles the results, and updates the job status.

        Args:
            request_id (str): Unique identifier for the timestamp job.
            onspot_request_task_group_output (dict): Output from `create_onspot_request_task_group` containing
                response details.

        Returns:
            None
        """
        from old_dags.dataservices_api.timestamp_report.ts_job import TimestampJob
        from old_dags.dataservices_api.timestamp_report.process_responses import ResponseProcessor

        ts_job = TimestampJob(request_id)
        ts_job.update_job("status", "COMPILING RESULTS")
        ts_job.update_job("location_count", onspot_request_task_group_output["location_count"])
        ts_job.update_job("os_request_count", onspot_request_task_group_output["os_request_count"])
        ts_job.update_job("locations_s3_path", f"s3://vr-timestamp/timestamp-report/{ts_job.env}/{ts_job.report_name}/locations.csv.gz")
        ts_job.update_job("addresstodevice_s3_path", f"s3://vr-timestamp/timestamp-report/{ts_job.env}/{ts_job.report_name}/addresstodevice.csv.gz")
        ts_job.update_job("observations_s3_path", f"s3://vr-timestamp/timestamp-report/{ts_job.env}/{ts_job.report_name}/observations.csv.gz")


        response_processor = ResponseProcessor(ts_job)
        print(vars(response_processor))
        response_processor.process_responses()
        print(ts_job.endpoint_configs)

        return None

    response_processor_output = process_responses(
        request_id="{{ dag_run.conf.get('request_id', 'default_request_id') }}",
        onspot_request_task_group_output=onspot_request_task_group_output
    )

    (
        requestor_output
        >> onspot_request_task_group_output
        >> add_response_partitions
        >> sb_geocoder
        >> response_processor_output
     )
