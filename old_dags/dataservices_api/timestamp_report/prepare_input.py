import json
import requests
import pandas as pd
import pyarrow as pa
from pygene.base_api import NextGenBaseAPI
import pyarrow.parquet as pq
from decimal import Decimal
from pymongo import MongoClient
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.dataservices_api.poly_utils import geojson_to_wkb, wkb_to_geojson
from old_dags.dataservices_api.timestamp_report.ts_job import TimestampJob


class Requestor:
    def __init__(self, job: TimestampJob):
        client = MongoClient("mongodb-2.middleearth.eltoro.com:27000")
        self.mongo = client['prod']
        self.job = job
        self.secret_dict = json.loads(job.dic.get_secret(f"{self.job.env}/bi/quoteenrichment"))
        self.trino_hook = TrinoHook(trino_conn_id="starburst")
        self.bucket = self.mongo.buckets.find_one({"_id": self.job.bucket_id})
        self.locations_s3_uri = f"s3://{self.job.result_bucket}/timestamp-report/{self.job.env}/{self.job.report_name}/locations.csv.gz"
        self.prepared_features_s3_uri = f"s3://{self.job.result_bucket}/timestamp-report/{self.job.env}/prepared_features/{self.job.request_id}.csv.gz"
        if self.job.env == "prod":
            self.polygon_svc_url = "https://polygon-mngmt-svc.api.eltoro.com"
        else:
            self.polygon_svc_url = "https://polygon-mngmt-svc.api.dev.eltoro.com"
        self.location_file_columns = [
            "location",
            "target",
            "etHashV1",
            "name",
            "address",
            "city",
            "state",
            "zip",
            "formatted_address",
            "loc_lat",
            "loc_lng",
        ]

        self.prepared_features_columns = [
            "location",
            "geojson",
            "start",
            "end"
        ]
        self.nextgen_api = NextGenBaseAPI(
            client_id=self.job.nextgen_client_id,
            client_secret=self.job.nextgen_client_secret,
            env=self.job.env
        )

    @staticmethod
    def format_business_bucklocs_for_locations_file_nextgen(polygons: list) -> pd.DataFrame:
        valid_polygons = []
        for polygon in polygons:
            # Default to 0,0 for coordinates
            polygon["loc_lat"] = 0
            polygon["loc_lng"] = 0

            # Try to get better coordinates if available
            try:
                if polygon.get("point") and polygon["point"].get("coordinates"):
                    polygon["loc_lat"] = polygon["point"]["coordinates"][1]
                    polygon["loc_lng"] = polygon["point"]["coordinates"][0]
                elif polygon.get("geometry") and polygon["geometry"].get("coordinates"):
                    # More careful nested access
                    coords = polygon["geometry"]["coordinates"]
                    if coords and isinstance(coords[0], list) and isinstance(coords[0][0], list):
                        polygon["loc_lat"] = coords[0][0][0][1]
                        polygon["loc_lng"] = coords[0][0][0][0]
            except (IndexError, TypeError):
                # Keep the default 0,0 if any access fails
                pass

            # Only add if geometry exists
            if polygon.get("geometry"):
                polygon["features"] = [{"geometry": polygon["geometry"]}]
                polygon["target"] = None
                valid_polygons.append(polygon)

        return pd.DataFrame(valid_polygons) if valid_polygons else pd.DataFrame()

    @staticmethod
    def format_business_bucklocs_for_locations_file(bucklocs: list) -> pd.DataFrame:
        for buckloc in bucklocs:
            try:
                buckloc["loc_lat"] = buckloc["map"]["features"][0]["geometry"][
                    "coordinates"
                ][0][0][0][1]
                buckloc["loc_lng"] = buckloc["map"]["features"][0]["geometry"][
                    "coordinates"
                ][0][0][0][0]
                buckloc["features"] = buckloc["map"]["features"]
                buckloc["target"] = None
            except Exception as e:
                buckloc_id = buckloc.get("_id")
                print(f"Cannot parse geometry of bucklock: {buckloc_id}")

        bucklocs = [b for b in bucklocs if b is not None]

        return pd.DataFrame(bucklocs)

    def write_locations_parquet(self, df: pd.DataFrame) -> None:
        """Process and write locations data to Parquet."""
        df["bucket"] = self.job.bucket_id
        df.rename(columns={"etHashV1": "ethash"}, inplace=True)
        df["loc_lat"] = df["loc_lat"].apply(Decimal)
        df["loc_lng"] = df["loc_lng"].apply(Decimal)
        df["target"] = df["target"].fillna("NA")

        table = pa.Table.from_pandas(df)
        pq.write_to_dataset(
            table,
            root_path=f"s3://{self.job.result_bucket}/ts-report-external/{self.job.dic.env}/locations/",
            partition_cols=["bucket"]
        )

    def get_business_features_nextgen(self):
        all_polygons = []
        limit = 500
        offset = 0

        while True:
            client_polygon_url = f"{self.polygon_svc_url}/api/v1/client-polygon?client_upload_id={self.job.bucket_id}&current_status=ACTIVE&limit={limit}&offset={offset}"
            if self.job.state is not None:
                client_polygon_url += f"&state={self.job.state}"

            headers = {
                'Authorization': f"Bearer {self.nextgen_api.access_token}",
                'Content-Type': 'application/json'
            }

            response = requests.get(client_polygon_url, headers=headers)
            page_polygons = response.json()['data']

            # Break the loop if we got no polygons
            if not page_polygons:
                break

            all_polygons.extend(page_polygons)
            print(f"Retrieved {len(page_polygons)} polygons. Total so far: {len(all_polygons)}")

            # Break if we got fewer polygons than the limit (reached the end)
            if len(page_polygons) < limit:
                break

            # Increase offset for next page
            offset += limit

        # Process all collected polygons
        polygons = self.format_business_bucklocs_for_locations_file_nextgen(all_polygons)
        polygons.rename(columns={"id": "location", "ethashv1": "etHashV1", "zipcode": "zip"}, inplace=True)
        polygons['formatted_address'] = polygons['address'] + ", " + polygons["city"] + ", " + polygons["state"] + " " + \
                                        polygons["zip"]
        df = polygons[["location", "features"]]

        polygons = polygons[self.location_file_columns]
        self.write_locations_parquet(polygons) 
        polygons.to_csv(self.locations_s3_uri, compression='gzip', index=False)
        return df

    def get_business_features_v2(self) -> pd.DataFrame:
        query = {"bucketIds": self.job.bucket_id, "status": 2}
        if self.job.state is not None:
            query["state"] = self.job.state

        bucklocs = list(self.mongo.bucket_locations.find(query))
        bucklocs = self.format_business_bucklocs_for_locations_file(bucklocs)

        bucklocs.rename(columns={"_id": "location", "etHash": "etHashV1"}, inplace=True)
        df = bucklocs[["location", "features"]]
        bucklocs = bucklocs[self.location_file_columns]

        self.write_locations_parquet(bucklocs) 
        bucklocs.to_csv(self.locations_s3_uri, compression='gzip', index=False)
        return pd.DataFrame(df)

    def get_business_features(self) -> pd.DataFrame:
        # Search in NextGen
        client_upload_url = f"{self.polygon_svc_url}/api/v1/client-upload?id={self.job.bucket_id}"
        headers = {
            'Authorization': f"Bearer {self.nextgen_api.access_token}",
            'Content-Type': 'application/json'
        }
        response = requests.get(client_upload_url, headers=headers)
        client_upload_list = response.json()['data']
        if len(client_upload_list) != 0:
            df = self.get_business_features_nextgen()
        else:
            df = self.get_business_features_v2()   
        return pd.DataFrame(df)


    def get_s3_features(self) -> pd.DataFrame:
        #file must be in format [etHashV1, formatted_address, latitude, longitude, address, zip, city, state, wkb]
        df = pd.read_csv(self.job.bucket_id)
        df.rename(
            columns={
                "latitude": "loc_lat",
                "longitude": "loc_lng",
            },
            inplace=True,
        )
        df["name"] = ""
        df["location"] = df["etHashV1"]
        df["target"] = ""
        df["features"] = df["wkb"].apply(wkb_to_geojson)
        df_locations = df[self.location_file_columns]
        df_locations.to_csv(self.locations_s3_uri, compression='gzip', index=False)
        self.write_locations_parquet(df_locations)

        # But only run ones that geocoded successfully through onspot
        df['features'] = df['features'].apply(lambda x: [{"geometry": x}] if x is not None else None)
        df = df[df["features"].apply(lambda x: isinstance(x, list))]
        df = df[~df["etHashV1"].isna()]
        return df[["location", "features", "wkb"]]

    def get_residential_features(self) -> pd.DataFrame:
        for file in self.bucket["files"]:
            if file["type"] == 2:
                s3_uri = f"s3://{file['bucket']}/{file['key']}"
                df = pd.read_csv(s3_uri, nrows=10000)  # yes, this is intentional, not just testing

        df = self.job.geocoder.geocode(
            df,
            df.columns.get_loc("address1"),
            None,
            df.columns.get_loc("zip"),
            ["geometryWKB", "latitude", "longitude", "city", "state"],
        )
        df.rename(
            columns={
                "geometryWKB": "wkb",
                "dic_address_string": "formatted_address",
                "address1": "address",
                "latitude": "loc_lat",
                "longitude": "loc_lng",
            },
            inplace=True,
        )

        df['features'] = df['wkb'].apply(wkb_to_geojson)
        df["name"] = ""
        df["location"] = df["etHash"]
        df.rename(columns={"etHash": "etHashV1"}, inplace=True)
        # Upload *all* rows to 'locations' file
        df_locs = df[self.location_file_columns]
        df_locs.to_csv(self.locations_s3_uri, compression='gzip', index=False)

        # But only run ones that geocoded successfully through onspot
        df['features'] = df['features'].apply(lambda x: [{"geometry": x}] if x is not None else None)
        df = df[df["features"].apply(lambda x: isinstance(x, list))]
        df = df[~df["etHashV1"].isna()]
        return df[["location", "features", "wkb"]]

    def prepare_features(self) -> None:
        print("Begin preparing features")
        if self.job.location_source_type == 'business':
            print("in business path")
            df = self.get_business_features()
        if self.job.location_source_type == 'residential':
            df = self.get_residential_features()
        elif self.job.location_source_type == 's3':
            df = self.get_s3_features()
        self.job.location_count = len(df)

        df["start"] = self.job.start
        df["end"] = self.job.end
        df.rename(columns={"features": "geojson"}, inplace=True)
        df = df[self.prepared_features_columns]
        df.to_csv(self.prepared_features_s3_uri, index=False)

        self.job.update_job("status", "SENDING REQUESTS")
        return
