import io
import json
import logging
import tempfile
import boto3
import gzip
import pandas as pd
from urllib.parse import urlparse
from old_dags.dataservices_api.timestamp_report.ts_job import TimestampJob
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook


logging.getLogger("boto3").setLevel(logging.WARNING)
logging.getLogger("botocore").setLevel(logging.WARNING)
logging.getLogger("datascience-implement-cache").setLevel(logging.WARNING)

class ResponseProcessor:
    def __init__(self, job: TimestampJob):
        self.job = job
        self.secret_dict = json.loads(job.dic.get_secret(f"{self.job.env}/bi/quoteenrichment"))
        self.trino_hook = TrinoHook(trino_conn_id="starburst")
        self.hh_count = 0
        self.did_count = 0

        self.s3_uri = f"s3://{self.job.result_bucket}/timestamp-report/{self.job.env}/{self.job.report_name}/:request_type.csv.gz"

    def produce_obs_results(self):
        s3_uri = self.s3_uri.replace(":request_type", "observations")
        parsed = urlparse(s3_uri)
        bucket = parsed.netloc
        key = parsed.path.lstrip('/')

        # Process in chunks
        chunk_size = 15_000_000
        offset = 0
        self.did_count = 0
        device_set = set()

        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv') as temp_file:
            # Prepare base query based on reduce_to_date setting
            if self.job.reduce_to_date:
                base_stmt = f"""
                    SELECT DISTINCT
                       location,
                       deviceid,
                       "date" "timestamp",
                       '' obs_lat,
                       '' obs_lng,
                       'onspot' data_source
                    FROM "s3"."bronze_ts_reports"."{self.job.env}_os_response_observations" 
                    WHERE request_id = '{self.job.request_id}' 
                """
            else:
                base_stmt = f"""
                    SELECT DISTINCT
                       location,
                       deviceid,
                       date_format(from_unixtime(cast(SUBSTRING(timestamp, 1, 10) as bigint), 'UTC'), '%Y-%m-%d %H:%i:%s') "timestamp",
                       lat obs_lat,
                       lng obs_lng,
                       'onspot' data_source
                    FROM "s3"."bronze_ts_reports"."{self.job.env}_os_response_observations" 
                    WHERE request_id = '{self.job.request_id}'
                """

            columns = [
                'location',
                'deviceid',
                'timestamp',
                'obs_lat',
                'obs_lng',
                'data_source'
            ]
            df_header = pd.DataFrame(columns=columns)
            df_header.to_csv(temp_file.name, index=False)

            while True:
                paginated_stmt = base_stmt + f" ORDER BY deviceid OFFSET {offset} LIMIT {chunk_size}"
                df_chunk = self.trino_hook.get_pandas_df(paginated_stmt)

                if df_chunk.empty:
                    break

                # Add unique devices to set for counting
                device_set.update(df_chunk['deviceid'].unique())

                # Append chunk to temp file (no headers after first chunk)
                df_chunk.to_csv(temp_file.name, index=False, mode='a', header=False)

                # Clean up memory
                del df_chunk
                offset += chunk_size

            # Set the count after processing all chunks
            self.did_count = len(device_set)

            # Upload completed file to S3
            temp_file.flush()
            temp_file.seek(0)

            # Create an in-memory buffer for the gzipped content
            with io.BytesIO() as compressed_data:
                with gzip.GzipFile(fileobj=compressed_data, mode='wb') as gzip_file:
                    with open(temp_file.name, 'rb') as file_data:
                        gzip_file.write(file_data.read())

                # Reset pointer to beginning AFTER GzipFile context is closed
                compressed_data.seek(0)

                s3_client = boto3.client('s3')
                s3_client.put_object(
                    Bucket=bucket,
                    Key=key,
                    Body=compressed_data.getvalue(),
                    ContentType='application/gzip'  # Correct content type
                )

        # Execute insert statement directly in database
        external_stmt = f"""
        INSERT INTO "s3"."bronze_ts_reports"."observations"
        SELECT DISTINCT
            location,
            deviceid,
            '{self.job.bucket_id}' bucket,
            'onspot' data_source,
            date(date_parse("date", '%Y-%m-%d')) "timestamp"
        FROM "s3"."bronze_ts_reports"."{self.job.env}_os_response_observations" WHERE request_id = '{self.job.request_id}'
        """
        self.trino_hook.run(external_stmt)


    def produce_atd_results(self):
        s3_uri = self.s3_uri.replace(":request_type", "addresstodevice")
        parsed = urlparse(s3_uri)
        bucket = parsed.netloc
        key = parsed.path.lstrip('/')

        # Process in chunks
        chunk_size = 15_000_000
        offset = 0
        self.hh_count = 0

        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv') as temp_file:
            columns = [
                'address',
                'city',
                'state',
                'zipcode',
                'zip4',
                'deviceid',
                'hh_lat',
                'hh_lng',
                'matchCode',
                'etHashV1'
            ]
            df_header = pd.DataFrame(columns=columns)
            df_header.to_csv(temp_file.name, index=False)

            while True:
                paginated_stmt = f"""
            WITH a AS (
                SELECT DISTINCT
                    address,
                    city,
                    state,
                    zipcode,
                    zip4,
                    deviceid
                FROM s3.bronze_ts_reports.{self.job.env}_os_response_addresstodevice atd
                CROSS JOIN UNNEST(SPLIT(atd.devices, ',')) AS b (deviceid)
                WHERE request_id = '{self.job.request_id}'
            )
            SELECT
                address,
                a.city,
                a.state,
                a.zipcode,
                zip4,
                deviceid,
                bdg.latitude AS hh_lat,
                bdg.longitude AS hh_lng,
                bdg.matchcode AS "matchCode",
                bdg.ethashv1 AS "etHashV1"
            FROM a
            JOIN "olympus"."bronze_eltoro_audiences"."onspot_address_ethash_bridge" bdg ON CONCAT(a.address, '_', a.zipcode) = bdg.address_pk 
            ORDER BY bdg.ethashv1  OFFSET {offset} LIMIT {chunk_size}
                """
                df_chunk = self.trino_hook.get_pandas_df(paginated_stmt)
                if df_chunk.empty:
                    break

                # Append chunk to temp file (with no headers after first chunk)
                df_chunk.to_csv(temp_file.name, index=False, mode='a', header=False)
                self.hh_count += df_chunk['etHashV1'].nunique()

                # Clean up to free memory
                del df_chunk
                offset += chunk_size

            # Upload completed file to S3
            temp_file.flush()
            temp_file.seek(0)

            # Create an in-memory buffer for the gzipped content
            with io.BytesIO() as compressed_data:
                with gzip.GzipFile(fileobj=compressed_data, mode='wb') as gzip_file:
                    with open(temp_file.name, 'rb') as file_data:
                        gzip_file.write(file_data.read())

                # Reset pointer to beginning AFTER GzipFile context is closed
                compressed_data.seek(0)

                s3_client = boto3.client('s3')
                s3_client.put_object(
                    Bucket=bucket,
                    Key=key,
                    Body=compressed_data.getvalue(),
                    ContentType='application/gzip'  # Correct content type
                )

        external_stmt = f"""
            INSERT INTO "s3"."bronze_ts_reports"."addresstodevice"
            WITH a AS (
                SELECT DISTINCT
                    address,
                    zipcode,
                    deviceid
                FROM s3.bronze_ts_reports.{self.job.env}_os_response_addresstodevice atd
                CROSS JOIN UNNEST(SPLIT(atd.devices, ',')) AS b (deviceid)
                WHERE request_id = '{self.job.request_id}'
            )
            SELECT DISTINCT
                deviceid,
                bdg.ethashv1 AS "etHashV1",
                '{self.job.bucket_id}' bucket,
                DATE('{str(self.job.created.date())}') request_date
            FROM a
            JOIN "olympus"."bronze_eltoro_audiences"."onspot_address_ethash_bridge" bdg ON CONCAT(a.address, '_', a.zipcode) = bdg.address_pk
        """
        self.trino_hook.run(external_stmt)

    def generate_id_dataset(self):
        stmt = f"""
        WITH a AS (
            SELECT DISTINCT
                address,
                zipcode
            FROM s3.bronze_ts_reports.{self.job.env}_os_response_addresstodevice atd
            WHERE request_id = '{self.job.request_id}'
        )            
        SELECT
            brg1.ethashv1 "etHashV1",
            brg2.clip,
            fips_code
        FROM a
        JOIN  "olympus"."bronze_eltoro_audiences"."onspot_address_ethash_bridge" AS brg1 ON CONCAT(a.address, '_', a.zipcode) = brg1.address_pk
        LEFT JOIN "s3"."silver_corelogic"."clip_ethash_bridge" AS brg2 ON brg2.ethashv2 = brg1.ethashv2
        LEFT JOIN "olympus"."silver_corelogic"."property_basic2" pb ON pb.clip = brg2.clip
        """

        df_ids = self.trino_hook.get_pandas_df(stmt)
        df_ids.to_csv(self.s3_uri.replace(":request_type", "ids"), index=False)
        del df_ids
        return

    def process_responses(self) -> None:
        """Main entry point for processing responses."""
        for endpoint in self.job.endpoint_map:
            print(endpoint)
            if endpoint["request_type"] == "observations":
                self.produce_obs_results()
            if endpoint["request_type"] == "addresstodevice":
                self.produce_atd_results()
                self.generate_id_dataset()

        self.job.update_job("hh_count", self.hh_count)
        self.job.update_job("maid_count", self.did_count)
        self.job.update_job("status", "COMPLETE")