CREATE TABLE s3.bronze_ts_reports.dev_os_response_observations (
   location_dup varchar,
   deviceid varchar,
   timestamp varchar,
   date varchar,
   time varchar,
   day_of_week varchar,
   lat varchar,
   lng varchar,
   request_id varchar,
   location varchar
)
WITH (
   external_location = 's3a://onspot-eltoro-vr-prod/inbound/ts-reports-v2/dev/request_type=observations/',
   format = 'CSV',
   skip_header_line_count=1,
   partitioned_by = ARRAY['request_id','location']
)