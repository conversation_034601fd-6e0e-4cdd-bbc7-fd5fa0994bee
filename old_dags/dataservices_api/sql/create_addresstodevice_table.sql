CREATE TABLE s3.bronze_ts_reports.dev_os_response_addresstodevice (
   address varchar,
   city varchar,
   state varchar,
   zipcode varchar,
   zip4 varchar,
   devices varchar,
   request_id varchar,
   location varchar
)
WITH (
   external_location = 's3a://onspot-eltoro-vr-prod/inbound/ts-reports-v2/dev/request_type=addresstodevice/',
   format = 'CSV',
   skip_header_line_count=1,
   partitioned_by = ARRAY['request_id','location']
)