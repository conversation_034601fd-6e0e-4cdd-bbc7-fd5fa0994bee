from airflow.decorators import task, task_group
import logging
from airflow.exceptions import AirflowFailException
from datetime import timedelta


logger = logging.getLogger(__name__)


@task_group(group_id="send_and_monitor_requests")
def create_onspot_request_task_group(request_id: str, requestor_output: dict):
    try:
        """
        Task Group to process OnSpot requests:
        1. **Partition & Format Requests** - Splits data for API requests.
        2. **Send Requests** - Sends requests to OnSpot API.
        3. **Monitor Responses** - Tracks responses and retries if needed.

        Args:
            request_id (str): Unique ID for the request.
            requestor_output (dict): Contains the prepared CSV S3 path and endpoint list.

        Returns:
            dict: Contains the final response S3 path and counts.
        """

        # --------------------
        # Task 1: Partition & Format Requests
        # --------------------
        @task.virtualenv(
            requirements=[
                "boto3",
                "pandas",
                "fsspec",
                "s3fs",
                "shapely",
                "area",
                "pyproj",
            ],
            system_site_packages=True,
            venv_cache_path="/tmp/airflow_venvs",
            retries=3,  # Data processing task - moderate retries
            retry_delay=timedelta(minutes=2),  # Quick retry for data processing
        )
        def partition_requests(requestor_output: dict):
            """
            Partitions and formats the request data before sending to OnSpot.

            Args:
                requestor_output (dict): Contains the prepared CSV S3 URI.

            Returns:
                dict: Partitioned features and the location count.
            """
            import logging

            task_logger = logging.getLogger("partition_requests")
            try:
                import json
                import pandas as pd
                from old_dags.dataservices_api.poly_utils import partition

                print(f"Partitioning input data: {requestor_output}")
                df = pd.read_csv(requestor_output["prepared_features_s3_uri"], dtype=str, compression="gzip")

                location_count = len(df)
                df = partition(df)

                partitioned_features = json.dumps(df.to_dict(orient="records"))

                return {
                    "partitioned_features": partitioned_features,
                    "location_count": location_count
                }

            except Exception as e:
                error_message = f"Partition requests failed: {str(e)}"
                task_logger.error(error_message, exc_info=True)

                return {
                    "error_message": error_message,
                    "task_id": "partition_requests",
                }

        partition_task_output = partition_requests(requestor_output=requestor_output)

        # --------------------
        # Task 2: Send Requests
        # --------------------
        @task.virtualenv(
            requirements=["awsrequests", "boto3", "pandas"],
            system_site_packages=True,
            venv_cache_path="/tmp/airflow_venvs",
            retries=3,  # API-dependent task 
            retry_delay=timedelta(minutes=10),  # Longer delay for API calls
        )
        def send_requests(requestor_output: dict, partition_task_output: dict, request_id: str):
            """
            Sends partitioned requests to OnSpot API and tracks the request count.

            Args:
                requestor_output (dict): Contains endpoint list.
                partition_task_output (dict): Contains partitioned features and location count.
                request_id (str): Unique request ID.

            Returns:
                dict: Contains request count and response S3 path.
            """
            import logging

            task_logger = logging.getLogger("send_requests")

            if "error_message" in partition_task_output:
                return partition_task_output

            try:
                from airflow.providers.amazon.aws.hooks.secrets_manager import SecretsManagerHook
                from airflow.models import Variable
                from airflow.providers.amazon.aws.hooks.s3 import S3Hook
                from old_dags.dataservices_api.poly_utils import format_features, chunk_features_by_bytesize, send_request_to_onspot
                import pandas as pd
                import json

                env = Variable.get("environment")
                os_request_count = 0
                response_s3_paths = []
                endpoint_list = requestor_output["endpoint_list"]
                secret_name = requestor_output["secret_name"]
                partitioned_features = partition_task_output["partitioned_features"]

                hook = SecretsManagerHook(region_name="us-east-1")
                s3_hook = S3Hook(aws_conn_id="s3_conn")
                secrets = json.loads(hook.get_secret(secret_name=secret_name))

                df = pd.DataFrame(json.loads(partitioned_features))

                for endpoint in endpoint_list:
                    request_type = "observations" if endpoint == "/save/geoframe/all/observations" else "addresstodevice"
                    response_s3_path = f"s3://onspot-eltoro-vr-prod/inbound/ts-reports-v2/{env}/request_type={request_type}/request_id={request_id}/"
                    response_s3_paths.append(response_s3_path)

                    preexisting_keys = s3_hook.list_keys(
                        bucket_name="onspot-eltoro-vr-prod",
                        prefix=response_s3_path.lstrip("s3://onspot-eltoro-vr-prod/")
                    )
                    s3_hook.delete_objects(
                        bucket="onspot-eltoro-vr-prod",
                        keys=preexisting_keys
                    )

                    features = format_features(df, response_s3_path)
                    os_request_count += len(features)

                    for chunk in chunk_features_by_bytesize(features):
                        send_request_to_onspot(chunk, endpoint, secrets)

                return {
                    "location_count": partition_task_output["location_count"],
                    "os_request_count": os_request_count,
                    "response_s3_paths": response_s3_paths
                }

            except Exception as e:
                error_message = f"Send requests failed: {str(e)}"
                task_logger.error(error_message, exc_info=True)

                return {
                    "error_message": error_message,
                    "task_id": "send_requests",
                }

        send_requests_output = send_requests(
            request_id=request_id,
            partition_task_output=partition_task_output,
            requestor_output=requestor_output
        )

        # --------------------
        # Task 3: Monitor Responses
        # --------------------
        @task.virtualenv(
            requirements=["boto3"],
            system_site_packages=True,
            venv_cache_path="/tmp/airflow_venvs",
            retries=5,  # API monitoring task - higher retries for external dependencies
            retry_delay=timedelta(minutes=8),  # Moderate delay for monitoring
        )
        def monitor_responses(send_requests_output: dict, requestor_output: dict):
            """
            Monitors multiple S3 response paths and applies retry logic.

            Args:
                send_requests_output (dict): Contains request count and a list of response S3 URIs.
                requestor_output (dict): Contains retry threshold.

            Returns:
                dict: Response metadata including collected response count.
            """
            import logging

            task_logger = logging.getLogger("monitor_responses")

            if "error_message" in send_requests_output:
                return send_requests_output

            try:
                import time
                from urllib.parse import urlparse
                from airflow.providers.amazon.aws.hooks.s3 import S3Hook

                s3_hook = S3Hook()
                retries = 0
                current_response_count = 0
                previous_response_count = 0
                retry_threshold = requestor_output.get("retry_threshold", 0.90)
                os_request_count = send_requests_output["os_request_count"]

                # Now expecting a list of S3 URIs.
                response_s3_uris = send_requests_output["response_s3_paths"]
                print(f"Monitoring responses in S3 for URIs: {response_s3_uris}")

                # Continue checking until full count is reached, or the alternative condition is met.
                while current_response_count < os_request_count and \
                        (current_response_count < os_request_count * retry_threshold or retries <= 20):
                    time.sleep(20)
                    current_response_count = 0

                    # Sum counts across all provided S3 URIs.
                    for s3_uri in response_s3_uris:
                        parsed = urlparse(s3_uri)
                        bucket_name = parsed.netloc
                        prefix = parsed.path.lstrip("/")
                        print(bucket_name, prefix)
                        files = s3_hook.list_keys(bucket_name=bucket_name, prefix=prefix) or []
                        current_response_count += len(files)

                    print(f"{current_response_count} responses received across all URIs. Expected: {os_request_count}")

                    # If no new responses are observed, increase the retry counter.
                    if current_response_count == previous_response_count:
                        retries += 1
                    else:
                        retries = 0
                    previous_response_count = current_response_count

                return {
                    "location_count": send_requests_output["location_count"],
                    "os_request_count": os_request_count,
                    "response_s3_paths": response_s3_uris,
                    "final_response_count": current_response_count
                }

            except Exception as e:
                error_message = f"Monitor responses failed: {str(e)}"
                task_logger.error(error_message, exc_info=True)

                return {
                    "error_message": error_message,
                    "task_id": "monitor_responses",
                }

        monitor_responses_task = monitor_responses(
            requestor_output=requestor_output,
            send_requests_output=send_requests_output
        )

        @task(
            trigger_rule="all_done",
            retries=2,  # Error checking task - fewer retries needed
            retry_delay=timedelta(minutes=3),  # Quick retry for error checking
        )
        def check_onspot_for_errors(monitor_responses_task_output, **kwargs):
            """
            Checks if any task in the group encountered an error and raises an exception if found.
            This task marks the entire task group as failed if any component task had an error.
            """
            if monitor_responses_task_output is None:
                error_message = "Monitor responses task failed and returned None."
                kwargs["ti"].xcom_push(key="error_message", value=error_message)
                raise AirflowFailException(error_message)

            if isinstance(monitor_responses_task_output, dict) and "error_message" in monitor_responses_task_output:
                error_message = monitor_responses_task_output["error_message"]
                task_id = monitor_responses_task_output.get("task_id", "unknown")
                kwargs["ti"].xcom_push(key="error_message", value=error_message)

                raise AirflowFailException(error_message) # Raise an exception to mark the task as failed   
            return monitor_responses_task_output

        # Define task dependencies
        partition_task_output >> send_requests_output >> monitor_responses_task

        final_output = check_onspot_for_errors(monitor_responses_task)

        return final_output
    except Exception as e:
        error_message = f"Task group failed: {str(e)}"
        logger.error(error_message, exc_info=True)

        raise AirflowFailException(f"Task group failed: {e}")
