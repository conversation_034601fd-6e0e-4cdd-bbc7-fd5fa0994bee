import pandas as pd
import gzip
import time
import datetime
import json
import os

from pytz import timezone
from etdag import ETDAG
from airflow.providers.trino.hooks.trino import TrinoHook, TrinoException
from airflow.providers.amazon.aws.operators.s3 import S3Hook
from airflow.providers.amazon.aws.sensors.s3 import S3KeySensor
from airflow.decorators import task
from airflow.utils.dates import days_ago

# Define default_args for the main DAG
default_args = {
    "owner": "Tim Hoang",
    "retries": 0,
}

# Initialize S3 Hook
s3_hook = S3Hook(aws_conn_id="s3_conn")

# Set the path to the README file
dag_directory = os.path.dirname(os.path.abspath(__file__))
readme_file = os.path.join(dag_directory, "README.md")
with open(readme_file, "r") as f:
    readme_content = f.read()


def read_json_from_s3(bucket_name, file_key):
    print(f"Reading JSON from S3 bucket: {bucket_name}, key: {file_key}")
    response = s3_hook.read_key(key=file_key, bucket_name=bucket_name)
    return json.loads(response)


def init_trino_connection():
    try:
        return TrinoHook(trino_conn_id="starburst")
    except TrinoException as e:
        raise ConnectionError(f"Failed to connect to Trino: {str(e)}")


def fetch_and_write_to_s3(job_config):
    cursor = init_trino_connection().get_cursor()
    cursor.execute(job_config["trino_query"])
    columns = [desc[0] for desc in cursor.description]

    chunk_size = 1_000_000
    chunk_number = 1

    while True:
        print(f"Processing chunk {chunk_number}...")
        rows = cursor.fetchmany(chunk_size)
        if not rows:
            break
        all_data = rows

        if job_config.get("LargeDataSet", False):
            process_large_file_and_upload_data(
                all_data, columns, job_config, chunk_number
            )
        else:
            process_and_upload_data(all_data, columns, job_config)

        chunk_number += 1


def process_large_file_and_upload_data(all_data, columns, job_config, chunk_number):
    df = pd.DataFrame(all_data, columns=columns)
    if df.empty:
        raise ValueError("Dataframe is empty. Failing the Task.")

    s3_file_path = f's3://vr-timestamp/bi_sources/tim_test/temp/{job_config["reportName"]}/{job_config["reportName"]}_{chunk_number}.csv'
    s3_hook.load_string(
        df.to_csv(index=False),
        key=s3_file_path,
        bucket_name=job_config["s3_bucket"],
        replace=True,
    )
    print(f"Large data chunk {chunk_number} uploaded to {s3_file_path}")


def process_and_upload_data(all_data, columns, job_config):
    df = pd.DataFrame(all_data, columns=columns)
    print("Uploading dataset to S3...")
    if job_config.get("compress_to_gzip", False):
        upload_csv_to_s3_gzip(df, job_config)
    else:
        upload_csv_to_s3(df, job_config)


def get_time_of_day():
    current_time = datetime.datetime.now(timezone("US/Eastern")).time()
    if 5 <= current_time.hour < 12:
        return "morning"
    elif 12 <= current_time.hour < 17:
        return "afternoon"
    else:
        return "daily"


def upload_csv_to_s3(df, job_config):
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")

    for location in job_config["s3_locations"]:
        time_period = get_time_of_day()
        s3_key = (
            location["s3_key"]
            .replace("%TIME_PERIOD%", time_period)
            .replace("%TODAY%", current_date)
        )
        s3_file_path = f's3://{location["s3_bucket"]}/{s3_key}'

        s3_hook.load_string(
            df.to_csv(index=False),
            key=s3_key,
            bucket_name=location["s3_bucket"],
            replace=True,
        )
        print(f"Uploaded CSV to {s3_file_path}")


def upload_csv_to_s3_gzip(df, job_config):
    for location in job_config["s3_locations"]:
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        time_period = get_time_of_day()
        s3_key = (
            location["s3_key"]
            .replace("%TIME_PERIOD%", time_period)
            .replace("%TODAY%", current_date)
            + ".gz"
        )
        s3_file_path = f's3://{location["s3_bucket"]}/{s3_key}'

        with gzip.GzipFile(filename=s3_file_path, mode="wb") as gzip_file:
            s3_hook.load_bytes(
                gzip_file.write(df.to_csv(index=False).encode("utf-8")),
                key=s3_key,
                bucket_name=location["s3_bucket"],
                replace=True,
            )
        print(f"Uploaded Gzipped CSV to {s3_file_path}")


@task
def refresh_materialized_view():
    materialized_views = [
        """refresh MATERIALIZED VIEW "s3"."dev_prototyping"."xandr_ol_stats" """,
        """refresh MATERIALIZED VIEW "s3"."dev_prototyping"."pacing_assigned_v1" """,
    ]

    cursor = init_trino_connection().get_cursor()

    for mv in materialized_views:
        print(f"Refreshing materialized view: {mv}")
        cursor.execute(mv)


@task
def main_process_task(schedule_config, **kwargs):
    for job_name, job_config in schedule_config.items():
        if job_config.get("enable", False):
            start_time = time.process_time_ns()
            print(f"Starting job: {job_name} ({job_config['reportName']})")
            fetch_and_write_to_s3(job_config)
            end_time = time.process_time_ns()
            print(
                f"Job completed in {(end_time - start_time) / 1_000_000_000:.2f} seconds"
            )


with ETDAG(
    dag_id="pacing_reports",
    default_args=default_args,
    description="Generate Pacing Reports",
    schedule_interval="0 6,14 * * *",
    catchup=False,
    concurrency=1,
    start_date=days_ago(2),
    tags=["starburst", "pacing-report", "nextgen"],
    doc_md=readme_content,
) as dag:

    # Sensor to wait for the configuration file on S3
    wait_for_config = S3KeySensor(
        task_id="wait_for_config_file",
        bucket_name="vr-timestamp",
        bucket_key="bi_sources/airflow_config_sb_s3_sync/pacing_report_config/pacing_report_schedule_config.json",
        aws_conn_id="s3_conn",
        timeout=60 * 60,  # 1 hour
        poke_interval=60,  # 1 minute
        mode="poke",
    )

    # Task to read the configuration from S3
    @task
    def fetch_schedule_config():
        bucket_name = "vr-timestamp"
        file_key = "bi_sources/airflow_config_sb_s3_sync/pacing_report_config/pacing_report_schedule_config.json"
        return read_json_from_s3(bucket_name, file_key)

    schedule_config = fetch_schedule_config()

    refresh_mv_task = refresh_materialized_view()
    main_process = main_process_task(schedule_config=schedule_config)

    wait_for_config >> schedule_config >> refresh_mv_task >> main_process
