from airflow.decorators import task
from airflow.providers.trino.hooks.trino import TrinoHook, TrinoException
from airflow.providers.amazon.aws.operators.s3 import S3Hook
from airflow.operators.python import  task
from airflow.models import Variable
from airflow.utils.dates import days_ago
from etdag import ETDAG
import pandas as pd
import s3fs
import gzip
import time
import boto3
import json
import datetime
from pytz import timezone
import os

# Define default_args for the main DAG
default_args = {
    "owner": "Tim Hoang",
    "retries": 0,
}

s3_hook = S3Hook(aws_conn_id="s3_conn")

dag_directory = os.path.dirname(os.path.abspath(__file__))
readme_file = os.path.join(dag_directory, "README.md")
with open(readme_file, "r") as f:
    readme_content = f.read()


def read_json_from_s3(bucket_name, file_key):
    print(f"Reading JSON from S3 bucket: {bucket_name}, key: {file_key}")
    response = s3_hook.read_key(key=file_key, bucket_name=bucket_name)
    return json.loads(response)


def init_trino_connection():
    try:
        return TrinoHook(trino_conn_id="starburst")
    except TrinoException as e:
        print(str(e))


def fetch_and_write_to_s3(job_config):
    # s3 = s3fs.S3FileSystem()
    cursor = init_trino_connection().get_cursor()
    cursor.execute(job_config["trino_query"])
    columns = [desc[0] for desc in cursor.description]
    all_data = []

    chunk_size = 1_000_000
    chunk_number = 1

    while True:
        print(f"Currently on chunk: {chunk_number}")
        rows = cursor.fetchmany(chunk_size)
        if not rows:
            break
        print("append chunk data into list------")
        all_data.extend(rows)
        if "LargeDataSet" in job_config and job_config["LargeDataSet"]:
            print("processing large dataset....")
            process_large_file_and_upload_data(
                all_data, columns, job_config, chunk_number
            )
        else:
            print("processing and uploading dataset to s3....")
            process_and_upload_data(all_data, columns, job_config)
        chunk_number += 1

    # all_data.clear()


def process_large_file_and_upload_data(all_data, columns, job_config, chunk_number):
    if df.empty:
        raise ValueError("Dataframe is empty. Failing the Task.")
    
    df = pd.DataFrame(all_data, columns=columns)
    temp_file_s3 = f's3://vr-timestamp/bi_sources/tim_test/temp/{job_config["reportName"]}/{job_config["reportName"]}_{chunk_number}.csv'
    # with s3.open(temp_file_s3, "wb") as s3_file:
    df.to_csv(temp_file_s3, index=False, encoding="utf-8")


def process_and_upload_data(all_data, columns, job_config):
    # Convert processed data to a Pandas DataFrame
    print("Convert list to DataFrame------")
    df = pd.DataFrame(all_data, columns=columns)
    # df = pl.DataFrame(all_data)

    print("Upload to s3 based----")
    if job_config["compress_to_gzip"]:
        upload_csv_to_s3_gzip(df, job_config)
    else:
        upload_csv_to_s3(df, job_config)

    # Empty Dataframe
    # df = pd.DataFrame()


def get_time_of_day():
    current_time = datetime.datetime.now(timezone("US/Eastern")).time()

    if current_time.hour >= 5 and current_time.hour < 12:
        return "morning"
    elif current_time.hour >= 12 and current_time.hour < 17:
        return "afternoon"
    else:
        return "daily"


def upload_csv_to_s3(df, job_config):
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")

    for location in job_config["s3_locations"]:
        time_period = get_time_of_day()
        s3_key = (
            location["s3_key"]
            .replace("%TIME_PERIOD%", time_period)
            .replace("%TODAY%", current_date)
        )
        s3_file_path = f's3://{location["s3_bucket"]}/{s3_key}'

        s3_hook.load_string(
            df.to_csv(index=False),
            key=s3_key,
            bucket_name=location["s3_bucket"],
            replace=True,
        )
        print(f"Uploaded CSV to {s3_file_path}")


def upload_csv_to_s3_gzip(df, job_config):
    for location in job_config["s3_locations"]:
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        time_period = get_time_of_day()
        s3_key = (
            location["s3_key"]
            .replace("%TIME_PERIOD%", time_period)
            .replace("%TODAY%", current_date)
            + ".gz"
        )
        s3_file_path = f's3://{location["s3_bucket"]}/{s3_key}'

        with gzip.GzipFile(filename=s3_file_path, mode="wb") as gzip_file:
            s3_hook.load_bytes(
                gzip_file.write(df.to_csv(index=False).encode("utf-8")),
                key=s3_key,
                bucket_name=location["s3_bucket"],
                replace=True,
            )
        print(f"Uploaded Gzipped CSV to {s3_file_path}")


@task()
def refresh_materialized_view():
    refresh_materialized_view = [
        """refresh MATERIALIZED VIEW "s3"."dev_prototyping"."xandr_ol_stats" """,
        """refresh MATERIALIZED VIEW "s3"."dev_prototyping"."pacing_assigned_v1" """,
    ]

    cursor = init_trino_connection().get_cursor()

    for index, value in enumerate(refresh_materialized_view):
        print(f"currently refreshing {index} - {value}")
        cursor.execute(value)


@task()
def main_process_task(schedule_config, **kwargs) -> None:
    for job_name, job_config in schedule_config.items():
        starting_time = time.process_time_ns()
        print(f"Starting at {starting_time}")
        print(job_name)
        print(job_config["reportName"])
        if job_config["enable"]:
            fetch_and_write_to_s3(job_config)
        ending_time = time.process_time_ns()
        print(f"End Time: {ending_time}")
        print(
            f"Elapsed Time from starting to end in Seconds: {(ending_time - starting_time) / 1_000_000_000}"
        )
        print("Next Process-----------------")


with ETDAG(
    dag_id="pacing_reports",
    default_args=default_args,
    description="Generate Pacing Reports",
    schedule_interval="0 6,14 * * *",
    catchup=False,
    concurrency=1,
    start_date=days_ago(2),
    tags=["starburst", "pacing-report", "nextgen"],
) as dag:
    dag.doc_md = readme_content

    bucket_name = "vr-timestamp"
    file_key = "bi_sources/airflow_config_sb_s3_sync/pacing_report_config/pacing_report_schedule_config.json"
    schedule_config = read_json_from_s3(bucket_name, file_key)

    refresh_mv_task = refresh_materialized_view()
    main_process = main_process_task(schedule_config=schedule_config)

    refresh_mv_task >> main_process


if __name__ == "__main__":
    dag.test()
    run = dag.test(mark_success_pattern="wait_for_.*|pandas_python")
    print(f"Intermediate csv: {run.get_task_instance('numpy_result')}")
