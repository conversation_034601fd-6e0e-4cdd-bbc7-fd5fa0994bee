# Airflow Process
DAG Name: trino_to_s3_main_dag </br>
Schedule Interval: 7:30 A.M. and 3:30 P.M. From M to F
</br>owner: <PERSON><PERSON>

# Background Info:
generate Pacing Report for nextgen dashboard via executing a query and deliever the report to S3. Each run should then archive the file depending whether is a morning or afternonn refresh.

# Failure Scenario:
There are 3 retries, failure is unexpected and will have to be troubleshoot in case a day or deal failes

# Source Data:
Trino: 3.dev_prototyping.pacing_assigned_v1

# Escalation Path:
Contact Data Service BI team for DataRep
contact <PERSON><PERSON>ang for DAG failure

## Configuration
* The current configuration are currently located in the following AWS S3 location: s3://vr-timestamp/bi_sources/tim_test/pacing_report_config/pacing_report_schedule_config.json. Please download the report to your local environment. make any changes and reupload to the same location.


</br>

For the pacing report I've added a key to identify how to archived the files based on the time period

| Key                 | Types    |                    Notes                     |                                                      Example |
| ------------------- | :------- | :------------------------------------------: | -----------------------------------------------------------: |
| reportName          | string   |                                              |                                              "auto_bucklocs" |
| enable              | boolean  |         To enable or disable process         |                                                   True/False |
| LargeDataSet        | boolean  |    will there be a large Dataset (>45mil)    |                                                   True/False |
| schedule_type       | string   |                                              |                                                       "cron" |
| schedule_value      | string   |                                              |                                                  "0 1 * * *" |
| trino_query         | string   |                                              | "SELECT * FROM s3.dev_prototyping.pacing_assigned_v1" |
| s3_bucket           | string   |                                              |                                               "vr-timestamp" |
| compression_to_gzip | boolean  |        Does the file need to be gzip         |                                                   True/False |
| s3_locations        | string[] | Does the report need to go multiple location |


### Example 
    "job1": {
        "reportName": "daily_pacing_assign_nexgen",
        "enable": true,
        "LargeDataSet": false,
        "schedule_type": "cron",
        "schedule_value": "30 7* * *",
        "trino_query": "SELECT * FROM s3.dev_prototyping.pacing_assigned_v1",
        "compress_to_gzip": false,
        "s3_locations": [
            {
                "s3_bucket": "vr-timestamp",
                "s3_key": "bi_sources/pacing_reports_test/daily_pacing_assign_nextgen.csv",
                "time_period": "None"
            },
            {
                "s3_bucket": "vr-timestamp",
                "s3_key": "bi_sources/pacing_reports_test/%TIME_PERIOD%/day=%TODAY%/daily_pacing_assign_nextgen.csv",
                "time_period": "morning"
            },
            {
                "s3_bucket": "vr-timestamp",
                "s3_key": "bi_sources/pacing_reports_test/%TIME_PERIOD%/day=%TODAY%/daily_pacing_assign_nextgen.csv",
                "time_period": "afternoon"
            }
        ]
    }