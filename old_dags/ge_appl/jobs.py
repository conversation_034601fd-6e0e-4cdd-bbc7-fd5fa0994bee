from typing import TypedDict
from airflow.models import Variable

class S3Loc(TypedDict):
    s3_bucket: str  # vr-timestamp
    s3_key: str  # bi_sources/toyota_dashboard_datasets/auto_bucklocs.csv

class Job(TypedDict):
    trino_query: str  
    s3_location: S3Loc
    chunk: bool  # False
    gzip_compress: bool # False

env_var = Variable.get("environment", "dev") #default value dev
if env_var == "prod":
    s3_key_prefix = "bi_sources/gea_intent/"
else:
    s3_key_prefix = "bi_sources/gea_intent/dev/"


jobs = {
    "geospring": Job(
        trino_query='SELECT * FROM s3.dev_prototyping.gea_geospring_audience;', 
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}gea_geospring_audience.csv"),
        chunk=False,
        gzip_compress=False
    ),
    "hvac": Job(
        trino_query='SELECT * FROM s3.dev_prototyping.gea_hvac_audience;',
        s3_location=S3Loc(s3_bucket="vr-timestamp", s3_key=f"{s3_key_prefix}gea_hvac_audience.csv"),
        chunk=False,
        gzip_compress=False
    ),
}
