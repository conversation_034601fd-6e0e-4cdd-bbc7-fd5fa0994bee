from airflow.decorators import task
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import pandas as pd
import tempfile
from old_dags.ge_appl.jobs import Job, jobs
import gzip
import shutil

@task
def fetch_and_write_to_s3(job_config: Job):
    print(job_config["trino_query"])
    s3_key = job_config["s3_location"]["s3_key"]
    bucket_name = job_config["s3_location"]["s3_bucket"]
    if job_config["chunk"]:
        df_chunks = TrinoHook(trino_conn_id="starburst").get_pandas_df_by_chunks(
            sql=job_config["trino_query"], chunksize=1_000_000
        )
        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
        for i, df_chunk in enumerate(df_chunks):
            print(f"writing chunk {i}")
            if i == 0:
                df_chunk.to_csv(temp_file_path, index=False)
            else:
                df_chunk.to_csv(temp_file_path, mode="a", header=False, index=False)
    else:
        df = TrinoHook(trino_conn_id="starburst").get_pandas_df(
            sql=job_config["trino_query"]
        )
        with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
        df.to_csv(temp_file_path, index=False)

    if job_config["gzip_compress"]:
        print("compressing file")
        compressed_file_path = temp_file_path + ".gz"
        s3_key = s3_key + ".gz"
        with open(temp_file_path, "rb") as f_in:
            with gzip.open(temp_file_path + ".gz", "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)

        file_to_upload = compressed_file_path
    else:
        file_to_upload = temp_file_path
    
    s3_hook = S3Hook(aws_conn_id="s3_conn")
    s3_hook.load_file(
                filename=file_to_upload,
                key=s3_key,
                bucket_name=bucket_name,
                replace=True,
            )

default_args = {
    "owner": "Luke McArthur",
}
with ETDAG(
    dag_id="ge_appliances_intent",
    description="Uploads enriched GE appliance intent data to S3",
    start_date=days_ago(2),
    default_args=default_args,
    schedule_interval="0 8 * * 1,3,5",
    et_failure_msg=True,
    catchup=False,
    tags=["ge", "intent"],
) as dag:

    for job_name, job_config_item in jobs.items():
        fetch_and_write_to_s3.override(task_id=f"fetch_and_write_{job_name}")(
            job_config=job_config_item
        )




