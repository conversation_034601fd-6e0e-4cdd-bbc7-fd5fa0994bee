from flask import g
from etdag import ETDAG
from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from datetime import timedelta
from datetime import datetime
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.models import Variable

docs = """
# New Mover ETL
Background Info:
Failure Scenario:
Source Data:
Result Data Destination:  
Escalation Path:  
* Contact Beaven for DataRep
In Case Data Source Breaks:  
"""

with ETDAG(
    dag_id="new_mover_fci_etl",
    default_args= {
        "owner": "<PERSON>huyler", 
        "retries":3, 
        "retry_delay": timedelta(seconds=60),
    },
    is_paused_upon_creation=True,
    schedule_interval="0 10 * * 3", 
    max_active_runs=1,
    catchup=False,
    et_failure_msg=False,
    tags=["new_mover_fci"],
) as dag:
    results_bucket = "vr-timestamp"
    results_base_path = "bi_sources/fci/new_mover_append/"
    @task
    def generate_results(results_bucket: str, results_base_path: str, trino_conn_id='trino_conn'):
        df = TrinoHook(trino_conn_id=trino_conn_id).get_pandas_df(
            """
            SELECT * FROM "s3"."dev_prototyping"."new_mover_daily_append" 
            """
        )
        results_full_path = f"{results_base_path}new_mover_append_extract.csv.gz"
        df.to_csv(f"s3://{results_bucket}/{results_full_path}", index=False, compression='gzip')
        return results_full_path

    generate_results_task = generate_results(results_bucket, results_base_path)

    env = Variable.get('ENVIRONMENT', default_var='dev')
   

    generate_results_task