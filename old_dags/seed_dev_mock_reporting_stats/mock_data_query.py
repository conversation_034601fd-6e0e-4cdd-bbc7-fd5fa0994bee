query = """
    WITH ol_creative AS (
        SELECT
            order_line_id,
            c.creative_id
        FROM s3.silver_platform_services.dev_order_lines op
        CROSS JOIN UNNEST(op.creatives) c (creative_refid, creative_id, "creative_type")
    ),
    hours AS (
        SELECT 
            date_add('hour', hour, CAST(DATE(':DATE') AS TIMESTAMP)) AS day
        FROM UNNEST(SEQUENCE(0, 23)) AS t (hour)
    )
    SELECT DISTINCT
        hours.day,
        max(orgs.ref_id) AS org_id_appnexus,
        ol.org_id,
        ol.org_name,
        ol.campaign_id,
        ol.campaign_name,
        111 campaign_id_appnexus,
        ol.order_line_id,
        ol.order_line_name,
        max(ol.ref_id) AS order_line_id_appnexus,
        c.id AS creative_id, 
        c.name AS creative_name,
        max(c.ref_id) AS creative_id_appnexus,
        max(CONCAT(CAST(c.height AS VARCHAR), 'x', CAST(c.width AS VARCHAR))) AS size,
        0 AS ol_target_type,
        0 AS ol_creative_type,
        3 AS bidder_source,
        -- Mocking metric columns with random values
        CAST(rand() * 5000 AS INTEGER) AS imps,
        CAST(rand() * 5000 AS INTEGER) AS imp_requests,
        CAST(rand() * 100 AS INTEGER) AS imps_blank,  
        CAST(rand() * 50 AS INTEGER) AS imps_psa,
        CAST(rand() * 10 AS INTEGER) AS imps_psa_error,
        CAST(rand() * 20 AS INTEGER) AS imps_default_error,
        CAST(rand() * 50 AS INTEGER) AS imps_default_bidder,
        CAST(rand() * 100 AS INTEGER) AS imps_kept,
        CAST(rand() * 100 AS INTEGER) AS imps_resold,
        CAST(rand() * 5000 AS INTEGER) AS imps_rtb,
        CAST(rand() * 50 AS INTEGER) AS external_impression,
        CAST(rand() * 100 AS INTEGER) AS clicks,
        CAST(rand() * 10 AS INTEGER) AS external_click,
        ROUND(rand() * 1000, 2) AS cost,
        ROUND((rand() * 1000) + (rand() * 50), 2) AS cost_including_fees,
        ROUND(rand() * 1500, 2) AS revenue,
        ROUND((rand() * 1500) + (rand() * 50), 2) AS revenue_including_fees,
        ROUND(rand() * 1500, 2) AS booked_revenue,
        ROUND(rand() * 1350, 2) AS booked_revenue_adv_curr,
        ROUND(rand() * 1000, 2) AS booked_revenue_ecpm,
        ROUND(rand() * 150, 2) AS reseller_revenue,
        ROUND((rand() * 1500) - (rand() * 1000), 2) AS profit,
        ROUND((rand() * 1500) - (rand() * 1000) - (rand() * 20), 2) AS profit_including_fees,
        CAST(rand() * 100 AS INTEGER) AS commissions,
        CAST(rand() * 10 AS INTEGER) AS post_click_convs,
        CAST(rand() * 100 AS INTEGER) * 10 AS post_click_revenue,
        CAST(rand() * 10 AS INTEGER) AS post_view_convs,
        CAST(rand() * 10 AS INTEGER) * 15 AS post_view_revenue,
        CAST(rand() * 20 AS INTEGER) AS conversions,
        CAST(rand() * 1000 AS INTEGER) AS imps_viewed,
        CAST(rand() * 500 AS INTEGER) AS view_measured_imps,
        ROUND(rand() * 100, 2) AS data_costs,
        ROUND(rand() * 500, 2) AS media_cost_pub_curr,
        ROUND(rand() * 1000, 2) - ROUND(rand() * 500, 2) AS serving_fees,
        CAST(rand() * 100 AS INTEGER) AS pcts_25,
        CAST(rand() * 100 AS INTEGER) AS pcts_50,
        CAST(rand() * 100 AS INTEGER) AS pcts_75,
        CAST(rand() * 50 AS INTEGER) AS skips,
        CAST(rand() * 100 AS INTEGER) AS starts,
        CAST(rand() * 100 AS INTEGER) AS completions,
        CAST(rand() * 5000 AS INTEGER) AS served,
        CAST(rand() * 20 AS INTEGER) AS errors
    FROM s3.silver_platform_services.dev_order_lines ol
    JOIN s3.silver_platform_services.dev_orgs orgs ON orgs.id = ol.org_id
    JOIN ol_creative ON ol_creative.order_line_id = ol.order_line_id
    JOIN s3.silver_platform_services.dev_creatives c ON c.id = ol_creative.creative_id
    CROSS JOIN hours
    WHERE 
    hours.day BETWEEN ol.order_line_start AND ol.order_line_start + INTERVAL '6' DAY
    GROUP BY 1, 3, 4, 5, 6, 7, 8, 9, 11, 12 
    ORDER BY ol.order_line_id, hours.day

"""

narollupv2 = {
    "columns": [
        "day",
        "org_id_appnexus",
        "org_id",
        "org_name",
        "campaign_id_appnexus",
        "campaign_id",
        "campaign_name",
        "order_line_id",
        "order_line_name",
        "order_line_id_appnexus",
        "creative_id",
        "creative_name",
        "creative_id_appnexus",
        "size",
        "imps",
        "imp_requests",
        "imps_blank",
        "imps_psa",
        "imps_psa_error",
        "imps_default_error",
        "imps_default_bidder",
        "imps_kept",
        "imps_resold",
        "imps_rtb",
        "external_impression",
        "clicks",
        "external_click",
        "cost",
        "cost_including_fees",
        "revenue",
        "revenue_including_fees",
        "booked_revenue",
        "booked_revenue_adv_curr",
        "booked_revenue_ecpm",
        "reseller_revenue",
        "profit",
        "profit_including_fees",
        "commissions",
        "post_click_convs",
        "post_click_revenue",
        "post_view_convs",
        "post_view_revenue",
        "conversions",
        "media_cost_pub_curr",
        "serving_fees",
        "imps_viewed",
        "view_measured_imps",
        "data_costs",
        "pcts_75",
        "pcts_50",
        "skips",
        "starts",
        "completions",
        "pcts_25",
        "served",
        "errors",
        "bidder_source",
        "ol_target_type",
        "ol_creative_type"
    ]
}