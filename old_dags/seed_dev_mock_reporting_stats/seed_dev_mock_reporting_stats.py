from etdag import ETDAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonVirtualenvOperator

docs = """
DAG: seed_dev_mock_reporting_stats

Summary:
Seeds Reporting Stats for Nextgen 'deployed' order lines into the reporting Elasticsearch database.

Time Dependency:
- It runs every day at 6:00 AM (schedule interval: "0 6 * * 0").
- Catchup is enabled, meaning the DAG will backfill missed runs.

Failures:
- The one and only task, PythonVirtualenvOperator task which allows 3 retries with a 10-minute delay.
- In the event of a failure clear the task that failed.

Escalation:
- If rerunning the Dag <NAME_EMAIL>.

Dependencies:
- DAG inputs are dependent on Starburst, specifically the Platform Services DEV BI Export. Trace your way back from s3.silver_platform_services.dev_order_lines if the dag is succeeding but there are reports that the reporting data is not updating.
- Contact Platform Services if you have confirmed that the external tables supporting this have not updated in the last 24 hours.
- Dag outputs are dependent on an Elasticsearch cluster not in k8s.
- It uses the `PythonVirtualenvOperator` for isolated execution due to an elasticsearch dependency that needs to be frozen.

Enhancements:
- If the Platform Services team requests enhancements to the mock data (mocked in relation to ordered impressions and reasonably distributed between the start/end dates, all these modification can be made in the SQL query and someone on the BI team can likely assist in that)

Results/Outputs:
- The result of the DAG is that the mock reporting data for 'deployed' order lines is generated and inserted into the Elasticsearch index `devnetworkanalytics`.
- This output could potentially be easily viewed by going to https://eltoro-ui.api.dev.eltoro.com/, finding a dev orderline that is currently active, and navigating to the reporting tab and looking in the network tab in the browser. 
"""

default_args = {
    'owner': 'Rorie Lizenby',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0
}

with ETDAG(
    dag_id="seed_dev_mock_reporting_stats",
    description="Seeds Reporting Stats for Nextgen 'deployed' order lines into the reporting elasticsearch database",
    start_date=datetime(2025,2,1),
    default_args=default_args,
    max_active_runs=1,
    schedule_interval="0 6 * * *",
    catchup=True,
    tags=["application:reporting", "team:DND"],
) as dag:
    dag.doc_md = docs
    def query_and_upsert_mock_reporting_stats(date):
        import pandas as pd
        from old_dags.seed_dev_mock_reporting_stats.mock_data_query import query, narollupv2
        from trino.dbapi import connect
        from elasticsearch import Elasticsearch, helpers
        from airflow.hooks.base import BaseHook

        trino_conn = BaseHook.get_connection('starburst')
        print(date)

        conn = connect(
            http_scheme='http',
            host=trino_conn.host.replace("http://", ""),
            port=trino_conn.port,
            user=trino_conn.login,
            catalog='s3',
            schema='default',
        )

        def build_id_for_narollup2(record):
            newtime = record['day'].replace(" ", "_").replace(":", "-")

            if record['org_id'].endswith("-p"):
                id = record["org_id"]
                id = id[:len(id) - 2]
                record["org_id"] = id

            key = f"{str(newtime)}_{str(record['org_id'])}_{str(record['campaign_id'])}_{str(record['order_line_id'])}_{str(record['creative_id'])}_{str(record['creative_name'])}_0_0_{str(record['bidder_source'])}"
            if str(record['campaign_id']) == "--" or str(record['order_line_id']) == '--':
                return '', False
            else:
                return key, True

        def upsert_narollup2(df):
            es_new = Elasticsearch(
                hosts=[{'host': 'elasticsearch.middleearth.eltoro.com', 'port': 9200, "scheme": "http"}],
                request_timeout=100)
            actions = []
            for record in df[narollupv2['columns']].to_dict(orient='records'):
                action = {
                    "_index": "devnetworkanalytics",
                    "_type": "devnetworkanalytics",
                    "_id": build_id_for_narollup2(record)[0],
                    "_source": record,
                    "doc_as_upsert": "true"}
                actions.append(action)
            response = helpers.bulk(es_new, actions, index="devnetworkanalytics")
            print(response)

        def upsert_date(day):
            # Most of the data preparation is taken care of in the query.
            print(f"Upserting stats for {day}")
            df = pd.read_sql(query.replace(":DATE", day), conn)
            df['day'] = pd.to_datetime(df['day']).dt.strftime('%Y-%m-%dT%H:%M:%S')

            upsert_narollup2(df)

        upsert_date(date[:10])


    my_isolated_task = PythonVirtualenvOperator(
        task_id="query_and_upsert_mock_reporting_stats",
        python_callable=query_and_upsert_mock_reporting_stats,
        op_args=['{{ data_interval_end }}'],
        requirements=[
            "trino==0.327.0",
            "elasticsearch==7.13.4",
            "boto3",
        ],
        retries=3,
        retry_delay=timedelta(minutes=10)
    )