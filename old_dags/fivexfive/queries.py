# Change the topic
INSERT_INTO_BRONZE = """
INSERT INTO olympus.bronze_5x5.{env}aigds
SELECT distinct
    sha256_lc_hem,
    id,
    topic,
    score,
    DATE('{year}-{month}-{day}') event_date
FROM s3.external_5x5.aigds_feed where dt = '{date}' and topic {sign} '4eyes_111111'
"""

INSERT_INTO_BRONZE_CUSTOM = """
INSERT INTO olympus.bronze_5x5.{env}aigds_custom
SELECT distinct
    sha256_lc_hem,
    id,
    topic,
    score,
    DATE('{year}-{month}-{day}') event_date
FROM s3.external_5x5.aigds_custom where yyyy = '{year}' and mm = '{month}' and dd = '{day}'
"""

GET_DATE_PARTITIONS = """
SELECT * FROM s3.external_5x5."aigds_feed$partitions"
"""

GET_DATE_PARTITIONS_CUSTOM = """
SELECT * FROM s3.external_5x5."aigds_custom$partitions"
"""