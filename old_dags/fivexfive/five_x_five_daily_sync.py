from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.decorators import task
from airflow.utils.dates import days_ago
from airflow.models import Variable
from operators.sync_s3_cross_region_operator import CrossRegionS3SyncOperator
from airflow.providers.trino.hooks.trino import <PERSON><PERSON>Hook

# from airflow.exceptions import AirflowFailException, AirflowSkipException
from typing import List
import pendulum
from etdag import ETDAG
from etdag import task_slack_error_alert
import os
import git
import re
import time
from airflow.operators.python import task, get_current_context
from datetime import timedelta, datetime

default_args = {
    "owner": "BP",
    "retries": 3,  # Each task retries up to 3 times on failure
    "retry_delay": timedelta(minutes=5),
}

TRINO_CATALOG = "s3"
TRINO_SCHEMA = "external_5x5"
dest_s3_conn = "s3_conn"
source_s3_conn = "fivexfive_s3_conn"
intent_source_bucket = "trovo-coop-eltoro"
intent_source_prefix = "outgoing/"
env = Variable.get("environment", default_var="dev")

git_repo = git.Repo(
    os.path.dirname(os.path.realpath(__file__)), search_parent_directories=True
)
repo_uri = "eltorocorp/airflow-dags"  # git_repo.remotes.origin.url
repo_path = os.path.realpath(__file__).removeprefix(git_repo.working_dir)


@task()
def list_folders(source_bucket: str, source_prefix: str) -> List[str]:
    """List folders in the source S3 bucket with filtering logic based on bucket name."""
    print(f"Listing folders in bucket {source_bucket} with prefix {source_prefix}...")
    s3_hook = S3Hook(aws_conn_id=source_s3_conn)
    s3_client = s3_hook.get_conn()

    paginator = s3_client.get_paginator("list_objects_v2")
    pages = paginator.paginate(
        Bucket=source_bucket, Prefix=source_prefix or "", Delimiter="/"
    )

    folder_paths = []
    for page in pages:
        for prefix in page.get("CommonPrefixes", []):
            folder_paths.append(prefix["Prefix"])

    if source_bucket == "trovo-coop-eltoro":
        excluded_paths = {"outgoing/custom_topics/", "outgoing/intent_feed/"}
        folder_paths = [
            path
            for path in folder_paths
            if not any(path.startswith(excluded) for excluded in excluded_paths)
        ]
    elif source_bucket == "trovo-coop-fe-sync":
        seven_days_ago = pendulum.now("UTC").subtract(days=7)
        dt_pattern = re.compile(r"dt=(\d{8})/")
        filtered = []
        for path in folder_paths:
            match = dt_pattern.search(path)
            if match:
                dt_str = match.group(1)
                dt = pendulum.parse(dt_str, tz="UTC")
                if dt >= seven_days_ago:
                    filtered.append(path)
        folder_paths = filtered

    print(f"Filtered {len(folder_paths)} folders: {folder_paths}")
    return folder_paths


@task(
    map_index_template="{{ my_custom_map_index }}",
    retries=3,
    retry_delay=timedelta(minutes=5),
)
def s3_intent_sync(folder: str):
    try:
        context = get_current_context()
        folder_name = folder.split("/")[-2]
        task_id = f"s3_sync_{folder.replace('/', '_')}"
        context["my_custom_map_index"] = folder_name
        intent_feed = CrossRegionS3SyncOperator(
            task_id=task_id,
            source_bucket="trovo-coop-eltoro",
            dest_bucket="eltoro-data-sources",
            source_prefix="outgoing/",
            dest_prefix="5x5/outgoing/",
            dest_s3_conn=dest_s3_conn,
            source_s3_conn=source_s3_conn,
            source_region="us-west-2",
            dest_region="us-east-1",
            folder=folder,
        )

        return intent_feed.execute(context)
    except Exception as e:
        raise Exception(f"Failed to copy folder {folder_name}: {e}S")


with ETDAG(
    dag_id="5x5_daily_sync",
    description="Syncs 5x5 data files between S3 buckets",
    start_date=days_ago(2),
    default_args=default_args,
    on_failure_callback=task_slack_error_alert,
    schedule_interval="@daily",
    catchup=False,
    et_failure_msg=True,
    is_paused_upon_creation=True,
    tags=["intent", "5x5"],
) as dag:

    list_intent_folders = list_folders(intent_source_bucket, intent_source_prefix)
    sync_intent_tasks = s3_intent_sync.partial().expand(folder=list_intent_folders)

    list_intent_folders >> sync_intent_tasks
