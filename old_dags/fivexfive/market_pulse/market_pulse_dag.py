from airflow.decorators import task
import logging
import pendulum
from airflow.utils.dates import days_ago
from etdag import ETDAG
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.decorators import task
from airflow.operators.python import get_current_context
from datetime import timed<PERSON>ta
from airflow.utils.task_group import TaskGroup
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import BranchPythonOperator

logger = logging.getLogger(__name__)
from etdag import task_slack_error_alert
from old_dags.fivexfive.market_pulse.market_pulse_imports import (
    list_folders,
    convert_to_dest_path,
    copy_new_files,
    taxonomy_file_needs_copy,
)
from old_dags.fivexfive.market_pulse.market_pulse_queries import (
    INSERT_MISSING_B2B_ROWS,
    INSERT_MISSING_B2C_ROWS,
    GET_B2C_TAXONOMY_COUNTS,
    GET_B2B_TAXONOMY_COUNTS,
    FIND_MISSING_BUSINESS_DATES,
    FIND_MISSING_CONSUMER_DATES,
    FIND_MISSING_COMBINED_DATES,
)
from old_dags.fivexfive.market_pulse.market_pulse_insert import (
    load_consumer,
    insert_missing_b2b_ids,
    check_taxonomy_counts,
    load_limited_market_pulse_business,
    load_market_pulse_combined,
    drop_old_combined_partitions,
    insert_special_limited_b2b_id,
    backfill_90_days_for_topic,
)


default_args = {
    "owner": "BP",
    "retries": 3,  # Each task retries up to 3 times on failure
    "retry_delay": timedelta(minutes=5),
}

"""
DAG: market_pulse_import_and_stage

This DAG orchestrates the daily and backfill ETL process for Market Pulse data.
It supports both normal daily loads and a special backfill mode for a specific topic and date range.

Features:
- Loads consumer and business market pulse data from S3 into Olympus tables.
- Combines consumer and business data into a single partitioned table.
- Handles missing IDs and partitions with idempotent logic.
- Drops old partitions to retain only the most recent 90 days.
- Supports a "special backfill" mode, triggered by DAG params, to backfill a specific topic for 90 days and update taxonomy tables.
- Uses dynamic task mapping and robust branching to ensure only the correct path runs per execution.

Params:
- min_date: Earliest date to consider for backfill or missing data.
- special_topic_id: Topic ID for special backfill mode (optional).
- special_backfill_date: Start date for special backfill mode (optional).
- special_confirm: Boolean flag to confirm special backfill mode (must be True to activate).

Branching:
- If special params are set and confirmed, only the special backfill chain runs.
- Otherwise, the normal daily ETL chain runs.

All tasks are designed to be idempotent and safe for retries.
"""

SOURCE_S3_CONN = "fivexfive_s3_conn"
DEST_S3_CONN = "s3_conn"
SOURCE_REGION = "us-west-2"
DEST_REGION = "us-east-1"

BUCKET_CONFIGS = [
    {
        "source_bucket": "trovo-coop-mp-consumer",
        "dest_bucket": "eltoro-data-sources",
        "source_prefix": "delivery/",
        "dest_prefix": "5x5/delivery/consumer/",
        "taxonomy_prefix": "5x5/delivery/consumer_taxonomy/",
    },
    {
        "source_bucket": "trovo-coop-mp-business",
        "dest_bucket": "eltoro-data-sources",
        "source_prefix": "delivery/",
        "dest_prefix": "5x5/delivery/business/",
        "taxonomy_prefix": "5x5/delivery/business_taxonomy/",
    },
]


def branch_on_b2b_counts(**context):
    should_insert = context["ti"].xcom_pull(task_ids="check_taxonomy_counts_b2b")
    if should_insert:
        return "insert_missing_b2b_ids"
    else:
        return "skip_insert_b2b_ids"


def branch_on_b2c_counts(**context):
    should_insert = context["ti"].xcom_pull(task_ids="check_taxonomy_counts_b2c")
    if should_insert:
        return "insert_missing_b2c_rows"
    else:
        return "skip_insert_missing_b2c_rows"


def branch_on_special_params(**context):
    params = context["params"]
    if (
        params.get("special_topic_id")
        and params.get("special_backfill_date")
        and params.get("special_confirm") is True
    ):
        return "special_backfill_start"
    return "normal_start"


@task(trigger_rule="all_done")
def get_folders_to_copy(
    source_bucket, dest_bucket, source_prefix, dest_prefix, taxonomy_prefix
):
    ctx = get_current_context()
    param = ctx["params"].get("min_date")

    if param:
        min_date = pendulum.parse(param).date()
    else:
        min_date = param

    logger.info("📊 Gathering folder date metadata...")

    source_folders = list_folders(
        aws_conn=SOURCE_S3_CONN,
        bucket=source_bucket,
        prefix=source_prefix,
        min_date=min_date,
    )

    s3_source = S3Hook(aws_conn_id=SOURCE_S3_CONN).get_conn()
    s3_dest = S3Hook(aws_conn_id=DEST_S3_CONN).get_conn()

    folders_to_copy = []

    for source_folder in source_folders:
        print(f"Processing source folder: {source_folder}")
        if source_folder == "delivery/current-taxonomy/":
            if taxonomy_file_needs_copy(
                s3_source,
                s3_dest,
                source_bucket,
                dest_bucket,
                source_folder,
                taxonomy_prefix,
            ):
                dest_folder = taxonomy_prefix
                folders_to_copy.append((source_folder, dest_folder))
            continue
        else:
            dest_folder = convert_to_dest_path(source_folder, dest_prefix)

        # List objects in source
        src_objs = s3_source.list_objects_v2(
            Bucket=source_bucket, Prefix=source_folder, RequestPayer="requester"
        ).get("Contents", [])
        src_files = [obj["Key"] for obj in src_objs if not obj["Key"].endswith("/")]

        # List objects in destination
        dest_objs = s3_dest.list_objects_v2(
            Bucket=dest_bucket, Prefix=dest_folder, RequestPayer="requester"
        ).get("Contents", [])
        dest_files = [obj["Key"] for obj in dest_objs if not obj["Key"].endswith("/")]

        src_count = len(src_files)
        dest_count = len(dest_files)

        logger.info(
            f"📁 Folder: {source_folder} | Source: {src_count} files | Dest: {dest_count} files"
        )

        if src_count != dest_count:
            logger.info(f"📦 Marked for copy: {source_folder}")
            folders_to_copy.append((source_folder, dest_folder))

    logger.info(f"✅ Total folders to copy: {len(folders_to_copy)}")
    return folders_to_copy


@task(max_active_tis_per_dag=10)
def copy_single_folder(
    folder_pair,
    source_s3_conn,
    dest_s3_conn,
    source_region,
    dest_region,
    source_bucket,
    dest_bucket,
):
    source_folder, dest_folder = folder_pair
    copy_new_files(
        source_folder,
        source_s3_conn,
        dest_s3_conn,
        source_region,
        dest_region,
        source_bucket,
        dest_bucket,
        dest_folder,
    )


@task
def delete_taxonomy_parquet_files():
    """
    Delete consumer_taxo.parquet and business_taxo.parquet files from S3.
    This task runs after normal_start and before consumer_sync to clean up old taxonomy files.
    """
    s3_hook = S3Hook(aws_conn_id=DEST_S3_CONN)

    files_to_delete = [
        {
            "bucket": "eltoro-data-sources",
            "key": "5x5/delivery/consumer_taxonomy/consumer_taxo.parquet",
            "description": "consumer taxonomy parquet file",
        },
        {
            "bucket": "eltoro-data-sources",
            "key": "5x5/delivery/business_taxonomy/business_taxo.parquet",
            "description": "business taxonomy parquet file",
        },
    ]

    for file_info in files_to_delete:
        try:
            # Check if file exists before attempting to delete
            if s3_hook.check_for_key(file_info["key"], bucket_name=file_info["bucket"]):
                s3_hook.delete_objects(
                    bucket=file_info["bucket"], keys=[file_info["key"]]
                )
                logger.info(
                    f"✅ Successfully deleted {file_info['description']}: s3://{file_info['bucket']}/{file_info['key']}"
                )
            else:
                logger.info(
                    f"ℹ️  {file_info['description']} not found, skipping: s3://{file_info['bucket']}/{file_info['key']}"
                )
        except Exception as e:
            logger.error(f"❌ Failed to delete {file_info['description']}: {str(e)}")
            # Continue with the next file even if one fails
            continue

    logger.info("🧹 Taxonomy parquet file cleanup completed")


def build_delivery_sync_chain():
    taskgroups = {}

    for config in BUCKET_CONFIGS:
        table = config["dest_prefix"].rstrip("/").split("/")[-1]
        group_id = table + "_sync"  # e.g., "consumer_sync" or "business_sync"

        with TaskGroup(group_id=group_id) as tg:
            folders = get_folders_to_copy.override(
                task_id=f"copy_{group_id}", trigger_rule="none_skipped"
            )(
                config["source_bucket"],
                config["dest_bucket"],
                config["source_prefix"],
                config["dest_prefix"],
                config["taxonomy_prefix"],
            )

            copy = copy_single_folder.partial(
                source_s3_conn=SOURCE_S3_CONN,
                dest_s3_conn=DEST_S3_CONN,
                source_region=SOURCE_REGION,
                dest_region=DEST_REGION,
                source_bucket=config["source_bucket"],
                dest_bucket=config["dest_bucket"],
            ).expand(folder_pair=folders)

            update_intent = SQLExecuteQueryOperator(
                task_id="update_external_intent",
                conn_id="trino_conn",
                sql=f"CALL s3.system.sync_partition_metadata('external_5x5', 'market_pulse_{table}', 'ADD')",
                handler=list,
            )

            update_taxonomy = SQLExecuteQueryOperator(
                task_id="update_external_taxonomy",
                conn_id="trino_conn",
                sql=f"CALL s3.system.sync_partition_metadata('external_5x5', 'market_pulse_{table}', 'ADD')",
                handler=list,
            )

            folders >> copy >> update_intent >> update_taxonomy

        taskgroups[table] = tg

    return taskgroups


with ETDAG(
    dag_id="market_pulse_import_and_stage",
    description="",
    start_date=days_ago(2),
    default_args=default_args,
    on_failure_callback=task_slack_error_alert,
    schedule_interval="0 7 * * *",  # Run every day at 3 AM
    catchup=False,
    et_failure_msg=True,
    params={
        "min_date": (pendulum.today("EST") - timedelta(days=1)).to_date_string(),
        "special_topic_id": None,
        "special_backfill_date": (
            pendulum.today("EST") - timedelta(days=1)
        ).to_date_string(),
        "special_confirm": False,
    },
    tags=["intent", "5X5"],
) as dag:

    ###### EMPTY OPERATORS FOR BRANCHING ######
    skip_insert_b2b_ids = EmptyOperator(task_id="skip_insert_b2b_ids")
    skip_insert_missing_b2b_rows = EmptyOperator(task_id="skip_insert_missing_b2b_rows")
    finalize = EmptyOperator(
        task_id="all_done", trigger_rule="none_failed_min_one_success"
    )
    skip_insert_missing_b2c_rows = EmptyOperator(task_id="skip_insert_missing_b2c_rows")
    special_backfill_start = EmptyOperator(task_id="special_backfill_start")
    normal_start = EmptyOperator(task_id="normal_start")

    ##### PATH FOR ADDING EXTRA TOPICS AND BACKFILLING ######
    branch_special = BranchPythonOperator(
        task_id="branch_on_special_params",
        python_callable=branch_on_special_params,
        provide_context=True,
    )

    special_insert = insert_special_limited_b2b_id(
        topic_id="{{ params.special_topic_id }}"
    )
    special_backfill = backfill_90_days_for_topic(
        topic_id="{{ params.special_topic_id }}",
        start_date="{{ params.special_backfill_date }}",
    )

    ###### TASKS FOR B2B TAXONMOMY COUNTS AND INSERTS ######
    branch_b2b = BranchPythonOperator(
        task_id="branch_on_b2b_counts",
        python_callable=branch_on_b2b_counts,
        provide_context=True,
    )

    b2b_tax_counts = check_taxonomy_counts.override(
        task_id="check_taxonomy_counts_b2b"
    )(query=GET_B2B_TAXONOMY_COUNTS)

    insert_missing_b2b_ids = insert_missing_b2b_ids()

    insert_missing_b2b_rows = SQLExecuteQueryOperator(
        task_id="insert_missing_b2b_rows",
        conn_id="trino_conn",
        sql=INSERT_MISSING_B2B_ROWS,
    )

    ###### TASKS FOR B2C TAXONMOMY COUNTS AND INSERTS ######
    branch_b2c = BranchPythonOperator(
        task_id="branch_on_b2c_counts",
        python_callable=branch_on_b2c_counts,
        provide_context=True,
    )

    b2c_tax_counts = check_taxonomy_counts.override(
        task_id="check_taxonomy_counts_b2c"
    )(query=GET_B2C_TAXONOMY_COUNTS)

    insert_missing_b2c_rows = SQLExecuteQueryOperator(
        task_id="insert_missing_b2c_rows",
        conn_id="trino_conn",
        sql=INSERT_MISSING_B2C_ROWS,
    )

    ###### TASK GROUP FOR INSERTING INTO CONSUMER TABLE IN OLYMPUS ######
    with TaskGroup("consumer_load") as tg_consumer:
        missing_consumer_dates = SQLExecuteQueryOperator(
            task_id="find_missing_consumer_dates",
            conn_id="trino_conn",
            sql=FIND_MISSING_CONSUMER_DATES.format(min_date="{{ params.min_date }}"),
            handler=lambda results: sorted(
                [str(row[0]) for row in results if row and row[0]]
            ),
            trigger_rule="none_failed_min_one_success",
        )

        load_consumer_task = load_consumer.expand(
            missing_date=missing_consumer_dates.output
        )

        exit_consumer = EmptyOperator(
            task_id="exit_consumer", trigger_rule="none_failed_min_one_success"
        )
        missing_consumer_dates >> load_consumer_task >> exit_consumer
        missing_consumer_dates >> exit_consumer

    ###### TASK GROUP FOR INSERTING INTO business TABLE IN OLYMPUS ######
    with TaskGroup("business_load") as tg_business:
        missing_business_dates = SQLExecuteQueryOperator(
            task_id="find_missing_business_dates",
            conn_id="trino_conn",
            sql=FIND_MISSING_BUSINESS_DATES.format(min_date="{{ params.min_date }}"),
            handler=lambda results: sorted(
                [str(row[0]) for row in results if row and row[0]]
            ),
            trigger_rule="none_failed_min_one_success",
        )

        insert_limited_business = load_limited_market_pulse_business.expand(
            missing_date=missing_business_dates.output
        )

        exit_business = EmptyOperator(
            task_id="exit_business", trigger_rule="none_failed_min_one_success"
        )
        missing_business_dates >> insert_limited_business >> exit_business
        missing_business_dates >> exit_business

    ###### TASK GROUP FOR INSERTING INTO COMBINED TABLE IN OLYMPUS ######

    with TaskGroup("combined_load") as tg_combined:
        missing_combined_dates = SQLExecuteQueryOperator(
            task_id="find_missing_combined_dates",
            conn_id="trino_conn",
            sql=FIND_MISSING_COMBINED_DATES.format(min_date="{{ params.min_date }}"),
            handler=lambda results: sorted(
                [str(row[0]) for row in results if row and row[0]]
            ),
            trigger_rule="none_failed_min_one_success",
        )

        insert_combined = load_market_pulse_combined.expand(
            missing_date=missing_combined_dates.output
        )

        exit_conmbined = EmptyOperator(
            task_id="exit_combined", trigger_rule="none_failed_min_one_success"
        )
        missing_combined_dates >> insert_combined >> exit_conmbined
        missing_combined_dates >> exit_conmbined

    ###### DROP OLD PARTITIONS TASK ######
    drop_old = drop_old_combined_partitions()

    ###### DELETE TAXONOMY PARQUET FILES TASK ######
    delete_taxonomy_files = delete_taxonomy_parquet_files()
    ########### BRANCHING LOGIC ###############
    delivery_sync_groups = build_delivery_sync_chain()
    consumer_sync = delivery_sync_groups["consumer"]
    business_sync = delivery_sync_groups["business"]

    branch_special >> special_backfill_start
    special_backfill_start >> special_insert >> special_backfill

    # Normal path
    branch_special >> normal_start
    (
        normal_start
        >> delete_taxonomy_files
        >> consumer_sync
        >> business_sync
        >> [b2b_tax_counts, b2c_tax_counts]
    )

    # Taxonomy branch logic (unchanged)
    b2b_tax_counts >> branch_b2b
    branch_b2b >> [insert_missing_b2b_ids, skip_insert_b2b_ids]
    insert_missing_b2b_ids >> insert_missing_b2b_rows
    skip_insert_b2b_ids >> skip_insert_missing_b2b_rows

    b2c_tax_counts >> branch_b2c
    branch_b2c >> [insert_missing_b2c_rows, skip_insert_missing_b2c_rows]

    [
        insert_missing_b2b_rows,
        skip_insert_missing_b2b_rows,
        insert_missing_b2c_rows,
        skip_insert_missing_b2c_rows,
    ] >> tg_consumer

    # Sequential loading
    tg_consumer >> tg_business >> tg_combined >> drop_old >> finalize
