from airflow.decorators import task
from airflow.providers.trino.hooks.trino import <PERSON><PERSON><PERSON><PERSON>
from airflow.exceptions import AirflowSkipException, AirflowException
import logging
from datetime import timed<PERSON><PERSON>
import pendulum
from old_dags.fivexfive.market_pulse.market_pulse_queries import (
    INSERT_OLYMPUS_CONSUMER,
    GET_MISSING_B2B_IDS,
    INSERT_MISSING_LIMITED_B2B_IDS,
    INSERT_LIMITED_MARKET_PULSE_BUSINESS,
    INSERT_MARKET_PULSE_COMBINED,
    DROP_OLD_COMBINED_PARTITIONS,
    FIND_TOPIC_ID,
    INSERT_LIMITED_B2B_ID,
)

logging.basicConfig()
logger = logging.getLogger(__name__)
"""
Market Pulse Insert Tasks

This module defines all Airflow @task functions for inserting, updating, and maintaining
Market Pulse data in Olympus tables.

Key Tasks:
- load_consumer: Loads consumer data for a given date if the partition does not already exist.
- load_limited_market_pulse_business: Loads limited business data for a given date if not already present.
- load_market_pulse_combined: Combines consumer and business data for a date into the combined table, skipping if the partition exists.
- check_taxonomy_counts: Compares row counts between Olympus and S3 for taxonomy tables.
- insert_missing_b2b_ids: Inserts missing B2B taxonomy IDs into the limited taxonomy table.
- drop_old_combined_partitions: Drops partitions from the combined table to retain only the most recent 90 days.
- insert_special_limited_b2b_id: Inserts a topic ID into the limited taxonomy table only if not already present.
- backfill_90_days_for_topic: Backfills 90 days of business data for a specific topic, skipping dates already present.

All tasks are idempotent and include exception handling for robust, retry-safe operation.
"""


@task(
    max_active_tis_per_dag=1,
)
def load_consumer(missing_date: str) -> None:
    """
    Load consumer data for a given date into Olympus if the partition does not already exist.

    Args:
        missing_date (str): The date to load (YYYY-MM-DD).
        table (str): Table name (not used in current implementation).

    Raises:
        AirflowSkipException: If the partition already exists.
        AirflowException: On failure to check or insert data.
    """
    print(missing_date)
    tr = TrinoHook(trino_conn_id="trino_conn")

    query = f"""
        SELECT DISTINCT partition.dt
        FROM olympus.bronze_5x5."market_pulse_consumer$partitions"
        WHERE partition.dt = DATE('{missing_date}')
    """
    try:
        logger.info(f"Checking if partition exists for date: {missing_date}")
        result = tr.get_records(sql=query)
        partition_date = result[0][0] if result and result[0] else None
    except Exception as e:
        logger.error(f"Failed to check partition for {missing_date}: {e}")
        raise AirflowException(f"Partition check failed for {missing_date}")

    if partition_date:
        logger.info(f"Partition already exists for {missing_date}, skipping load.")
        raise AirflowSkipException(
            f"Partition already exists for {missing_date}, skipping load."
        )
    else:
        try:
            logger.info(f"Loading consumer data for date: {missing_date}")
            tr.run(sql=INSERT_OLYMPUS_CONSUMER.format(missing_date=missing_date))
        except Exception as e:
            logger.error(f"Failed to insert consumer table for {missing_date}: {e}")
            raise AirflowException(f"Load step failed for {missing_date}")


@task(max_active_tis_per_dag=1)
def load_limited_market_pulse_business(missing_date: str) -> None:
    """
    Load limited business data for a given date into Olympus if the partition does not already exist.

    Args:
        missing_date (str): The date to load (YYYY-MM-DD).

    Raises:
        AirflowSkipException: If the partition already exists.
        AirflowException: On failure to check or insert data.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    # Check if partition exists for this date
    check_query = f"""
        SELECT DISTINCT partition.dt
        FROM olympus.bronze_5x5."market_pulse_business$partitions"
        WHERE partition.dt = DATE('{missing_date}')
    """
    try:
        logger.info(f"Checking if partition exists for date: {missing_date}")
        result = tr.get_records(sql=check_query)
        partition_date = result[0][0] if result and result[0] else None
    except Exception as e:
        logger.error(f"Failed to check partition for {missing_date}: {e}")
        raise AirflowException(f"Partition check failed for {missing_date}")

    if partition_date:
        logger.info(f"Partition already exists for {missing_date}, skipping insert.")
        raise AirflowSkipException(
            f"Partition already exists for {missing_date}, skipping insert."
        )
    else:
        insert_query = INSERT_LIMITED_MARKET_PULSE_BUSINESS.format(
            missing_date=missing_date
        )
        try:
            logger.info(f"Inserting limited business rows for date: {missing_date}")
            tr.run(insert_query)
        except Exception as e:
            logger.error(
                f"Failed to insert limited business rows for {missing_date}: {e}"
            )
            raise AirflowException(f"Insert failed for {missing_date}")


@task(max_active_tis_per_dag=1)
def load_market_pulse_combined(missing_date: str) -> None:
    """
    Combine consumer and business data for a given date into the combined table,
    skipping if the partition already exists.

    Args:
        missing_date (str): The date to load (YYYY-MM-DD).

    Raises:
        AirflowSkipException: If the partition already exists.
        AirflowException: On failure to check or insert data.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    # Check if partition exists for this date
    check_query = f"""
        SELECT DISTINCT partition.dt
        FROM olympus.bronze_5x5."market_pulse_combined$partitions"
        WHERE partition.dt = DATE('{missing_date}')
    """
    try:
        logger.info(f"Checking if partition exists for date: {missing_date}")
        result = tr.get_records(sql=check_query)
        partition_date = result[0][0] if result and result[0] else None
    except Exception as e:
        logger.error(f"Failed to check partition for {missing_date}: {e}")
        raise AirflowException(f"Partition check failed for {missing_date}")

    if partition_date:
        logger.info(f"Partition already exists for {missing_date}, skipping insert.")
        raise AirflowSkipException(
            f"Partition already exists for {missing_date}, skipping insert."
        )
    else:
        insert_query = INSERT_MARKET_PULSE_COMBINED.format(missing_date=missing_date)
        try:
            logger.info(f"Inserting combined rows for date: {missing_date}")
            tr.run(insert_query)
        except Exception as e:
            logger.error(f"Failed to insert combined rows for {missing_date}: {e}")
            raise AirflowException(f"Insert failed for {missing_date}")


@task
def check_taxonomy_counts(query):
    """
    Compare row counts between Olympus and S3 for taxonomy tables.

    Args:
        query (str): SQL query to execute.

    Returns:
        bool: True if S3 count is greater than Olympus count, else False.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    result = tr.get_first(query)
    olympus_count, s3_count = result
    print(f"Olympus count: {olympus_count}, S3 count: {s3_count}")
    if s3_count > olympus_count:
        return True
    return False


@task
def insert_missing_b2b_ids():
    """
    Insert missing B2B taxonomy IDs into the limited taxonomy table.

    Raises:
        AirflowSkipException: If no missing IDs are found.
        AirflowException: On failure to insert IDs.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    missing_ids = tr.get_records(GET_MISSING_B2B_IDS)
    print(f"Missing IDs: {missing_ids}")
    if not missing_ids:
        raise AirflowSkipException("No missing IDs to insert.")
    # Prepare values for bulk insert
    values = ",".join(f"({repr(row[0])})" for row in missing_ids)
    insert_query = INSERT_MISSING_LIMITED_B2B_IDS.format(values=values)
    try:
        tr.run(insert_query)
    except Exception as e:
        raise AirflowException(f"Failed to insert missing IDs: {e}")


@task
def drop_old_combined_partitions():
    """
    Drop partitions from the combined table to retain only the most recent 90 days.

    Raises:
        AirflowException: On failure to drop partitions.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    old_partitions = tr.get_records(DROP_OLD_COMBINED_PARTITIONS)
    if not old_partitions:
        logger.info("No old partitions to drop.")
        return
    for (dt,) in old_partitions:
        drop_query = f"DELETE FROM olympus.bronze_5x5.market_pulse_combined WHERE dt = DATE '{dt}'"
        logger.info(f"Dropping old partition (deleting data for dt={dt})")
        tr.run(drop_query)


@task
def insert_special_limited_b2b_id(topic_id: str):
    """
    Insert a topic ID into the limited taxonomy table only if not already present.

    Args:
        topic_id (str): The topic ID to insert.

    Raises:
        AirflowException: If the topic ID is not found or insert fails.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    try:
        exists = tr.get_first(FIND_TOPIC_ID.format(topic_id=topic_id))
        if not exists:
            raise AirflowException(f"Topic ID {topic_id} not found in b2b_taxonomy")
        already_limited = tr.get_first(
            f"SELECT id FROM olympus.bronze_5x5.limited_b2b_taxonomy WHERE id = '{topic_id}'"
        )
        if already_limited:
            logger.info(
                f"Topic ID {topic_id} already present in limited_b2b_taxonomy, skipping insert."
            )
            return
        tr.run(INSERT_LIMITED_B2B_ID.format(topic_id=topic_id))
    except Exception as e:
        logger.error(f"Failed to insert topic_id {topic_id}: {e}")
        raise AirflowException(f"Insert failed for topic_id {topic_id}")


@task
def backfill_90_days_for_topic(topic_id: str, start_date: str):
    """
    Backfill 90 days of business data for a specific topic into the combined table,
    skipping dates already present.

    Args:
        topic_id (str): The topic ID to backfill.
        start_date (str): The start date for backfill (YYYY-MM-DD).

    Raises:
        AirflowException: On failure to insert data for any date.
    """
    tr = TrinoHook(trino_conn_id="trino_conn")
    start = pendulum.parse(start_date)
    for i in range(90):
        dt = (start - timedelta(days=i)).to_date_string()
        try:
            exists = tr.get_first(
                f"SELECT 1 FROM olympus.bronze_5x5.market_pulse_combined WHERE topic_id = '{topic_id}' AND dt = DATE('{dt}') LIMIT 1"
            )
            if exists:
                logger.info(
                    f"Data for topic_id={topic_id} and dt={dt} already present, skipping."
                )
                continue
            insert_query = f"""
            INSERT INTO olympus.bronze_5x5.market_pulse_combined (sha256_lc_hem, up_id, topic_id, dt)
            SELECT _col_0, _col_1, _col_2, dt
            FROM s3.external_5x5.market_pulse_business
            WHERE _col_2 = '{topic_id}' AND dt = DATE('{dt}')
            """
            tr.run(insert_query)
        except Exception as e:
            logger.error(f"Failed to backfill topic_id={topic_id}, dt={dt}: {e}")
            raise AirflowException(f"Backfill failed for topic_id={topic_id}, dt={dt}")
