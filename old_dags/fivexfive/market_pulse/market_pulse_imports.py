from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from rclone_python import rclone
import logging
from airflow.exceptions import AirflowException
from typing import Dict
from airflow.models import Variable
import botocore.credentials
from datetime import datetime
import re
import os


logger = logging.getLogger(__name__)
env = Variable.get("environment")


rclone_args = [
    "--transfers=16",
    "--checkers=32",
    "--s3-chunk-size=32M",
    "--s3-upload-concurrency=16",
    "--buffer-size=64M",
    "--fast-list",
    "--ignore-existing",
    "--include",
    "*.parquet",
    "--s3-requester-pays",
]

if env == "dev":
    rclone_args.append("--dry-run")


def s3conn_to_creds(s3_creds: str) -> botocore.credentials.Credentials:
    """
    Fetch S3 credentials for a connection.
    """
    s3_hook = S3Hook(s3_creds)
    session = s3_hook.get_session()
    creds = session.get_credentials()
    return creds


def rclone_conn_str(creds: botocore.credentials.Credentials, region_name: str) -> str:
    """
    Generate an rclone connection string for S3.
    """
    rclone_conn = (
        f"s3,provider=AWS,env_auth=false,region={region_name},"
        f"access_key_id={creds.access_key},secret_access_key={creds.secret_key}"
    )
    if creds.token:
        rclone_conn += f",session_token={creds.token}"
    return f":{rclone_conn}:"


def list_folders(
    aws_conn: str, bucket: str, prefix: str, min_date: datetime = None
) -> Dict[str, datetime]:
    """
    Recursively list all date folders under the given prefix (e.g., delivery/YYYY/MM/DD/)
    and return a dict mapping folder path -> date.
    """
    from collections import deque

    logger.info(
        f"Recursively listing folders in bucket: {bucket} with prefix: {prefix}"
    )
    s3_hook = S3Hook(aws_conn_id=aws_conn)
    s3 = s3_hook.get_conn()

    queue = deque([prefix])
    results = {}

    while queue:
        current_prefix = queue.popleft()
        response = s3.list_objects_v2(
            Bucket=bucket,
            Prefix=current_prefix,
            Delimiter="/",
            RequestPayer="requester",
        )
        common_prefixes = response.get("CommonPrefixes", [])
        for entry in common_prefixes:
            folder_path = entry["Prefix"]
            # Check if this folder matches YYYY/MM/DD
            match = re.match(r"delivery/(\d{4})/(\d{2})/(\d{2})/$", folder_path)
            if match:
                year, month, day = match.groups()
                folder_date = datetime.strptime(f"{year}{month}{day}", "%Y%m%d").date()
                if min_date is None or folder_date >= min_date:
                    results[folder_path] = folder_date
            else:

                queue.append(folder_path)

    taxonomy_prefix = "delivery/current-taxonomy/"
    results[taxonomy_prefix] = None

    print(f"Found {len(results)} date folders in bucket {bucket} with prefix {prefix}")
    return results


def taxonomy_file_needs_copy(
    s3_source,
    s3_dest,
    source_bucket,
    dest_bucket,
    taxonomy_prefix,
    dest_taxonomy_prefix,
):
    """
    Returns True if the taxonomy Parquet file in the source is newer (by date) than in the destination, or does not exist in dest.
    Assumes only one Parquet file in the taxonomy folder.
    """
    # List source files (only parquet)
    src_objs = s3_source.list_objects_v2(
        Bucket=source_bucket, Prefix=taxonomy_prefix, RequestPayer="requester"
    ).get("Contents", [])
    src_files = [obj for obj in src_objs if obj["Key"].endswith(".parquet")]

    if not src_files:
        return False  # No parquet file to copy

    # Assume only one parquet file in taxonomy folder
    src_file = src_files[0]
    src_key = src_file["Key"]
    src_last_modified = src_file["LastModified"].date()  # <-- Only use date
    print(f"Source Parquet file: {src_key}, Last Modified: {src_last_modified}")

    # Check if parquet file exists in destination
    print(taxonomy_prefix)
    dest_objs = s3_dest.list_objects_v2(
        Bucket=dest_bucket,
        Prefix=dest_taxonomy_prefix,  # or "" if you want to search the whole bucket/prefix
    ).get("Contents", [])
    print("Destination objects:", [obj["Key"] for obj in dest_objs])

    # Match by filename only
    dest_files = [
        obj
        for obj in dest_objs
        if os.path.basename(obj["Key"]) == os.path.basename(src_key)
    ]

    if not dest_files:
        print("No destination parquet file found, will copy.")
        return True  # Parquet file does not exist in dest, needs copy

    dest_last_modified = dest_files[0]["LastModified"].date()
    print(
        f"Destination Parquet file: {dest_files[0]['Key']}, Last Modified: {dest_last_modified}"
    )

    return src_last_modified > dest_last_modified


def convert_to_dest_path(source_folder: str, dest_prefix: str) -> str:
    """
    Converts 'delivery/YYYY/MM/DD/' to '5x5/delivery/dt=YYYY-MM-DD/'
    """
    match = re.match(r"delivery/(\d{4})/(\d{2})/(\d{2})/$", source_folder)
    if not match:
        raise ValueError(f"Invalid source folder format: {source_folder}")
    year, month, day = match.groups()
    return f"{dest_prefix}dt={year}-{month}-{day}/"


def batch_copy_folder(
    source_path: str, dest_path: str, folder: str, dest_folder: str
) -> bool:
    try:

        logger.info(f"Starting copy of folder: {folder} to {dest_folder}")
        rclone.copy(
            f"{source_path}/{folder}",
            f"{dest_path}/{dest_folder}",
            args=rclone_args,
            show_progress=True,
        )
        logger.info(f"✅ Successfully copied folder: {folder}")
        return True
    except Exception as e:
        logger.error(f"Error copying folder {folder}: {str(e)}", exc_info=True)
        return False


def copy_new_files(
    folder: str,
    source_s3_conn: str,
    dest_s3_conn: str,
    source_region: str,
    dest_region: str,
    source_bucket: str,
    dest_bucket: str,
    dest_folder: str,
) -> None:
    source_creds = s3conn_to_creds(source_s3_conn)
    dest_creds = s3conn_to_creds(dest_s3_conn)

    src_conn_str = rclone_conn_str(source_creds, source_region)
    dest_conn_str = rclone_conn_str(dest_creds, dest_region)

    source_path = f"{src_conn_str}{source_bucket}"
    dest_path = f"{dest_conn_str}{dest_bucket}"

    logger.info(f"➡️ Copying folder: {folder}")
    if not batch_copy_folder(source_path, dest_path, folder, dest_folder):
        raise AirflowException(f"❌ Failed to copy folder: {folder}")
    logger.info(f"✅ Folder copied: {folder}")
