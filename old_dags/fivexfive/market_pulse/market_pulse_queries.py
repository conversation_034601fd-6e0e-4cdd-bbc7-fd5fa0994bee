"""
Market Pulse SQL Queries

This module contains all SQL query templates used by the Market Pulse DAG and tasks.
Each query is documented for its purpose, usage, and idempotency/duplication considerations.
"""

# --- Consumer Table Inserts and Checks ---

INSERT_OLYMPUS_CONSUMER = """
/*
Insert consumer data for a given date into olympus.bronze_5x5.market_pulse_consumer.
Idempotent: Only run if the partition for the date does not already exist.
*/
INSERT INTO olympus.bronze_5x5.market_pulse_consumer (
    sha256_lc_hem,
    up_id,
    topic_id,
    dt
)
SELECT
    _col_0 AS sha256_lc_hem,
    _col_1 AS up_id,
    _col_2 AS topic_id,
    dt
FROM s3.external_5x5.market_pulse_consumer
WHERE dt = DATE('{missing_date}')
ORDER BY _col_2
"""

FIND_MISSING_CONSUMER_DATES = """
/*
Find dates present in S3 consumer partitions but missing in Olympus consumer partitions.
Used to identify which dates need to be loaded.
*/
SELECT dt
FROM (
    SELECT DISTINCT dt
    FROM s3.external_5x5."market_pulse_consumer$partitions"
    WHERE dt >= DATE('{min_date}')
) external_partitions
WHERE dt NOT IN (
    SELECT DISTINCT partition.dt
    FROM olympus.bronze_5x5."market_pulse_consumer$partitions"
)
"""

# --- Business Table Inserts and Checks ---

FIND_MISSING_BUSINESS_DATES = """
/*
Find dates present in S3 business partitions but missing in Olympus business partitions.
Used to identify which dates need to be loaded.
*/
SELECT dt
FROM (
    SELECT DISTINCT dt
    FROM s3.external_5x5."market_pulse_business$partitions"
    WHERE dt >= DATE('{min_date}')
) external_partitions
WHERE dt NOT IN (
    SELECT DISTINCT partition.dt
    FROM olympus.bronze_5x5."market_pulse_business$partitions"
)
"""

# --- Combined Table Inserts and Checks ---

FIND_MISSING_COMBINED_DATES = """
/*
Find dates present in either Olympus consumer or business partitions but missing in the combined table.
Ensures combined table is up-to-date with source tables.
*/
SELECT dt
FROM (
    SELECT DISTINCT dt
    FROM (
        SELECT DISTINCT partition.dt FROM olympus.bronze_5x5."market_pulse_consumer$partitions"
        UNION
        SELECT DISTINCT partition.dt FROM olympus.bronze_5x5."market_pulse_business$partitions"
    ) all_dates
    WHERE dt >= DATE('{min_date}')
) source_dates
WHERE dt NOT IN (
    SELECT DISTINCT partition.dt
    FROM olympus.bronze_5x5."market_pulse_combined$partitions"
)
"""

# --- Taxonomy Table Counts and Inserts ---

GET_B2B_TAXONOMY_COUNTS = """
/*
Get row counts for B2B taxonomy in Olympus and S3.
Used for comparison and conditional inserts.
*/
SELECT
    (SELECT COUNT(id) FROM olympus.bronze_5x5.b2b_taxonomy) AS olympus_count,
    (SELECT COUNT(topic_id) FROM s3.external_5x5.b2b_taxonomy) AS s3_count
"""

GET_B2C_TAXONOMY_COUNTS = """
/*
Get row counts for B2C taxonomy in Olympus and S3.
Used for comparison and conditional inserts.
*/
SELECT
    (SELECT COUNT(id) FROM olympus.bronze_5x5.b2c_taxonomy) AS olympus_count,
    (SELECT COUNT(topic_id) FROM s3.external_5x5.b2c_taxonomy) AS s3_count
"""

GET_MISSING_B2B_IDS = """
/*
Find B2B topic IDs present in S3 but missing in Olympus.
Used to identify which IDs need to be inserted.
*/
SELECT topic_id
FROM s3.external_5x5.b2b_taxonomy
WHERE topic_id NOT IN (
    SELECT id FROM olympus.bronze_5x5.b2b_taxonomy
)
"""

INSERT_MISSING_B2B_ROWS = """
/*
Insert missing B2B taxonomy rows from S3 into Olympus.
Idempotent: Only inserts rows not already present.
*/
INSERT INTO olympus.bronze_5x5.b2b_taxonomy (id, topic, description)
SELECT topic_id, topic, description  -- match destination schema
FROM s3.external_5x5.b2b_taxonomy
WHERE topic_id NOT IN (
    SELECT id FROM olympus.bronze_5x5.b2b_taxonomy
)
"""

INSERT_MISSING_B2C_ROWS = """
/*
Insert missing B2C taxonomy rows from S3 into Olympus.
Idempotent: Only inserts rows not already present.
*/
INSERT INTO olympus.bronze_5x5.b2c_taxonomy (id, category, sub_category, topic, description)
SELECT topic_id, topic_category, topic_subcategory, topic, description 
FROM s3.external_5x5.b2c_taxonomy
WHERE topic_id NOT IN (
    SELECT id FROM olympus.bronze_5x5.b2c_taxonomy
)
"""

INSERT_MISSING_LIMITED_B2B_IDS = """
/*
Bulk insert missing B2B IDs into the limited taxonomy table.
Idempotent: Only insert IDs not already present.
*/
INSERT INTO olympus.bronze_5x5.limited_b2b_taxonomy (id)
VALUES {values}
"""

# --- Business Table Limited Insert ---

INSERT_LIMITED_MARKET_PULSE_BUSINESS = """
/*
Insert business data for limited B2B topics and a given date into Olympus.
Idempotent: Only run if the partition for the date does not already exist.
*/
INSERT INTO olympus.bronze_5x5.market_pulse_business (
    sha256_lc_hem,
    up_id,
    topic_id,
    dt
)
SELECT
    _col_0 AS sha256_lc_hem,
    _col_1 AS up_id,
    _col_2 AS topic_id,
    dt
FROM s3.external_5x5.market_pulse_business
WHERE
    _col_2 IN (
        SELECT id FROM olympus.bronze_5x5.limited_b2b_taxonomy
    )
    AND dt = DATE('{missing_date}')
"""

# --- Combined Table Insert ---

INSERT_MARKET_PULSE_COMBINED = """
/*
Insert consumer and business data for a given date into the combined table.
Idempotent: Only run if the partition for the date does not already exist.
*/
INSERT INTO olympus.bronze_5x5.market_pulse_combined (
    sha256_lc_hem,
    up_id,
    topic_id,
    dt
)
SELECT sha256_lc_hem, up_id, topic_id, dt
FROM olympus.bronze_5x5.market_pulse_consumer
WHERE dt = DATE('{missing_date}')
UNION ALL
SELECT sha256_lc_hem, up_id, topic_id, dt
FROM olympus.bronze_5x5.market_pulse_business
WHERE dt = DATE('{missing_date}')
"""

# --- Partition Management ---

DROP_OLD_COMBINED_PARTITIONS = """
/*
Find distinct dates in the combined table older than the 90 most recent days.
Used to keep only the latest 90 days of data, regardless of topic_id.
*/
WITH ranked AS (
    SELECT dt,
           ROW_NUMBER() OVER (ORDER BY dt DESC) AS rn
    FROM (
        SELECT DISTINCT partition.dt
        FROM olympus.bronze_5x5."market_pulse_combined$partitions" partition
    )
)
SELECT dt
FROM ranked
WHERE rn > 90
ORDER BY dt
"""

# --- Special Backfill Support ---

FIND_TOPIC_ID = """
/*
Check if a topic ID exists in the B2B taxonomy table.
Used for validation before special backfill.
*/
SELECT id FROM olympus.bronze_5x5.b2b_taxonomy WHERE id = '{topic_id}'
"""

INSERT_LIMITED_B2B_ID = """
/*
Insert a topic ID into the limited B2B taxonomy table.
Idempotent: Only insert if not already present.
*/
INSERT INTO olympus.bronze_5x5.limited_b2b_taxonomy (id)
VALUES ('{topic_id}')
"""
