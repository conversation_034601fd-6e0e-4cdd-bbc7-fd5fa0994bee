apiVersion: "sparkoperator.k8s.io/v1beta2"
kind: SparkApplication
metadata:
  name: spark-corelogic-analytics
  namespace: spark
spec:
  type: Python
  pythonVersion: "3"
  mode: cluster
  image: harbor.k8s.eltoro.com/apache/spark:3.2.0-py
  imagePullPolicy: IfNotPresent
  mainApplicationFile: s3a://diagonalley/spark/python/spark-corelogic-analytics.py
  sparkVersion: "3.1.1"
  arguments:
    - dev
    - '20211119'
  volumes:
    - name: "spark-local-dir-1"
      hostPath:
        path: "/mnt/spark-pv-1"
    - name: "spark-local-dir-2"
      hostPath:
        path: "/mnt/spark-pv-2"
  dynamicAllocation:
    enabled: true
    minExecutors: 1
    maxExecutors: 2

  sparkConf:
    # Keep events for the history server
    # "spark.kubernetes.driver.reusePersistentVolumeClaim": "true"
    # "spark.kubernetes.driver.ownPersistentVolumeClaim": "true"
    # "spark.kubernetes.executor.volumes.persistentVolumeClaim.data.options.claimName": "OnDemand"
    # "spark.kubernetes.executor.volumes.persistentVolumeClaim.data.options.storageClass": "standard"
    # "spark.kubernetes.executor.volumes.persistentVolumeClaim.data.options.sizeLimit": "500Gi"
    # "spark.kubernetes.executor.volumes.persistentVolumeClaim.data.mount.path": "/var/data"
    # "spark.kubernetes.executor.volumes.persistentVolumeClaim.data.mount.readonly": "false"
    "spark.eventLog.enabled": "true"
    "spark.eventLog.dir": "s3a://diagonalley/spark-logs/"
    "spark.history.fs.inProgressOptimization.enabled": "true"
    "spark.history.fs.update.interval": "5s"
    # ORC Related Settings
    "spark.sql.orc.filterPushdown": "true"
    "spark.sql.orc.splits.include.file.footer": "true"
    "spark.sql.orc.cache.stripe.details.size": "536870912"
    "spark.sql.hive.metastorePartitionPruning": "true"
    # Autoscale the Executors
    "spark.dynamicAllocation.enabled": "true"
    "spark.dynamicAllocation.shuffleTracking.enabled": "true"
    "spark.dynamicAllocation.executorAllocationRatio": "0.33"
    "spark.dynamicAllocation.sustainedSchedulerBacklogTimeout": "30"
    "spark.sql.adaptive.enabled": "true"
    # Partition Settings
    "spark.default.parallelism": "1024"
    "spark.sql.shuffle.partitions": "128"
    # S3 Magic Committer
    "spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a": "org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory"
    "spark.hadoop.fs.s3a.committer.name": "magic"
    "spark.hadoop.fs.s3a.committer.magic.enabled": "true"
    "spark.hadoop.fs.s3a.buffer.dir": "/tmp/spark-pv-2"
    "spark.hadoop.fs.s3a.connection.ssl.enabled": "false"
    "spark.hadoop.fs.s3a.fast.upload": "true"
    # Use Kryo for serialization, may be used by default
    "spark.serializer": "org.apache.spark.serializer.KryoSerializer"
    "spark.kryo.unsafe": "true"
    "spark.kryoserializer.buffer": "256"
    "spark.kryoserializer.buffer.max": "2000"
    # General Spark settings
    "spark.driver.maxResultSize": "500G"
    "spark.memory.offHeap.enabled": "true"
    "spark.memory.offHeap.size": "10737418240"
    "spark.speculation": "true"
    "spark.rdd.compress": "true"
    "spark.shuffle.compress": "true"
    "spark.shuffle.file.buffer": "2M"
    "spark.shuffle.spill.compress": "true"
    "spark.scheduler.barrier.maxConcurrentTasksCheck.maxFailures": "5"
    "spark.executor.cores": "17"
    "spark.sql.warehouse.dir": "s3a://diagonalley/spark/warehouse"
    "spark.sql.execution.arrow.pyspark.enabled": "true"
  hadoopConf:
    "fs.s3a.impl": "org.apache.hadoop.fs.s3a.S3AFileSystem"
    "fs.s3a.connection.maximum": "5000"
    "fs.s3a.assumed.role.sts.endpoint.region": "us-east-1"
    "fs.s3a.committer.staging.tmp.path": "/tmp/spark-pv-1/staging"

  driver:
    annotations:
      iam.amazonaws.com/role: airflow-worker-s3-role
    cores: 4
    memory: 32G   # Consistent OOM at 8G
    labels:
      version: 3.1.1
    serviceAccount: spark
    javaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=404 -XX:+UnlockExperimentalVMOptions -Dio.netty.tryReflectionSetAccessible=true --add-opens java.base/jdk.internal.misc=ALL-UNNAMED"
    volumeMounts:
      - name: spark-local-dir-1
        mountPath: /tmp/spark-pv-1
      - name: spark-local-dir-2
        mountPath: /tmp/spark-pv-2
    tolerations:
      - effect: NoSchedule
        key: dedicated
        operator: Equal
        value: spark
      - effect: PreferNoSchedule
        operator: Exists
    nodeSelector:
      instancegroup: spark

  executor:
    annotations:
      iam.amazonaws.com/role: airflow-worker-s3-role
    cores: 17
    memory: 128G # At 52 & 64G we consistently hit OOM
    memoryOverhead: 2G
    labels:
      version: 3.1.1
    javaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=404 -XX:+UnlockExperimentalVMOptions -Dio.netty.tryReflectionSetAccessible=true --add-opens java.base/jdk.internal.misc=ALL-UNNAMED"
    volumeMounts:
      - name: spark-local-dir-1
        mountPath: /tmp/spark-pv-1
      - name: spark-local-dir-2
        mountPath: /tmp/spark-pv-2
    tolerations:
      - effect: NoSchedule
        key: dedicated
        operator: Equal
        value: spark
      - effect: PreferNoSchedule
        operator: Exists
    nodeSelector:
      instancegroup: spark