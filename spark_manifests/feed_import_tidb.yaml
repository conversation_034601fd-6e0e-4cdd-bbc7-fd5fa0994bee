apiVersion: sparkoperator.k8s.io/v1beta2
kind: SparkApplication
metadata:
  name: feed-import-tidb-{{ ds }}
  namespace: spark
spec:
  image: {{ image }}
  imagePullPolicy: Always
  mainApplicationFile: s3a://diagonalley/spark/jars/{{ jar }}
  mainClass: com.eltoro.spark.TiDBTableWriter
  mode: cluster
  arguments:
    - "{{ batch_size }}"
    - "{{ data_interval_start | ds }}"
    - "{{ data_interval_end | ds }}"
  deps:
    files:
      - s3a://eltoro-data-sources/maxmind/20220506/GeoIP2-Connection-Type.mmdb
    packages:
      - org.apache.spark:spark-hadoop-cloud_2.12:3.2.1
      - mysql:mysql-connector-java:8.0.28
      - org.apache.iceberg:iceberg-spark-runtime-3.2_2.12:0.13.1
      - org.apache.kyuubi:kyuubi-extension-spark-3-2_2.12:1.5.1-incubating
  driver:
    annotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: 'false'
      iam.amazonaws.com/role: airflow-worker-s3-role
    cores: 8
    env:
      - name: AWS_REGION
        value: us-east-1
      - name: KUBERNETES_CONNECTION_TIMEOUT
        value: '0'
      - name: KUBERNETES_REQUEST_TIMEOUT
        value: '0'
      - name: KUBERNETES_UPLOAD_CONNECTION_TIMEOUT
        value: '0'
      - name: KUBERNETES_UPLOAD_REQUEST_TIMEOUT
        value: '0'
      - name: TIDB_HOST
        value: tidb-prod-tidb.tidb.svc.cluster.local
    javaOptions: |
      -XX:+UnlockExperimentalVMOptions
      -XX:+UseG1GC
      -XX:+ParallelRefProcEnabled
      -XX:ParallelGCThreads=4
      -XX:ConcGCThreads=4
      -Dfile.encoding=UTF-8
      -Dio.netty.tryReflectionSetAccessible=true
      -Divy.cache.dir=/tmp
      -Divy.home=/tmp
    memory: 10G
    memoryOverhead: 1000M
    serviceAccount: spark
    terminationGracePeriodSeconds: 300
  dynamicAllocation:
    enabled: true
    initialExecutors: 50
    maxExecutors: 600
  executor:
    annotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: 'false'
      iam.amazonaws.com/role: airflow-worker-s3-role
    coreLimit: 8000m
    cores: 8
    env:
      - name: AWS_REGION
        value: us-east-1
      - name: TIDB_HOST
        value: tidb-prod-tidb.tidb.svc.cluster.local
    initContainers:
      - command:
          - sh
          - '-c'
          - >-
            sysctl -w vm.swappiness=0; sysctl -w vm.max_map_count=9999999;
            sysctl -w fs.file-max=9999999; sysctl -w fs.nr_open=9999999; sysctl
            -w fs.inotify.max_user_watches=1048576; sysctl -w
            net.core.somaxconn=65535;
        image: ************.dkr.ecr.us-east-1.amazonaws.com/busybox:latest
        imagePullPolicy: IfNotPresent
        name: init
        securityContext:
          privileged: true
        volumeMounts:
          - mountPath: /tmp/spark-pv-1
            name: spark-local-dir-3
          - mountPath: /tmp/spark-pv-2
            name: spark-local-dir-2
    javaOptions: |
      -XX:+UnlockDiagnosticVMOptions
      -XX:+UnlockExperimentalVMOptions
      -XX:+ParallelRefProcEnabled
      -XX:ParallelGCThreads=8
      -XX:+AlwaysPreTouch
      -XX:ConcGCThreads=8
      -XX:+PrintFlagsFinal
      -XX:+UseG1GC
      -Dfile.encoding=UTF-8
      -Dio.netty.tryReflectionSetAccessible=true
      -Divy.cache.dir=/tmp
      -Divy.home=/tmp
    memory: 42G
    memoryOverhead: 10G
    nodeSelector:
      instancegroup: spark
    securityContext:
      privileged: true
    terminationGracePeriodSeconds: 300
    tolerations:
      - effect: NoSchedule
        key: dedicated
        operator: Equal
        value: spark
    volumeMounts:
      - mountPath: /tmp/spark-pv-1
        name: spark-local-dir-3
      - mountPath: /tmp/spark-pv-2
        name: spark-local-dir-2
  hadoopConf:
    fs.defaultFS: s3a://et-data-staging/warehouse
    fs.s3.maxRetries: '20'
    fs.s3a.assumed.role.session.duration: 9999m
    fs.s3a.assumed.role.sts.endpoint.region: us-east-1
    fs.s3a.block.size: 128m
    fs.s3a.bucket.all.committer.magic.enabled: 'true'
    fs.s3a.buffer.dir: /tmp/spark-pv-1,/tmp/spark-pv-2
    fs.s3a.committer.name: magic
    fs.s3a.connection.establish.timeout: '0'
    fs.s3a.connection.maximum: '10000'
    fs.s3a.connection.ssl.enabled: 'false'
    fs.s3a.connection.timeout: '0'
    fs.s3a.directory.marker.retention: keep
    fs.s3a.downgrade.syncable.exceptions: 'true'
    fs.s3a.endpoint: s3.us-east-1.amazonaws.com
    fs.s3a.fast.upload: 'true'
    fs.s3a.fast.upload.buffer: disk
    fs.s3a.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    fs.s3a.max.threads: '32'
    fs.s3a.maxRetries: '20'
    prometheus.endpoint.enabled: 'true'
  sparkConf:
    spark.app.status.metrics.enabled: 'true'
    spark.broadcast.checksum: 'false'
    spark.broadcast.compress: 'false'
    spark.driver.maxResultSize: 24gb
    spark.dynamicAllocation.executorIdleTimeout: 300s
    spark.dynamicAllocation.shuffleTracking.enabled: 'true'
    spark.eventLog.dir: s3a://diagonalley/spark-logs
    spark.eventLog.enabled: 'true'
    spark.eventLog.logStageExecutorMetrics: 'true'
    spark.executor.extraClassPath: /tmp/mysql-java-connector-8.0.28.jar:/tmp/*:/opt/spark/jars/*
    spark.executor.heartbeatInterval: 30s
    spark.executor.processTreeMetrics.enabled: 'true'
    spark.files.io.clientThreads: '16'
    spark.files.io.serverThreads: '16'
    spark.files.io.threads: '16'
    spark.hadoop.hive.metastore.uris: thrift://hive.starburst.svc.cluster.local:9083
    spark.hadoop.hive.metastore.warehouse.dir: s3a://et-data-staging/warehouse
    spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version: '2'
    spark.history.fs.inProgressOptimization.enabled: 'true'
    spark.history.fs.update.interval: 20s
    spark.kryo.unsafe: 'true'
    spark.kryoserializer.buffer: 1mb
    spark.kryoserializer.buffer.max: 128mb
    spark.kubernetes.allocation.batch.size: '50'
    spark.kubernetes.executor.lostCheck.maxAttempts: '10'
    spark.locality.wait: '0'
    spark.locality.wait.node: '0'
    spark.locality.wait.process: '0'
    spark.locality.wait.rack: '0'
    spark.logConf: 'true'
    spark.metrics.appStatusSource.enabled: 'true'
    spark.network.timeout: 180s
    spark.rdd.compress: 'false'
    spark.reducer.maxSizeInFlight: 1024p
    spark.rpc.io.clientThreads: '16'
    spark.rpc.io.mode: EPOLL
    spark.rpc.io.serverThreads: '16'
    spark.rpc.io.threads: '16'
    spark.rpc.lookupTimeout: 140s
    spark.scheduler.barrier.maxConcurrentTasksCheck.maxFailures: '10000'
    spark.serializer: org.apache.spark.serializer.KryoSerializer
    spark.shuffle.compress: 'false'
    spark.shuffle.file.buffer: 1m
    spark.shuffle.io.clientThreads: '16'
    spark.shuffle.io.maxRetries: '15'
    spark.shuffle.io.serverThreads: '16'
    spark.shuffle.io.threads: '16'
    spark.shuffle.manager: sort
    spark.shuffle.registration.maxAttempts: '10'
    spark.shuffle.sort.bypassMergeThreshold: '200'
    spark.shuffle.sort.initialBufferSize: '4194304'
    spark.shuffle.spill.compress: 'false'
    spark.sql.catalog.datalake: org.apache.iceberg.spark.SparkCatalog
    spark.sql.catalog.datalake.s3.staging-dir: /tmp/spark-pv-1,/tmp/spark-pv-2
    spark.sql.catalog.datalake.type: hive
    spark.sql.catalog.datalake.uri: thrift://hive.starburst.svc.cluster.local:9083
    spark.sql.catalog.datalake.warehouse: s3a://et-datalake-segmentor-feed
    spark.sql.catalogImplementation: hive
    spark.sql.codegen.wholeStage: 'true'
    spark.sql.crossJoin.enabled: 'true'
    spark.sql.extensions: org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
    spark.sql.files.maxPartitionBytes: '210255769'
    spark.sql.files.openCostInBytes: '0'
    spark.sql.hive.filesourcePartitionFileCacheSize: '1048576000'
    spark.sql.hive.metastorePartitionPruning: 'true'
    spark.sql.parquet.compression.codec: snappy
    spark.sql.shuffle.partitions: '65000'
    spark.sql.statistics.fallBackToHdfs: 'true'
    spark.sql.ui.explainMode: extended
    spark.sql.warehouse.dir: s3a://et-data-staging/warehouse
    spark.stage.maxConsecutiveAttempts: '100000'
    spark.storage.replication.proactive: 'true'
    spark.task.maxFailures: '5000'
    spark.ui.prometheus.enabled: 'true'
    spark.unsafe.exceptionOnMemoryLeak: 'false'
  sparkVersion: 3.2.1
  type: Scala
  volumes:
    - hostPath:
        path: /mnt/spark-pv-1/
      name: spark-local-dir-3
    - hostPath:
        path: /mnt/spark-pv-2/
      name: spark-local-dir-2
