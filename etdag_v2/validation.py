"""
DAG validation logic for ETDAG framework.

This module provides validation functions and classes for ensuring
DAG configurations meet required standards and best practices.
"""

import re
from typing import List, Optional, Union

try:
    from et_config.environments import get_environment_config
except ImportError:
    import os
    import sys

    # Add multiple potential paths for different deployment scenarios
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)

    # Add parent directory (for local development)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Add current directory's parent for Airflow repo structure
    repo_dir = os.path.join(parent_dir, "..")
    if os.path.exists(repo_dir) and repo_dir not in sys.path:
        sys.path.insert(0, repo_dir)

    try:
        from et_config.environments import get_environment_config
    except ImportError as e:
        # Fallback: try direct import from registry
        try:
            from et_config.environments.registry import get_environment_config
        except ImportError:
            raise ImportError(
                f"Cannot import get_environment_config. Tried paths: {sys.path}. "
                f"Original error: {e}"
            )


class DAGValidationError(Exception):
    """Custom exception for DAG validation errors."""

    pass


def validate_required_fields(
    dag_id: str,
    owner: str,
    description: str,
    tags: Union[List[str], set],
    business_purpose: Optional[str] = None,
) -> None:
    """Validate that all required fields are provided and valid.

    Args:
        dag_id: The DAG identifier
        owner: The DAG owner (team or individual)
        description: Description of what the DAG does
        tags: List or set of tags for categorization
        business_purpose: Business justification (required for production)

    Raises:
        DAGValidationError: If validation fails
    """
    env_config = get_environment_config()

    if not dag_id:
        raise DAGValidationError("dag_id is required and cannot be empty")

    if not owner:
        raise DAGValidationError("owner is required and cannot be empty")

    if not description:
        raise DAGValidationError("description is required and cannot be empty")

    # Handle both list and set types for tags
    if not tags or (not isinstance(tags, (list, set))) or len(tags) == 0:
        raise DAGValidationError("tags must be a non-empty list")

    # Production-specific validations
    if env_config.is_prod:
        if not business_purpose:
            raise DAGValidationError("business_purpose is required for production DAGs")

    # Validate DAG ID format
    if not re.match(r"^[a-zA-Z0-9._-]+$", dag_id):
        raise DAGValidationError(
            "dag_id must consist exclusively of alphanumeric characters, dashes, dots and underscores"
        )

    # Validate owner format (team-based ownership)
    if not re.match(r"^[a-zA-Z0-9._-]+$", owner):
        raise DAGValidationError("owner must not contain special characters")

    # Validate tags
    for tag in tags:
        if not isinstance(tag, str) or not tag.strip():
            raise DAGValidationError("All tags must be non-empty strings")


def enhance_tags(
    tags: List[str],
    owner: str,
    environment: str,
    business_purpose: Optional[str] = None,
) -> List[str]:
    """Enhance tags with environment and owner information.

    Args:
        tags: Original tags list
        owner: DAG owner
        environment: Current environment
        business_purpose: Business purpose for the DAG

    Returns:
        Enhanced tags list with additional metadata
    """
    enhanced_tags = list(tags)  # Copy original tags

    # Add environment tag
    enhanced_tags.append(f"env:{environment}")

    # Add owner tag
    enhanced_tags.append(f"owner:{owner}")

    # Add business purpose tag if available
    if business_purpose:
        # Create a sanitized business purpose tag
        purpose_tag = business_purpose.lower().replace(" ", "-")[:20]
        enhanced_tags.append(f"purpose:{purpose_tag}")

    return enhanced_tags


def apply_production_safety_checks(kwargs: dict) -> None:
    """Apply additional safety checks for production environment.

    Args:
        kwargs: DAG configuration dictionary to validate
    """
    import logging

    dag_id = kwargs.get("dag_id")

    # Ensure production DAGs have appropriate settings
    if kwargs.get("catchup", True):
        logging.warning(
            "Production DAG %s has catchup=True. "
            "Consider setting catchup=False for production.",
            dag_id,
        )

    # Ensure appropriate concurrency limits
    max_active_tasks = kwargs.get("max_active_tasks", 1)
    if max_active_tasks > 5:
        logging.warning(
            "Production DAG %s has high max_active_tasks=%s. "
            "Consider lower concurrency for production stability.",
            dag_id,
            max_active_tasks,
        )

    # Ensure retries are configured
    default_args = kwargs.get("default_args", {})
    retries = default_args.get("retries", 0)
    if retries < 2:
        logging.warning(
            "Production DAG %s has low retries=%s. "
            "Consider at least 2 retries for production resilience.",
            dag_id,
            retries,
        )


# Create alias for validate_required_fields for backward compatibility
validate_dag = validate_required_fields

# Note: ValidationError will be imported from __init__.py after module initialization
# This ensures consistency between etdag.ValidationError and etdag.validation.ValidationError
