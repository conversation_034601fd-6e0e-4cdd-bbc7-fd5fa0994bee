"""
Enhanced ElToro DAG (ETDAG) Framework

This package provides an enhanced DAG framework for Airflow with:
- Environment-aware configuration
- Comprehensive validation
- Modular notification system
- Enhanced monitoring and safety features

Main exports:
- ETDAG: Enhanced DAG class
- DAGValidationError: Custom validation exception
- Utility functions for environment checks
- Notification handlers and functions
"""

from etdag_v2 import validation
from etdag_v2.core import ETDAG
from etdag_v2.decorators import etdag
from etdag_v2.notifications.base import (
    BaseNotificationHandler,
    get_alert_emoji,
    get_status_text,
)
from etdag_v2.notifications.opsgenie import (
    OpsGenieNotificationHandler,
    send_opsgenie_alert,
)
from etdag_v2.notifications.slack import (
    SlackNotificationHandler,
    task_slack_error_alert,
    task_slack_success_alert,
)
from etdag_v2.utils import is_development, is_production, log_failure, log_success
from etdag_v2.validation import DAGValidationError
from etdag_v2.validation import validate_required_fields as validate_dag


# Create alias for backward compatibility that inherits from ValueError
class ValidationError(ValueError, DAGValidationError):
    """Validation error that inherits from both ValueError and DAGValidationError for compatibility."""

    pass


# Create is_local alias for is_development
is_local = is_development

__all__ = [
    # Core class and decorator
    "ETDAG",
    "etdag_v2",
    # Validation
    "validate_dag",
    "ValidationError",
    "DAGValidationError",
    # Utilities
    "is_production",
    "is_development",
    "is_local",
    "log_success",
    "log_failure",
    # Notification handlers
    "BaseNotificationHandler",
    "SlackNotificationHandler",
    "OpsGenieNotificationHandler",
    # Notification functions
    "task_slack_success_alert",
    "task_slack_error_alert",
    "send_opsgenie_alert",
    # Notification utilities
    "get_alert_emoji",
    "get_status_text",
]


# Dynamically add ValidationError to the validation module
setattr(validation, "ValidationError", ValidationError)
