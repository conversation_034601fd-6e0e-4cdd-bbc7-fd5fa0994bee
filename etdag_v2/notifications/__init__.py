"""
Notification system for ETDAG framework.

This package provides modular notification capabilities including:
- Slack notifications
- Email notifications
- OpsGenie integration
- Base notification classes and utilities

Main exports:
- Slack notification functions
- Email notification functions
- OpsGenie integration functions
- Base notification classes
"""

from .base import BaseNotificationHandler
from .slack import task_slack_error_alert, task_slack_success_alert

__all__ = [
    "task_slack_success_alert",
    "task_slack_error_alert",
    "BaseNotificationHandler",
]
