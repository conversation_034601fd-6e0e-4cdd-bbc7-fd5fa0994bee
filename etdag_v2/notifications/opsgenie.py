"""
OpsGenie notification functionality for ETDAG framework.

This module provides OpsGenie integration for critical production alerts
with team-based routing and escalation capabilities.
"""

import os
from typing import Any, Dict, Optional

import requests

from etdag_v2.notifications.base import BaseNotificationHandler
from etdag_v2.utils import is_production


class OpsGenieNotificationHandler(BaseNotificationHandler):
    """Handler for OpsGenie notifications."""

    def __init__(self, team: Optional[str] = None, environment: Optional[str] = None):
        """Initialize OpsGenie notification handler.

        Args:
            team: OpsGenie team name
            environment: Target environment
        """
        super().__init__(environment)
        self.team = team

    def send_success_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """OpsGenie typically doesn't send success notifications.

        Args:
            context: Airflow context dictionary

        Returns:
            None (success notifications not typically sent to OpsGenie)
        """
        self.logger.info("OpsGenie success notifications are typically disabled")
        return None

    def send_failure_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send OpsGenie failure alert.

        Args:
            context: Airflow context dictionary

        Returns:
            OpsGenie API response or None if failed/disabled
        """
        if not is_production():
            self.logger.info("OpsGenie alerts disabled in non-production environments")
            return None

        return self._send_opsgenie_alert(context)

    def _send_opsgenie_alert(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send alert to OpsGenie for production failures.

        Args:
            context: Airflow context dictionary

        Returns:
            OpsGenie API response or None if failed
        """
        try:
            context_info = self._extract_context_info(context)

            # Resolve team
            opsgenie_team = self._resolve_team()
            if not opsgenie_team:
                self.logger.warning(
                    "Cannot send OpsGenie alert: no team specified for DAG %s",
                    context_info["dag_id"],
                )
                return None

            # Create alert payload
            alert_payload = self._build_alert_payload(context_info, opsgenie_team)

            # TODO: Implement actual OpsGenie API call
            self.logger.info(
                "Would send OpsGenie alert for DAG %s, task %s at %s to team %s",
                context_info["dag_id"],
                context_info["task_id"],
                context_info["execution_date"],
                opsgenie_team,
            )

            # Placeholder for actual implementation
            return self._send_to_opsgenie_api(alert_payload)

        except (KeyError, ValueError, TypeError) as e:
            self._handle_notification_error(e, "opsgenie")
            return None

    def _resolve_team(self) -> Optional[str]:
        """Resolve the OpsGenie team to send alerts to.

        Returns:
            OpsGenie team name or None if not found
        """
        if self.team:
            return self.team

        # Try to resolve from team mappings
        # This would need to be enhanced based on how team ownership is determined
        return None

    def _build_alert_payload(
        self, context_info: Dict[str, Any], team: str
    ) -> Dict[str, Any]:
        """Build OpsGenie alert payload.

        Args:
            context_info: Extracted context information
            team: OpsGenie team name

        Returns:
            OpsGenie alert payload dictionary
        """
        error_msg = str(
            context_info.get("exception") or context_info.get("reason", "Unknown error")
        )

        return {
            "message": f"Airflow DAG Failure: {context_info['dag_id']}",
            "description": f"Task {context_info['task_id']} failed with error: {error_msg}",
            "teams": [{"name": team}],
            "priority": "P2",  # High priority for production failures
            "source": "Airflow",
            "entity": context_info["dag_id"],
            "alias": f"{context_info['dag_id']}-{context_info['task_id']}-{context_info['execution_date']}",
            "details": {
                "dag_id": context_info["dag_id"],
                "task_id": context_info["task_id"],
                "execution_date": str(context_info["execution_date"]),
                "environment": context_info["environment"],
                "error": error_msg,
                "dag_url": f"https://airflow.k8s.eltoro.com/dags/{context_info['dag_id']}/grid",
            },
            "tags": [
                "airflow",
                "dag-failure",
                f"env:{context_info['environment']}",
                f"dag:{context_info['dag_id']}",
            ],
        }

    def _send_to_opsgenie_api(self, payload: Dict[str, Any]) -> Optional[Any]:
        """Send alert to OpsGenie API.

        Args:
            payload: OpsGenie alert payload

        Returns:
            API response or None
        """
        api_key = os.getenv("OPSGENIE_API_KEY")
        if not api_key:
            self.logger.error("OPSGENIE_API_KEY environment variable not set")
            return None

        url = "https://api.opsgenie.com/v2/alerts"
        headers = {
            "Authorization": f"GenieKey {api_key}",
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()

            api_response = response.json()
            self.logger.info(
                "OpsGenie alert created successfully: %s", api_response.get("requestId")
            )
            return api_response

        except requests.exceptions.RequestException as e:
            self.logger.error("Failed to send OpsGenie alert: %s", str(e))
            return None
        except Exception as e:
            self.logger.error("Unexpected error sending OpsGenie alert: %s", str(e))
            return None


def send_opsgenie_alert(
    context: Dict[str, Any], team: Optional[str] = None
) -> Optional[Any]:
    """Send OpsGenie alert for DAG failure.

    Args:
        context: Airflow context dictionary
        team: OpsGenie team name (optional)

    Returns:
        OpsGenie API response or None
    """
    handler = OpsGenieNotificationHandler(team=team)
    return handler.send_failure_notification(context)
