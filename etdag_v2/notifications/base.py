"""
Base notification classes and utilities for ETDAG framework.

This module provides base classes and common functionality
for all notification types in the ETDAG framework.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from et_config.environments import get_current_environment


class BaseNotificationHandler(ABC):
    """Base class for all notification handlers."""

    def __init__(self, environment: Optional[str] = None):
        """Initialize the notification handler.

        Args:
            environment: Target environment, defaults to current environment
        """
        self.environment = environment or get_current_environment()
        # Create unique logger name to ensure independence between instances
        logger_name = f"{self.__class__.__name__}_{id(self)}"
        self.logger = logging.getLogger(logger_name)

    @abstractmethod
    def send_success_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send a success notification.

        Args:
            context: Airflow context dictionary

        Returns:
            Response from notification service or None
        """
        pass

    @abstractmethod
    def send_failure_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send a failure notification.

        Args:
            context: Airflow context dictionary

        Returns:
            Response from notification service or None
        """
        pass

    def _extract_context_info(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract common information from Airflow context.

        Args:
            context: Airflow context dictionary

        Returns:
            Dictionary with extracted context information
        """
        task_instance = context.get("task_instance")
        dag = context.get("dag")
        task = context.get("task")

        # Extract dag_id from multiple possible sources
        dag_id = "unknown"
        if task_instance and hasattr(task_instance, "dag_id"):
            dag_id_val = getattr(task_instance, "dag_id", None)
            # Handle case where dag_id might be a string, mock, or other value
            dag_id = str(dag_id_val) if dag_id_val is not None else "unknown"
        elif dag and hasattr(dag, "dag_id"):
            dag_id_val = getattr(dag, "dag_id", None)
            # Handle case where dag_id might be a string, mock, or other value
            dag_id = str(dag_id_val) if dag_id_val is not None else "unknown"

        # Extract task_id from multiple possible sources
        task_id = "unknown"
        if task_instance and hasattr(task_instance, "task_id"):
            task_id_val = getattr(task_instance, "task_id", None)
            # Handle case where task_id might be a string, mock, or other value
            task_id = str(task_id_val) if task_id_val is not None else "unknown"
        elif task and hasattr(task, "task_id"):
            task_id_val = getattr(task, "task_id", None)
            # Handle case where task_id might be a string, mock, or other value
            task_id = str(task_id_val) if task_id_val is not None else "unknown"

        # Extract try_number if available
        try_number = None
        if task_instance and hasattr(task_instance, "try_number"):
            try_number = getattr(task_instance, "try_number", None)

        result = {
            "dag_id": dag_id,
            "task_id": task_id,
            "execution_date": context.get("execution_date"),
            "exception": context.get("exception"),
            "reason": context.get("reason"),
            "environment": self.environment,
        }

        # Add try_number if available
        if try_number is not None:
            result["try_number"] = try_number

        return result

    def _handle_notification_error(
        self,
        error: Exception,
        notification_type: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Handle errors that occur during notification sending.

        Args:
            error: The exception that occurred
            notification_type: Type of notification (e.g., 'slack', 'email')
            context: Optional context information for enhanced error logging
        """
        error_msg = f"Failed to send {notification_type} notification: {error}"

        if context:
            # Extract context info for enhanced logging
            context_info = self._extract_context_info(context)
            dag_id = context_info.get("dag_id", "unknown")
            if dag_id != "unknown":
                error_msg += f" (DAG: {dag_id})"

        self.logger.error(error_msg)

    def _format_execution_date(self, execution_date) -> str:
        """Format execution date for display.

        Args:
            execution_date: Execution date (datetime, string, or None)

        Returns:
            Formatted date string
        """
        if execution_date is None:
            return "unknown"

        # Handle datetime objects
        if hasattr(execution_date, "strftime"):
            return execution_date.strftime("%Y-%m-%d %H:%M:%S")

        # Handle string representations
        return str(execution_date)


def get_alert_emoji(is_success: bool, is_production: bool) -> str:
    """Get appropriate emoji for alert based on success status and environment.

    Args:
        is_success: Whether this is a success or failure alert
        is_production: Whether we're in production environment

    Returns:
        Appropriate emoji string
    """
    if is_success:
        if is_production:
            return "✅"  # Production success
        else:
            return "🎉"  # Non-production success
    else:
        if is_production:
            return "❌"  # Production failure
        else:
            return "⚠️"  # Non-production failure


def get_status_text(is_success: bool) -> str:
    """Get status text for notifications.

    Args:
        is_success: Whether this is a success or failure

    Returns:
        Status text string
    """
    return "Success" if is_success else "Failed"
