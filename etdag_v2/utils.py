"""
Utility functions for ETDAG framework.

This module provides utility functions for environment detection,
logging, and other common operations used throughout the ETDAG framework.
"""

import logging

from et_config.environments import get_environment_config

# Import SQLAlchemy exceptions for safe handling
try:
    from sqlalchemy.orm.exc import DetachedInstanceError
except ImportError:
    # If SQLAlchemy is not available, create a dummy exception class
    class DetachedInstanceError(Exception):
        """Dummy DetachedInstanceError for when SQLAlchemy is not available."""


def is_production() -> bool:
    """Check if we're running in production environment."""
    env_config = get_environment_config()
    return env_config.is_prod


def is_development() -> bool:
    """Check if we're running in development environment."""
    env_config = get_environment_config()
    return env_config.is_dev


def is_local() -> bool:
    """Check if we're running in local environment."""
    env_config = get_environment_config()
    return env_config.is_local


def log_success(context):
    """Basic success logging callback."""
    task_instance = context.get("task_instance")

    # Safe attribute access to avoid DetachedInstanceError
    dag_id = "unknown"
    if task_instance:
        try:
            dag_id_val = getattr(task_instance, "dag_id", None)
            dag_id = str(dag_id_val) if dag_id_val is not None else "unknown"
        except (DetachedInstanceError, AttributeError, Exception):
            # Fallback if SQLAlchemy session is detached or other errors
            dag_id = "unknown"

    execution_date = context.get("execution_date", "unknown")

    logging.info("%s has completed successfully at %s", dag_id, execution_date)
    return None


def log_failure(context):
    """Basic failure logging callback."""
    task_instance = context.get("task_instance")

    # Safe attribute access to avoid DetachedInstanceError
    dag_id = "unknown"
    if task_instance:
        try:
            dag_id_val = getattr(task_instance, "dag_id", None)
            dag_id = str(dag_id_val) if dag_id_val is not None else "unknown"
        except (DetachedInstanceError, AttributeError, Exception):
            # Fallback if SQLAlchemy session is detached or other errors
            dag_id = "unknown"

    execution_date = context.get("execution_date", "unknown")

    # Extract task_id with safe attribute access
    task_id = "unknown"
    if task_instance:
        try:
            task_id_val = getattr(task_instance, "task_id", None)
            task_id = str(task_id_val) if task_id_val is not None else "unknown"
        except (DetachedInstanceError, AttributeError, Exception):
            # Fallback if SQLAlchemy session is detached or other errors
            task_id = "unknown"
    elif context.get("task"):
        try:
            task_id_val = getattr(context.get("task"), "task_id", None)
            task_id = str(task_id_val) if task_id_val is not None else "unknown"
        except (DetachedInstanceError, AttributeError, Exception):
            task_id = "unknown"

    exception = context.get("exception")
    exception_msg = str(exception) if exception else "Unknown error"

    log_message = f"FAILURE: {dag_id} task {task_id} has failed at {execution_date} - {exception_msg}"

    # Use logging.error directly for consistency with first test
    logging.error(log_message)
    return None
