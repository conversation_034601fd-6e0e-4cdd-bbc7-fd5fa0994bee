import string
import random
from decimal import Decimal
from datetime import datetime, timedelta
from airflow.providers.trino.hooks.trino import <PERSON><PERSON>Hook
from datascience_implement_cache import DIC
from datascience_implement_cache.geocoder import Geocoder
from pandas.errors import SettingWithCopyWarning
import warnings
from airflow.utils.decorators import apply_defaults
from airflow.models.baseoperator import BaseOperator

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

supported_geocoder_columns = {
    "addressLine": "varchar",
    "cityLine": "varchar",
    "streetNumber": "varchar",
    "address": "varchar",
    "province": "varchar",
    "county": "varchar",
    "city": "varchar",
    "state": "varchar",
    "country": "varchar",
    "zipcode": "varchar",
    "zip4": "varchar",
    "dataset": "varchar",
    "matchCode": "varchar",
    "matchDescription": "varchar",
    "buildingName": "varchar",
    "firmName": "varchar",
    "streetName": "varchar",
    "streetAddress": "varchar",
    "streetNamePreDir": "varchar",
    "streetNamePostDir": "varchar",
    "streetNamePrefix": "varchar",
    "streetNameSuffix": "varchar",
    "streetNameBase": "varchar",
    "latitude": "decimal(10, 6)",
    "longitude": "decimal(10, 6)",
    "isBestName": "boolean",
    "isIntersection": "boolean",
    "streetSide": "varchar",
    "unitNumber": "varchar",
    "unitType": "varchar",
    "unitNumberLowerBound": "varchar",
    "unitNumberUpperBound": "varchar",
    "unitRangeType": "varchar",
    "geocodeSurfaceAreaWKB": "varchar",
    "geometryWKB": "varchar",
    "fipsCode": "varchar",
    "rdi": "varchar",
    "etHashV1": "varchar",
    "etHashV2": "varchar"
}

class StarburstGeocoderOperator(BaseOperator):
    '''
    The StarburstGeocoderOperator is a utility meant to simplify the process of geocoding the addresses
    in an imported dataset.
    It does the following:
    * Automatically creates a new table that will contain your PK from the source table
    * Resilient, in that it runs in chunks and stores successfully ethashed data in s3 to be reloaded via an external
    * Rerunnable, safe to rerun and will only geocode and update rows where the PK is not in the bridge table
    * Configurable ttl on geocoded rows (default 365 days). This will auto drop any rows in the bridge older and re-geocode
    :param source_table_name: Full starburst table path of source.  Source can be in any catalog and have a unique PK
    :type arg: str
    :param source_row_identifier_column_name: PK name from source table or can be and expression to create a single field (composite key)
    :type arg: str
    :param bridge_table_name: full starburst path to bridge table operator will create. Must be Olympus(Iceberg) Catalog
    :type arg: str
    :param address1_column_name: address1 column name from source table
    :type arg: str
    :param address2_column_name: This is optional, will default to None and be handled within the operator
    :type arg: str
    :param zipcode_column_name: address1 column name from source table.  Must be clean 5 character string
    :type arg: str
    :param bridge_record_ttl_days: how long to retain geocoded rows before re-ethashing them. default=365
    :type arg: int
    :param source_where_clause: optional where clause to use on source table, useful if you're only selecting from one partition
    :type arg: str
    :param source_row_alias_column_name: required if a composite key is used in source_row_identifier_column_name.  This will be the name of the new composite column moving forward
    :type arg: str
    '''

    template_fields = ["source_where_clause"]

    @apply_defaults
    def __init__(
        self,
        *,
        source_table_name: str,
        source_row_identifier_column_name: str,
        address1_column_name: str,
        zipcode_column_name: str,
        bridge_table_name: str,
        geocoder_columns: list,
        address2_column_name=None,
        bridge_record_ttl_days=365,
        source_where_clause=None,
        source_row_alias_column_name=None,
        request_id=None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.source_table_name = source_table_name
        self.source_row_identifier_column_name = source_row_identifier_column_name
        self.address1_column_name = address1_column_name
        self.zipcode_column_name = zipcode_column_name
        self.bridge_table_name = bridge_table_name
        self.geocoder_columns = geocoder_columns
        self.bridge_record_ttl_days = bridge_record_ttl_days
        self.source_where_clause = source_where_clause
        if source_row_alias_column_name is None:
            self.source_row_alias_column_name = source_row_identifier_column_name
        else:
            self.source_row_alias_column_name = source_row_alias_column_name

        characters = string.ascii_letters
        self.session_id = ''.join(random.choice(characters) for _ in range(12))
        dic = DIC("StarburstGeocoder", "airflow")
        self.dc_geocoder = Geocoder(dic)

        if address2_column_name is not None:
            self.address1_column_name = f"CONCAT({self.address1_column_name}, ' ', {address2_column_name})"

        self.trino_hook = TrinoHook("starburst")

    def _create_external_table(self):
        geocoder_columns_strs = []
        for column in self.geocoder_columns:
            geocoder_columns_strs.append(f"{column.lower()} {supported_geocoder_columns[column]}")

        column_stmts = ", \n".join(geocoder_columns_strs)

        stmt = f"""
            CREATE TABLE s3."dev_data_analytics".temp_{self.session_id} (
            {self.source_row_alias_column_name} varchar, 
            {column_stmts}
        )
        WITH (
           external_location = 's3a://vr-timestamp/bi_sources/starburst_geocoder_app/{self.session_id}/',
           format = 'PARQUET'
        )     
        """
        self.trino_hook.run(stmt)

    def _create_bridge_table(self):
        geocoder_columns_strs = []
        for column in self.geocoder_columns:
            geocoder_columns_strs.append(f"{column.lower()} {supported_geocoder_columns[column]}")

        column_stmts = ", \n".join(geocoder_columns_strs)

        stmt = f"""
        CREATE TABLE IF NOT EXISTS {self.bridge_table_name} (
            {self.source_row_alias_column_name} varchar, 
            {column_stmts},
            geocoded_date date
        )
        WITH (
            sorted_by = ARRAY['{self.source_row_alias_column_name}']
        )     
        """
        self.trino_hook.run(stmt)

    def _drop_external_table(self):
        stmt = f"""
            DROP TABLE s3."dev_data_analytics".temp_{self.session_id}
        """
        self.trino_hook.run(stmt)

    def _insert_to_bridge_table(self):
        column_stmts = ", \n".join(self.geocoder_columns)
        for c in self.geocoder_columns:
            if supported_geocoder_columns[c] != 'varchar':
                column_stmts.replace(c, f"CAST({c} AS {supported_geocoder_columns[c]})")

        stmt = f"""
            INSERT INTO {self.bridge_table_name} (
            SELECT
            {self.source_row_alias_column_name},
            {column_stmts},
            DATE('{datetime.today().isoformat()[:10]}') geocoded_date
            FROM s3."dev_data_analytics".temp_{self.session_id}
            )
        """
        self.trino_hook.run(stmt)

    def _delete_stale_records_in_bridge(self):
        stmt = f"""
            DELETE FROM {self.bridge_table_name}
            WHERE geocoded_date < DATE('{(datetime.now() - timedelta(days=self.bridge_record_ttl_days)).isoformat()[:10]}')
        """
        self.trino_hook.run(stmt)

    def _select_ungeocoded_address_info(self):
        stmt = f"""
            SELECT DISTINCT
              {self.source_row_identifier_column_name} {self.source_row_alias_column_name},
              {self.address1_column_name} address,
              {self.zipcode_column_name} zipcode
            FROM
              {self.source_table_name}
            WHERE
              {self.address1_column_name} is not null
              and trim({self.address1_column_name}) != ''
              and {self.zipcode_column_name} is not null
              and {self.zipcode_column_name} != ''
              and regexp_like({self.zipcode_column_name}, '^\d+$')
              and {self.source_row_identifier_column_name} not in (
                SELECT distinct
                  {self.source_row_alias_column_name}
                FROM
                  {self.bridge_table_name})
        """
        if self.source_where_clause is not None:
            stmt = stmt.replace(":where_clause", self.source_where_clause.lower().replace('where', ''))
        else:
            stmt = stmt.replace(":where_clause", "")
        print(stmt)

        if self.source_where_clause is not None:
            stmt += f"AND {self.source_where_clause.lower()}"

        print(stmt)

        df = self.trino_hook.get_pandas_df(stmt)
        return df

    def _split_dataframe(self, df, chunk_size=1000000):
        chunks = list()
        num_chunks = len(df) // chunk_size + 1
        for i in range(num_chunks):
            chunks.append(df[i * chunk_size:(i + 1) * chunk_size])
        return chunks

    def _geocode_ungeocoded_addresses(self, df):
        chunks = self._split_dataframe(df)
        for i, chunk in enumerate(chunks):
            try:
                print(f"Processing chunk {i}", end='\r')
                gc_columns = self.geocoder_columns.copy()
                chunk = self.dc_geocoder.geocode(chunk, 1, None, 2, gc_columns, scrub=False)
                chunk.columns = [x.lower() for x in chunk.columns]
                gc_columns = [c.lower() for c in self.geocoder_columns]
                if 'ethash' in gc_columns:
                    gc_columns.remove('ethash')
                    gc_columns.append('ethashv1')

                chunk.rename(
                    columns={"ethash": "ethashv1"}, inplace=True
                )
                for name, dtype in supported_geocoder_columns.items():
                    if dtype.startswith("decimal"):
                        if name.lower() in gc_columns:
                            chunk[name.lower()] = chunk[name.lower()].fillna(0)
                            chunk[name.lower()] = chunk[name.lower()].apply(lambda x: Decimal(f"{x:.6f}"))

                    if dtype == "boolean":
                        if name.lower() in gc_columns:
                            chunk[name.lower()] = chunk[name.lower()].fillna(False)

                chunk = chunk.fillna('')
                chunk = chunk[[self.source_row_alias_column_name] + gc_columns]

                path = f"s3://vr-timestamp/bi_sources/starburst_geocoder_app/{self.session_id}/chunk-{i}.parquet"
                chunk.to_parquet(path=path)

            except TypeError as e:
                print(f"Error in chunk {i}")

    def execute(self, context):
        print(f"Session id: {self.session_id}")
        self._create_external_table()
        self._create_bridge_table()
        self._delete_stale_records_in_bridge()
        df = self._select_ungeocoded_address_info()
        print(f"Length UnGeocoded Addresses: {len(df)}")
        if len(df) > 0:
            self._geocode_ungeocoded_addresses(df)
            self._insert_to_bridge_table()
            self._drop_external_table()
        return