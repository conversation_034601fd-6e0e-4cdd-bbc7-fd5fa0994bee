from pyspark.sql import SparkSession
from pyspark.sql.utils import AnalysisException
from pyspark.sql.dataframe import Data<PERSON>rame
from pyspark.sql.functions import udf
from pyspark.sql.functions import col, lit, to_date, concat, current_date
from pyspark.sql.types import (
    IntegerType,
    FloatType,
    DateType,
    StringType,
    StructType,
    StructField,
)
from datetime import datetime
import json
import http.client
import time

schema = StructType(
    [
        StructField("ethash", StringType(), True),
        StructField("ethash_v1", StringType(), True),
        StructField("ethash_v2", StringType(), True),
    ]
)

ATLAS_HOST = "atlas.k8s.eltoro.com"
headers = {"Content-Type": "application/json"}


def ethash_request(address, retries=3):
    conn = http.client.HTTPSConnection(ATLAS_HOST, timeout=30)

    null_response = (None, None, None)

    ref_id = "silver_owner_transfer-airflow"

    def geocode_request(address):
        query = f"""
        {{
            geocodeAddress(address: ["{address}"], refId: "{ref_id}") {{
                etHash
                etHashV1
                etHashV2
            }}
        }}
        """
        body = json.dumps({"query": query})

        try:
            conn.request("POST", "/", headers=headers, body=body)
            response = conn.getresponse()
            status = response.status
            if status == 200:
                data = json.loads(response.read().decode())
                geocode = data["data"]["geocodeAddress"][0]
                return (
                    geocode["etHash"],
                    geocode["etHashV1"],
                    geocode["etHashV2"],
                )

        except (
            http.client.HTTPException,
            http.client.ssl.SSLError,
            TimeoutError,
        ) as e:
            print(f"Request failed for {address}.")
            print(f"Request error: {e}")
            return null_response

        except Exception as e:
            print(f"Request failed for {address}.")
            print(f"Unhandled error: {e}")
            return null_response

    if address == "" or address is None:
        return null_response

    for attempt in range(retries + 1):

        res = geocode_request(address)
        if res is None:
            etHash, etHashV1, etHashV2 = null_response
        else:
            etHash, etHashV1, etHashV2 = geocode_request(address)

        if etHash is not None:
            return etHash, etHashV1, etHashV2
        if attempt < retries:
            retries -= 1
            delay = 2 ** retries
            time.sleep(delay)
            print(f"Request failed. Retrying in {delay} seconds...")
            conn.close()
            conn = http.client.HTTPSConnection(ATLAS_HOST, timeout=30)

            ethash_request(address, retries)

    print(f"GeocodeAddress failed for {address}")

    return null_response


ethash = udf(ethash_request, schema)


class SilverOwnertransfer:
    def __init__(
        self,
        env: str = "local",
    ):
        self.env = env
        schema_name = "corelogic"
        self.silver_bucket = f"et-datalake-corelogic"
        self.silver_prefix = "silver_ownertransfer"
        self.bronze_table = f"olympus.bronze_corelogic.ownertransfer_v3"
        self.silver_table = f"olympus.silver_corelogic.ownertransfer"

        self.max_transfer_date = None

        # Create spark client.
        self.spark = SparkSession.builder.appName(
            "CoreLogicSilver"
        ).getOrCreate()

        if env == "local":
            pass
            query = f"""
                CREATE SCHEMA IF NOT EXISTS olympus.silver_corelogic
            """
            self.spark.sql(query)

    def _get_unloaded_file_transfer_dates(self):
        bronze_query = (
            f"SELECT distinct(file_transfer_date) FROM {self.bronze_table}"
        )
        silver_query = (
            f"SELECT distinct(file_transfer_date) FROM {self.silver_table}"
        )
        bronze_res = self.spark.sql(bronze_query).collect()
        bronze_dates = set([d[0] for d in bronze_res])
        silver_dates = set([])
        try:
            silver_res = self.spark.sql(silver_query).collect()
            silver_dates = set([d[0] for d in silver_res])
        except:
            print("Silver Ownetransfer does not exist.")

        return list(bronze_dates - silver_dates)

    def run_job(self):
        self.create_table()
        transfer_dates = self._get_unloaded_file_transfer_dates()
        if len(transfer_dates) == 0:
            print("Silver Ownertransfer is caught up. No data to load!")
            return

        print("**********************************")
        print([str(td) for td in transfer_dates])
        print("**********************************")

        for td in transfer_dates:
            print("----------------------------------")
            print(f"Running file transfer date {td}")
            print("----------------------------------")
            self.load_table(td)
            print("----------------------------------")
            print(f"Finished file transfer date {td}")
            print("----------------------------------")

    def load_table(self, transfer_date):
        """
        load_date DATE,
        ethash_beta VARCHAR(12),
        ethash_v1 VARCHAR(12),
        ethash_v2 VARCHAR(12),
        formatted_address VARCHAR(60)
        """

        formatted_address = concat(
            col("deed_situs_street_address_static"),
            lit(","),
            col("deed_situs_city_static"),
            lit(","),
            col("deed_situs_state_static"),
            lit(" "),
            col("deed_situs_zip_code_static"),
        )

        (
            self.spark.table(self.bronze_table)
            .filter(col("file_transfer_date") == transfer_date)
            .withColumn(
                "apn_sequence_number",
                col("apn_sequence_number").cast(IntegerType()),
            )
            .withColumn(
                "total_number_of_buildings",
                col("total_number_of_buildings").cast(IntegerType()),
            )
            .withColumn(
                "transaction_batch_date",
                col("transaction_batch_date").cast(DateType()),
            )
            .withColumn(
                "transaction_batch_sequence_number",
                col("transaction_batch_sequence_number").cast(IntegerType()),
            )
            .withColumn("sale_amount", col("sale_amount").cast(FloatType()))
            .withColumn(
                "sale_derived_date", col("sale_derived_date").cast(DateType())
            )
            .withColumn(
                "sale_derived_recording_date",
                col("sale_derived_recording_date").cast(DateType()),
            )
            .withColumn(
                "ownership_transfer_percentage",
                col("ownership_transfer_percentage").cast(IntegerType()),
            )
            .withColumn("ethash_date", current_date())
            .withColumn(
                "formatted_address", formatted_address.cast(StringType())
            )
            .withColumn("ethash_struct", ethash(col("formatted_address")))
            .withColumn("ethash_beta", col("ethash_struct.ethash"))
            .withColumn("ethash_v1", col("ethash_struct.ethash_v1"))
            .withColumn("ethash_v2", col("ethash_struct.ethash_v2"))
            .drop("ethash_struct")
            .sort("sale_derived_recording_date")
            .writeTo(self.silver_table)
            .partitionedBy("fips_code")
            .append()
        )

    def cast_num_to_date(self, num_date):
        """Cast a numbered date in the format of 'yyyyMMdd' to sql date.

        Handles the cases where 00 is used for the month or day by replacing
        00 with 01.
        """

        return f"""
            to_date(
                regexp_replace(
                    regexp_extract('{num_date}', '^([12][9  ]{{7}})$'),
                    '(?<=.{{4}})(?<!1)00(?!1)',
                        '01'
                ),
                'yyyyMMdd'
            )
        """

    def create_table(self):
        """Create a new Iceberg table with the partition specification."""
        query = f"""
        CREATE TABLE IF NOT EXISTS {self.silver_table}(
            clip VARCHAR(10),
            previous_clip VARCHAR(10),
            fips_code VARCHAR(5),
            apn_parcel_number_unformatted VARCHAR(45),
            apn_sequence_number INTEGER,
            composite_property_linkage_key VARCHAR(53),
            original_apn VARCHAR(45),
            tax_account_number VARCHAR(60),
            online_formatted_parcel_id VARCHAR(60),
            land_use_code_static VARCHAR(3),
            county_use_description_static VARCHAR(30),
            state_use_description_static VARCHAR(30),
            mobile_home_indicator VARCHAR(1),
            zoning_code_static VARCHAR(15),
            property_indicator_code_static VARCHAR(3),
            actual_year_built_static VARCHAR(4),
            effective_year_built_static VARCHAR(4),
            total_number_of_buildings INTEGER,
            deed_situs_house_number_static VARCHAR(10),
            deed_situs_house_number_suffix_static VARCHAR(10),
            deed_situs_house_number_2_static VARCHAR(20),
            deed_situs_direction_static VARCHAR(2),
            deed_situs_street_name_static VARCHAR(30),
            deed_situs_mode_static VARCHAR(5),
            deed_situs_quadrant_static VARCHAR(2),
            deed_situs_unit_number_static VARCHAR(10),
            deed_situs_city_static VARCHAR(40),
            deed_situs_state_static VARCHAR(2),
            deed_situs_zip_code_static VARCHAR(9),
            deed_situs_county_static VARCHAR(30),
            deed_situs_carrier_route_static VARCHAR(40),
            deed_situs_street_address_static VARCHAR(60),
            standardized_address_confidence_code VARCHAR(4),
            transaction_fips_code VARCHAR(5),
            owner_transfer_composite_transaction_id VARCHAR(18),
            transaction_batch_date DATE,
            transaction_batch_sequence_number INTEGER,
            pending_record_indicator VARCHAR(1),
            multi_or_split_parcel_code VARCHAR(1),
            primary_category_code VARCHAR(1),
            deed_category_type_code VARCHAR(3),
            sale_type_code VARCHAR(1),
            sale_amount FLOAT,
            sale_derived_date DATE,
            sale_derived_recording_date DATE,
            sale_document_type_code VARCHAR(6),
            sale_recorded_document_number VARCHAR(12),
            sale_recorded_document_book_number VARCHAR(6),
            sale_recorded_document_page_number VARCHAR(6),
            ownership_transfer_percentage INTEGER,
            title_company_name VARCHAR(60),
            title_company_code VARCHAR(5),
            cash_purchase_indicator VARCHAR(1),
            mortgage_purchase_indicator VARCHAR(1),
            interfamily_related_indicator VARCHAR(1),
            investor_purchase_indicator VARCHAR(1),
            resale_indicator VARCHAR(1),
            new_construction_indicator VARCHAR(1),
            residential_indicator VARCHAR(1),
            short_sale_indicator VARCHAR(1),
            foreclosure_reo_indicator VARCHAR(1),
            foreclosure_reo_sale_indicator VARCHAR(1),
            buyer_1_full_name VARCHAR(60),
            buyer_1_last_name VARCHAR(32),
            buyer_1_first_name_and_middle_initial VARCHAR(44),
            buyer_2_full_name VARCHAR(60),
            buyer_2_last_name VARCHAR(32),
            buyer_2_first_name_and_middle_initial VARCHAR(44),
            buyer_1_corporate_indicator VARCHAR(1),
            buyer_2_corporate_indicator VARCHAR(1),
            buyer_3_full_name VARCHAR(60),
            buyer_3_last_name VARCHAR(32),
            buyer_3_first_name_and_middle_initial VARCHAR(44),
            buyer_3_corporate_indicator VARCHAR(1),
            buyer_4_full_name VARCHAR(60),
            buyer_4_last_name VARCHAR(32),
            buyer_4_first_name_and_middle_initial VARCHAR(44),
            buyer_4_corporate_indicator VARCHAR(1),
            buyer_etal_code VARCHAR(1),
            buyer_ownership_rights_code VARCHAR(3),
            buyer_relationship_type_code VARCHAR(2),
            buyer_care_of_name VARCHAR(60),
            buyer_occupancy_code VARCHAR(28),
            partial_interest_indicator VARCHAR(1),
            buyer_mailing_house_number VARCHAR(10),
            buyer_mailing_house_number_suffix VARCHAR(10),
            buyer_mailing_house_number_2 VARCHAR(10),
            buyer_mailing_direction VARCHAR(2),
            buyer_mailing_street_name VARCHAR(30),
            buyer_mailing_mode VARCHAR(5),
            buyer_mailing_quadrant VARCHAR(2),
            buyer_mailing_unit_number VARCHAR(10),
            buyer_mailing_city VARCHAR(40),
            buyer_mailing_state VARCHAR(2),
            buyer_mailing_zip_code VARCHAR(9),
            buyer_mailing_carrier_route VARCHAR(4),
            buyer_mailing_street_address VARCHAR(60),
            buyer_mailing_opt_out_indicator VARCHAR(1),
            seller_1_full_name VARCHAR(60),
            seller_1_last_name VARCHAR(32),
            seller_1_first_name VARCHAR(32),
            seller_2_full_name VARCHAR(60),
            record_action_indicator VARCHAR(1),
            file_transfer_type VARCHAR(5),
            file_transfer_date DATE,
            ethash_date DATE,
            formatted_address VARCHAR(60),
            ethash_beta VARCHAR(12),
            ethash_v1 VARCHAR(12),
            ethash_v2 VARCHAR(12)
        )
        PARTITIONED BY (fips_code)
        LOCATION "s3a://{self.silver_bucket}/{self.silver_prefix}"
        OPTIONS (
            `write.format.default-sort-order` = 'sale_derived_recording_date'
        )
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)


if __name__ == "__main__":
    job = SilverOwnertransfer("prod")

    job.run_job()
