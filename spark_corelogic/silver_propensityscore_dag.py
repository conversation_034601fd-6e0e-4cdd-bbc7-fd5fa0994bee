import logging
import random
import os
from datetime import datetime as dt
from tempfile import NamedTemporaryFile

import pandas as pd
import boto3
from airflow import DAG, Dataset
from airflow.decorators import task
from airflow.models import Variable
from airflow.models.param import Param
from airflow.operators.empty import EmptyOperator
from airflow.providers.trino.hooks.trino import Tri<PERSON>Hook
from airflow.utils.dates import days_ago

from spark_corelogic.spark_operator import SparkKubernetesOperatorWrapper

logging.basicConfig()
logger = logging.getLogger(__name__)

SILVER_TABLE = "olympus.silver_corelogic.propensityscore"
BRONZE_TABLE = "olympus.bronze_corelogic.propensityscore1_dpc"
STAGING_BUCKET = "et-datalake-corelogic-staging"
STAGING_PREFIX = "source/propensityscore1_dpc/full/"


class S3:
    def __init__(self, env="local"):
        if env == "local":
            endpoint_url = os.environ.get("AWS_ENDPOINT_URL_S3")
            if endpoint_url is None:
                endpoint_url = "http://localhost:9000"
            self.s3 = boto3.client(
                "s3",
                endpoint_url=endpoint_url,
                aws_access_key_id="admin",
                aws_secret_access_key="password",
                verify=False,
            )

        if env == "prod" or env == "dev":
            self.s3 = boto3.client("s3")

        self.env = env
        self.corelogic_paths = None

    def get_file_paths(self, bucket: str, prefix: str):
        paginator = self.s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
        file_paths = []
        for page in pages:
            paths = page.get("Contents", {})
            file_paths += paths
        return file_paths

    def corelogic_datasets(self, paths):
        files = {
            "dataset": [],
            "transfer_type": [],
            "transfer_date": [],
            "filename": [],
            "key": [],
            "file_size": [],
        }
        for fp in paths:
            path = fp["Key"].split("/")
            if len(path) == 5:
                files["dataset"].append(path[1])
                files["transfer_type"].append(path[2])
                files["transfer_date"].append(path[3])
                files["filename"].append(path[4])
                files["key"].append(fp["Key"])
                files["file_size"].append(fp["Size"])

        return pd.DataFrame(files, dtype="string")

    def get_file_transfer_dates(
        self, bucket: str, prefix: str, from_date: str = None
    ):
        staging_file_paths = self.get_file_paths(bucket=bucket, prefix=prefix)
        df = self.corelogic_datasets(staging_file_paths)
        df.transfer_date = df.transfer_date.apply(
            lambda d: dt.strftime(dt.strptime(d, "%Y%m%d"), "%Y-%m-%d")
        )

        # Filter date strings less than `from_date`.
        if from_date is not None:
            df = df[df.transfer_date >= from_date]

        return df.transfer_date.to_list()


def get_unloaded_file_transfer_dates(
    env, file_transfer_date=None, backfill_date=None
) -> list[str]:
    """
    Find `file_transfer_date` months that haven't been inserted into
    silver table.
    """
    tr = TrinoHook(trino_conn_id="starburst")
    s3 = S3(env)
    staging_dates = s3.get_file_transfer_dates(
        bucket=STAGING_BUCKET,
        prefix=STAGING_PREFIX,
        from_date=backfill_date,
    )
    staging_dates = set(staging_dates)

    silver_query = f"""
            SELECT DISTINCT
                file_transfer_date
            FROM {SILVER_TABLE}
        """
    print("===============================================")
    print(silver_query)
    print("-----------------------------------------------")
    silver_res = tr.get_records(silver_query)
    silver_dates = set([str(d[0]) for d in silver_res if d[0] is not None])
    print(f"silver_dates: {silver_dates}")
    print("===============================================")

    if (
        file_transfer_date is not None
        and file_transfer_date not in silver_dates
    ):
        return [file_transfer_date]
    elif file_transfer_date is not None:
        return []

    print("===============================================")
    print(f"staging dates: {staging_dates}")
    print("===============================================")

    # Diff the sets.
    results = list(staging_dates - silver_dates)
    # Sort results.
    results.sort()
    print(f"results - {results}")

    return results


def get_latest_bronze_transfer_date() -> str | None:
    bronze_query = f"""
        SELECT max(file_transfer_date)
        FROM {BRONZE_TABLE}
        WHERE fips_code = '36061' -- NYC
    """
    print("++++++++++++++++++++++++++++")
    print(bronze_query)
    print("++++++++++++++++++++++++++++")

    tr = TrinoHook(trino_conn_id="starburst")

    records = tr.get_records(bronze_query)
    bronze_max_transfer_date = dt.strftime(records[0][0], "%Y-%m-%d")

    silver_query = f"""
        SELECT max(file_transfer_date)
        FROM {SILVER_TABLE}
    """
    print("++++++++++++++++++++++++++++")
    print(silver_query)
    print("++++++++++++++++++++++++++++")

    silver_res = tr.get_records(silver_query)

    silver_max_transfer_date = dt.strftime(silver_res[0][0], "%Y-%m-%d")

    if bronze_max_transfer_date == silver_max_transfer_date:
        return None
    elif bronze_max_transfer_date > silver_max_transfer_date:
        return bronze_max_transfer_date

default_args = {
    "owner": "Clay Morton",
    "depends_on_past": False,
    "start_date": days_ago(2),
    "start_date": dt(2024, 12, 1),
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 0,
}

env = Variable.get("environment")

hive_uri = None
if env == "local":
    hive_uri = "thrift://localhost:9083"
if env == "prod":
    hive_uri = "thrift://hive.olympus:9083"

run = format(random.getrandbits(20), "x")
app_name = f"silver-propensityscore-{run}"

with DAG(
    dag_id="silver_propensityscore",
    description="Corelogic silver_propensityscore update.",
    default_args=default_args,
    schedule_interval=None,
    schedule=[Dataset(f"olympus/bronze_corelogic/propensityscore1_dpc")],
    catchup=False,
    max_active_runs=1,
    template_searchpath="/opt/airflow/dags/repo",
    params={
        "transfer_date": Param(
            None,
            type=["null", "string"],
            title="Silver propensityscore file transfer date.",
            description="Date string with yyyy-MM-dd format: ex. '2024-01-01'.",
        ),
        "backfill_date": Param(
            None,
            type=["null", "string"],
            title="Silver propensityscore backfill from file transfer date.",
            description="Date string with yyyy-MM-dd format: ex. '2024-01-01'.",
        ),
    },
    user_defined_macros={
        "app_name": app_name,
        "hive_uri": hive_uri,
        "application_file": "silver_propensityscore.py",
    },
    tags=["corelogic", "spark", "starburst", "silver"],
) as dag:

    @task
    def derive_run_dates(**kwargs):
        transfer_date = kwargs["params"]["transfer_date"]
        backfill_date = kwargs["params"]["backfill_date"]
        env = Variable.get("environment")
        logger.debug(f"Environment: ------>>> {env}")
        if transfer_date is not None:
            return [transfer_date]
        if backfill_date is not None:
            # Get list of file transfer dates from the bronze table.
            s3 = S3(env)

            return get_unloaded_file_transfer_dates(
                env, file_transfer_date=None, backfill_date=backfill_date
            )

        latest_date = get_latest_bronze_transfer_date()

        if latest_date is not None:
            return [latest_date]
        else:
            return []

    run_dates = derive_run_dates()

    update_table = SparkKubernetesOperatorWrapper(
        task_id="load_table",
        application_file="spark_corelogic/iceberg_silver.yaml",
        namespace="spark",
        retries=0,
        dag=dag,
    )

    end = EmptyOperator(task_id="end")

    run_dates >> update_table >> end


if __name__ == "__main__":
    logger.setLevel(logging.DEBUG)

    run = dag.test(
        conn_file_path="new_mover/connections.json",
        variable_file_path="new_mover/variables.json",
        run_conf={
            "file_transfer_date": "2024-09-5",
            # "backfill_date": "2024-09-01",
            "environment": "local",
        },
        mark_success_pattern="wait_for_.*|end",
    )

    paths = run.get_task_instance("derive_run_dates").xcom_pull()

    assert len(paths) == 2
