from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import (
    SparkKubernetesOperator,
)
from kubernetes.client.rest import ApiException

class SparkKubernetesOperatorWrapper(SparkKubernetesOperator):

    def execute(self, context):
        try:
            super().execute(context)
        except ApiException as e:
            if e.status == 410:
                self.log.warning("Watch expired, but job likely completed successfully")
                self.log.warning(e)
            else:
                self.log.warning("** Unable to complete operation. **")
                raise e
