apiVersion: sparkoperator.k8s.io/v1beta2
kind: SparkApplication
metadata:
  name: "{{ app_name }}"
  namespace: spark
spec:
  arguments:
    - "-e={{ env }}"
    - "-d={{ dag_run.conf['dataset'] }}"
    - "-tt={{ dag_run.conf['transfer_type'] }}"
    - "-td={{ dag_run.conf['transfer_date'] }}"
  deps:
    packages:
      - nl.basjes.hadoop:splittablegzip:1.3
      - software.amazon.awssdk:bundle:2.20.87
      - org.apache.iceberg:iceberg-spark-runtime-3.4_2.12:1.3.0
      - org.apache.spark:spark-hadoop-cloud_2.12:3.4.0
  driver:
    annotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: 'false'
      iam.amazonaws.com/role: airflow-worker-s3-role
    labels:
      name: spark-driver
    cores: 8
    env:
      - name: AWS_REGION
        value: us-east-1
    javaOptions: |
      -XX:+UnlockExperimentalVMOptions
      -XX:+UseG1GC
      -XX:+ParallelRefProcEnabled
      -XX:ParallelGCThreads=4
      -XX:ConcGCThreads=4
      -Dfile.encoding=UTF-8
      -Dio.netty.tryReflectionSetAccessible=true
      -Divy.cache.dir=/tmp
      -Divy.home=/tmp
      --add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
      --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
      --add-opens=java.base/java.io=ALL-UNNAMED
      --add-opens=java.base/java.net=ALL-UNNAMED
      --add-opens=java.base/java.nio=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED
      --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
      --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
      --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
      --add-opens=java.base/sun.nio.cs=ALL-UNNAMED
      --add-opens=java.base/sun.security.action=ALL-UNNAMED
      --add-opens=java.base/sun.util.calendar=ALL-UNNAMED
      --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED
    memory: 24G
    memoryOverhead: 8604M
    securityContext:
      privileged: true
    serviceAccount: spark
    terminationGracePeriodSeconds: 300
    nodeSelector:
      instancegroup: trino
    tolerations:
      - effect: NoSchedule
        key: dedicated
        operator: Equal
        value: trino
  dynamicAllocation:
    enabled: true
    initialExecutors: 10
    minExecutors: 1
    maxExecutors: 10
  executor:
    annotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: 'false'
      iam.amazonaws.com/role: airflow-worker-s3-role
    cores: 8
    env:
      - name: AWS_REGION
        value: us-east-1
    initContainers:
      - command:
          - sh
          - '-c'
          - >-
            sysctl -w vm.swappiness=0; sysctl -w vm.max_map_count=9999999;
            sysctl -w fs.file-max=9999999; sysctl -w fs.nr_open=9999999; sysctl
            -w fs.inotify.max_user_watches=1048576; sysctl -w
            net.core.somaxconn=65535;
        image: ************.dkr.ecr.us-east-1.amazonaws.com/busybox:latest
        imagePullPolicy: IfNotPresent
        name: init
        securityContext:
          privileged: true
    javaOptions: |
      -XX:+HeapDumpOnOutOfMemoryError
      -XX:+UnlockExperimentalVMOptions
      -XX:+UseG1GC
      -XX:+AlwaysPreTouch
      -XX:+ParallelRefProcEnabled
      -XX:+UseNUMA
      -XX:ParallelGCThreads=6
      -XX:ConcGCThreads=6
      -Dfile.encoding=utf-8
      -Dio.netty.tryReflectionSetAccessible=true
      -Divy.cache.dir=/tmp
      -Divy.home=/tmp
      --add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
      --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
      --add-opens=java.base/java.io=ALL-UNNAMED
      --add-opens=java.base/java.net=ALL-UNNAMED
      --add-opens=java.base/java.nio=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED
      --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
      --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
      --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
      --add-opens=java.base/sun.nio.cs=ALL-UNNAMED
      --add-opens=java.base/sun.security.action=ALL-UNNAMED
      --add-opens=java.base/sun.util.calendar=ALL-UNNAMED
      --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED
    memory: 52G
    memoryOverhead: 10G
    nodeSelector:
      instancegroup: trino
    securityContext:
      privileged: true
    terminationGracePeriodSeconds: 300
    tolerations:
      - effect: NoSchedule
        key: dedicated
        operator: Equal
        value: trino
  hadoopConf:
    fs.s3.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    fs.s3a.assumed.role.sts.endpoint.region: us-east-1
    fs.s3a.committer.staging.tmp.path: /tmp/spark-pv-1/staging
    fs.s3a.connection.maximum: '10000'
    fs.s3a.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    io.compression.codecs: nl.basjes.hadoop.io.compress.SplittableGzipCodec
  image: >-
    harbor.k8s.eltoro.com/dockerhub/apache/spark:3.4.0-scala2.12-java11-python3-r-ubuntu
  imagePullPolicy: Always
  mainApplicationFile: s3a://nocturnalley/python/{{ application_file }}
  mode: cluster
  sparkConf:
    spark.default.parallelism: '50'
    spark.driver.maxResultSize: 500G
    spark.dynamicAllocation.enabled: 'true'
    spark.dynamicAllocation.executorAllocationRatio: '0.33'
    spark.dynamicAllocation.shuffleTracking.enabled: 'true'
    spark.dynamicAllocation.sustainedSchedulerBacklogTimeout: '30'
    spark.eventLog.dir: s3a://diagonalley/spark-logs/
    spark.eventLog.enabled: 'true'
    spark.hadoop.fs.s3a.committer.magic.enabled: 'true'
    spark.hadoop.fs.s3a.committer.name: magic
    spark.hadoop.fs.s3a.connection.ssl.enabled: 'false'
    spark.hadoop.fs.s3a.fast.upload: 'true'
    spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a: org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory
    spark.hadoop.mapreduce.input.fileinputformat.split.minsize: '25600'
    spark.history.fs.inProgressOptimization.enabled: 'true'
    spark.history.fs.update.interval: 5s
    spark.kryo.unsafe: 'true'
    spark.kryoserializer.buffer: '256'
    spark.kryoserializer.buffer.max: '2000'
    spark.memory.offHeap.enabled: 'true'
    spark.memory.offHeap.size: '10737418240'
    spark.rdd.compress: 'true'
    spark.scheduler.barrier.maxConcurrentTasksCheck.maxFailures: '5'
    spark.serializer: org.apache.spark.serializer.KryoSerializer
    spark.shuffle.compress: 'true'
    spark.shuffle.file.buffer: 2M
    spark.shuffle.spill.compress: 'true'
    spark.speculation: 'true'
    spark.sql.adaptive.enabled: 'true'
    spark.sql.ansi.enabled: 'false'
    spark.sql.catalog.olympus: org.apache.iceberg.spark.SparkCatalog
    spark.sql.catalog.olympus.io-impl: org.apache.iceberg.aws.s3.S3FileIO
    spark.sql.catalog.olympus.type: hive
    spark.sql.catalog.olympus.uri: "{{ hive_uri }}"
    spark.sql.catalog.olympus.warehouse: s3a://et-datalake-corelogic-prod
    spark.sql.execution.arrow.pyspark.enabled: 'true'
    spark.sql.extensions: org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
    spark.sql.files.maxPartitionBytes: '1048576000'
    spark.sql.hive.metastorePartitionPruning: 'true'
    spark.sql.orc.cache.stripe.details.size: '536870912'
    spark.sql.orc.filterPushdown: 'true'
    spark.sql.orc.splits.include.file.footer: 'true'
    spark.sql.shuffle.partitions: '500'
    spark.sql.warehouse.dir: s3a://diagonalley/spark/warehouse
  sparkVersion: 3.4.0
  type: Python
