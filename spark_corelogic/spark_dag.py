"""This file defines a group of dags that are responsible for running unique
dbt models.

The SparkDAG  handles all parameters that are known at compile time. The for loop
that iterates over the various models passes the model variables to the dag
declaration which wraps the task defined by the spark_task function. The spark_task
function handles all the runtime parameters."""

from etdag import ETDAG
from airflow.models import Variable
from airflow import Dataset
from airflow.utils.dates import days_ago
from airflow.utils.trigger_rule import TriggerRule
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from spark_corelogic.spark_operator import SparkKubernetesOperatorWrapper
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.operators.dummy import DummyOperator
from airflow.decorators import task
from airflow.sensors.time_delta import TimeDeltaSensorAsync
from datetime import datetime, timedelta
import random
import yaml


class SparkDAG(ETDAG):
    """A generalized dag to invoke a dbt model run.

    SparkDAG  packages params needed to specify a dbt model."""

    default_args = {
        "owner": "<PERSON>",
        "depends_on_past": False,
        "start_date": days_ago(2),
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 0,
    }

    params = {
        "dataset": "{{ dag_run.conf['dataset'] }}",
        "transfer_type": "{{ dag_run.conf['transfser_type'] }}",
        "transfer_date": "{{ dag_run.conf['transfer_date'] }}",
    }

    def __init__(
        self,
        dataset: str,
        incremental_strategy: str = None,
        **kwargs,
    ):
        kwargs["tags"] = ["corelogic", "dbt", "starburst", "bronze"]
        kwargs["dag_id"] = f"bronze_{dataset}"
        kwargs["schedule_interval"] = None
        kwargs["catchup"] = False
        kwargs["default_args"] = self.default_args
        kwargs["params"] = self.params
        kwargs["params"]["dataset"] = dataset

        if incremental_strategy != "merge":
            kwargs["max_active_runs"] = 16
        else:
            kwargs["max_active_runs"] = 1

        super().__init__(**kwargs)

# Order represents time to execution by factor of 20 minutes.
# See comment above `delay` task.
models = [
    ("mls_listings_replace", "append"),
    ("mortgage_basic3", "merge"),
    ("vol_lien_status_m2_dpc", "append"),
    ("ownertransfer_v3", "merge"),
    ("propensityscore1_dpc", "append"),
    ("property_basic2", "merge"),
    ("building_detail1", "merge"),
    ("digital_audiences", "append"),
    ("trigger_events", "append"),
    ("buildingpermit_3", "replace"),
    ("thvxcpf1_dpc", "append"),
    ("solarcon1_dpc", "merge"),
]
for index, (dataset, strategy) in enumerate(models):
    with SparkDAG(dataset, strategy) as dag:
        run = format(random.getrandbits(64), "x")
        dataset_name = dataset.replace("_", "-")
        app_name = f"spark-bronze-{dataset_name}-{run}"
        env = Variable.get("environment")
        hive_uri = None
        if env == "local":
            hive_uri = "thrift://localhost:9083"
        if env == "prod":
            hive_uri = "thrift://hive.olympus:9083"

        params = {
            "app_name": app_name,
            "env": env,
            "hive_uri": hive_uri,
            "application_file": "bronze_tables.py",
        }

        dag.user_defined_macros = params

        outlet = Dataset(f"olympus/bronze_corelogic/{dataset}")
        trigger_silver = DummyOperator(task_id="outlet", outlets=outlet)

        @task(trigger_rule=TriggerRule.ALL_DONE)
        def check_transfer_data(**kwargs):
            dataset = kwargs["params"]["dataset"]
            transfer_type = kwargs["params"]["transfer_type"]
            transfer_date = kwargs["params"]["transfer_date"]
            date = datetime.strptime(transfer_date, "%Y%m%d").date().isoformat()
            schema = "bronze_corelogic"
            if env == "local":
                schema += "_local"
            query = f"""
            SELECT count(file_transfer_date)
            FROM olympus.{schema}.{dataset}
            WHERE file_transfer_date = date('{date}')
            AND file_transfer_type = '{transfer_type}'
            """
            tr = TrinoHook(trino_conn_id="trino_conn")
            res = tr.get_records(query)
            file_path = f"s3://et-datalake-corelogic-staging/source/{dataset}/{transfer_type}/{transfer_date}/"
            if res[0][0] <= 0:
                raise Exception(f"File: {file_path} did not transfer!!")

        @task
        def get_file_size(**kwargs) -> int:
            bucket = "et-datalake-corelogic-staging"
            if env == "local":
                bucket = f"{bucket}-local"
            dataset = kwargs["params"]["dataset"]
            transfer_type = kwargs["params"]["transfer_type"]
            transfer_date = kwargs["params"]["transfer_date"]
            prefix = f"source/{dataset}/{transfer_type}/{transfer_date}/"
            s3_hook = S3Hook("s3_conn")
            files = s3_hook.get_file_metadata(bucket_name=bucket, prefix=prefix)
            file_size = files[0]["Size"]
            return file_size

        @task.branch
        def choose_codec(file_size):
            print("**************************")
            print(f"file_size -- {file_size}")
            print("**************************")

            if file_size < 54_581_494:
                return "load_table"
            else:
                return "load_table_gzip"

        iceberg_yaml = ""
        with open("/opt/airflow/dags/repo/spark_corelogic/iceberg.yaml") as f:
            iceberg_yaml = yaml.load(f, Loader=yaml.CLoader)
        load_table = SparkKubernetesOperatorWrapper(
            task_id="load_table",
            template_spec=iceberg_yaml,
            namespace="spark",
            retries=0,
            dag=dag,
        )

        iceberg_gzip_yaml = ""
        with open(
            "/opt/airflow/dags/repo/spark_corelogic/iceberg_gzip.yaml"
        ) as f:
            iceberg_gzip_yaml = yaml.load(f, Loader=yaml.CLoader)
        load_table_gzip = SparkKubernetesOperatorWrapper(
            task_id="load_table_gzip",
            template_spec=iceberg_gzip_yaml,
            namespace="spark",
            retries=0,
            dag=dag,
        )

        # Use a staggered delay in executing datasets.
        # This will prevent too many spark dags running at once
        # which causes failures due to resource constraint.
        delay = TimeDeltaSensorAsync(
            task_id="delay",
            delta=timedelta(minutes=index * 20),
        )

        file_size = get_file_size()
        choose_task = choose_codec(file_size)
        check_transfer = check_transfer_data()


        (
            delay
            >> choose_task
            >> [load_table, load_table_gzip]
            >> check_transfer
            >> trigger_silver
        )
