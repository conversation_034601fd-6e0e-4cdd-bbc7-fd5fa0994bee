from etdag import ETDAG
from airflow.utils.trigger_rule import TriggerRule
from airflow.providers.trino.hooks.trino import Tri<PERSON>Hook
from airflow.decorators import task
from spark_corelogic.spark_operator import SparkKubernetesOperatorWrapper
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow import Dataset
import yaml
import random

default_args = {
    "owner": "<PERSON> Morton",
    "depends_on_past": False,
    "start_date": days_ago(2),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 0,
}

env = Variable.get("environment")
hive_uri = None
if env == "local":
    hive_uri = "thrift://localhost:9083"
if env == "prod":
    hive_uri = "thrift://hive.olympus:9083"

run = format(random.getrandbits(20), "x")
app_name = f"silver-ownertransfer-{run}"

docs = """
# Silver Owner Transfer

Background Info:
* This is a PySpark job that backfills the Starburst table `olympus.silver_corelogic.ownertransfer`.
* Dag is triggered with data-aware scheduling string `olympus/bronze_corelogic/ownertransfer_v3`.
* The code for the `load_data` task is found in the file `./silver_ownertransfer.py`.

Failure Scenario:
* Failures occur with all Dags using the SparkKubernetesOperatorWrapper as resources are conditionally available.
* The task `check_transfer_date` can fail even when `update_table` is successful.
* In case of failure clear the whole dag.

Source Data:
* Starburst table `olympus.bronze_corelogic.ownertransfer_v3`.

Result Data Destination:
* Starburst table `olympus.silver_corelogic.ownertransfer`.

Escalation Path:
* Clay Morton or Rorie Lizenby.
"""

with ETDAG(
    dag_id="silver_ownertransfer",
    description="Corelogic silver_ownertransfer update.",
    default_args=default_args,
    schedule_interval=None,
    schedule=[Dataset("olympus/bronze_corelogic/ownertransfer_v3")],
    catchup=False,
    max_active_runs=1,
    template_searchpath="/opt/airflow/dags/repo",
    user_defined_macros={
        "app_name": app_name,
        "hive_uri": hive_uri,
        "application_file": "silver_ownertransfer.py",
    },
    tags=["corelogic", "spark", "starburst", "silver"],
    doc_md=docs,
) as _:

    @task(trigger_rule=TriggerRule.ALL_SUCCESS)
    def check_transfer_data(**kwargs):
        bronze_schema = "bronze_corelogic"
        silver_schema = "silver_corelogic"
        if env == "local":
            bronze_schema += "_local"
            silver_schema += "_local"
        bronze_query = f"""
        SELECT max(file_transfer_date)
        FROM olympus.{bronze_schema}.ownertransfer_v3
        """
        silver_query = f"""
        SELECT max(file_transfer_date)
        FROM olympus.{silver_schema}.ownertransfer
        """
        tr = TrinoHook(trino_conn_id="trino_conn")
        bronze_res = tr.get_records(bronze_query)
        silver_res = tr.get_records(silver_query)

        if bronze_res != silver_res:
            raise Exception(f"File: {bronze_res} did not transfer!!")

    iceberg_yaml = ""
    with open("/opt/airflow/dags/repo/spark_corelogic/iceberg_silver.yaml") as f:
        iceberg_yaml = yaml.load(f, Loader=yaml.CLoader)

    update_table = SparkKubernetesOperatorWrapper(
        task_id="load_table",
        template_spec=iceberg_yaml,
        namespace="spark",
        retries=0,
    )

    update_table >> check_transfer_data()
