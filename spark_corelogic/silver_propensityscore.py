from pyspark.sql import SparkSession
from pyspark.sql.utils import AnalysisException
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import col, lit, to_date, concat, current_date
from pyspark.sql.types import (
    IntegerType,
    FloatType,
    DateType,
    StringType,
)
from datetime import datetime as dt
from dateutil.relativedelta import relativedelta
import json
import time
import logging
import argparse
import itertools
import os

logger = logging.getLogger(__name__)


class SilverPropensityScore:
    def __init__(
        self,
        env: str = "local",
    ):
        self.env = env
        schema_name = "corelogic"
        self.silver_bucket = "et-datalake-corelogic"
        self.silver_prefix = "silver_propensityscore"
        self.staging_file_path = "s3a://et-datalake-corelogic-staging/source/propensityscore1_dpc/full/"
        self.staging_bucket = "et-datalake-corelogic-staging"
        self.staging_prefix = "source/propensityscore1_dpc/full/"
        self.silver_table = "olympus.silver_corelogic.propensityscore"

        self.max_transfer_date = None

        # Create spark client.
        self.spark = SparkSession.builder.appName(
            "CoreLogicSilver"
        ).getOrCreate()

        if env == "local":
            query = f"""
                CREATE SCHEMA IF NOT EXISTS olympus.silver_corelogic
            """
            self.spark.sql(query)

    def run_job(self, run_dates: list[str]):
        print("run_job -----> execution ------>>>>")
        print(f"Run dates: {run_dates}")
        self.create_table()
        if len(run_dates) == 0:
            print("Silver PropensityScore is caught up. No data to load!")
            return

        # [TODO] For initial testing just run one run date.
        for ftd in run_dates:
            print("----------------------------------")
            print(f"Processing run date {ftd}")
            print("----------------------------------")
            self.create_staging_view(ftd.replace("-", ""))
            self.load_table(ftd)
            self.check_transfer_date(ftd)
            print("----------------------------------")
            print(f"Finished processing run date {ftd}")
            print("----------------------------------")

    def check_transfer_date(self, file_transfer_date: str):
        query = f"""
            SELECT count(file_transfer_date)
            FROM {self.silver_table}
            WHERE file_transfer_date = date('{file_transfer_date}')
        """
        res = self.spark.sql(query).collect()

        if res[0][0] < 1:
            raise Exception(
                f"Transfer file {file_transfer_date} did not get loaded!!!"
            )

        pass

    def create_table(self):
        """Create a new Iceberg table with the partition specification."""
        query = f"""
            CREATE TABLE IF NOT EXISTS {self.silver_table}(
                clip varchar(10),
                fips_code varchar(5),
                propensity_score_run_date date,
                purchase_mortgage_model_propensity_score SMALLINT,
                list_for_sale_model_propensity_score SMALLINT,
                heloc_model_propensity_score SMALLINT,
                list_to_rent_model_propensity_score SMALLINT,
                file_transfer_date date
            )
            PARTITIONED BY (propensity_score_run_date, bucket(48,clip))
            LOCATION "s3a://{self.silver_bucket}/{self.silver_prefix}"
            OPTIONS (
                `write.format.default-sort-order` = 'clip'
            )
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)

    def create_staging_view(self, file_transfer_date):
        query = f"""
            DROP VIEW IF EXISTS propensity_score_staging
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)
        query = f"""
            CREATE TEMPORARY VIEW propensity_score_staging USING CSV
            OPTIONS (
                path = '{self.staging_file_path}/{file_transfer_date}/',
                header = 'true',
                inferSchema = 'true',
                sep = '|',
                compression = 'gzip'
            )
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)

    def create_run_date_views(self, file_transfer_date):
        """Create views for each run date of each model type in source file."""
        model_types = [
            "purchase mortgage",
            "list for sale",
            "list to rent",
            "heloc",
        ]
        # Get run date for each model. Key is model type. Value is list of run dates.
        model_dates = {}
        for mt in model_types:
            query = f"""
                SELECT distinct(`{mt} model propensity score run date`)
                FROM propensity_score_staging
            """
            res = self.spark.sql(query).collect()
            dates = [str(d[0]) for d in res if d[0] is not None]
            model_dates[mt] = dates

        # Flatten distinct date values. We will be deriving the unique list of run dates to build
        # a view for each one with all model types that have that particular run date.
        dates = list(itertools.chain.from_iterable(model_dates.values()))
        # Sort dates; this is important if there are multiple run dates for a model type in a source file.
        dates.sort()

        # List of model type score columns for each run date view. This will be needed for the insert query.
        run_date_views = {}
        for d in set(dates):
            view_name = f"run_date_{d}"
            run_date_views[view_name] = []
            # Convert date to be first day of next month.
            run_date = (
                dt.strptime(d, "%Y%m%d") + relativedelta(months=1)
            ).replace(day=1)
            run_date = run_date.strftime("%Y-%m-%d")
            drop_query = f"""
            DROP VIEW IF EXISTS {view_name}
            """
            query: str = f"""
            CREATE TEMPORARY VIEW {view_name} AS
            SELECT
                clip,
                `fips code` as fips_code,
                date('{run_date}') as propensity_score_run_date,
            """
            # Use `insert_space` for nicely formatted output logs.
            insert_space = " " * 12
            for mt in model_types:
                if len(model_dates[mt]) > 1:
                    logger.warning(
                        f"Model type {mt} contains multiple dates: {model_dates[mt]} for transfer file {file_transfer_date}!!!"
                    )
                column = f"{mt} model propensity score"
                column_clean = column.replace(" ", "_")
                if d in model_dates[mt]:
                    run_date_views[view_name].append((column_clean, True))
                    score = f"\n{insert_space}    `{column}` as {column_clean},"
                    query += score
                else:
                    run_date_views[view_name].append((column_clean, False))

            query += f"\n{insert_space}    ROW_NUMBER() OVER (PARTITION BY clip ORDER BY clip) as row_num"
            query += f"\n{insert_space}FROM propensity_score_staging"

            print(drop_query)
            self.spark.sql(drop_query)
            print(query)
            self.spark.sql(query)

        return run_date_views

    def load_table(self, file_transfer_date):
        views = self.create_run_date_views(file_transfer_date)
        for view in views:
            update_set = ""
            insert_columns = """
                    clip,
                    fips_code,
                    propensity_score_run_date,
            """
            insert_values = """
                    source.clip,
                    source.fips_code,
                    source.propensity_score_run_date,
            """
            insert_space = " " * 20
            for score_column, use_column in views[view]:
                if use_column is True:
                    update_set += f"\n{insert_space}silver.{score_column} = source.{score_column},"
                    insert_columns += f"\n{insert_space}{score_column},"
                    insert_values += f"\n{insert_space}source.{score_column},"
                # else:
                #     insert_values += f"\n{insert_space}NULL,"
            # update_set = update_set[:-1]
            insert_columns += f"\n{insert_space}file_transfer_date"
            insert_values += f"\n{insert_space}date('{file_transfer_date}')"
            update_set += f"\n{insert_space}silver.file_transfer_date = date('{file_transfer_date}')"

            query = f"""
            MERGE INTO {self.silver_table} silver
            USING {view} source
            ON
                silver.propensity_score_run_date = source.propensity_score_run_date
                AND silver.clip = source.clip
            WHEN MATCHED
                AND date('{file_transfer_date}') > silver.file_transfer_date
                THEN UPDATE SET {update_set}
            WHEN NOT MATCHED
                THEN INSERT ({insert_columns}
                )
                VALUES ({insert_values}
                )
            """
            print(query)
            self.spark.sql(query)


def main():
    parser = argparse.ArgumentParser(
        prog="CorelogicSilver",
        description="Load corelogic silver tables from bronze files.",
    )
    parser.add_argument(
        "-td",
        "--transfer_date",
        type=str,
        help="Currently unused but implemented because it is in the template.",
    )
    parser.add_argument(
        "-bd",
        "--backfill_date",
        type=str,
        help="Currently unused but implemented because it is in the template.",
    )
    parser.add_argument(
        "-rds",
        "--run_dates",
        type=str,
        help="List of file transfer dates to process(yyyy-MM-dd format).",
    )

    args = parser.parse_args()

    rd = args.run_dates
    # All data comes through as strings and so a list needs to be converted to a json compatible string.
    run_dates = json.loads(rd.replace("'", '"'))

    job = SilverPropensityScore("prod")

    job.run_job(run_dates=run_dates)


if __name__ == "__main__":
    main()
