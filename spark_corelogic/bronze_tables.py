"""This module builds and loads iceberg tables from corelogic S3 source files.

building_detail1(merge) - buckets=25, transfer_dates=89
buildingpermit_3(append) - buckets=3495, transfer_dates=17
digital_audiences(append) - buckets=540, transfer_dates=21
mls_listings_replace(append) - buckets=110, transfer_dates=122
mortgage_basic3(merge) - buckets=250, transfer_dates=424
ownertransfer_v3(merge) - buckets=235, transfer_dates=424
propensityscore1_dpc(append) - buckets=4390, transfer_dates=91
property_basic2(merge) - buckets=165, transfer_dates=89
solarcon1_dpc(merge) - buckets=5, transfer_dates=26
thvxcpf1_dpc(append) - buckets=400, transfer_dates=6
trigger_events(append) - buckets=45, transfer_dates=455
vol_lien_status_m2_dpc(append) - buckets=3390, transfer_dates=26
"""
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lit, to_date
from pyspark.sql.utils import AnalysisException
from pyspark.sql.dataframe import DataFrame
import argparse
import os
from datetime import datetime

ROOT_DIR = os.path.realpath(os.path.dirname(__file__))


class BronzeTables:
    def __init__(
        self,
        env: str,
        dataset: str,
        transfer_type: str,
        transfer_date: str,
    ):
        self.env = env
        self.dataset = dataset
        self.transfer_type = transfer_type
        self.transfer_date = transfer_date
        self._set_data_attributes()
        self.bucket = "et-datalake-corelogic-staging"
        self.schema = "bronze_corelogic"
        self.key = f"source/{dataset}/{transfer_type}/{transfer_date}"
        self.path = f"s3a://{self.bucket}/{self.key}"
        if self.file_type == "parquet":
            self.path = f"{self.path}/*.parquet"
        # Create spark client.
        self.spark = SparkSession.builder.appName(
            "CoreLogicBronze"
        ).getOrCreate()
        # Set the query attributes for the dataset.
        # Create default schema.
        if env == "local":
            query = f"""
                CREATE SCHEMA IF NOT EXISTS olympus.{self.schema}
            """
            self.spark.sql(query)

    def _set_data_attributes(self):
        dataset_attributes = {}
        for d in CORELOGIC_DATASETS:
            if d["dataset"] == self.dataset:
                dataset_attributes = d
        print("*************************************")
        print(f"env - {self.env}")
        print(f"dataset - {self.dataset}")
        print(f"transfer_type - {self.transfer_type}")
        print(f"transfer_date - {self.transfer_date}")
        print(dataset_attributes)
        print("*************************************")
        self.strategy = dataset_attributes.get("strategy")
        self.partition_keys: str = dataset_attributes.get("partition_keys", "")
        self.unique_keys: str = dataset_attributes.get("unique_keys", "")
        self.sort_keys: str = dataset_attributes.get("sort_keys", "")
        self.file_type = dataset_attributes.get("file_type") or "csv"

    def load_data(self):
        if not self.table_exists():
            self.create_table()
        if self.strategy == "append":
            self.append()
        if self.strategy == "merge":
            self.merge()
        if self.strategy == "replace":
            self.append()
            self.flush_old_data()

    def table_exists(self):
        exists = False
        table = f"olympus.{self.schema}.{self.dataset}"
        try:
            self.spark.catalog.getTable(table)
            exists = True
        except AnalysisException:
            print(f"Table {table} does not exist.")

        return exists

    def flush_old_data(self):
        date = self.transfer_date
        date = f"{date[:4]}-{date[4:6]}-{date[6:]}"
        query = f"""
            DELETE FROM olympus.{self.schema}.{self.dataset}
            WHERE file_transfer_date < date('{date}')
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)

    def _read_file(self, limit: int = None) -> DataFrame:
        read = self.spark.read
        df = None
        if limit is None:
            if self.file_type == "parquet":
                df = read.parquet(self.path)
            else:
                df = read.csv(self.path, header=True, sep="|")
        else:
            if self.file_type == "parquet":
                df = read.parquet(self.path).limit(limit)
            else:
                df = read.csv(self.path, header=True, sep="|").limit(limit)

        return df

    def create_table(self):
        # Read header.
        df = self._read_file(limit=0)
        # Transform column names to snakecase.
        df = self.clean_columns(df)
        df = self.add_file_transfer_columns(df)
        # Register the DataFrame as a temporary view so it can be referenced in
        # query.
        df.createOrReplaceTempView(f"stg_{self.dataset}")
        # Create a new Iceberg table with the partition specification
        bucket = "et-datalake-corelogic"

        query = f"""
            CREATE TABLE olympus.{self.schema}.{self.dataset}
            USING iceberg
            PARTITIONED BY ({self.partition_keys})
            LOCATION 's3a://{bucket}/bronze_{self.dataset}'
            OPTIONS (
                `write.format.default-unique-key` = '{self.unique_keys}',
                `write.format.default-sort-order` = '{self.sort_keys}'
            )
            AS SELECT * FROM stg_{self.dataset}
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)

    def append(self):
        ## inferSchema=True
        df = self._read_file()
        df = self.add_file_transfer_columns(df)
        df = self.clean_columns(df)

        df.writeTo(f"olympus.{self.schema}.{self.dataset}").append()

    def join_on_query(self, tmp_view: str) -> str:
        comparisons = []
        for key in self.unique_keys.split(","):
            comparisons.append(f"{self.dataset}.{key} = {tmp_view}.{key}")
        # Use 12 spaces to match indentation of merge query :)
        join_chr = f"\n{12 * ' '}AND "
        query = join_chr.join(comparisons)

        return query

    def merge(self):
        df = self._read_file()
        df = self.clean_columns(df)
        df = self.add_file_transfer_columns(df)
        # Register the DataFrame as a temporary view.
        tmp_view = f"stg_{self.dataset}"
        df.createOrReplaceTempView(tmp_view)
        file_transfer_date = str(
            datetime.strptime(self.transfer_date, "%Y%m%d").date()
        )
        query = f"""
            MERGE INTO olympus.{self.schema}.{self.dataset}
            USING {tmp_view}
            ON {self.join_on_query(tmp_view)}
            WHEN MATCHED
            AND {self.dataset}.file_transfer_date < date('{file_transfer_date}')
            OR (
                CASE
                    WHEN {self.dataset}.file_transfer_date = date('{file_transfer_date}')
                    AND {self.dataset}.file_transfer_type != 'full'
                    THEN 0
                END
            ) = 1
            THEN UPDATE SET *
            WHEN NOT MATCHED THEN INSERT *
        """
        print("*************************************")
        print(query)
        print("*************************************")
        self.spark.sql(query)

    def add_file_transfer_columns(self, df: DataFrame) -> DataFrame:
        df = df.withColumn("file_transfer_type", lit(self.transfer_type))
        # if "=" in self.transfer_date:
        #     transfer_date = self.transfer_date.split("=")[1]
        #     transfer_date = transfer_date.replace("-", "")
        # else:
        #     transfer_date = self.transfer_date
        df = df.withColumn(
            "file_transfer_date", to_date(lit(self.transfer_date), "yyyyMMdd")
        )
        return df

    def clean_columns(self, df: DataFrame) -> DataFrame:
        return df.toDF(*[self.snake_case_column(c) for c in df.columns])

    def snake_case_column(self, name: str) -> str:
        """
        Normalizes the `name` value according to our spec.

        :param name: The column name.
        :type name: str
        :param replacement: A value to replace all invalid characters with.
        :type replacement: str
        :return: A normalized column name.
        :rtype: str
        """
        replacement = "_"
        name = name.replace(" ", replacement)
        for character in INVALID_HEADER_CHARS:
            # Gets rid of any weird things like ___
            while character in name:
                name = name.replace(character, replacement)
        if name.endswith(replacement) or name[-1] == "\r":
            name = name[:-1]
        if name.startswith(replacement):
            name = name[1:]
        return name.lower()


def test():
    d = "propensityscore1_dpc"
    dates = [
        "20240104",
        "20240919",
        "20240926",
        "20241114",
    ]
    job = None
    for td in dates:
        tt = "full"
        job = BronzeTables("local", d, tt, td)
        job.load_data()


    return job


INVALID_HEADER_CHARS = [
    "`",
    "~",
    "!",
    "?",
    "@",
    "#",
    "$",
    "%",
    "^",
    "&",
    "*",
    "(",
    ")",
    "-",
    "=",
    "+",
    "/",
    ":",
    ";",
    "-",
    ",",
    "{",
    "}",
    " ",
    "[",
    "]",
    "\t",
    "__",
    "  ",
]

CORELOGIC_DATASETS = [
    {
        "dataset": "building_detail1",
        "strategy": "merge",
        "unique_keys": "clip,composite_building_linkage_key",
        "partition_keys": "bucket(25,fips_code)",
        "sort_keys": "fips_code,clip",
    },
    {
        "dataset": "buildingpermit_3",
        "strategy": "replace",
        "partition_keys": "bucket(405,fips_code)",
        "sort_key": "fips_code,clip",
    },
    {
        "dataset": "digital_audiences",
        "strategy": "append",
        "partition_keys": "bucket(410,state_physical)",
        "sort_keys": "state_physical",
    },
    {
        "dataset": "mortgage_basic3",
        "strategy": "merge",
        "unique_keys": "clip,composite_property_linkage_key,mortgage_composite_transaction_id",
        "partition_keys": "bucket(255,fips_code)",
        "sort_keys": "mortgage_recording_date,mortgage_composite_transaction_id",
    },
    {
        "dataset": "ownertransfer_v3",
        "strategy": "merge",
        "unique_keys": "clip,composite_property_linkage_key,owner_transfer_composite_transaction_id",
        "partition_keys": "bucket(240,fips_code)",
        "sort_keys": "sale_derived_recording_date,owner_transfer_composite_transaction_id",
    },
    {
        "dataset": "propensityscore1_dpc",
        "strategy": "append",
        "partition_keys": "bucket(3450,fips_code)",
        "sort_keys": "fips_code,file_transfer_date",
    },
    {
        "dataset": "property_basic2",
        "strategy": "merge",
        "unique_keys": "clip,composite_property_linkage_key",
        "partition_keys": "bucket(170,fips_code)",
        "sort_keys": "fips_code,clip",
    },
    {
        "dataset": "solarcon1_dpc",
        "strategy": "append",
        "partition_keys": "bucket(15,fips_code)",
        "sort_keys": "fips_code,clip",
    },
    {
        "dataset": "thvxcpf1_dpc",
        "strategy": "append",
        "partition_keys": "file_transfer_date,bucket(135,fips_code)",
        "sort_keys": "fips_code,clip",
    },
    {
        "dataset": "trigger_events",
        "strategy": "append",
        "partition_keys": "bucket(45,file_transfer_date)",
        "sort_keys": "fips_code,clip",
    },
    {
        "dataset": "vol_lien_status_m2_dpc",
        "strategy": "append",
        "partition_keys": "file_transfer_date,bucket(130,clip)",
    },
]


def main():
    parser = argparse.ArgumentParser(
        prog="CorelogicBronze",
        description="Load corelogic bronze tables from source files.",
    )
    parser.add_argument(
        "-e",
        "--env",
        type=str,
        help="the environment to run in",
        choices=["local", "prod"],
        default="local",
    )
    parser.add_argument(
        "-d",
        "--dataset",
        type=str,
        help="dataset name",
    )
    parser.add_argument(
        "-tt",
        "--transfer_type",
        type=str,
        help="type of file to process",
        choices=["init", "full", "delta"],
    )
    parser.add_argument(
        "-td",
        "--transfer_date",
        type=str,
        help="date file was transfered(%Y%m%d)",
    )
    args = parser.parse_args()
    job = BronzeTables(
        args.env,
        args.dataset,
        args.transfer_type,
        args.transfer_date,
    )
    job.load_data()


if __name__ == "__main__":
    main()
