# Changelog

All notable changes to this project will be documented in this file.

## 2025-07-15 - Removed Unused Registry Utility Functions

### Description:
Removed three unused utility functions from et_config/environments/registry.py that were redundant and no longer following current patterns.

### Files Changed:
- `et_config/environments/registry.py` - Removed unused utility functions and cleaned up imports

### Functions Removed:

#### 1. `get_notification_configuration()`
- **Purpose**: Returned notification settings from environment config
- **Issue**: Redundant wrapper around `env_config.notifications` direct access
- **Replacement**: Use `get_environment_config().notifications` directly

#### 2. `validate_configuration()`
- **Purpose**: Generic validation that only checked if environment config exists
- **Issue**: Too generic and misleading name, not actually DAG-specific
- **Replacement**: DAGs implement their own specific validation logic

#### 3. `list_available_configurations()`
- **Purpose**: Listed available connection IDs for debugging
- **Issue**: Static information that duplicated what's available through direct access
- **Replacement**: Use `get_environment_config().connections` directly when needed

### Benefits:
- ✅ **Reduced Complexity**: Eliminated redundant wrapper functions
- ✅ **Clearer Patterns**: Encourages direct use of `get_environment_config()`
- ✅ **Less Maintenance**: No need to keep utility functions in sync with config changes
- ✅ **Better Performance**: Eliminates unnecessary function call overhead

### Migration Guide:
```python
# Old pattern (removed)
notification_config = get_notification_configuration()
slack_webhook = notification_config["slack_webhook"]

# New pattern (recommended)
env_config = get_environment_config()
slack_webhook = env_config.notifications.slack_webhook
```

## 2025-07-15 - Centralized PyGene Connection Configuration

### Description:
Moved PyGene connection configuration from hardcoded environment-specific strings to centralized environment configuration, improving maintainability and environment separation.

### Files Changed:
- `et_config/models/environment.py` - Added pygene_conn_id to ConnectionConfig
- `et_config/environments/dev.py` - Added pygene_conn_dev configuration
- `et_config/environments/local.py` - Added pygene_conn_dev configuration (shared with dev)
- `et_config/environments/prod.py` - Added pygene_conn_prod configuration
- `dags/signal2segment/tasks/audience_generation.py` - Updated to use environment config
- `dags/signal2segment/tasks/delivery.py` - Updated to use environment config

### Problem:
- **Issue**: PyGene connection IDs were hardcoded as `f'pygene_conn_{env}'` in task functions
- **Maintainability**: Connection configuration was scattered across multiple task files
- **Environment Separation**: No clear separation between dev/local and prod connection configurations

### Solution:
- **Centralized Configuration**: Added `pygene_conn_id` to `ConnectionConfig` class
- **Environment-Specific**: Each environment now explicitly defines its PyGene connection ID
- **Cleaner Code**: Tasks now use `env_config.connections.pygene_conn_id` instead of string formatting
- **Shared Dev/Local**: Both local and dev environments use the same PyGene connection for consistency

### Technical Details:

#### Environment Configuration:
```python
# Before (hardcoded in tasks)
connection_id = f'pygene_conn_{env}'
credentials = BaseHook.get_connection(connection_id)

# After (centralized configuration)
env_config = get_environment_config()
credentials = BaseHook.get_connection(env_config.connections.pygene_conn_id)
```

#### Connection Mapping:
- **Local**: `pygene_conn_dev` (shared with dev environment)
- **Dev**: `pygene_conn_dev`
- **Prod**: `pygene_conn_prod`

### Benefits:
- ✅ **Centralized Management**: All connection IDs managed in environment configs
- ✅ **Environment Separation**: Clear distinction between dev and prod connections
- ✅ **Maintainability**: Easy to update connection IDs without touching task code
- ✅ **Consistency**: Standardized approach for all PyGene-related tasks
- ✅ **Scalability**: Easy to add new environments or modify connection patterns

## 2025-07-15 - Fixed @etdag Decorator Email Parameter Conflicts

### Description:
Fixed critical issue where the @etdag decorator was passing deprecated email parameters to task operators, causing "Invalid arguments" errors during task execution.

### Files Changed:
- `etdag_v2/decorators.py` - Removed deprecated email_on_* parameters from default_args

### Problem:
- **Issue**: Tasks were failing with "Invalid arguments were passed to _PythonDecoratedOperator" error
- **Root Cause**: Deprecated `email_on_success`, `email_on_failure`, and `email_on_retry` parameters were being passed to task operators
- **Impact**: DAGs using @etdag decorator could not execute tasks successfully

### Solution:
- **Fix**: Removed deprecated email parameters from default_args construction
- **Approach**: Email notifications are now handled exclusively through custom notification handlers
- **Compatibility**: Maintains notification functionality while fixing operator compatibility

### Technical Details:
```python
# Before (broken)
default_args = {
    "owner": owner,
    "retries": default_retries,
    "retry_delay": default_retry_delay,
    "email_on_failure": final_notification_config.email_on_failure,  # Deprecated
    "email_on_retry": False,  # Deprecated
    "email_on_success": final_notification_config.email_on_success,  # Deprecated
}

# After (fixed)
default_args = {
    "owner": owner,
    "retries": default_retries,
    "retry_delay": default_retry_delay,
}
```

### Impact:
- ✅ **Task Execution**: Tasks now execute without parameter conflicts
- ✅ **Modern Compatibility**: Compatible with current Airflow operator implementations
- ✅ **Notification Preservation**: Email notifications still work through custom handlers
- ✅ **Future-Proof**: Prepared for Airflow 4.0+ SmtpNotifier migration

## 2025-07-15 - Fixed @etdag Decorator Schedule=None Handling

### Description:
Fixed critical issue where the @etdag decorator was not respecting `schedule=None` parameter, causing DAGs to show default daily schedule instead of manual-only triggering.

### Files Changed:
- `etdag_v2/decorators.py` - Modified None value filtering to preserve schedule=None

### Problem:
- **Issue**: DAGs with `schedule=None` were showing "Schedule: 1 day, 0:00:00" instead of manual-only
- **Root Cause**: The decorator was filtering out all None values, including `schedule=None`
- **Impact**: DAGs intended for manual triggering were being scheduled to run daily

### Solution:
- **Fix**: Modified None value filtering to preserve `schedule=None` as it's a valid Airflow value
- **Result**: DAGs with `schedule=None` now properly show as manual-trigger only
- **Behavior**: `schedule=None` is preserved while other None values are still filtered out

### Technical Details:
```python
# Before (broken)
dag_params = {k: v for k, v in dag_params.items() if v is not None}

# After (fixed)
dag_params = {
    k: v for k, v in dag_params.items()
    if v is not None or k == "schedule"
}
```

## 2025-07-15 - Fixed @etdag Decorator Parameter Passing

### Description:
Fixed critical issue where the @etdag decorator was not passing through the `params` parameter to the underlying Airflow DAG, preventing manual DAG triggering with configuration through the Airflow UI.

### Files Changed:
- `etdag_v2/decorators.py` - Added `params` parameter passthrough to underlying @dag decorator

### Problem:
- **Issue**: Signal2segment DAG could not be manually triggered with configuration through Airflow UI
- **Root Cause**: The `@etdag` decorator was not passing the `params` parameter to the underlying `@dag` decorator
- **Impact**: Users could not input configurations when triggering DAGs manually, unlike the old ETDAG context manager pattern

### Solution:
- **Fix**: Added `"params": kwargs.get("params")` to the `dag_params` dictionary in the @etdag decorator
- **Result**: DAGs using @etdag decorator now properly support manual triggering with parameters through Airflow UI
- **Compatibility**: Maintains backward compatibility while enabling manual configuration input

### Technical Details:
The @etdag decorator now properly passes through the `params` parameter:

```python
# Before (broken)
dag_params = {
    "dag_id": dag_id,
    "description": description,
    # ... other params
    "doc_md": kwargs.get("doc_md"),
}

# After (fixed)
dag_params = {
    "dag_id": dag_id,
    "description": description,
    # ... other params
    "doc_md": kwargs.get("doc_md"),
    "params": kwargs.get("params"),  # Pass through params for manual triggering
}
```

### Impact:
- ✅ **Manual DAG Triggering**: Users can now trigger @etdag DAGs manually with configurations
- ✅ **UI Parameter Input**: Airflow UI now shows parameter input fields for @etdag DAGs
- ✅ **Backward Compatibility**: Existing DAGs continue to work without changes
- ✅ **Feature Parity**: @etdag decorator now has same parameter capabilities as legacy ETDAG context manager

## 2025-07-15 - PyGene Credentials Migration and Notification Pattern Update

### Description:
Migrated PyGene credentials from Variable-based approach to Airflow Connections using BaseHook.get_connection(), and standardized notification configuration pattern using dedicated _get_notification_config() function.

### Files Changed:
- `dags/signal2segment/tasks/audience_generation.py` - Updated to use BaseHook.get_connection() with environment-aware connection IDs
- `dags/signal2segment/tasks/delivery.py` - Updated to use BaseHook.get_connection() with environment-aware connection IDs
- `dags/signal2segment/signal2segment_dag.py` - Updated to use _get_notification_config() function pattern
- `dags/signal2segment/config.py` - Updated validation function to use connection-based credentials
- `et_config/environments/registry.py` - Removed deprecated get_pygene_credentials function
- `et_config/__init__.py` - Removed get_pygene_credentials from exports
- `.augment-guidelines` - Added new patterns for PyGene credentials and notification configuration

### Key Changes:

#### 1. PyGene Credentials Migration
- **Old Pattern**: `credentials = get_pygene_credentials()` using Airflow Variables
- **New Pattern**: `credentials = BaseHook.get_connection(f'pygene_conn_{env}')` using Airflow Connections
- **Credential Mapping**: `client_id = credentials.password`, `client_secret = credentials.login`
- **Environment Awareness**: Connection IDs follow pattern `pygene_conn_{environment}` (e.g., `pygene_conn_dev`, `pygene_conn_prod`)

#### 2. Notification Configuration Pattern
- **Pattern**: Define notifications in separate `_get_notification_config()` function
- **Benefits**: Cleaner code, reusable configuration, consistent pattern across DAGs
- **Configuration**: Uses `team_owner="analytics-engineering"` for proper routing

#### 3. Task Decorator Simplification
- **Changed**: From `@task.virtualenv()` to `@task` for audience generation
- **Benefit**: Simpler task definition, dependencies imported inside task functions

## 2025-07-14 - Signal2Segment Cleanup: Notifications, Retry Behavior, and Dynamic Task Mapping

### Description:
Cleaned up signal2segment DAG by removing custom Slack notifications in favor of etdag_v2's built-in system, fixed retry behavior by replacing AirflowFailException with AirflowException, and implemented proper dynamic task mapping.

### Files Changed:
- `dags/signal2segment/tasks/delivery.py` - Removed custom Slack functions, replaced `AirflowFailException` with `AirflowException`
- `dags/signal2segment/tasks/validation.py` - Removed custom Slack functions, replaced `AirflowFailException` with `AirflowException`
- `dags/signal2segment/tasks/audience_generation.py` - Removed custom Slack functions, replaced `AirflowFailException` with `AirflowException`
- `dags/signal2segment/signal2segment_dag.py` - Fixed XComArg iteration issue, implemented dynamic task mapping, cleaned up imports
- `dags/signal2segment/README.md` - Updated documentation to reflect etdag_v2 notifications
- `et_config/environments/registry.py` - Added missing `get_pygene_credentials` function
- `et_config/__init__.py` - Added `get_pygene_credentials` to exports
- All task files - Removed unused imports

### Key Improvements:

#### 1. Notification System Cleanup
- **Reduced Code Duplication**: Eliminated ~150 lines of custom Slack notification code
- **Consistent Notifications**: All notifications now use etdag_v2's standardized format and routing
- **Environment-Aware Routing**: Notifications automatically route to correct channels based on environment
- **Team-Based Routing**: Uses `team_owner="signal2segment"` to route to `#signal2segment-alerts` channel

#### 2. Fixed Retry Behavior
- **Problem**: Tasks were using `AirflowFailException` which bypasses retry configuration
- **Solution**: Replaced with `AirflowException` to respect retry settings
- **Impact**: Tasks now properly retry on temporary failures (network timeouts, server errors, Lambda failures)
- **Retry Configuration**:
  - Validation: 3 retries with 1-minute delay
  - Delivery: 3 retries with 15-second delay
  - Audience Generation: 3 retries (DAG default) with 5-minute delay

#### 3. Fixed Dynamic Task Creation
- **Problem**: DAG was trying to iterate over XComArg at definition time, causing `TypeError: 'XComArg' object is not iterable`
- **Solution**: Replaced task_group iteration with dynamic task mapping using `expand()` method
- **Impact**: DAG now properly creates one task instance per configuration at runtime
- **Benefits**: Cleaner code, better Airflow UI visualization, proper parallel processing

#### 4. Fixed Missing PyGene Credentials Function
- **Problem**: `get_pygene_credentials` function was missing from et_config exports
- **Solution**: Added function to `et_config/environments/registry.py` with proper fallback logic
- **Features**: Environment-aware credentials, local development fallback, proper error handling

#### 5. Fixed DAG Decorator Usage
- **Problem**: `TypeError: 'DAG' object is not callable` when trying to call `signal2segment_dag()`
- **Solution**: Changed `dag = signal2segment_dag()` to `dag = signal2segment_dag`
- **Explanation**: The `@etdag` decorator returns a DAG object, not a callable function

### Configuration:
The DAG now relies entirely on etdag_v2's `NotificationConfig`:
- `slack_on_failure=True` - Sends Slack notifications for DAG failures
- `slack_on_success=False` - No redundant success notifications
- `team_owner="signal2segment"` - Routes to team-specific channel
- `email_on_failure=True` - Email notifications for failures

## 2025-07-14 - Configuration Architecture Reorganization

### Description:
Major reorganization of configuration architecture to properly isolate DAG-specific configurations while moving universal configurations to appropriate locations. This addresses the scalability concerns with hundreds of future DAGs.

### Files Changed:
- `et_config/universal_registry.py` - **REMOVED** - Functions migrated to environments/registry.py
- `et_config/environments/registry.py` - Consolidated configuration functions, removed DAG-specific functions
- `et_config/environments/prod.py` - Removed query_maps and pygene_credentials, added pygene_conn_id
- `et_config/environments/dev.py` - Removed query_maps and pygene_credentials, added pygene_conn_id
- `et_config/environments/local.py` - Removed query_maps and pygene_credentials, added pygene_conn_id
- `et_config/models/environment.py` - Added pygene_conn_id to ConnectionConfig
- `dags/signal2segment/config.py` - Added DAG-specific query maps, updated to use pygene connection
- `et_config/__init__.py` - Updated imports to reflect new architecture
- `.augment-guidelines` - Updated configuration standards

### Key Changes:
- **DAG-Specific Isolation**: Query maps and lambda configurations moved from universal config to signal2segment DAG folder
- **Universal Connections**: PyGene credentials moved from variables to Airflow connections like other services
- **Simplified Registry**: Removed DAG-specific functions (get_pygene_credentials, get_query_map, get_lambda_configuration, get_performance_configuration) from universal registry
- **Connection-Based Auth**: PyGene now uses connection IDs instead of variable-based credentials
- **Complete DAG Isolation**: Lambda functions and performance settings moved to signal2segment DAG since only that DAG uses them
- **Removed Unused Config**: Performance settings were not actually being used by DAGs (hardcoded in @etdag decorators instead)
- **Clean Architecture**: Universal registry now only contains truly universal configurations used by multiple DAGs

### Architecture Principles Applied:
- **Universal configurations** (used by multiple DAGs): Currently none - all moved to DAG-specific folders
- **Universal connections** (used by multiple DAGs): starburst_conn_id, pygene_conn_id → Connection configs
- **DAG-specific configurations** (exclusive to one DAG): query_maps, lambda_functions, performance_settings → Isolated in DAG folders
- **Unused configurations removed**: Performance settings were not actually applied to DAG behavior

### Migration Notes:
- Query maps are now accessed via `SIGNAL2SEGMENT_QUERY_MAPS` in `dags/signal2segment/config.py`
- Lambda configurations are now accessed via `SIGNAL2SEGMENT_LAMBDA_CONFIGS` in `dags/signal2segment/config.py`
- PyGene authentication now uses Airflow connections instead of variables
- Performance settings removed - DAGs should configure performance directly in @etdag decorators
- DAG-specific configurations should be isolated within each DAG folder for scalability
- Universal registry now only contains connections - no universal configurations remain

## 2025-06-30 - DAG Configuration Isolation Refactoring (Previous)

### Description:
Refactored configuration system to isolate DAG-specific logic within individual DAG folders while maintaining universal configuration fields in environment files. This approach ensures scalability with hundreds of future DAGs by keeping DAG-specific validation and helper functions isolated rather than centralized.

### Files Changed:
- `dags/signal2segment/config.py` - Created DAG-specific configuration module with signal2segment validation logic
- `et_config/universal_registry.py` - Simplified to contain only generic configuration access functions, removed DAG-specific logic
- `et_config/__init__.py` - Removed signal2segment-specific imports to maintain DAG isolation
- `dags/signal2segment/tasks/validation.py` - Updated to use DAG-specific configuration functions
- `tests/test_signal2segment_dag_config.py` - Created comprehensive tests for DAG-isolated configuration approach

### Key Changes:
- **DAG Isolation**: Signal2segment-specific functions moved from universal registry to `dags/signal2segment/config.py`
- **Generic Universal Registry**: Universal registry now contains only generic helper functions (get_lambda_configuration, get_pygene_credentials, etc.)
- **Scalable Architecture**: DAG-specific logic isolated within each DAG folder to support hundreds of future DAGs
- **Maintained Universal Fields**: Environment files still contain universal configuration fields (lambda_functions, pygene_credentials, performance_settings, query_maps)

## 2025-06-30 - Universal Configuration Refactoring (Previous)

### Files Changed:
- `et_config/models/environment.py` - Added universal configuration fields (lambda_functions, pygene_credentials, performance_settings, query_maps)
- `et_config/environments/local.py` - Refactored to use universal configuration structure
- `et_config/environments/dev.py` - Refactored to use universal configuration structure
- `et_config/environments/prod.py` - Refactored to use universal configuration structure
- `et_config/universal_registry.py` - Created new universal registry replacing DAG-specific functions
- `et_config/__init__.py` - Updated imports to use universal registry
- `dags/signal2segment/tasks/validation.py` - Updated to use universal configuration functions
- `tests/test_et_config_signal2segment.py` - Updated tests to use universal configuration
- `et_config/models/dag_configs.py` - REMOVED (replaced by universal configuration)
- `et_config/dag_registry.py` - REMOVED (replaced by universal_registry.py)
- `dags/signal2segment/config.py` - REMOVED (unused file)

### Description:
Completed major refactoring to eliminate DAG-specific configuration functions and implement universal configuration pattern. Key changes:
- Removed DAGSpecificConfigs wrapper and DAG-dependent functions
- Integrated lambda, pygene, performance, and query configurations directly into environment files
- Created universal registry functions that work across all DAGs
- Maintained backward compatibility with existing function names
- Simplified configuration structure while preserving all functionality
- All tests passing with new universal configuration system

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### 2025-06-30
**Files changed:**
- `Changelog.md` (new)
- `dags/signal2segment/signal2segment_dag.py` (new)
- `dags/signal2segment/__init__.py` (new)
- `dags/signal2segment/config.py` (new)
- `dags/signal2segment/tasks/__init__.py` (new)
- `dags/signal2segment/tasks/validation.py` (new)
- `dags/signal2segment/tasks/audience_generation.py` (new)
- `dags/signal2segment/tasks/delivery.py` (new)
- `dags/signal2segment/README.md` (new)
- `dags/signal2segment/MIGRATION_PLAN.md` (new)
- `tests/test_signal2segment_dag.py` (new)
- `tests/test_signal2segment_dag_loader.py` (new)

**Description:** ✅ COMPLETED - Full modernization and refactoring of signal2segment DAG
- Created changelog file for tracking project changes
- ✅ Created new DAG structure using @etdag decorator pattern with TaskFlow API
- ✅ Modernized task definitions with @task decorator, proper type hints, and error handling
- ✅ Implemented environment-aware configuration with et_config (lazy loading patterns)
- ✅ Converted to absolute imports for Kubernetes compatibility
- ✅ Added comprehensive documentation and notification system integration
- ✅ Implemented robust validation using AWS Lambda functions
- ✅ Added audience generation with PyGene API integration
- ✅ Created delivery system for attaching audiences to order lines
- ✅ Added structured logging with emojis and performance monitoring
- ✅ Implemented idempotent operations and proper retry logic
- ✅ **REFACTORED** - Moved all configurations to centralized et_config structure
- ✅ Created DAG-specific configuration models (LambdaConfig, PyGeneConfig, etc.)
- ✅ Extended environment configurations with signal2segment-specific settings
- ✅ Updated team mappings for signal2segment notifications
- ✅ Created configuration registry for centralized DAG config access
- ✅ Refactored DAG and tasks to use new et_config structure

