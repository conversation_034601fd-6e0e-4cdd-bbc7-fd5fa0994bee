"""
Platform Starburst UDF Health Check DAG

This DAG validates that essential User Defined Functions (UDFs) are available
and functioning correctly in the Starburst/Trino environment. It provides:

## Features:
- **Environment-aware testing**: Only tests UDFs supported in current environment
- **Comprehensive UDF coverage**: Tests ethash_v1, ethash_v2, and geocodeAddress functions
- **Performance monitoring**: Tracks query execution times and success rates
- **Intelligent alerting**: Environment-specific notification routing
- **Resilient execution**: Graceful handling of unsupported UDFs

## UDF Test Coverage:
- **ethash_v1/v2**: Address hashing functions with both parts and string inputs
- **geocodeAddress**: Geocoding capabilities with real address samples

## Monitoring & Alerting:
- Slack notifications for failures (environment-specific channels)
- OpsGenie integration for production failures
- Performance metrics collection and trending
- Detailed error reporting with context

## Environment Behavior:
- **Local/Dev**: UDFs typically unavailable, tests gracefully skipped
- **Production**: Full UDF testing with comprehensive monitoring

Owner: platform-team
Business Purpose: Monitor critical UDF availability in Starburst for data processing reliability
Dependencies: Starburst/Trino cluster, UDF libraries
SLA: 2 hours (should complete within SLA for timely alerting)
"""

import logging
from typing import Dict

import pendulum
from airflow.decorators import task, task_group
from airflow.utils.trigger_rule import TriggerRule

from et_config import NotificationConfig, get_environment_config
from etdag_v2.decorators import etdag

# Configure logging
logger = logging.getLogger(__name__)


def _get_udf_test_mapping():
    """Get UDF test configuration. Moved to function to avoid top-level data structures."""
    return {
        "ethash_v1": [
            {
                "task_id": "test_ethash_v1_parts",
                "description": "Test ethash_v1 function with address parts",
                "sql": "SELECT ethash_v1('522', 'Market Street', '40202', '', '', 'E', '', '') as hash_result",
                "expected_result_type": "string",
                "timeout_seconds": 30,
                "critical": True,  # Mark as critical for alerting
            },
            {
                "task_id": "test_ethash_v1_string",
                "description": "Test ethash_v1 function with address string",
                "sql": "SELECT ethash_v1('522 E. Market Street, 40202') as hash_result",
                "expected_result_type": "string",
                "timeout_seconds": 30,
                "critical": True,
            },
        ],
        "ethash_v2": [
            {
                "task_id": "test_ethash_v2_parts",
                "description": "Test ethash_v2 function with address parts",
                "sql": "SELECT ethash_v2('522', 'Market Street', '40202', '', '', 'E', '', '') as hash_result",
                "expected_result_type": "string",
                "timeout_seconds": 30,
                "critical": True,
            },
            {
                "task_id": "test_ethash_v2_string",
                "description": "Test ethash_v2 function with address string",
                "sql": "SELECT ethash_v2('522 E. Market Street, 40202') as hash_result",
                "expected_result_type": "string",
                "timeout_seconds": 30,
                "critical": True,
            },
        ],
        "geocodeAddress": [
            {
                "task_id": "test_geocoder_vancouver",
                "description": "Test geocoding with Vancouver address",
                "sql": "SELECT geocodeAddress('6609 NE 71st Ave, Vancouver, WA', ARRAY['etHash','geometryWKB']) as geocode_result",
                "expected_result_type": "array",
                "timeout_seconds": 60,  # Geocoding may take longer
                "critical": True,
            },
            {
                "task_id": "test_geocoder_louisville",
                "description": "Test geocoding with Louisville address",
                "sql": "SELECT geocodeAddress('522 E. Market Street, Louisville, KY 40202', ARRAY['etHash','geometryWKB']) as geocode_result",
                "expected_result_type": "array",
                "timeout_seconds": 60,
                "critical": True,
            },
        ],
    }


def _get_udf_performance_thresholds():
    """Get UDF performance thresholds. Moved to function to avoid top-level data structures."""
    return {
        "ethash_v1": {"max_duration_seconds": 5, "warning_duration_seconds": 2},
        "ethash_v2": {"max_duration_seconds": 5, "warning_duration_seconds": 2},
        "geocodeAddress": {"max_duration_seconds": 15, "warning_duration_seconds": 10},
    }


def _get_notification_config():
    """Get notification configuration. Moved to function to avoid top-level object creation."""
    return NotificationConfig(
        email_on_success=False,
        email_on_failure=False,
        slack_on_success=False,
        slack_on_failure=True,
        team_owner="data-engineering",  # Use team-based routing
        slack_channel_override="#platform-alerts",  # Still allow override
        opsgenie_enabled=True,  # Will only be active in production
    )


@task
def execute_udf_test(test_config: dict, connection_id: str) -> dict:
    """
    Execute a UDF test query using TaskFlow.

    Args:
        test_config: Test configuration containing SQL, description, etc.
        connection_id: Database connection ID

    Returns:
        dict: Test execution result
    """
    import logging

    from airflow.providers.trino.hooks.trino import TrinoHook

    logger = logging.getLogger(__name__)

    task_id = test_config["task_id"]
    sql = test_config["sql"]
    description = test_config["description"]

    logger.info(f"🧪 Executing UDF test: {task_id}")
    logger.info(f"📝 Description: {description}")
    logger.info(f"🔍 SQL: {sql}")

    try:
        # Execute SQL using TrinoHook
        hook = TrinoHook(trino_conn_id=connection_id)
        result = hook.get_records(sql)

        # Log the result
        logger.info(f"✅ Test {task_id} completed successfully")
        logger.info(f"📊 Result: {result}")

        return {
            "task_id": task_id,
            "description": description,
            "sql": sql,
            "result": result,
            "status": "success",
            "row_count": len(result) if result else 0,
        }

    except Exception as e:
        logger.error(f"❌ Test {task_id} failed: {str(e)}")
        return {
            "task_id": task_id,
            "description": description,
            "sql": sql,
            "result": None,
            "status": "failed",
            "error": str(e),
            "row_count": 0,
        }


# Expose constants at module level for backward compatibility
UDF_TEST_MAPPING = _get_udf_test_mapping()
UDF_PERFORMANCE_THRESHOLDS = _get_udf_performance_thresholds()


@task
def execute_udf_test_conditional(udf_name: str, test_config: dict) -> dict:
    """
    Execute a UDF test conditionally based on environment support.

    Args:
        udf_name: Name of the UDF being tested
        test_config: Test configuration containing SQL, description, etc.

    Returns:
        dict: Test execution result
    """
    import logging

    from airflow.providers.trino.hooks.trino import TrinoHook

    from et_config import get_environment_config

    logger = logging.getLogger(__name__)

    # Get environment configuration to check UDF support
    env_config = get_environment_config()

    task_id = test_config["task_id"]
    sql = test_config["sql"]
    description = test_config["description"]

    # Check if UDF is supported in this environment
    if not env_config.supports_udf(udf_name):
        logger.info(
            f"⏭️ Skipping UDF test {task_id}: {udf_name} not supported in {env_config.name}"
        )
        return {
            "task_id": task_id,
            "udf_name": udf_name,
            "description": description,
            "status": "skipped",
            "reason": f"UDF {udf_name} not supported in {env_config.name} environment",
            "row_count": 0,
        }

    logger.info(f"🧪 Executing UDF test: {task_id} for {udf_name}")
    logger.info(f"📝 Description: {description}")
    logger.info(f"🔍 SQL: {sql}")

    try:
        # Execute SQL using TrinoHook
        connection_id = env_config.connections.starburst_conn_id
        hook = TrinoHook(trino_conn_id=connection_id)
        result = hook.get_records(sql)

        # Log the result
        logger.info(f"✅ Test {task_id} completed successfully")
        logger.info(f"📊 Result: {result}")

        return {
            "task_id": task_id,
            "udf_name": udf_name,
            "description": description,
            "sql": sql,
            "result": result,
            "status": "success",
            "row_count": len(result) if result else 0,
        }

    except Exception as e:
        logger.error(f"❌ Test {task_id} failed: {str(e)}")
        return {
            "task_id": task_id,
            "udf_name": udf_name,
            "description": description,
            "sql": sql,
            "result": None,
            "status": "failed",
            "error": str(e),
            "row_count": 0,
        }


@etdag(
    dag_id="starburst_udf_healthcheck",
    owner="analytics-team",
    description="Validates that essential UDFs (ethash_v1, ethash_v2, geocodeAddress) are available in Starburst",
    business_purpose="Monitor critical UDF availability in Starburst for data processing reliability",
    data_sources=["starburst.udfs"],
    downstream_systems=["data-pipelines", "geocoding-services", "enrichment-workflows"],
    tags=[
        "monitoring",
        "starburst",
        "udfs",
        "healthcheck",
    ],
    start_date=pendulum.today("UTC").add(days=-2),
    schedule="0 */2 * * *",  # Every 2 hours
    catchup=False,
    max_active_tasks=3,
    max_active_runs=1,
    default_retries=2,
    sla_minutes=120,  # Should complete within 2 hours
    notification_config=_get_notification_config(),
    doc_md=__doc__,
)
def starburst_udf_healthcheck():
    @task
    def get_environment_info() -> Dict:
        """Get current environment configuration for logging and validation.

        Returns comprehensive environment information including UDF support,
        connection details, and performance thresholds.
        """
        try:
            # Load configuration inside task to avoid top-level execution
            UDF_TEST_MAPPING = _get_udf_test_mapping()
            UDF_PERFORMANCE_THRESHOLDS = _get_udf_performance_thresholds()

            env_config = get_environment_config()
            supported_udfs = []
            unsupported_udfs = []
            udf_configs = {}

            # Check which UDFs are supported and gather their configurations
            for udf_name, test_configs in UDF_TEST_MAPPING.items():
                if env_config.supports_udf(udf_name):
                    supported_udfs.append(udf_name)
                    udf_configs[udf_name] = {
                        "test_count": len(test_configs),
                        "critical_tests": sum(
                            1
                            for config in test_configs
                            if config.get("critical", False)
                        ),
                        "max_timeout": max(
                            config.get("timeout_seconds", 30) for config in test_configs
                        ),
                        "performance_thresholds": UDF_PERFORMANCE_THRESHOLDS.get(
                            udf_name, {}
                        ),
                    }
                else:
                    unsupported_udfs.append(udf_name)

            logger.info(f"🔍 Running UDF health check in {env_config.name} environment")
            logger.info(
                f"🔗 Using Starburst connection: {env_config.connections.starburst_conn_id}"
            )
            logger.info(f"📊 Target catalog: {env_config.trino_catalog}")
            logger.info(f"✅ Supported UDFs ({len(supported_udfs)}): {supported_udfs}")
            logger.info(
                f"⏭️  Unsupported UDFs ({len(unsupported_udfs)}): {unsupported_udfs}"
            )

            # Log performance expectations
            for udf_name in supported_udfs:
                config = udf_configs[udf_name]
                logger.info(
                    f"📈 {udf_name}: {config['test_count']} tests, "
                    f"{config['critical_tests']} critical, "
                    f"max timeout: {config['max_timeout']}s"
                )

            return {
                "environment": env_config.name,
                "connection": env_config.connections.starburst_conn_id,
                "catalog": env_config.trino_catalog,
                "timeout": env_config.query_timeout,
                "supported_udfs": supported_udfs,
                "unsupported_udfs": unsupported_udfs,
                "udf_configs": udf_configs,
                "total_tests": sum(
                    len(configs)
                    for udf, configs in UDF_TEST_MAPPING.items()
                    if udf in supported_udfs
                ),
                "critical_tests": sum(
                    config["critical_tests"] for config in udf_configs.values()
                ),
            }
        except Exception as e:
            logger.error(f"❌ Failed to get environment configuration: {e}")
            raise

    @task
    def create_udf_test_tasks(env_info):
        """Create dynamic UDF test tasks based on environment support."""
        # Load configuration inside task to avoid top-level execution
        UDF_TEST_MAPPING = _get_udf_test_mapping()

        supported_udfs = env_info["supported_udfs"]
        unsupported_udfs = env_info["unsupported_udfs"]
        connection_id = env_info["connection"]

        # Return the configuration for downstream tasks to use
        test_plan = {
            "supported_udfs": supported_udfs,
            "unsupported_udfs": unsupported_udfs,
            "connection_id": connection_id,
            "udf_test_mapping": UDF_TEST_MAPPING,
        }

        logger.info(f"Created test plan for {len(supported_udfs)} supported UDFs")
        logger.info(f"Skipping {len(unsupported_udfs)} unsupported UDFs")

        return test_plan

    @task_group(group_id="udf_availability_checks")
    def udf_availability_checks():
        """Create UDF test tasks for all known UDFs."""
        # Load configuration at DAG definition time
        UDF_TEST_MAPPING = _get_udf_test_mapping()

        all_tasks = []

        for udf_name, test_configs in UDF_TEST_MAPPING.items():
            logger.info(f"Creating test tasks for {udf_name}")

            for test_config in test_configs:
                # Create TaskFlow task for UDF testing
                test_task = execute_udf_test_conditional.override(
                    task_id=test_config["task_id"],
                    doc_md=f"""
                    ## {test_config["description"]}

                    **Query**:
                    ```sql
                    {test_config["sql"]}
                    ```

                    **Purpose**: Validates that {udf_name} is available and returns valid values.
                    """,
                )(
                    udf_name=udf_name,
                    test_config=test_config,
                )
                all_tasks.append(test_task)

        # Return the tasks for dependency setting (task groups don't need explicit return)
        return all_tasks

    @task(trigger_rule=TriggerRule.ALL_DONE)
    def validate_udf_availability(env_info, test_tasks) -> Dict:
        """Final validation with comprehensive reporting and performance analysis.

        Analyzes test results, performance metrics, and provides detailed
        health status with actionable insights.
        """
        # Load configuration inside task to avoid top-level execution
        UDF_TEST_MAPPING = _get_udf_test_mapping()

        environment = env_info["environment"]
        supported_udfs = env_info["supported_udfs"]
        unsupported_udfs = env_info["unsupported_udfs"]
        total_tests = env_info.get("total_tests", 0)
        critical_tests = env_info.get("critical_tests", 0)

        logger.info(f"🏁 UDF tests completed in {environment} environment")
        logger.info(
            f"📊 Test Summary: {total_tests} total tests, {critical_tests} critical"
        )

        # Determine overall health status
        health_status = "healthy"
        warnings: list[str] = []
        errors: list[str] = []

        # Check if any critical UDFs are unsupported in production
        if environment == "prod" and unsupported_udfs:
            critical_missing = [
                udf
                for udf in unsupported_udfs
                if any(
                    config.get("critical", False)
                    for config in UDF_TEST_MAPPING.get(udf, [])
                )
            ]
            if critical_missing:
                health_status = "degraded"
                errors.append(
                    f"Critical UDFs unavailable in production: {critical_missing}"
                )

        # Performance analysis placeholder (would be enhanced with actual metrics)
        performance_summary = {
            "tests_executed": len(supported_udfs),
            "tests_skipped": len(unsupported_udfs),
            "critical_tests_passed": critical_tests if supported_udfs else 0,
            "environment_coverage": len(supported_udfs) / len(UDF_TEST_MAPPING) * 100,
        }

        # Build comprehensive result
        result = {
            "status": health_status,
            "environment": environment,
            "timestamp": pendulum.now().isoformat(),
            "summary": {
                "supported_udfs_tested": supported_udfs,
                "unsupported_udfs_skipped": unsupported_udfs,
                "total_udfs": len(UDF_TEST_MAPPING),
                "coverage_percentage": performance_summary["environment_coverage"],
            },
            "performance": performance_summary,
            "warnings": warnings,
            "errors": errors,
            "next_check": pendulum.now().add(hours=2).isoformat(),  # Based on schedule
        }

        # Log results with appropriate level
        if health_status == "healthy":
            logger.info(f"✅ UDF Health Check PASSED: {result}")
        elif health_status == "degraded":
            logger.warning(f"⚠️ UDF Health Check DEGRADED: {result}")
        else:
            logger.error(f"❌ UDF Health Check FAILED: {result}")

        # Environment-specific warnings
        if unsupported_udfs:
            if environment == "prod":
                logger.warning(
                    f"🚨 PRODUCTION ALERT: UDFs not available in production environment: "
                    f"{', '.join(unsupported_udfs)}. This may impact data processing pipelines."
                )
            else:
                logger.info(
                    f"ℹ️ Expected: UDFs not tested in {environment} environment: "
                    f"{', '.join(unsupported_udfs)} (normal for non-production)"
                )

        return result

    # Define task dependencies
    env_info_task = get_environment_info()

    # Run UDF test groups with environment-aware checks
    test_group = udf_availability_checks()

    # Final validation - use the task output as input to the validation task
    final_validation = validate_udf_availability(env_info_task, test_group)

    # Set dependencies
    env_info_task >> test_group >> final_validation

    # Return the DAG (not needed with @etdag decorator, but good practice)
    return None
