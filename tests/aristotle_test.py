from airflow.models import DagBag
import unittest


class TestAristotleDAG(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.dagbag = DagBag()
    def test_dag_loaded(self):
        dag = self.dagbag.get_dag(dag_id='aristotle')
        self.assertDictEqual(self.dagbag.import_errors, {})
        self.assertIsNotNone(dag)
        self.assertEqual(len(dag.tasks), 50)

if __name__ == '__main__':
    unittest.main()
