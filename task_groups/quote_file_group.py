"""
Quote File Processing Task Group

This module provides a task group for processing quote files in Airflow,
including target creation, audience creation, and file consolidation.
"""

import base64
import json
import logging
import tempfile
import time
from tempfile import NamedTemporaryFile
from typing import Callable, Dict, List

import backoff
import boto3
import pandas as pd
import requests
from airflow.decorators import task, task_group
from airflow.models import Variable
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.operators.empty import EmptyOperator   ################## FOR AIRFLOW 2
# from airflow.providers.standard.operators.empty import EmptyOperator    #########################UPDATE FOR AIRFLOW 3
# from airflow.sdk import DAG   #########################UPDATE FOR AIRFLOW 3
from airflow import DAG  ################## FOR AIRFLOW 2
from botocore.errorfactory import ClientError as S3ClientError

# Configure logging
logger = logging.getLogger(__name__)

VENV_CACHE_PATH = tempfile.gettempdir()
AWS_REGION = "us-east-1"

# Common requirements for all tasks
REQUIREMENTS = [
    "apache-airflow==3.0.2",
    "apache-airflow-providers-amazon",
    "pandas",
    "s3fs",
    "python-keycloak",
    "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
    "pygene==1.0.41",
]


class QuoteFileProcessingError(Exception):
    """Custom exception for quote file processing errors."""

    pass


# -----------------
# Utility Functions
# -----------------


def get_secret_value(secret: str) -> Dict:
    """
    Retrieve secret value from AWS Secrets Manager.

    Args:
        secret: Name of the secret to retrieve

    Returns:
        Dictionary containing the secret value

    Raises:
        QuoteFileProcessingError: If secret retrieval fails
    """
    try:
        client = boto3.client(
            service_name="secretsmanager",
            region_name=AWS_REGION,
        )
        response = client.get_secret_value(SecretId=secret)

        if "SecretString" in response:
            value = response["SecretString"]
        else:
            value = base64.b64decode(response["SecretBinary"])

        return json.loads(value)
    except Exception as e:
        raise QuoteFileProcessingError(
            f"Failed to retrieve secret '{secret}': {e}"
        ) from e


@backoff.on_exception(
    wait_gen=backoff.expo,
    exception=Exception,
    max_tries=5,
    max_time=120,
)
def poll_target(target_id, target_api):
    """
    Polls for the status of a target until it is ready.

    Args:
        target_id (str): The ID of the target to poll.
        target_api: The target API instance.

    Returns:
        None
    """
    logger.debug("************************************")
    logger.debug(f"Polling for target: {target_id}")
    logger.debug("************************************")
    target_api._poll_for_target_status_ready(target_id)


@backoff.on_exception(
    wait_gen=backoff.expo,
    exception=Exception,
    max_tries=10,
    max_time=60,
)
def poll_audience(audience_id, aud_api, AudienceStatus, get_aud_api):
    """
    Polls for the status of an audience until it is completed or ready.

    If a 503 error is returned from a request a new pygene audience client
    will be instantiated.

    Args:
        audience_id (str): The ID of the audience to poll.
        aud_api: The audience API instance.
        AudienceStatus: The audience status enum.
        get_aud_api: Function to get a new audience API instance.

    Returns:
        The audience object.
    """

    logger.debug("************************************")
    logger.debug("Polling for audience: %s", audience_id)
    logger.debug("************************************")
    audience = aud_api.get_audience(audience_id)
    status = audience.status
    while status not in (AudienceStatus.COMPLETED, AudienceStatus.READY):
        try:
            audience = aud_api.get_audience(audience_id)
            status = audience.status
            logger.debug("Audience %s status: %s", audience_id, status)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                logger.error("Handling 503 error from hagrid server: {err}")
                aud_api = get_aud_api()
            else:
                raise e
        if status == AudienceStatus.ERRORED:
            logger.error("AUDIENCE %s has errored!!!!!", audience_id)
            raise QuoteFileProcessingError(f"AUDIENCE {audience_id} has errored.")
        time.sleep(5)

    return audience


@backoff.on_exception(
    wait_gen=backoff.expo,
    exception=Exception,
    max_tries=5,
    max_time=120,
)
def create_target(dest_path: str, create_target_request: Callable[[str], str]) -> str:
    """
    Creates a target and polls for its status until it is ready.

    Args:
        dest_path (str): The destination path for the target.
        create_target_request (Callable[[str], str]): Function to create the target.

    Returns:
        str: The ID of the created target.
    """
    target_id = None
    while target_id is None:
        try:
            # Create target
            target_id = create_target_request(dest_path)

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                logger.error("Handling 503 error from hagrid server: {err}")
            else:
                raise e

        except Exception as e:
            logger.error("Create target error: {e}!!!")
            raise e

    return target_id


@backoff.on_exception(
    wait_gen=backoff.expo,
    exception=Exception,
    max_tries=5,
    max_time=120,
)
def create_audience(target_id, aud_name, create_audience_request) -> str:
    """
    Creates an audience and polls for its status until it is ready.

    Args:
        target_id (str): The ID of the target.
        aud_name (str): The name of the audience.
        create_audience_request (Callable[[str, str], str]): Function to create the audience.

    Returns:
        str: The ID of the created audience.
    """
    audience_id = None
    while audience_id is None:
        try:
            # Create audience.
            audience_id = create_audience_request(target_id, aud_name)

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                logger.error("Handling 503 error from hagrid server: {err}")
            else:
                raise e

        except Exception as e:
            logger.error(f"Create audience for target {target_id} error: {e}!!!")
            raise e

    return audience_id


# ---------
# Tasks
# ---------


@task.virtualenv(
    requirements=[
        "apache-airflow==3.0.2",
        "apache-airflow-providers-amazon",
        "pandas",
        "s3fs",
        "python-keycloak",
        "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
        "pygene==1.0.41",
        # "-e /opt/airflow/pygene",
    ],
    system_site_packages=True,
    venv_cache_path=VENV_CACHE_PATH,
)
def create_targets_from_file(
    source_file_path: str,
    column_headers: List[Dict[str, str]],
    chunk_size: int,
    target_file_type: str,
    data_source: str,
    org_id: str,
    env: str,
    target_records_cache: bool,
) -> List[str]:
    """
    Creates targets from a source file and uploads them to the target API.

    This function will record and upload the created target ids along with their corresponding
    chunk number in case of a task failure prior to completing all of the file chunks. Before
    the each loop through the file chunks the recorded state will be pulled from S3 if there
    is one and then used to skip chunks that have already been uploaded to the `create_target`
    endpoint. This enables the task to be retried without recreating targets.

    Args:
        source_file_path (str): The path to the source file.
        column_headers (List[Dict[str, str]]): The column headers for the target file.
        chunk_size (int): The size of the chunks to read from the source file.
        target_file_type (str): The type of the target file.
        data_source (str): The data source type.
        org_id (str): The organization ID.
        env (str): The environment (e.g., 'dev', 'prod').

    Returns:
        List[str]: A list of target IDs.
    """
    import json
    import logging

    from airflow.providers.amazon.aws.hooks.s3 import S3Hook
    from pygene.target_api import (
        DataSourceType,
        NextGenTargetAPI,
        TargetFileType,
        TargetStatus,
    )

    from task_groups.quote_file_group import (
        create_target,
        get_secret_value,
        poll_target,
    )

    logging.basicConfig()
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    s3_hook = S3Hook(aws_conn_id="aws_default")
    # s3_client = s3_hook.get_conn()

    filename_parts = source_file_path.split("/")[-1]
    filename = filename_parts.split(".")[0]

    creds = get_secret_value(f"{env}/bi/nextgen")
    get_target_api = lambda: NextGenTargetAPI(
        client_id=creds["CLIENT_ID"],
        client_secret=creds["CLIENT_SECRET"],
        org_id=org_id,
        env=env,
    )

    backup_file_bucket = ""
    backup_file_key = f"fallback/airflow-target-task/{filename}.json"
    backup_file_path = ""
    if env == "dev":
        backup_file_bucket = "et-data-dev"
        backup_file_path = f"s3://et-data-dev/{backup_file_key}"
    if env == "prod":
        backup_file_bucket = "et-data-staging"
        backup_file_path = f"s3://et-data-staging/{backup_file_key}"

    # Retrieve target records if they exist if not set to empty list.
    target_records = []
    if target_records_cache is True:
        try:
            logger.info(f"Fetching target records from {backup_file_path}.")
            res = s3_hook.read_key(key=backup_file_key, bucket_name=backup_file_bucket)
            target_records = json.loads(res)
        except S3ClientError as e:
            logger.warning(e)

    target_ids: list[str] = []

    def create_target_request(dest_path) -> str:
        target_api = get_target_api()
        target = target_api.upload_targets(
            local_file=dest_path,
            header_columns=column_headers,
            file_type=TargetFileType[target_file_type],
            data_source=DataSourceType[data_source],
            poll=False,
        )
        target_id = target.id

        return target_id

    def upload_records():
        logger.info(
            f"Uploading completed audience ids to - s3://{backup_file_bucket}/{backup_file_key}"
        )
        s3_hook.load_string(
            string_data=json.dumps(target_records),
            key=backup_file_key,
            bucket_name=backup_file_bucket,
            replace=True,
        )

    for i, chunk in enumerate(
        pd.read_csv(source_file_path, dtype="string", chunksize=chunk_size)
    ):
        # Check if chunk has already been uploaded.
        if i in [record["target_chunk"] for record in target_records]:
            target_id = [
                r["target_id"] for r in target_records if r["target_chunk"] == i
            ][0]
            logger.warning(
                f"Target chunk {i} with target {target_id} found in target records!!!"
            )
            target = None
            try:
                target_api = get_target_api()
                target = target_api.get_target(target_id)
                target_ids.append(target_id)
            except Exception as e:
                logger.error(e)
                logger.warning(
                    f"Target {target_id} not found re-creating target from chunk {i}, removing from records."
                )
                target_records.remove({"target_chunk": i, "target_id": target_id})

            if target is not None:
                logger.warning("❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆")
                logger.warning(f"Skipping target record {i} in {target_records}")
                logger.warning("❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆")
                continue

        with NamedTemporaryFile(delete=True) as tmp_file:
            dest_path = tmp_file.name
            chunk.to_csv(dest_path, index=False)

            target_id = None
            try:
                target_id = create_target(dest_path, create_target_request)

            except Exception as e:
                logger.error(f"Chunk: {i} for File: {filename} failed.")
                upload_records()
                raise e

            try:
                target_api = get_target_api()
                poll_target(target_id, target_api)
                target_ids.append(target_id)
                target_records.append({"target_id": target_id, "target_chunk": i})
            except Exception as e:
                target_api = get_target_api()
                target = target_api.get_target(target_id)
                if target.status == TargetStatus.ERRORED:
                    upload_records()
                    logger.error(e)
                    raise Exception("Target {target_id} errored!!!")
                else:
                    target_ids.append(target_id)
                    target_records.append({"target_id": target_id, "target_chunk": i})
                    upload_records()
                    raise e

    upload_records()

    return target_ids


@task.virtualenv(
    requirements=[
        "apache-airflow==3.0.2",
        "apache-airflow-providers-amazon",
        "pandas",
        "s3fs",
        "python-keycloak",
        "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
        "pygene==1.0.41",
        # "-e /opt/airflow/pygene",
    ],
    system_site_packages=True,
    venv_cache_path=VENV_CACHE_PATH,
)
def create_audiences_from_targets(
    source_file_path: str,
    target_ids: List[str],
    audience_type: str,
    org_id: str,
    env: str,
) -> List[str]:
    """
    Creates audiences from target IDs and uploads them to the audience API.

    This task will record and upload the created audience ids in case of a task failure
    prior to completing all of the target ids. Before the each loop through the target ids
    the recorded state will be pulled from S3 if there is one and then used to skip target ids
    that have already been used to a create an audience. This enables the task
    to be retried without recreating audiences.

    Args:
        source_file_path (str): The path to the source file.
        target_ids (List[str]): A list of target IDs.
        audience_type (str): The type of the audience.
        org_id (str): The organization ID.
        env (str): The environment (e.g., 'dev', 'prod').

    Returns:
        List[str]: A list of audience IDs.
    """
    import logging

    from pygene.audience_api import (
        AudienceType,
        NextGenAudienceAPI,
    )

    from task_groups.quote_file_group import create_audience, get_secret_value

    logging.basicConfig()
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    s3_hook = S3Hook(aws_conn_id="aws_default")

    filename_parts = source_file_path.split("/")[-1]
    filename = filename_parts.split(".")[0]

    creds = get_secret_value(f"{env}/bi/nextgen")
    get_aud_api = lambda: NextGenAudienceAPI(
        client_id=creds["CLIENT_ID"],
        client_secret=creds["CLIENT_SECRET"],
        org_id=org_id,
        env=env,
    )

    audience_ids = []

    def create_audience_request(target_id: str, audience_name: str):
        aud_api = get_aud_api()

        audience = aud_api.create_audience(
            target_id=target_id,
            audience_type=AudienceType[audience_type],
            name=audience_name,
            poll=False,
        )
        audience_id = audience.id

        return audience_id

    for target_id in target_ids:
        audience_name = f"{filename}_{target_id}"

        # Check if audience has already been created from target id.
        aud_api = get_aud_api()
        audiences = aud_api.list_audiences(filter=f'target_id="{target_id}"')
        if len(audiences) > 0:
            audience_id = audiences[-1].id
            audience_ids.append(audience_id)
            logger.warning("❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆")
            logger.warning("Skipping audience creation for target - %s", target_id)
            logger.warning("❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆ ❆")
            continue

        audience_id = create_audience(target_id, audience_name, create_audience_request)
        audience_ids.append(audience_id)

    return audience_ids


@task.virtualenv(
    requirements=[
        "apache-airflow==3.0.2",
        "apache-airflow-providers-amazon",
        "pandas",
        "s3fs",
        "python-keycloak",
        "--extra-index-url=https://nexus.k8s.eltoro.com/repository/python-hosted/simple/",
        "pygene==1.0.41",
        # "-e /opt/airflow/pygene",
    ],
    system_site_packages=True,
    venv_cache_path=VENV_CACHE_PATH,
)
def consolidate_quote_files(
    annotated_file_path: str,
    selected_file_path: str,
    audience_ids: List[str],
    chunk_size: int,
    org_id: str,
    env: str,
) -> List[str]:
    """
    Polls for audience creation completion, downloads quoted files, consolidates them, and
    uploads the consolidated files to S3.

    Args:
        annotated_file_path (str): The path to the annotated file.
        selected_file_path (str): The path to the selected file.
        audience_ids (List[str]): A list of audience IDs.
        org_id (str): The organization ID.
        env (str): The environment (e.g., 'dev', 'prod').

    Returns:
        List[str]: A list containing the paths to the annotated and selected files.
    """
    import logging
    from io import StringIO

    from airflow.providers.amazon.aws.hooks.s3 import S3Hook
    from pygene.audience_api import AudienceStatus, NextGenAudienceAPI

    from task_groups.quote_file_group import get_secret_value, poll_audience

    logging.basicConfig()
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    s3_hook = S3Hook(aws_conn_id="aws_default")
    s3_client = s3_hook.get_conn()

    creds = get_secret_value(f"{env}/bi/nextgen")
    get_aud_api = lambda: NextGenAudienceAPI(
        client_id=creds["CLIENT_ID"],
        client_secret=creds["CLIENT_SECRET"],
        org_id=org_id,
        env=env,
    )
    aud_api = get_aud_api()

    local_annotated_file_path = NamedTemporaryFile(delete=False).name
    local_selected_file_path = NamedTemporaryFile(delete=False).name
    first_chunk_header = True
    for audience_id in audience_ids:
        # Poll for audience completion. This will raise exception if polling fails.
        audience = poll_audience(audience_id, aud_api, AudienceStatus, get_aud_api)
        if audience.status == AudienceStatus.ERRORED:
            logger.error("Audience {audience_id} is in status 'Errored'!!!!!")
            # [TODO] Consider if this should raise an exception to stop the task.
            continue

        annotated_quoted_file_path = audience.result.annotated_file_location
        selected_quoted_file_path = audience.result.selected_file_location
        if annotated_quoted_file_path is None or selected_quoted_file_path is None:
            raise Exception("Annotated file data is missing.")

        # Fetching file contents and appending to local file.
        for source, dest in (
            (annotated_quoted_file_path, local_annotated_file_path),
            (selected_quoted_file_path, local_selected_file_path),
        ):
            bucket = source.split("/")[0]
            key = "/".join(source.split("/")[1:])

            response = s3_client.get_object(Bucket=bucket, Key=key)
            logger.debug(f"Downloading quoted file: {source}")
            csv_data = pd.read_csv(
                StringIO(response["Body"].read().decode("utf-8")),
                dtype="string",
            )

            # Check if we haven't been returned the full set of records unless it is the last audience which will be less.
            # [TODO] Implement requote when records returned are less than the `chunk_size`.
            if (
                "annotated" in source
                and csv_data.shape[0] < chunk_size
                and audience_id != audience_ids[-1]
            ):
                logger.error(
                    f"Quoted file for audience {audience_id} did not return all records!!"
                )
                logger.error(f"{source} has {csv_data.shape[0]}/{chunk_size} records.")

            csv_data.to_csv(dest, mode="a", header=first_chunk_header, index=False)
            first_chunk_header = False

    # Upload consolidated files to S3.
    for local_path, dest in (
        (local_annotated_file_path, annotated_file_path),
        (local_selected_file_path, selected_file_path),
    ):
        df = pd.read_csv(local_path, dtype="string")
        df.to_csv(dest, index=False)

    return [annotated_file_path, selected_file_path]


# -----------
# Task Group
# -----------


@task_group()
def quote_file(
    source_file_path,
    annotated_file_path,
    selected_file_path,
    org_id,
    column_headers,
    target_file_type: str = "ADDRESS",
    data_source: str = "CLIENT",
    audience_type: str = "B2C",
    chunk_size: int = 50_000,
    env: str = "dev",
    target_records_cache: bool = False,
):
    """
    Defines a task group for processing quote files.

    Args:
        source_file_path: The path to the source file.
        annotated_file_path: The path to the annotated file.
        selected_file_path: The path to the selected file.
        org_id: The organization ID.
        column_headers: The column headers for the target file.
        target_file_type (str): The type of the target file. Default is "ADDRESS".
        data_source (str): The data source type. Default is "CLIENT".
        audience_type (str): The type of the audience. Default is "B2C".
        chunk_size (int): The size of the chunks to read from the source file. Default is 50,000.
        env (str): The environment (e.g., 'dev', 'prod'). Default is "dev".
        target_records_cache (bool): Cache target records in a file so that job can be rerun without
            requoting files.

    Returns:
        None
    """

    # Create targets.
    target_ids = create_targets_from_file(
        source_file_path=source_file_path,
        column_headers=column_headers,
        chunk_size=chunk_size,
        target_file_type=target_file_type,
        data_source=data_source,
        org_id=org_id,
        env=env,
        target_records_cache=target_records_cache,
    )
    # Create audiences.
    audience_ids = create_audiences_from_targets(
        source_file_path=source_file_path,
        target_ids=target_ids,
        audience_type=audience_type,
        org_id=org_id,
        env=env,
    )
    # # Consolidate and upload quoted files.
    quote_files = consolidate_quote_files(
        annotated_file_path=annotated_file_path,
        selected_file_path=selected_file_path,
        audience_ids=audience_ids,
        chunk_size=chunk_size,
        org_id=org_id,
        env=env,
    )

    target_ids >> audience_ids >> quote_files


# -------------
# Testing Code
# -------------

# This Dag is only defined for use in testing, and shouldn't be parsed by the scheduler in prod.
if Variable.get("environment") == "dev":
    with DAG(
        dag_id="quote_file_dag",
        default_args={
            "owner": "Clay Morton",
            "retries": 0,
        },
        description="Quote file DAG",
        tags=["quote_file"],
    ) as dag:
        end = EmptyOperator(task_id="end")

        quote_file_task = quote_file(
            source_file_path="s3://et-data-dev/staging/new_movers/mover_type=cnm/file_transfer_date=2024-12-03/2024-12-03-cnm.csv",
            annotated_file_path="s3://et-data-dev/external/new_movers_test/annotated/mover_type=cnm/file_transfer_date=2024-12-03/2024-12-03-cnm-annotated.csv",
            selected_file_path="s3://et-data-dev/external/new_movers_test/selected/mover_type=cnm/file_transfer_date=2024-12-03/2024-12-03-cnm-selected.csv",
            org_id="crrgefqmoj5s73bmec4g",  # dev New Mover Data
            # org_id="csch7v3kv6sc73cditg0",  # prod New Mover Date
            column_headers=[
                {"index": 0, "value": "id", "type": "keep"},
                {"index": 1, "value": "zip", "type": "zip"},
                {"index": 2, "value": "full_address", "type": "address1"},
                {"index": 3, "value": "house_number", "type": "keep"},
                {"index": 4, "value": "street_name", "type": "keep"},
                {"index": 5, "value": "street_suffix", "type": "keep"},
                {"index": 6, "value": "city", "type": "city"},
                {"index": 7, "value": "st", "type": "state"},
                {"index": 8, "value": "plus4", "type": "zip4"},
                {"index": 9, "value": "dwellingTypeCode", "type": "keep"},
                {"index": 10, "value": "homeOwnerFlag", "type": "keep"},
                {"index": 11, "value": "income", "type": "keep"},
                {"index": 12, "value": "date", "type": "keep"},
            ],
            data_source="NEWMOVER",
            audience_type="ESCROWMOVER_ADDRESS",
            env="dev",
            chunk_size=340,
        )
        quote_file_task >> end


if __name__ == "__main__":
    logger.setLevel(logging.DEBUG)
    run = dag.test(
        conn_file_path="new_mover/connections.json",
        variable_file_path="new_mover/variables.json",
        mark_success_pattern="wait_for_.*|end",
    )

    quoted_files = run.get_task_instance(
        "quote_file.consolidate_quote_files"
    ).xcom_pull()

    assert quoted_files == [
        "s3://et-data-dev/external/new_movers_test/annotated/mover_type=cnm/file_transfer_date=2024-12-03/2024-12-03-cnm-annotated.csv",
        "s3://et-data-dev/external/new_movers_test/selected/mover_type=cnm/file_transfer_date=2024-12-03/2024-12-03-cnm-selected.csv",
    ]
