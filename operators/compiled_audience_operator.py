import json
import requests
from airflow.models import BaseOperator, Variable
from airflow.utils.decorators import apply_defaults
from pygene.base_api import NextGenBaseAPI
from typing import Optional
import time
from airflow.exceptions import AirflowException
import logging

logger = logging.getLogger(__name__)


"""CompiledAudienceOperator

    This operator is used to efficiently synchronize files between two S3 buckets in different regions using rclone.

    Parameters:
    - name: Name of the compiled audience
    - order_line_ids: Provide a list of order_line_ids
    - operation: calls the method to create or get the compiled audience
    - job_id: Job id of the compiled audience
    - wait_for_job_to_finish: If True, waits for the job to finish
    - waiter_delay: int = 60, ## seconds to wait between each status check
    - waiter_max_attempts: int = 360, ## max number of status checks before failing

    Example usage:

    get_compile_audience = CompiledAudienceOperator(
        task_id="get_audience",
        operation="get",  ## should be a get operation or create operation
        job_id=17,  ## job_id of the compiled audience
        max_active_tis_per_dag=3,
    )

    create_compile_audience = CompiledAudienceOperator(
        task_id="create_compiled_audience",
        operation="create",  ## should be a get operation or create operation
        name="airflow_test",  ## name of the compiled audience
        order_line_ids=["cti7v36moq6s73aia9c0"],  ## list of order_line_ids
        wait_for_job_to_finish=True,
        max_active_tis_per_dag=3,
    )
    """


def make_request_with_token_renewal(method, url, env, max_retries=5, **kwargs):
    if env == "local":
        pygene_env = "prod"
    else:
        pygene_env = env
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{pygene_env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    base_api = NextGenBaseAPI(client_id, client_secret, env=env)
    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }
    kwargs["headers"] = headers

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.request(method, url, timeout=10, **kwargs)
            if base_api._resp_handler(response):
                logger.info(f"📊 Attempt {attempt}: Refreshing token and retrying...")
                base_api.refresh_token()
                headers["Authorization"] = f"Bearer {base_api.access_token}"
                kwargs["headers"] = headers
                response = requests.request(method, url, timeout=10, **kwargs)

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Attempt {attempt} failed: {e}")
            time.sleep(60)
            if attempt == max_retries:
                raise AirflowException(
                    f"❌ Request to {url} failed after {max_retries} attempts: {e}"
                )

    return response


class CompiledAudienceOperator(BaseOperator):
    template_fields = ["name", "order_line_ids"]
    API_ENDPOINTS = {
        "create": "/api/v1/compiled-audiences",
        "get": "/api/v1/compiled-audiences/{job_id}",
    }

    @apply_defaults
    def __init__(
        self,
        *,
        name: Optional[str] = None,
        order_line_ids: Optional[str] = None,  # a list of ol_ids, comma separated
        operation: str = "create",  # "create", "get"
        job_id: Optional[int] = None,
        wait_for_job_to_finish: bool = False,
        waiter_delay: int = 60,
        waiter_max_attempts: int = 20,
        env: str = "dev",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.env = env
        if self.env == "local":
            self.base_url = "http://localhost:8000"
        if self.env == "dev":
            self.base_url = "https://bi-api.k8s.dev.eltoro.com"
        if self.env == "prod":
            self.base_url = "https://bi-api.k8s.eltoro.com"
        self.name = name
        self.order_line_ids = order_line_ids
        self.operation = operation
        self.job_id = job_id
        self.wait_for_job_to_finish = wait_for_job_to_finish
        self.waiter_delay = waiter_delay
        self.waiter_max_attempts = waiter_max_attempts

    def execute(self, context):
        if self.operation == "create":
            return self._create_report()
        elif self.operation == "get":
            return self._get_report()
        else:
            raise ValueError(f"Unsupported operation: {self.operation}")

    def _create_report(self):
        payload = {
            "name": self.name,
            "order_line_ids": self.order_line_ids,
        }
        url = f"{self.base_url}{self.API_ENDPOINTS['create']}"
        logger.info(
            f"🚀 Making POST request to {url} with payload: {json.dumps(payload)}"
        )
        response = make_request_with_token_renewal(
            "POST", url, env=self.env, json=payload
        )
        if response.status_code != 200:
            raise AirflowException(
                f"❌ Create call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()

        if self.wait_for_job_to_finish:
            response_data = self._wait_for_job_to_complete(response_data.get("id"))
            return response_data
        return response_data

    def _get_report(self):
        if not self.job_id:
            raise ValueError("❌ Job ID is required for the 'get' operation.")
        url = f"{self.base_url}{self.API_ENDPOINTS['get'].format(job_id=self.job_id)}"
        logger.info(f"🚀  Making GET request to {url}")
        response = make_request_with_token_renewal("GET", url, env=self.env)
        if response.status_code != 200:
            raise AirflowException(
                f"❌ Get call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()
        logger.info(f"✅ Successfully fetched job: {response_data}")
        return response_data

    def _wait_for_job_to_complete(self, job_id):
        url = f"{self.base_url}{self.API_ENDPOINTS['get'].format(job_id=job_id)}"

        for attempt in range(self.waiter_max_attempts):
            logger.info(
                f"📊 Checking job status for job_id: {job_id}, attempt: {attempt + 1}"
            )
            response = make_request_with_token_renewal("GET", url, env=self.env)
            if response.status_code != 200:
                raise AirflowException(
                    f"❌ Failed to fetch job status: {response.status_code}: {response.text}"
                )
            status = response.json().get("status")
            print(f"Job status: {status}")

            if status == "COMPLETE":
                logger.info(f"✅ Successfully completed job: {job_id}")
                response_data = response.json()
                return response_data
            elif status == "FAILED":
                raise AirflowException("❌ Job failed")

            logger.info(
                f"📊 Waiting {self.waiter_delay} seconds before next status check..."
            )
            time.sleep(self.waiter_delay)

        raise AirflowException(
            f"❌ Job did not complete within {self.waiter_max_attempts * self.waiter_delay} seconds."
        )
