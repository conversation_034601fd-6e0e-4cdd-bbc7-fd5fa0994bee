from airflow.models import BaseOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from typing import List


class S3SyncOperator(BaseOperator):
    template_fields = [
        "source_bucket",
        "dest_bucket",
        "source_prefix",
        "dest_prefix",
        "source_s3_conn",
        "dest_s3_conn",
        "overwrite",
    ]

    def __init__(
        self,
        *,
        source_bucket: str,
        dest_bucket: str,
        source_prefix: str = "",
        dest_prefix: str = "",
        source_s3_conn: str,
        dest_s3_conn: str,
        overwrite: bool = True,
        **kwargs,
    ) -> None:
        super().__init__(**kwargs)
        self.source_bucket = source_bucket
        self.dest_bucket = dest_bucket
        self.source_prefix = source_prefix
        self.dest_prefix = dest_prefix
        self.source_s3_conn = source_s3_conn
        self.dest_s3_conn = dest_s3_conn
        self.overwrite = overwrite

    def _s3_copy_kwargs(self) -> dict:
        if (
            self.dest_s3_conn == "dev_aa_s3_conn"
            or self.dest_s3_conn == "prod_aa_s3_conn"
        ):
            return {
                "ServerSideEncryption": "AES256",
                "ACL": "bucket-owner-full-control",
            }
        return {}

    def list_folders(self) -> List[str]:
        """List all folders or objects in the source S3 bucket."""
        self.log.info("Listing folders and files in source bucket...")
        s3_hook = S3Hook(aws_conn_id=self.source_s3_conn)
        s3_client = s3_hook.get_conn()

        response = s3_client.list_objects_v2(
            Bucket=self.source_bucket,
            Prefix=self.source_prefix or "",
            Delimiter="/",
        )

        folder_paths = [
            prefix["Prefix"] for prefix in response.get("CommonPrefixes", [])
        ]
        top_level_files = [
            obj["Key"]
            for obj in response.get("Contents", [])
            if obj["Key"] != self.source_prefix  # Exclude the prefix itself
        ]

        paths = folder_paths + top_level_files
        self.log.info(f"Found {len(paths)} paths: {paths}")
        return paths

    def check_folder_exists(self, folder_path: str) -> bool:
        """Check if a folder exists in the destination bucket."""
        self.log.info(f"Checking existence of folder: {folder_path}")
        s3_hook = S3Hook(aws_conn_id=self.dest_s3_conn)
        dest_prefix = folder_path.replace(self.source_prefix, self.dest_prefix, 1)

        try:
            s3_client = s3_hook.get_conn()
            response = s3_client.list_objects_v2(
                Bucket=self.dest_bucket,
                Prefix=dest_prefix,
                MaxKeys=1,
            )
            folder_exists = "Contents" in response
            self.log.info(f"Folder {folder_path} exists = {folder_exists}")
            return folder_exists
        except Exception as e:
            self.log.error(f"Error checking folder existence: {e}")
            return False

    def sync_s3_files(self, folder_path: str):
        """Sync files from source to destination bucket."""
        self.log.info(f"Syncing files for folder: {folder_path}")
        source_s3_hook = S3Hook(aws_conn_id=self.source_s3_conn)
        dest_s3_hook = S3Hook(aws_conn_id=self.dest_s3_conn)
        extra = self._s3_copy_kwargs()

        source_keys = source_s3_hook.list_keys(
            bucket_name=self.source_bucket, prefix=folder_path
        )
        dest_keys = dest_s3_hook.list_keys(
            bucket_name=self.dest_bucket, prefix=folder_path
        )

        if not source_keys:
            self.log.info(f"No files found in folder: {folder_path}")
            return

        dest_key_set = set(dest_keys or [])

        for source_key in source_keys:
            dest_key = source_key.replace(self.source_prefix, self.dest_prefix, 1)

            if dest_key not in dest_key_set:
                self.log.info(f"Copying new file: {source_key} to {dest_key}")
                dest_s3_hook.get_conn().copy_object(
                    CopySource={"Bucket": self.source_bucket, "Key": source_key},
                    Bucket=self.dest_bucket,
                    Key=dest_key,
                    **extra,
                )
            else:
                source_obj = source_s3_hook.get_key(
                    key=source_key, bucket_name=self.source_bucket
                )
                dest_obj = dest_s3_hook.get_key(
                    key=dest_key, bucket_name=self.dest_bucket
                )

                if (
                    source_obj.e_tag.strip('"') != dest_obj.e_tag.strip('"')
                    and self.overwrite
                ):
                    self.log.info(f"Updating file: {source_key} to {dest_key}")
                    dest_s3_hook.get_conn().copy_object(
                        CopySource={"Bucket": self.source_bucket, "Key": source_key},
                        Bucket=self.dest_bucket,
                        Key=dest_key,
                        **extra,
                    )
                else:
                    self.log.info(f"File already up-to-date: {source_key}")

    def copy_s3_folder(self, folder_path: str):
        """Copy an entire folder or object from source to destination."""
        self.log.info(f"Copying folder or object: {folder_path}")
        source_s3_hook = S3Hook(aws_conn_id=self.source_s3_conn)
        dest_s3_hook = S3Hook(aws_conn_id=self.dest_s3_conn)
        extra = self._s3_copy_kwargs()

        source_keys = source_s3_hook.list_keys(
            bucket_name=self.source_bucket, prefix=folder_path
        )

        if not source_keys:
            self.log.info(f"No files found in folder: {folder_path}")
            return

        for source_key in source_keys:
            dest_key = source_key.replace(self.source_prefix, self.dest_prefix, 1)
            self.log.info(f"Copying: {source_key} -> {dest_key}")
            dest_s3_hook.get_conn().copy_object(
                CopySource={"Bucket": self.source_bucket, "Key": source_key},
                Bucket=self.dest_bucket,
                Key=dest_key,
                **extra,
            )

    def execute(self, context):
        folder_paths = self.list_folders()

        for folder_path in folder_paths:
            folder_exists = self.check_folder_exists(folder_path)
            if folder_exists:
                self.sync_s3_files(folder_path)
            else:
                self.copy_s3_folder(folder_path)
