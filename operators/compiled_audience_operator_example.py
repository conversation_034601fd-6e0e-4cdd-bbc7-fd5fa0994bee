# from etdag import ETDAG
# from datetime import datetime
# from operators.compiled_audience_operator import CompiledAudienceOperator
# from airflow.decorators import task


# @task
# def look_at_response(response):
#     print(response)
#     print(response.get("id"))


# with ETDAG(
#     dag_id="compiled_operator_test",
#     default_args={"owner": "bp"},
#     catchup=False,
#     is_paused_upon_creation=True,
#     description="test to see if airflow can run compiled audience job",
#     start_date=datetime(2025, 2, 7),
#     max_active_runs=1,
#     tags=["compiled-audience-operator", "team:DND"],
#     et_failure_msg=False,
# ) as dag:

#     get_compile_audience = CompiledAudienceOperator(
#         task_id="get_audience",
#         operation="get",  ## should be a get operation or create operation
#         job_id=17,  ## job_id of the compiled audience
#         max_active_tis_per_dag=3,
#     )

#     create_compile_audience = CompiledAudienceOperator(
#         task_id="create_compiled_audience",
#         operation="create",  ## should be a get operation or create operation
#         name="airflow_test",  ## name of the compiled audience
#         order_line_ids=["cti7v36moq6s73aia9c0"],  ## list of order_line_ids
#         wait_for_job_to_finish=True,
#         max_active_tis_per_dag=3,
#     )

#     resp = look_at_response(get_compile_audience.output)

#     get_compile_audience >> resp
#     create_compile_audience >> resp
