# from airflow import DAG
# from airflow.providers.amazon.aws.hooks.s3 import S3Hook
# from airflow.decorators import task, task_group
# from airflow.utils.dates import days_ago
# from datetime import date
# from operators.s3_sync_operator import S3SyncOperator

# # from operators.sync_s3_cross_region_operator import CrossRegionS3SyncOperator

# from etdag import ETDAG

# default_args = {
#     "owner": "BP",
# }

# with ETDAG(
#     dag_id="5x5_sync_operator_test",
#     description="Syncs 5x5 data files between S3 buckets",
#     start_date=days_ago(2),
#     default_args=default_args,
#     schedule_interval="53 14 * * *",  # 10 AM EST
#     params={"run_date": date.today().strftime("%Y%m%d")},
#     catchup=False,
#     et_failure_msg=False,
#     tags=["intent", "5x5"],
# ) as dag:

#     s3_sync = S3SyncOperator(
#         task_id=f"s3_sync",
#         source_bucket="et-datalake-bi-test",
#         dest_bucket="test-vr-mt",
#         source_prefix="5x5/outgoing/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}",
#         dest_prefix="5x5/outgoing/{{ dag_run.conf.get('run_date') or macros.datetime.now().strftime('%Y%m%d') }}",
#         dest_s3_conn="s3_conn",
#         source_s3_conn="s3_conn",
#         overwrite=True,
#     )

# s3_intent_sync = CrossRegionS3SyncOperator(
#     task_id=f"s3_intent_sync",
#     source_bucket="trovo-coop-eltoro",
#     dest_bucket="eltoro-data-sources",
#     source_prefix="outgoing/",
#     dest_prefix="5x5/outgoing/",
#     dest_s3_conn="s3_conn",
#     source_s3_conn="fivexfive_s3_conn",
#     source_region="us-west-2",
#     dest_region="us-east-1",
# )
# s3_sync = CrossRegionS3SyncOperator(
#     task_id=f"s3_sync",
#     source_bucket="trovo-coop-fe-sync",
#     source_prefix="incoming/AIGDS/",
#     source_s3_conn="fivexfive_s3_conn",
#     source_region="us-west-2",
#     dest_bucket="eltoro-data-sources",
#     dest_prefix="5x5/outgoing/AIGDS/",
#     dest_s3_conn="s3_conn",
#     dest_region="us-east-1",
# )

# s3_sync = CrossRegionS3SyncOperator(
#     task_id=f"s3_sync",
#     source_bucket="trovo-coop-fe-sync",
#     source_prefix="incoming/AIGDS/",
#     source_s3_conn="fivexfive_s3_conn",
#     source_region="us-west-2",
#     dest_bucket="test-vr-mt",
#     dest_prefix="5x5/outgoing/AIGDS/",
#     dest_s3_conn="s3_conn",
#     dest_region="us-east-1",
# )

# s3_sync = CrossRegionS3SyncOperator(
#     task_id=f"s3_sync",
#     source_bucket="et-datalake-bi-test",
#     source_prefix="outgoing/",
#     source_s3_conn="s3_conn",
#     source_region="us-east-1",
#     dest_bucket="test-vr-mt",
#     dest_prefix="5x5/outgoing/",
#     dest_s3_conn="s3_conn",
#     dest_region="us-east-1",
# )
