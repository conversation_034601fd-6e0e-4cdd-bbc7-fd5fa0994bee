# from etdag import ETDAG
# from datetime import datetime


# from operators.timestamp_report_operator import TimestampReportOperator




# with ETDAG(
#     dag_id="ts_report_operator_test",
#     schedule="0 7 * * *",
#     default_args={"owner": "bp"},
#     catchup=False,
#     is_paused_upon_creation=True,
#     description="test to see if airflow can pass bearer token to bi api",
#     start_date=datetime(2025, 1, 7),
#     max_active_runs=1,
#     params={
#         "bucket_ids": [
#             "auto_dealers",
#             # "EBtGk7DnFuuJwRKvB",
#         ]
#     },
#     tags=["ts-report-operator", "team:DND"],
#     et_failure_msg=False,
# ) as dag:

# create_ts_report = TimestampReportOperator.partial(
#     task_id="run_ts_reports",
#     operation="create",
#     # start="{{ (execution_date - macros.timedelta(days=5)).strftime('%Y-%m-%d') }}",
#     # end="{{ (execution_date - macros.timedelta(days=4)).strftime('%Y-%m-%d') }}",
#     start="2024-12-01",
#     end="2024-12-02",
#     request_types=[
#         # TimestampReportOperator.REQ_TYPE_ADDRESS_TO_DEVICE,
#         TimestampReportOperator.REQ_TYPE_OBSERVATIONS,
#     ],
#     geocode=True,
#     state="AK",
#     reduce_to_date=True,
#     wait_for_job_to_finish=True,
#     max_active_tis_per_dag=3,
# ).expand(bucket_id=dag.params["bucket_ids"])
# create_ts_report

# list_ts_report = TimestampReportOperator(
#     task_id="list_timestamp_reports",
#     operation="list",
#     max_active_tis_per_dag=3,
# )
# list_ts_report

# get_report = TimestampReportOperator(
#     task_id="get_timestamp_report",
#     job_id=12211,
#     operation="get",
# )
# get_report

# cancel_report = TimestampReportOperator(
#     task_id="cancel_timestamp_report",
#     job_id=12218,
#     operation="cancel",
# )
