from airflow.models import BaseOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from rclone_python import rclone
import logging
from datetime import timed<PERSON><PERSON>
from airflow.exceptions import AirflowFailException
from typing import Dict
from airflow.models import Variable
import time
import botocore.credentials
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from typing import Any


logger = logging.getLogger(__name__)

"""CrossRegionS3SyncOperator

    This operator is used to efficiently synchronize files between two S3 buckets in different regions using rclone.

    Parameters:
    - source_bucket (str): The name of the source S3 bucket.
    - dest_bucket (str): The name of the destination S3 bucket.
    - source_prefix (str, optional): The prefix to filter files in the source bucket. Defaults to "".
    - dest_prefix (str, optional): The prefix to add to files in the destination bucket. Defaults to "".
    - source_s3_conn (str): The connection ID for the source S3 bucket.
    - dest_s3_conn (str): The connection ID for the destination S3 bucket.
    - source_region (str, optional): The region of the source S3 bucket. Defaults to "us-east-1".
    - dest_region (str, optional): The region of the destination S3 bucket. Defaults to "us-east-1".

    Example usage:

    sync_operator = CrossRegionS3SyncOperator(
        source_bucket="source-bucket",
        dest_bucket="destination-bucket",
        source_prefix="data/",
        dest_prefix="backup/",
        source_s3_conn="source-connection",
        dest_s3_conn="destination-connection",
        source_region="us-west-2",
        dest_region="eu-west-1",
    """


class CrossRegionS3SyncOperator(BaseOperator):
    template_fields = [
        "source_bucket",
        "dest_bucket",
        "source_prefix",
        "dest_prefix",
        "source_s3_conn",
        "dest_s3_conn",
        "source_region",
        "dest_region",
        "folder",
    ]

    def __init__(
        self,
        *,
        source_bucket: str,
        dest_bucket: str,
        source_prefix: str = "",
        dest_prefix: str = "",
        source_s3_conn: str,
        dest_s3_conn: str,
        source_region: str = "us-east-1",
        dest_region: str = "us-east-1",
        folder: str,
        **kwargs,
    ) -> None:
        super().__init__(**kwargs)
        self.source_bucket = source_bucket
        self.dest_bucket = dest_bucket
        self.source_prefix = source_prefix.rstrip("/")
        self.dest_prefix = dest_prefix.rstrip("/")
        self.source_s3_conn = source_s3_conn
        self.dest_s3_conn = dest_s3_conn
        self.source_region = source_region
        self.dest_region = dest_region
        self.env = Variable.get("environment")
        self.folder = folder

        # Base rclone arguments
        self.rclone_args = [
            "--transfers=16",
            "--checkers=32",
            "--s3-chunk-size=32M",
            "--s3-upload-concurrency=16",
            "--buffer-size=64M",
            "--fast-list",
            "--ignore-existing",
        ]

        if self.env == "dev":
            self.rclone_args.append("--dry-run")

    def s3conn_to_creds(self, s3_creds: str) -> botocore.credentials.Credentials:
        """
        Fetch S3 credentials for a connection.
        """
        s3_hook = S3Hook(s3_creds)
        session = s3_hook.get_session()
        creds = session.get_credentials()
        return creds

    def rclone_conn_str(
        self, creds: botocore.credentials.Credentials, region_name: str
    ) -> str:
        """
        Generate an rclone connection string for S3.
        """
        rclone_conn = (
            f"s3,provider=AWS,env_auth=false,region={region_name},"
            f"access_key_id={creds.access_key},secret_access_key={creds.secret_key}"
        )
        if creds.token:
            rclone_conn += f",session_token={creds.token}"
        return f":{rclone_conn}:"

    def list_files(
        self, aws_conn: str, bucket: str, prefix: str, data_interval_end
    ) -> Dict[str, Dict]:
        """
        Efficiently list files by using pagination and minimizing API calls.
        Returns a dictionary with file metadata for efficient comparison.
        """
        self.log.info(f"Listing files in bucket: {bucket} with prefix: {prefix}")
        s3_hook = S3Hook(aws_conn_id=aws_conn)
        s3_client = s3_hook.get_conn()

        one_week_ago = data_interval_end - timedelta(days=2)

        file_metadata = {}

        paginator = s3_client.get_paginator("list_objects_v2")
        page_iterator = paginator.paginate(
            Bucket=bucket,
            Prefix=prefix,
            PaginationConfig={"PageSize": 1000},
        )

        for page in page_iterator:
            for obj in page.get("Contents", []):
                if obj["LastModified"] >= one_week_ago:
                    key = obj["Key"]
                    if not key.endswith("/"):
                        file_metadata[key] = {
                            "LastModified": obj["LastModified"],
                            "Size": obj["Size"],
                            "ETag": obj["ETag"],
                        }

        return file_metadata

    def batch_copy_folder(
        self,
        source_path: str,
        dest_path: str,
        folder: str,
    ) -> bool:
        """
        Efficiently copy a folder using rclone with optimized settings for cross-region transfer.
        """
        try:

            dest_folder = folder.replace(self.source_prefix, self.dest_prefix, 1)

            logger.info(f"Starting copy of folder: {folder} to {dest_folder}")
            rclone.copy(
                f"{source_path}/{folder}",
                f"{dest_path}/{dest_folder}",
                args=self.rclone_args,
                show_progress=True,
            )
            logger.info(f"✅ Successfully copied folder: {folder}")

            return True
        except Exception as e:
            logger.error(f"Error copying folder {folder}: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Error details: {e.__dict__}")
            return False

    def copy_new_files(self, data_interval_end, init_folder: str) -> None:
        """
        Efficiently copy files cross-region using only rclone copy.
        """
        logger.info("📊 Gathering source and destination bucket statistics...")
        print(init_folder)

        source_metadata = self.list_files(
            aws_conn=self.source_s3_conn,
            bucket=self.source_bucket,
            prefix=init_folder,
            data_interval_end=data_interval_end,
        )
        if "incoming" in init_folder:
            init_folder = init_folder.replace("incoming", "outgoing", 1)
            print(init_folder)
        dest_metadata = self.list_files(
            aws_conn=self.dest_s3_conn,
            bucket=self.dest_bucket,
            prefix=f"5x5/{init_folder}",
            data_interval_end=data_interval_end,
        )

        source_file_count = len(source_metadata)
        dest_file_count = len(dest_metadata)
        file_difference = source_file_count - dest_file_count

        logger.info("📈 Bucket Statistics Summary:")


        if file_difference > 0:
            logger.info(f"🔍 Found {file_difference:,} files to potentially transfer")

        else:
            logger.info("ℹ️ Source and destination have the same number of files")

        source_folders = {}
        for key in source_metadata:
            folder = key.rsplit("/", 1)[0]
            source_folders.setdefault(folder, set()).add(key)

        dest_folders = {}
        for key in dest_metadata:
            folder = key.rsplit("/", 1)[0].replace(
                self.dest_prefix, self.source_prefix, 1
            )
            dest_folders.setdefault(folder, set()).add(
                key.replace(self.dest_prefix, self.source_prefix, 1)
            )

        folders_to_process = []
        files_to_transfer = 0

        for folder in source_folders:
            if folder not in dest_folders:
                files_to_transfer += len(source_folders[folder])
                logger.info(
                    f"New folder detected: {folder} with {len(source_folders[folder])} files"
                )
                folders_to_process.append(folder)
            else:
                # Existing folder - check for missing or updated files
                source_files = source_folders[folder]
                dest_files = dest_folders[folder]

                folder_updates_needed = 0
                needs_update = False

                for file in source_files:
                    dest_file = file.replace(self.source_prefix, self.dest_prefix, 1)
                    source_key = file

                    if source_key not in dest_files:
                        folder_updates_needed += 1
                        needs_update = True
                    elif (
                        source_metadata[file]["LastModified"]
                        > dest_metadata[dest_file]["LastModified"]
                        or source_metadata[file]["ETag"]
                        != dest_metadata[dest_file]["ETag"]
                    ):
                        folder_updates_needed += 1
                        needs_update = True

                if needs_update:
                    files_to_transfer += folder_updates_needed
                    logger.info(
                        f"Existing folder needs updates: {folder} with {folder_updates_needed} files to transfer"
                    )
                    folders_to_process.append(folder)

        logger.info("\n🔄 Transfer Summary:")
        logger.info(f"Total folders to process: {len(folders_to_process):,}")
        logger.info(f"Total files to transfer: {files_to_transfer:,}")
        logger.info("Starting transfer process...\n")

        total_folders = len(folders_to_process)
        if total_folders > 0:
            source_creds = self.s3conn_to_creds(self.source_s3_conn)
            dest_creds = self.s3conn_to_creds(self.dest_s3_conn)

            src_conn_str = self.rclone_conn_str(source_creds, self.source_region)
            dest_conn_str = self.rclone_conn_str(dest_creds, self.dest_region)

            source_path = f"{src_conn_str}{self.source_bucket}"
            dest_path = f"{dest_conn_str}{self.dest_bucket}"

        for index, folder in enumerate(folders_to_process, 1):

            logger.info(f"Processing folder {index}/{total_folders}: {folder}")
            success = self.batch_copy_folder(source_path, dest_path, folder)

            if not success:

                logger.error(f"Failed to process folder: {folder}")
                raise Exception(f"Failed to copy folder: {folder}")


            logger.info(f"Progress: {index}/{total_folders} folders processed")

        total_processed = len(folders_to_process)
        if total_processed > 0:
            logger.info(f"🎉 Transferred approximately {files_to_transfer:,} files")
        else:
            logger.info("No folders needed processing")


        logger.info("Cross-region copy operation complete!")


    def execute(self, context):
        """
        Execute the Airflow task.
        """
        logger.info(f"🚀 Starting S3RcloneSyncOperator in {self.env} environment.")


        data_interval_end = context["data_interval_end"]
        self.copy_new_files(data_interval_end, self.folder)
        logger.info("✅ Execution completed successfully")

