import json
import requests
from airflow.models import BaseOperator, Variable
from airflow.utils.decorators import apply_defaults
from pygene.base_api import NextGenBaseAPI
from typing import List, Optional
import time
from airflow.exceptions import AirflowSkipException, AirflowFailException


def make_request_with_token_renewal(method, url, env, max_retries=5, **kwargs):
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    base_api = NextGenBaseAPI(client_id, client_secret, env=env)

    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }
    kwargs["headers"] = headers

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.request(method, url, timeout=10, **kwargs)
            if base_api._resp_handler(response):
                print(f"Attempt {attempt}: Refreshing token and retrying...")
                base_api.refresh_token()
                headers["Authorization"] = f"Bearer {base_api.access_token}"
                kwargs["headers"] = headers
                response = requests.request(method, url, timeout=10, **kwargs)

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt} failed: {e}")
            time.sleep(180)  # Wait for 3 minutes before retrying
            if attempt == max_retries:
                raise AirflowFailException(
                    f"Request to {url} failed after {max_retries} attempts: {e}"
                )

    return response


class TimestampReportOperator(BaseOperator):
    template_fields = ["bucket_id", "start", "end", "request_types", "job_id"]
    REQ_TYPE_ADDRESS_TO_DEVICE = "addresstodevice"
    REQ_TYPE_OBSERVATIONS = "observations"
    API_ENDPOINTS = {
        "create": "/api/v1/timestamp-report",
        "get": "/api/v1/timestamp-report/{job_id}",
        "cancel": "/api/v1/timestamp-report/cancel/{job_id}",
        "list": "/api/v1/timestamp-report",
    }

    @apply_defaults
    def __init__(
        self,
        *,
        bucket_id: Optional[str] = None,
        start: Optional[str] = None,  # "2024-12-05",
        end: Optional[str] = None,  # "2025-01-02",
        geocode: bool = True,
        request_types: Optional[
            List[str]
        ] = None,  # TimestampReportOperator.REQ_TYPE_ADDRESS_TO_DEVICE, TimestampReportOperator.REQ_TYPE_OBSERVATIONS
        reduce_to_date: Optional[bool] = None,
        state: Optional[str] = None,
        operation: str = "create",  # "create", "get", "cancel", "list"
        job_id: Optional[int] = None,
        page: Optional[int] = 1,  # For list operation
        items_per_page: Optional[int] = 1000,  # For list operation
        wait_for_job_to_finish: bool = False,
        waiter_delay: int = 60,
        waiter_max_attempts: int = 360,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.env = Variable.get("environment")
        if self.env == "dev":
            self.base_url = "https://bi-api.k8s.dev.eltoro.com"
        elif self.env == "prod":
            self.base_url = "https://bi-api.k8s.eltoro.com"
        else:
            # Default to dev for any other environment
            self.base_url = "https://bi-api.k8s.dev.eltoro.com"
            print(f"Warning: Unknown environment '{self.env}', defaulting to dev base_url")
        self.bucket_id = bucket_id
        self.start = start
        self.end = end
        self.request_types = request_types
        self.reduce_to_date = reduce_to_date
        self.geocode = geocode
        self.state = state
        self.operation = operation
        self.job_id = job_id
        self.page = page
        self.items_per_page = items_per_page
        self.wait_for_job_to_finish = wait_for_job_to_finish
        self.waiter_delay = waiter_delay
        self.waiter_max_attempts = waiter_max_attempts

    def execute(self, context):
        if self.operation == "create":
            return self._create_report()
        elif self.operation == "get":
            return self._get_report()
        elif self.operation == "cancel":
            return self._cancel_report()
        elif self.operation == "list":
            return self._list_reports()
        else:
            raise ValueError(f"Unsupported operation: {self.operation}")

    def _create_report(self):
        payload = {
            "bucket_id": self.bucket_id,
            "request_types": self.request_types,
            "start": self.start,
            "end": self.end,
            "geocode": self.geocode,
            "reduce_to_date": self.reduce_to_date,
            "state": self.state,
        }
        url = f"{self.base_url}{self.API_ENDPOINTS['create']}"
        print(f"Making POST request to {url} with payload: {json.dumps(payload)}")
        response = make_request_with_token_renewal(
            "POST", url, env=self.env, json=payload
        )
        if response.status_code != 200:
            raise AirflowFailException(
                f"Create call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()

        if self.wait_for_job_to_finish:
            s3_paths = self._wait_for_job_to_complete(response_data.get("id"))
            return s3_paths
        return response_data

    def _get_report(self):
        if not self.job_id:
            raise ValueError("Job ID is required for the 'get' operation.")
        url = f"{self.base_url}{self.API_ENDPOINTS['get'].format(job_id=self.job_id)}"
        print(f"Making GET request to {url}")
        response = make_request_with_token_renewal("GET", url, env=self.env)
        if response.status_code != 200:
            raise AirflowFailException(
                f"Get call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()
        print(f"Get API response: {response_data}")
        return response_data

    def _cancel_report(self):
        if not self.job_id:
            raise ValueError("Job ID is required for the 'cancel' operation.")
        url = (
            f"{self.base_url}{self.API_ENDPOINTS['cancel'].format(job_id=self.job_id)}"
        )
        print(f"Making POST request to {url} for cancel operation")
        response = make_request_with_token_renewal("POST", url, env=self.env)
        if response.status_code != 200:
            raise AirflowFailException(
                f"Cancel call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()
        print(f"Cancel API response: {response_data}")
        return response_data

    def _list_reports(self):
        print(self.env)
        print(self.base_url)
        url = f"{self.base_url}{self.API_ENDPOINTS['list']}?page={self.page}&items_per_page={self.items_per_page}"
        print(f"Making GET request to {url} for listing reports")
        response = make_request_with_token_renewal("GET", url, env=self.env)
        if response.status_code != 200:
            raise AirflowFailException(
                f"List call failed: {response.status_code}: {response.text}"
            )
        response_data = response.json()
        print(f"List API response: {response_data}")
        return response_data

    def _wait_for_job_to_complete(self, job_id):
        url = f"{self.base_url}{self.API_ENDPOINTS['get'].format(job_id=job_id)}"

        for attempt in range(self.waiter_max_attempts):
            print(f"Checking job status for job_id: {job_id}, attempt: {attempt + 1}")
            response = make_request_with_token_renewal("GET", url, env=self.env)
            if response.status_code != 200:
                raise AirflowFailException(
                    f"Failed to fetch job status: {response.status_code}: {response.text}"
                )
            status = response.json().get("status")
            print(f"Job status: {status}")

            if status == "COMPLETE":
                print("Job completed successfully")
                print(f"locations_s3_path: {response.json().get('locations_s3_path')}")
                print(
                    f"observations_s3_path: {response.json().get('observations_s3_path')}"
                )
                print(
                    f"addresstodevice_s3_path: {response.json().get('addresstodevice_s3_path')}"
                )
                return {
                    "id": response.json().get("id"),
                    "addresstodevice_s3_path": response.json().get(
                        "addresstodevice_s3_path"
                    ),
                    "locations_s3_path": response.json().get("locations_s3_path"),
                    "observations_s3_path": response.json().get("observations_s3_path"),
                }
            elif status == "FAILED":
                raise AirflowFailException("Job failed")

            print(f"Waiting {self.waiter_delay} seconds before next status check...")
            time.sleep(self.waiter_delay)

        raise AirflowFailException(
            f"Job did not complete within {self.waiter_max_attempts * self.waiter_delay} seconds."
        )
