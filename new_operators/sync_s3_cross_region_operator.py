"""Cross-Region S3 Sync Operator for Apache Airflow.

This module provides an operator for efficiently synchronizing files between
S3 buckets across different AWS regions using rclone.
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, <PERSON>ple

import botocore.credentials
import botocore.exceptions
from airflow.exceptions import AirflowException
from airflow.models import BaseOperator, Variable
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from rclone_python import rclone

# Constants
DEFAULT_REGION = "us-east-1"
DEFAULT_LOOKBACK_DAYS = 2
DEFAULT_PAGE_SIZE = 1000

# RClone optimization settings
RCLONE_DEFAULT_ARGS = [
    "--transfers=16",
    "--checkers=32",
    "--s3-chunk-size=32M",
    "--s3-upload-concurrency=16",
    "--buffer-size=64M",
    "--fast-list",
    "--ignore-existing",
]

logger = logging.getLogger(__name__)


class CrossRegionS3SyncOperator(BaseOperator):
    """
    Operator for efficiently synchronizing files between S3 buckets across different regions.

    This operator uses rclone for optimized cross-region transfers and provides
    intelligent file comparison to only transfer new or modified files.

    Args:
        source_bucket: Name of the source S3 bucket
        dest_bucket: Name of the destination S3 bucket
        source_prefix: Prefix to filter files in the source bucket
        dest_prefix: Prefix to add to files in the destination bucket
        source_s3_conn: Connection ID for the source S3 bucket
        dest_s3_conn: Connection ID for the destination S3 bucket
        source_region: AWS region of the source S3 bucket
        dest_region: AWS region of the destination S3 bucket
        folder: Specific folder to sync
        lookback_days: Number of days to look back for file modifications

    Example:
        >>> sync_operator = CrossRegionS3SyncOperator(
        ...     task_id="sync_s3_buckets",
        ...     source_bucket="source-bucket",
        ...     dest_bucket="destination-bucket",
        ...     source_prefix="data/",
        ...     dest_prefix="backup/",
        ...     source_s3_conn="source-connection",
        ...     dest_s3_conn="destination-connection",
        ...     source_region="us-west-2",
        ...     dest_region="eu-west-1",
        ...     folder="incoming/2023/",
        ... )
    """

    template_fields = [
        "source_bucket",
        "dest_bucket",
        "source_prefix",
        "dest_prefix",
        "source_s3_conn",
        "dest_s3_conn",
        "source_region",
        "dest_region",
        "folder",
    ]

    def __init__(
        self,
        *,
        source_bucket: str,
        dest_bucket: str,
        source_s3_conn: str,
        dest_s3_conn: str,
        folder: str,
        source_prefix: str = "",
        dest_prefix: str = "",
        source_region: str = DEFAULT_REGION,
        dest_region: str = DEFAULT_REGION,
        lookback_days: int = DEFAULT_LOOKBACK_DAYS,
        **kwargs,
    ) -> None:
        """Initialize the CrossRegionS3SyncOperator."""
        super().__init__(**kwargs)

        # Validate required parameters
        self._validate_init_params(
            source_bucket, dest_bucket, source_s3_conn, dest_s3_conn, folder
        )

        # S3 configuration
        self.source_bucket = source_bucket
        self.dest_bucket = dest_bucket
        self.source_prefix = self._normalize_prefix(source_prefix)
        self.dest_prefix = self._normalize_prefix(dest_prefix)
        self.source_s3_conn = source_s3_conn
        self.dest_s3_conn = dest_s3_conn
        self.source_region = source_region
        self.dest_region = dest_region
        self.folder = folder
        self.lookback_days = lookback_days

        # Environment configuration
        self.env = self._get_environment()

        # RClone configuration
        self.rclone_args = self._build_rclone_args()

    @staticmethod
    def _validate_init_params(
        source_bucket: str,
        dest_bucket: str,
        source_s3_conn: str,
        dest_s3_conn: str,
        folder: str,
    ) -> None:
        """Validate required initialization parameters."""
        required_params = {
            "source_bucket": source_bucket,
            "dest_bucket": dest_bucket,
            "source_s3_conn": source_s3_conn,
            "dest_s3_conn": dest_s3_conn,
            "folder": folder,
        }

        for param_name, param_value in required_params.items():
            if not param_value or not isinstance(param_value, str):
                raise ValueError(f"Parameter '{param_name}' must be a non-empty string")

    @staticmethod
    def _normalize_prefix(prefix: str) -> str:
        """Normalize S3 prefix by removing trailing slashes."""
        return prefix.rstrip("/") if prefix else ""

    def _get_environment(self) -> str:
        """Get the current environment from Airflow Variables."""
        try:
            return Variable.get("environment", default_var="prod")
        except (KeyError, ValueError):
            logger.warning("Failed to get environment variable. Defaulting to 'prod'")
            return "prod"

    def _build_rclone_args(self) -> list:
        """Build rclone arguments based on environment and configuration."""
        args = RCLONE_DEFAULT_ARGS.copy()

        if self.env == "dev":
            args.append("--dry-run")
            logger.info("Running in dev mode - dry run enabled")

        return args

    def _get_s3_credentials(
        self, connection_id: str
    ) -> botocore.credentials.Credentials:
        """
        Retrieve S3 credentials for a given connection.

        Args:
            connection_id: Airflow connection ID for S3

        Returns:
            AWS credentials object

        Raises:
            AirflowException: If credentials cannot be retrieved
        """
        try:
            s3_hook = S3Hook(connection_id)
            session = s3_hook.get_session()
            credentials = session.get_credentials()

            if not credentials:
                raise AirflowException(
                    "No credentials found for connection: %s" % connection_id
                )

            return credentials
        except (
            botocore.exceptions.ClientError,
            botocore.exceptions.NoCredentialsError,
            ValueError,
        ) as e:
            raise AirflowException(
                "Failed to get S3 credentials for %s: %s" % (connection_id, str(e))
            ) from e

    def _build_rclone_connection_string(
        self, credentials: botocore.credentials.Credentials, region: str
    ) -> str:
        """
        Build rclone connection string for S3.

        Args:
            credentials: AWS credentials
            region: AWS region name

        Returns:
            RClone connection string
        """
        connection_parts = [
            "s3",
            "provider=AWS",
            "env_auth=false",
            f"region={region}",
            f"access_key_id={credentials.access_key}",
            f"secret_access_key={credentials.secret_key}",
        ]

        if credentials.token:
            connection_parts.append(f"session_token={credentials.token}")

        return f":{','.join(connection_parts)}:"

    def _list_bucket_files(
        self, connection_id: str, bucket: str, prefix: str, data_interval_end
    ) -> Dict[str, Dict]:
        """
        Efficiently list files in S3 bucket using pagination.

        Args:
            connection_id: Airflow S3 connection ID
            bucket: S3 bucket name
            prefix: S3 prefix to filter files
            data_interval_end: End of data interval for filtering files

        Returns:
            Dictionary mapping file keys to their metadata
        """
        self.log.info("Listing files in s3://%s/%s", bucket, prefix)

        try:
            s3_hook = S3Hook(aws_conn_id=connection_id)
            s3_client = s3_hook.get_conn()

            # Calculate cutoff date for file filtering
            cutoff_date = data_interval_end - timedelta(days=self.lookback_days)

            file_metadata = {}

            paginator = s3_client.get_paginator("list_objects_v2")
            page_iterator = paginator.paginate(
                Bucket=bucket,
                Prefix=prefix,
                PaginationConfig={"PageSize": DEFAULT_PAGE_SIZE},
            )

            files_processed = 0
            for page in page_iterator:
                for obj in page.get("Contents", []):
                    # Filter by modification date and exclude directories
                    if obj["LastModified"] >= cutoff_date and not obj["Key"].endswith(
                        "/"
                    ):
                        file_metadata[obj["Key"]] = {
                            "LastModified": obj["LastModified"],
                            "Size": obj["Size"],
                            "ETag": obj["ETag"],
                        }
                        files_processed += 1

            self.log.info(
                "Found %s files in s3://%s/%s", f"{files_processed:,}", bucket, prefix
            )
            return file_metadata

        except (
            botocore.exceptions.ClientError,
            botocore.exceptions.NoCredentialsError,
        ) as e:
            raise AirflowException(
                "Failed to list files in s3://%s/%s: %s" % (bucket, prefix, str(e))
            ) from e

    def _copy_folder_with_rclone(
        self,
        source_path: str,
        dest_path: str,
        folder: str,
    ) -> bool:
        """
        Copy a folder using rclone with optimized settings.

        Args:
            source_path: Source path for rclone
            dest_path: Destination path for rclone
            folder: Folder to copy

        Returns:
            True if successful, False otherwise
        """
        try:
            dest_folder = folder.replace(self.source_prefix, self.dest_prefix, 1)

            source_full_path = f"{source_path}/{folder}"
            dest_full_path = f"{dest_path}/{dest_folder}"

            self.log.info("Copying folder: %s -> %s", folder, dest_folder)

            rclone.copy(
                source_full_path,
                dest_full_path,
                args=self.rclone_args,
                show_progress=True,
            )

            self.log.info("✅ Successfully copied folder: %s", folder)
            return True

        except (OSError, RuntimeError, ValueError) as e:
            self.log.error("❌ Failed to copy folder %s: %s", folder, str(e))
            self.log.error("Error type: %s", type(e).__name__)
            return False

    def _process_folder_prefix(self, folder: str) -> str:
        """Process folder prefix for destination mapping."""
        processed_folder = folder
        if "incoming" in folder:
            processed_folder = folder.replace("incoming", "outgoing", 1)
        return processed_folder

    def _analyze_sync_requirements(
        self, source_metadata: Dict, dest_metadata: Dict
    ) -> Tuple[list, int]:
        """
        Analyze which folders need synchronization.

        Returns:
            Tuple of (folders_to_process, estimated_files_to_transfer)
        """
        # Group files by folder
        source_folders = {}
        for key in source_metadata:
            folder = key.rsplit("/", 1)[0]
            source_folders.setdefault(folder, set()).add(key)

        dest_folders = {}
        for key in dest_metadata:
            folder = key.rsplit("/", 1)[0].replace(
                self.dest_prefix, self.source_prefix, 1
            )
            dest_folders.setdefault(folder, set()).add(
                key.replace(self.dest_prefix, self.source_prefix, 1)
            )

        folders_to_process = []
        files_to_transfer = 0

        # Check each source folder
        for folder, files in source_folders.items():
            if folder not in dest_folders:
                # New folder - needs full sync
                files_count = len(files)
                files_to_transfer += files_count
                self.log.info(
                    "New folder detected: %s (%s files)", folder, f"{files_count:,}"
                )
                folders_to_process.append(folder)
            else:
                # Existing folder - check for changes
                folder_changes = self._check_folder_changes(
                    files,
                    dest_folders[folder],
                    source_metadata,
                    dest_metadata,
                )

                if folder_changes > 0:
                    files_to_transfer += folder_changes
                    self.log.info(
                        "Folder needs updates: %s (%s files)",
                        folder,
                        f"{folder_changes:,}",
                    )
                    folders_to_process.append(folder)

        return folders_to_process, files_to_transfer

    def _check_folder_changes(
        self,
        source_files: set,
        dest_files: set,
        source_metadata: Dict,
        dest_metadata: Dict,
    ) -> int:
        """Check how many files in a folder need to be updated."""
        changes_needed = 0

        for file in source_files:
            dest_file = file.replace(self.source_prefix, self.dest_prefix, 1)

            if file not in dest_files:
                changes_needed += 1
            elif dest_file in dest_metadata:
                # Compare modification time and ETag
                source_modified = source_metadata[file]["LastModified"]
                dest_modified = dest_metadata[dest_file]["LastModified"]
                source_etag = source_metadata[file]["ETag"]
                dest_etag = dest_metadata[dest_file]["ETag"]

                if source_modified > dest_modified or source_etag != dest_etag:
                    changes_needed += 1

        return changes_needed

    def _setup_rclone_paths(self) -> Tuple[str, str]:
        """Setup RClone source and destination paths."""
        try:
            source_creds = self._get_s3_credentials(self.source_s3_conn)
            dest_creds = self._get_s3_credentials(self.dest_s3_conn)

            src_conn_str = self._build_rclone_connection_string(
                source_creds, self.source_region
            )
            dest_conn_str = self._build_rclone_connection_string(
                dest_creds, self.dest_region
            )

            source_path = f"{src_conn_str}{self.source_bucket}"
            dest_path = f"{dest_conn_str}{self.dest_bucket}"

            return source_path, dest_path
        except (
            botocore.exceptions.ClientError,
            botocore.exceptions.NoCredentialsError,
            ValueError,
        ) as e:
            raise AirflowException("Failed to setup RClone paths: %s" % str(e)) from e

    def _execute_sync_operation(self, data_interval_end, folder: str) -> None:
        """
        Execute the main synchronization operation.

        Args:
            data_interval_end: End of data interval
            folder: Folder to synchronize
        """
        self.log.info("📊 Analyzing synchronization requirements...")

        # Process folder for destination mapping
        processed_folder = self._process_folder_prefix(folder)

        # Get file metadata from both buckets
        source_metadata = self._list_bucket_files(
            connection_id=self.source_s3_conn,
            bucket=self.source_bucket,
            prefix=folder,
            data_interval_end=data_interval_end,
        )

        dest_metadata = self._list_bucket_files(
            connection_id=self.dest_s3_conn,
            bucket=self.dest_bucket,
            prefix=f"5x5/{processed_folder}",
            data_interval_end=data_interval_end,
        )

        # Analyze what needs to be synchronized
        folders_to_process, files_to_transfer = self._analyze_sync_requirements(
            source_metadata, dest_metadata
        )

        # Log statistics
        self._log_sync_statistics(
            source_metadata, dest_metadata, folders_to_process, files_to_transfer
        )

        # Execute the synchronization if needed
        if folders_to_process:
            self._execute_folder_sync(folders_to_process, files_to_transfer)
        else:
            self.log.info("✅ No synchronization required - all files are up to date")

    def _log_sync_statistics(
        self,
        source_metadata: Dict,
        dest_metadata: Dict,
        folders_to_process: list,
        files_to_transfer: int,
    ) -> None:
        """Log synchronization statistics."""
        source_count = len(source_metadata)
        dest_count = len(dest_metadata)
        folder_count = len(folders_to_process)

        self.log.info("📈 Synchronization Analysis:")
        self.log.info("  Source files: %s", f"{source_count:,}")
        self.log.info("  Destination files: %s", f"{dest_count:,}")
        self.log.info("  Folders to process: %s", f"{folder_count:,}")
        self.log.info("  Estimated files to transfer: %s", f"{files_to_transfer:,}")

        if files_to_transfer > 0:
            self.log.info("🔄 Starting synchronization process...")

    def _execute_folder_sync(
        self, folders_to_process: list, estimated_files: int
    ) -> None:
        """Execute the actual folder synchronization."""
        try:
            source_path, dest_path = self._setup_rclone_paths()

            total_folders = len(folders_to_process)
            successful_folders = 0

            for index, folder in enumerate(folders_to_process, 1):
                self.log.info(
                    "Processing folder %s/%s: %s", index, total_folders, folder
                )

                success = self._copy_folder_with_rclone(source_path, dest_path, folder)

                if success:
                    successful_folders += 1
                    self.log.info(
                        "Progress: %s/%s folders processed", index, total_folders
                    )
                else:
                    error_msg = "Failed to copy folder: %s" % folder
                    self.log.error(error_msg)
                    raise AirflowException(error_msg)

            # Log final results
            self.log.info("🎉 Synchronization completed successfully!")
            self.log.info(
                "  Processed %s/%s folders", successful_folders, total_folders
            )
            self.log.info(
                "  Transferred approximately %s files", f"{estimated_files:,}"
            )

        except (OSError, RuntimeError, ValueError, AirflowException) as e:
            raise AirflowException("Synchronization failed: %s" % str(e)) from e

    def execute(self, context):
        """
        Execute the Airflow task.

        Args:
            context: Airflow context dictionary
        """
        self.log.info(
            "🚀 Starting CrossRegionS3SyncOperator in %s environment", self.env
        )

        data_interval_end = context.get("data_interval_end")
        if not data_interval_end:
            raise AirflowException("data_interval_end not found in task context")

        try:
            self._execute_sync_operation(data_interval_end, self.folder)
            self.log.info("✅ Synchronization completed successfully")
        except (OSError, RuntimeError, ValueError, AirflowException) as e:
            self.log.error("❌ Synchronization failed: %s", str(e))
            raise
