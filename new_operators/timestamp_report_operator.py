"""
Refactored TimestampReportOperator for production use.

This module provides a production-ready Airflow operator for interacting with
the Timestamp Report API, with proper error handling, logging, and pythonic design.
"""

import json
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional

import requests
from airflow.exceptions import AirflowFailException
from airflow.models import BaseOperator, Variable
from pygene.base_api import NextGenBaseAPI


class RequestType(Enum):
    """Enum for timestamp report request types."""

    ADDRESS_TO_DEVICE = "addresstodevice"
    OBSERVATIONS = "observations"


class Operation(Enum):
    """Enum for supported operations."""

    CREATE = "create"
    GET = "get"
    CANCEL = "cancel"
    LIST = "list"


class JobStatus(Enum):
    """Enum for job status values."""

    COMPLETE = "COMPLETE"
    FAILED = "FAILED"
    RUNNING = "RUNNING"
    PENDING = "PENDING"


@dataclass
class S3Paths:
    """Data class for S3 paths returned by completed job."""

    addresstodevice_s3_path: Optional[str] = None
    locations_s3_path: Optional[str] = None
    observations_s3_path: Optional[str] = None


class TimestampReportAPIClient:
    """Client for interacting with the Timestamp Report API."""

    API_ENDPOINTS = {
        "create": "/api/v1/timestamp-report",
        "get": "/api/v1/timestamp-report/{job_id}",
        "cancel": "/api/v1/timestamp-report/cancel/{job_id}",
        "list": "/api/v1/timestamp-report",
    }

    DEFAULT_TIMEOUT = 10
    DEFAULT_MAX_RETRIES = 5

    def __init__(self, env: str, logger):
        """Initialize the API client.

        Args:
            env: Environment (dev/prod)
            logger: Logger instance
        """
        self.env = env
        self.logger = logger
        self.base_url = self._get_base_url()

    def _get_base_url(self) -> str:
        """Get the base URL for the given environment."""
        if self.env == "dev":
            return "https://bi-api.k8s.dev.eltoro.com"
        elif self.env == "prod":
            return "https://bi-api.k8s.eltoro.com"
        else:
            raise ValueError(f"Unsupported environment: {self.env}")

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers with bearer token."""
        try:
            pygene_creds = json.loads(
                Variable.get(f"dataservices_gene_creds_{self.env}")
            )
            client_id = pygene_creds["client_id"]
            client_secret = pygene_creds["client_secret"]
            base_api = NextGenBaseAPI(client_id, client_secret, env=self.env)

            return {
                "Authorization": f"Bearer {base_api.access_token}",
                "Content-Type": "application/json",
            }
        except Exception as e:
            raise AirflowFailException(
                f"Failed to get authentication credentials: {e}"
            ) from e

    def make_request(
        self,
        method: str,
        url: str,
        max_retries: int = DEFAULT_MAX_RETRIES,
        timeout: int = DEFAULT_TIMEOUT,
        **kwargs,
    ) -> requests.Response:
        """Make HTTP request with token renewal and retry logic.

        Args:
            method: HTTP method
            url: Request URL
            max_retries: Maximum number of retry attempts
            timeout: Request timeout in seconds
            **kwargs: Additional request parameters

        Returns:
            Response object

        Raises:
            AirflowFailException: If request fails after all retries
        """
        headers = self._get_auth_headers()
        kwargs["headers"] = headers

        last_exception = None

        for attempt in range(1, max_retries + 1):
            try:
                self.logger.info(
                    f"Making {method} request to {url} (attempt {attempt})"
                )
                response = requests.request(method, url, timeout=timeout, **kwargs)

                # Handle token refresh if needed
                if response.status_code == 401:
                    self.logger.info(
                        f"Attempt {attempt}: Token expired, refreshing and retrying..."
                    )
                    headers = self._get_auth_headers()
                    kwargs["headers"] = headers
                    response = requests.request(method, url, timeout=timeout, **kwargs)

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                last_exception = e
                self.logger.warning(f"Attempt {attempt} failed: {e}")
                if attempt < max_retries:
                    time.sleep(2**attempt)  # Exponential backoff

        raise AirflowFailException(
            f"Request to {url} failed after {max_retries} attempts: {last_exception}"
        ) from last_exception


class TimestampReportOperator(BaseOperator):
    """
    Operator for interacting with the Timestamp Report API.

    This operator supports creating, getting, canceling, and listing timestamp reports.
    It includes proper error handling, logging, and waiting for job completion.

    Args:
        bucket_id: The bucket ID for the report
        start: Start date for the report (YYYY-MM-DD format)
        end: End date for the report (YYYY-MM-DD format)
        geocode: Whether to include geocoding in the report
        request_types: List of request types to include
        reduce_to_date: Whether to reduce results to date level
        state: State filter for the report
        operation: Operation to perform (create, get, cancel, list)
        job_id: Job ID for get/cancel operations
        page: Page number for list operation
        items_per_page: Items per page for list operation
        wait_for_job_to_finish: Whether to wait for job completion
        waiter_delay: Delay between status checks in seconds
        waiter_max_attempts: Maximum number of status check attempts
    """

    template_fields = ["bucket_id", "start", "end", "request_types"]

    # Class constants for backwards compatibility
    REQ_TYPE_ADDRESS_TO_DEVICE = RequestType.ADDRESS_TO_DEVICE.value
    REQ_TYPE_OBSERVATIONS = RequestType.OBSERVATIONS.value

    def __init__(
        self,
        *,
        bucket_id: Optional[str] = None,
        start: Optional[str] = None,
        end: Optional[str] = None,
        geocode: bool = True,
        request_types: Optional[List[str]] = None,
        reduce_to_date: Optional[bool] = None,
        state: Optional[str] = None,
        operation: str = Operation.CREATE.value,
        job_id: Optional[int] = None,
        page: int = 1,
        items_per_page: int = 1000,
        wait_for_job_to_finish: bool = False,
        waiter_delay: int = 60,
        waiter_max_attempts: int = 360,
        **kwargs,
    ):
        super().__init__(**kwargs)

        # Validate operation
        if operation not in [op.value for op in Operation]:
            raise ValueError(
                f"Invalid operation: {operation}. Must be one of {[op.value for op in Operation]}"
            )

        self.env = Variable.get("environment")
        self.api_client = TimestampReportAPIClient(self.env, self.log)

        # Report parameters
        self.bucket_id = bucket_id
        self.start = start
        self.end = end
        self.request_types = request_types or []
        self.reduce_to_date = reduce_to_date
        self.geocode = geocode
        self.state = state

        # Operation parameters
        self.operation = operation
        self.job_id = job_id
        self.page = page
        self.items_per_page = items_per_page

        # Waiting parameters
        self.wait_for_job_to_finish = wait_for_job_to_finish
        self.waiter_delay = waiter_delay
        self.waiter_max_attempts = waiter_max_attempts

        # Validate required parameters based on operation
        self._validate_parameters()

    def _validate_parameters(self) -> None:
        """Validate parameters based on the operation type."""
        if self.operation == Operation.CREATE.value:
            if not self.bucket_id:
                raise ValueError("bucket_id is required for create operation")
            if not self.start or not self.end:
                raise ValueError(
                    "start and end dates are required for create operation"
                )

        elif self.operation in [Operation.GET.value, Operation.CANCEL.value]:
            if not self.job_id:
                raise ValueError(f"job_id is required for {self.operation} operation")

    def execute(self, context) -> Dict:
        """Execute the timestamp report operation."""
        self.log.info(f"Executing {self.operation} operation")

        operation_map = {
            Operation.CREATE.value: self._create_report,
            Operation.GET.value: self._get_report,
            Operation.CANCEL.value: self._cancel_report,
            Operation.LIST.value: self._list_reports,
        }

        return operation_map[self.operation]()

    def _create_report(self) -> Dict:
        """Create a new timestamp report."""
        payload = {
            "bucket_id": self.bucket_id,
            "request_types": self.request_types,
            "start": self.start,
            "end": self.end,
            "geocode": self.geocode,
        }

        # Add optional parameters if provided
        if self.reduce_to_date is not None:
            payload["reduce_to_date"] = self.reduce_to_date
        if self.state:
            payload["state"] = self.state

        url = f"{self.api_client.base_url}{self.api_client.API_ENDPOINTS['create']}"
        self.log.info(f"Creating report with payload: {json.dumps(payload, indent=2)}")

        response = self.api_client.make_request("POST", url, json=payload)
        response_data = response.json()

        self.log.info(
            f"Report creation response: {json.dumps(response_data, indent=2)}"
        )

        if self.wait_for_job_to_finish:
            job_id = response_data.get("id")
            if not job_id:
                raise AirflowFailException("No job ID returned from create request")

            s3_paths = self._wait_for_job_to_complete(job_id)
            return s3_paths.__dict__ if isinstance(s3_paths, S3Paths) else s3_paths

        return response_data

    def _get_report(self) -> Dict:
        """Get status and details of a timestamp report."""
        url = f"{self.api_client.base_url}{self.api_client.API_ENDPOINTS['get'].format(job_id=self.job_id)}"
        self.log.info(f"Getting report status for job_id: {self.job_id}")

        response = self.api_client.make_request("GET", url)
        response_data = response.json()

        self.log.info(f"Report status: {json.dumps(response_data, indent=2)}")
        return response_data

    def _cancel_report(self) -> Dict:
        """Cancel a timestamp report."""
        url = f"{self.api_client.base_url}{self.api_client.API_ENDPOINTS['cancel'].format(job_id=self.job_id)}"
        self.log.info(f"Canceling report for job_id: {self.job_id}")

        response = self.api_client.make_request("POST", url)
        response_data = response.json()

        self.log.info(f"Cancel response: {json.dumps(response_data, indent=2)}")
        return response_data

    def _list_reports(self) -> Dict:
        """List timestamp reports with pagination."""
        url = f"{self.api_client.base_url}{self.api_client.API_ENDPOINTS['list']}"
        params = {"page": self.page, "items_per_page": self.items_per_page}

        self.log.info(f"Listing reports with params: {params}")

        response = self.api_client.make_request("GET", url, params=params)
        response_data = response.json()

        self.log.info(f"List response: {json.dumps(response_data, indent=2)}")
        return response_data

    def _wait_for_job_to_complete(self, job_id: int) -> S3Paths:
        """Wait for a job to complete and return S3 paths.

        Args:
            job_id: The job ID to monitor

        Returns:
            S3Paths object with the paths to the generated files

        Raises:
            AirflowFailException: If job fails or times out
        """
        url = f"{self.api_client.base_url}{self.api_client.API_ENDPOINTS['get'].format(job_id=job_id)}"

        self.log.info(f"Waiting for job {job_id} to complete...")
        self.log.info(
            f"Will check every {self.waiter_delay} seconds for up to {self.waiter_max_attempts} attempts"
        )

        for attempt in range(1, self.waiter_max_attempts + 1):
            self.log.info(
                f"Checking job status (attempt {attempt}/{self.waiter_max_attempts})"
            )

            response = self.api_client.make_request("GET", url)
            response_data = response.json()
            status = response_data.get("status")

            self.log.info(f"Job status: {status}")

            if status == JobStatus.COMPLETE.value:
                self.log.info("Job completed successfully!")

                s3_paths = S3Paths(
                    addresstodevice_s3_path=response_data.get(
                        "addresstodevice_s3_path"
                    ),
                    locations_s3_path=response_data.get("locations_s3_path"),
                    observations_s3_path=response_data.get("observations_s3_path"),
                )

                self.log.info(f"S3 paths: {s3_paths}")
                return s3_paths

            elif status == JobStatus.FAILED.value:
                error_msg = response_data.get("error_message", "Unknown error")
                raise AirflowFailException(f"Job {job_id} failed: {error_msg}")

            # Job is still running, wait before next check
            if attempt < self.waiter_max_attempts:
                self.log.info(
                    f"Job still running, waiting {self.waiter_delay} seconds..."
                )
                time.sleep(self.waiter_delay)

        # If we get here, we've exceeded max attempts
        total_time = self.waiter_max_attempts * self.waiter_delay
        raise AirflowFailException(
            f"Job {job_id} did not complete within {total_time} seconds "
            f"({self.waiter_max_attempts} attempts)"
        )
