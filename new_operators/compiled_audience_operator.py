"""
Compiled Audience Operator for Airflow.

This module provides an operator for creating and managing compiled audiences
through the ElToro BI API.
"""

import json
import logging
import time
from enum import Enum
from http import HTTPStatus
from typing import Any, Dict, List, Optional

import requests
from airflow.exceptions import AirflowFailException
from airflow.models import BaseOperator, Variable
from pygene.base_api import NextGenBaseAPI

logger = logging.getLogger(__name__)


class OperationType(Enum):
    """Supported operation types for the CompiledAudienceOperator."""

    CREATE = "create"
    GET = "get"


class JobStatus(Enum):
    """Job status values returned by the API."""

    COMPLETE = "COMPLETE"
    FAILED = "FAILED"
    PENDING = "PENDING"
    RUNNING = "RUNNING"


class Environment(Enum):
    """Supported environments."""

    DEV = "dev"
    PROD = "prod"


# Constants
DEFAULT_WAITER_DELAY = 60  # seconds
DEFAULT_WAITER_MAX_ATTEMPTS = 360  # attempts
DEFAULT_REQUEST_TIMEOUT = 10  # seconds
DEFAULT_MAX_RETRIES = 5

BASE_URLS = {
    Environment.DEV.value: "https://bi-api.k8s.dev.eltoro.com",
    Environment.PROD.value: "https://bi-api.k8s.eltoro.com",
}

API_ENDPOINTS = {
    OperationType.CREATE.value: "/api/v1/compiled-audiences",
    OperationType.GET.value: "/api/v1/compiled-audiences/{job_id}",
}


class CompiledAudienceAPIError(AirflowFailException):
    """Custom exception for Compiled Audience API errors."""

    pass


def make_request_with_token_renewal(
    method: str,
    url: str,
    env: str,
    max_retries: int = DEFAULT_MAX_RETRIES,
    **kwargs: Any,
) -> requests.Response:
    """
    Make HTTP request with automatic token renewal on authentication failure.

    Args:
        method: HTTP method (GET, POST, etc.)
        url: Target URL
        env: Environment (dev/prod)
        max_retries: Maximum number of retry attempts
        **kwargs: Additional arguments passed to requests.request

    Returns:
        Response object from successful request

    Raises:
        CompiledAudienceAPIError: If all retries are exhausted
        ValueError: If credentials are invalid
    """
    try:
        pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
        client_id = pygene_creds["client_id"]
        client_secret = pygene_creds["client_secret"]
    except (KeyError, json.JSONDecodeError) as e:
        raise ValueError(
            f"Invalid credentials configuration for environment '{env}': {e}"
        )

    base_api = NextGenBaseAPI(client_id, client_secret, env=env)
    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }
    kwargs["headers"] = headers

    last_exception = None

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.request(
                method, url, timeout=DEFAULT_REQUEST_TIMEOUT, **kwargs
            )

            # Handle token refresh if needed
            if base_api._resp_handler(response):
                logger.info("� Attempt %d: Refreshing token and retrying...", attempt)
                base_api.refresh_token()
                headers["Authorization"] = f"Bearer {base_api.access_token}"
                kwargs["headers"] = headers
                response = requests.request(
                    method, url, timeout=DEFAULT_REQUEST_TIMEOUT, **kwargs
                )

            response.raise_for_status()
            return response

        except requests.exceptions.RequestException as e:
            last_exception = e
            logger.error("❌ Attempt %d failed: %s", attempt, e)
            if attempt < max_retries:
                logger.info("⏳ Retrying in a moment...")
                time.sleep(min(2**attempt, 30))  # Exponential backoff, max 30s

    raise CompiledAudienceAPIError(
        f"❌ Request to {url} failed after {max_retries} attempts. Last error: {last_exception}"
    )


class CompiledAudienceOperator(BaseOperator):
    """
    Airflow operator for creating and managing compiled audiences.

    This operator provides functionality to create new compiled audiences or retrieve
    existing ones through the ElToro BI API. It supports waiting for job completion
    and automatic retry logic.

    Args:
        name: Name of the compiled audience (required for create operation)
        order_line_ids: List of order line IDs (required for create operation)
        operation: Operation type - 'create' or 'get'
        job_id: Job ID for get operations
        wait_for_job_to_finish: Whether to wait for job completion
        waiter_delay: Seconds to wait between status checks
        waiter_max_attempts: Maximum number of status check attempts
        env: Environment ('dev' or 'prod')

    Example:
        # Create a new compiled audience
        create_task = CompiledAudienceOperator(
            task_id="create_compiled_audience",
            operation="create",
            name="my_audience",
            order_line_ids=["id1", "id2"],
            wait_for_job_to_finish=True,
            env="prod"
        )

        # Get an existing compiled audience
        get_task = CompiledAudienceOperator(
            task_id="get_audience",
            operation="get",
            job_id=12345,
            env="prod"
        )
    """

    template_fields = ["name", "order_line_ids", "job_id"]

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        order_line_ids: Optional[List[str]] = None,
        operation: str = OperationType.CREATE.value,
        job_id: Optional[int] = None,
        wait_for_job_to_finish: bool = False,
        waiter_delay: int = DEFAULT_WAITER_DELAY,
        waiter_max_attempts: int = DEFAULT_WAITER_MAX_ATTEMPTS,
        env: str = Environment.DEV.value,
        **kwargs: Any,
    ):
        super().__init__(**kwargs)

        # Validate environment
        if env not in [e.value for e in Environment]:
            raise ValueError(
                f"Invalid environment: {env}. Must be one of: {[e.value for e in Environment]}"
            )

        # Validate operation
        if operation not in [op.value for op in OperationType]:
            raise ValueError(
                f"Invalid operation: {operation}. Must be one of: {[op.value for op in OperationType]}"
            )

        self.env = env
        self.base_url = BASE_URLS[env]
        self.name = name
        self.order_line_ids = order_line_ids or []
        self.operation = operation
        self.job_id = job_id
        self.wait_for_job_to_finish = wait_for_job_to_finish
        self.waiter_delay = waiter_delay
        self.waiter_max_attempts = waiter_max_attempts

        # Validate required parameters based on operation
        self._validate_parameters()

    def _validate_parameters(self) -> None:
        """Validate required parameters based on the operation type."""
        if self.operation == OperationType.CREATE.value:
            if not self.name:
                raise ValueError("Parameter 'name' is required for create operation")
            if not self.order_line_ids:
                raise ValueError(
                    "Parameter 'order_line_ids' is required for create operation"
                )
        elif self.operation == OperationType.GET.value:
            if self.job_id is None:
                raise ValueError("Parameter 'job_id' is required for get operation")

    def execute(self, context) -> Dict[str, Any]:
        """
        Execute the operator based on the specified operation.

        Args:
            context: Airflow task context

        Returns:
            Dictionary containing the API response data
        """
        logger.info("🚀 Executing %s operation for compiled audience", self.operation)

        if self.operation == OperationType.CREATE.value:
            return self._create_audience()
        elif self.operation == OperationType.GET.value:
            return self._get_audience()
        else:
            raise ValueError(f"Unsupported operation: {self.operation}")

    def _create_audience(self) -> Dict[str, Any]:
        """
        Create a new compiled audience.

        Returns:
            Dictionary containing the created audience data
        """
        payload = {
            "name": self.name,
            "order_line_ids": self.order_line_ids,
        }

        url = f"{self.base_url}{API_ENDPOINTS[OperationType.CREATE.value]}"
        logger.info("🎯 Creating compiled audience: %s", self.name)
        logger.debug("📋 Payload: %s", json.dumps(payload, indent=2))

        response = make_request_with_token_renewal(
            "POST", url, env=self.env, json=payload
        )

        if response.status_code != HTTPStatus.OK.value:
            raise CompiledAudienceAPIError(
                f"❌ Create operation failed: {response.status_code}: {response.text}"
            )

        response_data = response.json()
        logger.info(
            "✅ Successfully created compiled audience with ID: %s",
            response_data.get("id"),
        )

        if self.wait_for_job_to_finish:
            job_id = response_data.get("id")
            if job_id:
                response_data = self._wait_for_job_completion(job_id)
            else:
                logger.warning("⚠️  No job ID returned from create operation")

        return response_data

    def _get_audience(self) -> Dict[str, Any]:
        """
        Retrieve an existing compiled audience.

        Returns:
            Dictionary containing the audience data
        """
        url = f"{self.base_url}{API_ENDPOINTS[OperationType.GET.value].format(job_id=self.job_id)}"
        logger.info("� Fetching compiled audience with ID: %s", self.job_id)

        response = make_request_with_token_renewal("GET", url, env=self.env)

        if response.status_code != HTTPStatus.OK.value:
            raise CompiledAudienceAPIError(
                f"❌ Get operation failed: {response.status_code}: {response.text}"
            )

        response_data = response.json()
        logger.info(
            "✅ Successfully fetched compiled audience: %s",
            response_data.get("name", "N/A"),
        )

        return response_data

    def _wait_for_job_completion(self, job_id: int) -> Dict[str, Any]:
        """
        Wait for a job to complete by polling its status.

        Args:
            job_id: The job ID to monitor

        Returns:
            Dictionary containing the final job data

        Raises:
            CompiledAudienceAPIError: If job fails or times out
        """
        url = f"{self.base_url}{API_ENDPOINTS[OperationType.GET.value].format(job_id=job_id)}"
        max_wait_time = self.waiter_max_attempts * self.waiter_delay

        logger.info(
            "⏱️  Waiting for job %d to complete (max wait: %d seconds)",
            job_id,
            max_wait_time,
        )

        for attempt in range(1, self.waiter_max_attempts + 1):
            logger.info(
                "📊 Status check %d/%d for job %d",
                attempt,
                self.waiter_max_attempts,
                job_id,
            )

            try:
                response = make_request_with_token_renewal("GET", url, env=self.env)

                if response.status_code != HTTPStatus.OK.value:
                    raise CompiledAudienceAPIError(
                        f"❌ Failed to fetch job status: {response.status_code}: {response.text}"
                    )

                response_data = response.json()
                status = response_data.get("status", "UNKNOWN")

                logger.info("📈 Job %d status: %s", job_id, status)

                if status == JobStatus.COMPLETE.value:
                    logger.info("🎉 Job %d completed successfully", job_id)
                    return response_data
                elif status == JobStatus.FAILED.value:
                    error_msg = response_data.get(
                        "error_message", "No error message provided"
                    )
                    raise CompiledAudienceAPIError(
                        f"❌ Job {job_id} failed: {error_msg}"
                    )

                # Job is still running, wait before next check
                if attempt < self.waiter_max_attempts:
                    logger.info(
                        "⏳ Waiting %d seconds before next status check...",
                        self.waiter_delay,
                    )
                    time.sleep(self.waiter_delay)

            except CompiledAudienceAPIError:
                raise  # Re-raise API errors
            except Exception as e:
                logger.error("💥 Unexpected error during status check: %s", e)
                if attempt == self.waiter_max_attempts:
                    raise CompiledAudienceAPIError(f"Failed to check job status: {e}")

        raise CompiledAudienceAPIError(
            f"⏰ Job {job_id} did not complete within {max_wait_time} seconds"
        )
