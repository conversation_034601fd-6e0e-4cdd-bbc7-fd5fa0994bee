"""
Custom Airflow operators for ElToro DAGs.
"""

# Import custom operators here when they are created
# Example:
# from .custom_operator import CustomOperator

from new_operators.alert_if_skipped_operator import AlertIfSkippedOperator
from new_operators.compiled_audience_operator import CompiledAudienceOperator
from new_operators.gcs_to_s3_operator import GCSToS3Operator
from new_operators.s3_sync_operator import S3SyncOperator
from new_operators.starburst_geocoder_operator import StarburstGeocoderOperator
from new_operators.timestamp_report_operator import TimestampReportOperator

__all__ = [
    "AlertIfSkippedOperator",
    "CompiledAudienceOperator",
    "S3SyncOperator",
    "StarburstGeocoderOperator",
    "GCSToS3Operator",
    "TimestampReportOperator",
]
