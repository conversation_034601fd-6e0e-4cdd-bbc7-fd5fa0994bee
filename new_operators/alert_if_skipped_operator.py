"""
Alert If Skipped Operator for Airflow.

This module provides an operator for monitoring task execution and sending
Slack alerts when tasks haven't run successfully within a specified timeframe.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import pytz
from airflow.exceptions import AirflowException
from airflow.models import <PERSON><PERSON>perator, DagRun, TaskInstance, Variable
from airflow.providers.slack.operators.slack_webhook import SlackWebho<PERSON>Operator
from airflow.utils.session import provide_session

logger = logging.getLogger(__name__)


class AlertIfSkippedOperator(BaseOperator):
    """
    Airflow operator to monitor task execution and send Slack alerts.

    This operator checks if a specified task has been successfully executed
    within a given time period. If the task hasn't run successfully, it sends
    a Slack alert with detailed information.

    The operator performs the following steps:
    1. Queries DAG runs within the specified timeframe (doubled for verification)
    2. Checks if the target task has run successfully in any of those runs
    3. Sends a Slack alert if the task hasn't run successfully

    Args:
        inspected_task_id: The task ID to monitor for successful execution
        days: Number of days to look back for successful task execution
        msg: Custom message to include in the Slack alert
        slack_channel_id: Optional Slack channel ID (uses default if not provided)

    Example:
        alert_task = AlertIfSkippedOperator(
            task_id="check_etl_task",
            inspected_task_id="daily_etl",
            days=2,
            msg="ETL task hasn't run in the expected timeframe",
            slack_channel_id="data-alerts"
        )
    """

    template_fields = ["inspected_task_id", "msg", "slack_channel_id"]

    def __init__(
        self,
        *,
        inspected_task_id: str,
        days: int,
        msg: str,
        slack_channel_id: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        # Validate inputs
        if not inspected_task_id:
            raise ValueError("inspected_task_id cannot be empty")
        if days <= 0:
            raise ValueError("days must be a positive integer")
        if not msg:
            raise ValueError("msg cannot be empty")

        self.inspected_task_id = inspected_task_id
        self.days = days
        self.msg = msg
        self.slack_channel_id = slack_channel_id

    @provide_session
    def execute(self, context, session=None) -> None:
        """
        Check task execution status and send Slack alert if needed.

        Args:
            context: Airflow task execution context
            session: Database session (provided by decorator)

        Raises:
            AirflowException: If unable to determine DAG ID or other critical errors
        """
        if session is None:
            raise AirflowException("Database session is required")

        dag_id = self._get_dag_id(context)
        logger.info(
            "🔍 Checking task '%s' execution status in DAG '%s'",
            self.inspected_task_id,
            dag_id,
        )

        time_bounds = self._calculate_time_bounds()
        dag_runs = self._get_dag_runs(session, dag_id, time_bounds)

        # Skip check if not enough historical data
        if self._should_skip_check(dag_runs, time_bounds["end_date"]):
            logger.info("⏭️  Insufficient historical data, skipping check")
            return

        task_ran_successfully = self._check_task_execution(session, dag_id, dag_runs)

        if task_ran_successfully:
            logger.info(
                "✅ Task '%s' has run successfully in the last %d days",
                self.inspected_task_id,
                self.days,
            )
        else:
            logger.warning(
                "⚠️  Task '%s' has NOT run successfully in the last %d days",
                self.inspected_task_id,
                self.days,
            )
            self._send_slack_alert(context)

    def _get_dag_id(self, context) -> str:
        """Extract DAG ID from context safely."""
        dag = context.get("dag")
        task_instance = context.get("task_instance")

        dag_id = None
        if dag:
            dag_id = dag.dag_id
        elif task_instance:
            dag_id = task_instance.dag_id

        if not dag_id:
            raise AirflowException("Unable to determine dag_id from context")

        return dag_id

    def _calculate_time_bounds(self) -> Dict[str, datetime]:
        """Calculate start and end dates for the query."""
        utc = pytz.UTC
        end_date = datetime.now(utc)
        start_date = end_date - timedelta(days=self.days)
        extended_start_date = start_date - timedelta(days=self.days)

        return {
            "end_date": end_date,
            "start_date": start_date,
            "extended_start_date": extended_start_date,
        }

    def _get_dag_runs(
        self, session, dag_id: str, time_bounds: Dict[str, datetime]
    ) -> List[DagRun]:
        """Query DAG runs within the extended time period."""
        return (
            session.query(DagRun)
            .filter(
                DagRun.dag_id == dag_id,
                DagRun.execution_date.between(
                    time_bounds["extended_start_date"], time_bounds["end_date"]
                ),
            )
            .all()
        )

    def _should_skip_check(self, dag_runs: List[DagRun], end_date: datetime) -> bool:
        """
        Determine if we should skip the check due to insufficient historical data.

        This prevents false alerts during the initial period after DAG deployment.
        """
        if not dag_runs:
            return True

        earliest_run = min(dr.execution_date for dr in dag_runs)
        threshold_date = end_date - timedelta(days=self.days)

        return earliest_run > threshold_date

    def _check_task_execution(
        self, session, dag_id: str, dag_runs: List[DagRun]
    ) -> bool:
        """
        Check if the target task has run successfully in any of the DAG runs.

        Returns:
            True if task ran successfully at least once, False otherwise
        """
        for dag_run in dag_runs:
            task_instances = (
                session.query(TaskInstance)
                .filter(
                    TaskInstance.dag_id == dag_id,
                    TaskInstance.task_id == self.inspected_task_id,
                    TaskInstance.execution_date == dag_run.execution_date,
                )
                .all()
            )

            for task_instance in task_instances:
                logger.debug("📊 Task instance state: %s", task_instance.state)
                if task_instance.state == "success":
                    return True

        return False

    def _send_slack_alert(self, context) -> None:
        """Send Slack alert about the missing task execution."""
        try:
            task_instance = context.get("task_instance")
            execution_date = context.get("execution_date", "Unknown")

            if not task_instance:
                raise AirflowException("No task_instance found in context")

            slack_blocks = self._build_slack_message(
                task_instance.dag_id, execution_date
            )

            environment = Variable.get("environment", default_var="dev")
            slack_conn_id = f"slack_alert_conn_{environment}"

            slack_operator = SlackWebhookOperator(
                task_id="slack_alert_notification",
                slack_webhook_conn_id=slack_conn_id,
                blocks=slack_blocks,
            )

            # Execute the slack operator with proper context handling
            slack_operator.execute(context)
            logger.info("📨 Alert sent to Slack via connection '%s'", slack_conn_id)

        except Exception as e:
            logger.error("💥 Failed to send Slack alert: %s", e)
            raise AirflowException(f"Failed to send Slack alert: {e}") from e

    def _build_slack_message(
        self, dag_id: str, execution_date: Any
    ) -> List[Dict[str, Any]]:
        """Build the Slack message blocks."""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"⚠️ Task Alert: {dag_id}",
                    "emoji": True,
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Execution Date*: {execution_date}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*DAG URL*: https://airflow.k8s.eltoro.com/dags/{dag_id}/grid",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Monitored Task*: `{self.inspected_task_id}`",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Alert Task*: `{self.task_id}`",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Message*: {self.msg}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Timeframe*: Last {self.days} days",
                },
            },
        ]
