# Testing Standards for Airflow DAGs

This document outlines the testing standards and patterns to follow when developing tests for Airflow DAGs in this project. The goal is to ensure consistent, maintainable, and effective test coverage.

## General Principles

1. **Test Organization**
   - One test module per DAG in the appropriate directory
   - Test module name should match the DAG module name being tested (e.g., `test_starburst_udf_healthcheck.py` for testing `starburst_udf_healthcheck.py`)
   - Unit tests in `tests/unit/`
   - Integration tests in `tests/integration/`
   - Aim for at least 50% test coverage

2. **Code Structure**
   - Keep all DAG-specific mocking within the test module itself
   - No DAG-specific content should appear in `fixtures/` or `utils/` directories
   - General-purpose fixtures and utilities only in shared directories

3. **Documentation**
   - Each test module should include a docstring explaining its purpose and organization
   - Test functions should have clear docstrings explaining what they test

## Unit Test Patterns

### Test Module Structure

Follow this pattern for organizing test modules:

```python
"""
Unit tests for [DAG_NAME].

These tests focus on the core logic and configuration of the DAG without
excessive coupling to Airflow internals. Tests are organized to validate:

1. [First area of focus]
2. [Second area of focus]
3. [Third area of focus]

DAG-specific fixtures are contained within this test file, making it
self-contained and independent of conftest.py fixtures.
"""

import os
import sys
from unittest.mock import MagicMock, patch

import pytest

from tests.utils.mock_airflow import MockDagBag

# Add necessary path for imports
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# DAG-specific fixtures
@pytest.fixture
def mock_fixture():
    """
    Description of what this fixture provides.
    """
    # Implementation
    pass
```

### Test Areas to Cover

1. **DAG Structure and Properties**
   - DAG loading without import errors
   - DAG ID, description, schedule, tags, etc.
   - Task presence and relationships

2. **Task Function Logic**
   - Test core function logic independently of Airflow decorators
   - Mock dependencies for isolated testing

3. **Task Dependencies**
   - Validate correct upstream/downstream relationships
   - Check that the full workflow chain is correct

4. **Configuration and Constants**
   - Validate query configurations
   - Test constant definitions

5. **Basic Module Import**
   - Test module can be imported
   - Verify key objects and functions exist

### Mocking S3

When testing code that interacts with S3, use the appropriate fixtures to mock the interactions:

```python
@pytest.fixture
def mock_s3(mocker):
    """
    Mock S3 interactions for unit testing.
    
    This fixture should be used for any test that interacts with S3.
    """
    # Mock boto3 client and resource
    mock_client = mocker.patch('boto3.client')
    mock_resource = mocker.patch('boto3.resource')
    
    # Mock specific S3 operations as needed
    mock_s3_client = mocker.MagicMock()
    mock_client.return_value = mock_s3_client
    
    # Set up any required responses
    mock_s3_client.list_objects_v2.return_value = {
        'Contents': [{'Key': 'test_key', 'Size': 100}]
    }
    
    return mock_s3_client
```

Usage in a test:

```python
def test_s3_interaction(mock_s3):
    # Your test code here
    # The mock_s3 fixture will be injected automatically
    result = your_function_that_uses_s3()
    
    # Assert expected interactions with S3
    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket='your-bucket',
        Prefix='your/prefix'
    )
```

## Integration Testing

Integration tests should validate the DAG works correctly with actual dependencies, using controlled environments:

1. **Setup**
   - Create a dedicated test environment configuration
   - Use localstack or similar tools for AWS service mocking

2. **Test Execution**
   - Test actual DAG execution with minimal mocking
   - Verify database state changes
   - Check output files created

3. **Cleanup**
   - Ensure proper cleanup of test data

## Parameterized Testing

Use parameterized tests to reduce duplication and test multiple scenarios:

```python
@pytest.mark.parametrize(
    "input_param,expected_output",
    [
        ("case1", "result1"),
        ("case2", "result2"),
        ("case3", "result3"),
    ],
)
def test_parameterized_function(input_param, expected_output):
    """Test function with multiple input scenarios."""
    result = function_under_test(input_param)
    assert result == expected_output
```

## Review Checklist

Before submitting tests, review for:

1. **DRY Principle**
   - Remove any duplicated test code
   - Extract common assertions into helper functions

2. **Mocking Consistency**
   - DAG-specific mocks are in the test module
   - Shared mocking utilities are properly used

3. **Coverage**
   - Aim for at least 50% test coverage
   - Cover all critical paths and edge cases

4. **Documentation**
   - All tests are well-documented
   - Purpose of each test is clear

5. **Performance**
   - Tests execute quickly
   - Unnecessary operations are mocked

## Example

See `tests/unit/test_starburst_udf_healthcheck.py` for a reference implementation following these standards.

## Additional Resources

- [pytest Documentation](https://docs.pytest.org/)
- [Airflow Testing Documentation](https://airflow.apache.org/docs/apache-airflow/stable/best-practices.html#testing)