# DAG Development Best Practices

This document outlines the best practices for developing Airflow DAGs in this repository, incorporating official Airflow best practices and our enhanced ETDAG framework patterns.

## Core Principles

### 1. Use @etdag Decorator with TaskFlow API (Recommended)

Always use the modern `@etdag` decorator pattern combined with TaskFlow API for new DAGs:

```python
from etdag.decorators import etdag
from config import NotificationConfig
from airflow.decorators import task
import pendulum

@etdag(
    dag_id="my_dag",
    owner="team-name",
    description="Clear description of what this DAG does",
    business_purpose="Business justification for this DAG",
    data_sources=["source1", "source2"],
    downstream_systems=["system1", "system2"],
    tags=["category", "team", "purpose"],
    start_date=pendulum.today("UTC").add(days=-2),
    schedule="@daily",
    catchup=False,
    max_active_tasks=3,
    max_active_runs=1,
    default_retries=3,
    sla_minutes=120,
    notification_config=NotificationConfig(
        slack_on_failure=True,
        team_owner="team-name"
    ),
    doc_md=__doc__,
)
def my_dag():
    """Define your DAG tasks here using TaskFlow API."""

    @task
    def extract_data() -> dict:
        """Extract data with proper type hints."""
        return {"data": "extracted"}

    @task
    def transform_data(input_data: dict) -> dict:
        """Transform data with automatic XCom handling."""
        return {"transformed": input_data["data"]}

    @task
    def load_data(transformed_data: dict) -> None:
        """Load data to destination."""
        print(f"Loading: {transformed_data}")

    # TaskFlow automatically handles dependencies and data passing
    extracted = extract_data()
    transformed = transform_data(extracted)
    load_data(transformed)
```

**Benefits:**

- Modern Airflow 3.0+ decorator pattern with TaskFlow API
- Automatic XCom handling and data passing
- Type safety with Python type hints
- Automatic environment-aware configuration
- Standardized notification handling
- Enhanced validation and safety checks
- Consistent metadata and documentation
- Better IDE support and debugging

### 1.1. Legacy ETDAG Pattern (Maintenance Only)

For existing DAGs only - **do not use for new development**:

```python
from etdag import ETDAG
from config import NotificationConfig

with ETDAG(
    dag_id="legacy_dag",
    owner="team-name",
    description="Legacy DAG pattern",
    tags=["legacy"],
    # ... other configuration
) as dag:
    # Your tasks here
```

**Migration Note:** All legacy DAGs should be migrated to the `@etdag` decorator pattern with TaskFlow API during maintenance cycles.

### 2. Avoid Top-Level Code (Critical for Performance)

Following official Airflow best practices, avoid expensive operations at the top level of DAG files:

```python
# ❌ BAD: Top-level expensive operations
import pandas as pd  # Heavy import
from some_heavy_library import expensive_function

# This runs every time DAG is parsed (every 30 seconds by default)
expensive_result = expensive_function()
large_dataframe = pd.read_csv("large_file.csv")

@etdag(...)
def my_dag():
    @task
    def process_data():
        # Uses top-level variables - causes parsing overhead
        return expensive_result
```

```python
# ✅ GOOD: Lazy loading and local imports
@etdag(...)
def my_dag():
    @task
    def process_data():
        # Import only when task runs
        import pandas as pd
        from some_heavy_library import expensive_function

        # Execute expensive operations only during task execution
        result = expensive_function()
        df = pd.read_csv("large_file.csv")
        return result
```

### 3. Environment-Aware Configuration

Use the environment configuration system for all environment-specific settings:

```python
def _get_environment_config():
    """Lazy load environment configuration to avoid top-level execution."""
    from config import get_environment_config
    return get_environment_config()

@task
def environment_aware_task():
    """Task that uses environment configuration."""
    env_config = _get_environment_config()

    # Use environment-specific connections
    conn_id = env_config.connections.starburst_conn_id

    # Use environment-specific S3 paths with ObjectStoragePath
    from airflow.sdk import ObjectStoragePath
    base_path = ObjectStoragePath(f"s3://{conn_id}@{env_config.s3.bucket}/my_dag/")

    # Check environment-specific feature availability
    if env_config.supports_udf("my_udf"):
        logger.info("✅ UDF available, proceeding with enhanced processing")
    else:
        logger.info("ℹ️ UDF not available, using fallback processing")
```

**Anti-pattern to avoid:**

```python
# DON'T do this - avoid top-level Variable.get() calls
from airflow.models import Variable
env = Variable.get("environment")  # Network call during DAG parsing!
if env == "prod":
    bucket = "prod-bucket"
else:
    bucket = "dev-bucket"
```

**Best Practice:** Use lazy loading functions and local imports to avoid top-level execution of expensive operations during DAG parsing.

### 4. Always Use Absolute Imports

**Critical for Kubernetes Compatibility:** All imports in Airflow DAGs and modules must use absolute import paths, never relative imports.

#### ❌ BAD: Relative Imports (Will Fail in Kubernetes)

```python
# In config/environments/dev.py
from ..models.environment import EnvironmentConfig  # FAILS in K8s git-sync

# In dags/my_dag/my_dag.py
from .config import MY_CONFIG  # FAILS in K8s git-sync
from .utils import helper_function  # FAILS in K8s git-sync
```

#### ✅ GOOD: Absolute Imports (Works Everywhere)

```python
# In config/environments/dev.py
from config.models.environment import EnvironmentConfig  # ✅ Works in K8s

# In dags/my_dag/my_dag.py
from dags.my_dag.config import MY_CONFIG  # ✅ Works in K8s
from dags.my_dag.utils import helper_function  # ✅ Works in K8s
```

#### Why This Matters

- **Local Development**: Both relative and absolute imports work
- **Kubernetes with git-sync**: Only absolute imports work reliably
- **Error**: `ImportError: attempted relative import with no known parent package`

This happens because git-sync loads DAGs directly from `/opt/airflow/dags/repo/`, and Python doesn't recognize the module hierarchy properly with relative imports in this context.

#### Import Path Examples

```python
# Config modules
from et_config.environments.registry import get_environment_config
from et_config.models.environment import EnvironmentConfig
from et_config.teams.utils import get_team_emails

# DAG modules
from dags.my_dag.config import TABLE_LIST
from dags.my_dag.tasks import extract_data_task
from dags.shared_utils.helpers import common_function

# ETDAG framework
from etdag.decorators import etdag
from etdag.utils import is_production
```

**Rule of Thumb:** If you can't import it from the repository root directory, Airflow can't import it in Kubernetes.

### 5. Comprehensive Documentation

Every DAG should have comprehensive documentation following this template:

```python
"""
DAG Name and Purpose

## Business Purpose
Clear explanation of why this DAG exists and what business value it provides.

## Features
- **Environment-aware processing**: Adapts behavior based on environment
- **Robust error handling**: Comprehensive validation and retry logic
- **Performance monitoring**: Tracks execution times and success rates
- **Intelligent alerting**: Environment-specific notification routing
- **Cloud-native storage**: Uses ObjectStoragePath for portable file operations

## Dependencies
- **Source System**: Description of data source or trigger
- **Target System**: Description of destination system
- **External Services**: Any external APIs or services

## Environment Behavior
- **Local**: Uses LocalStack/mock services with simplified processing
- **Dev**: Uses development connections and reduced data volumes
- **Prod**: Full production processing with all features enabled

## Data Flow
1. Extract: Description of data extraction
2. Transform: Description of data transformation
3. Load: Description of data loading
4. Validate: Description of validation steps

Owner: team-name
SLA: X hours
"""
```

**Note:** The `doc_md=__doc__` parameter in the `@etdag` decorator automatically uses this docstring for DAG documentation in the Airflow UI.

## Task Development Patterns

### 1. Use TaskFlow API with @task Decorator (Required)

Always use the `@task` decorator for new tasks following official Airflow 3.0 best practices:

```python
from airflow.decorators import task
from airflow.sdk import ObjectStoragePath
from typing import Dict, List
import logging
import pendulum

@task
def extract_data() -> Dict:
    """Extract data with proper type hints and documentation.

    Returns:
        Dictionary with extracted data and metadata
    """
    logger = logging.getLogger(__name__)

    try:
        # Simulate data extraction
        data = {"records": [1, 2, 3], "source": "api"}

        logger.info(f"✅ Extracted {len(data['records'])} records from {data['source']}")
        return data

    except Exception as e:
        logger.error(f"❌ Data extraction failed: {e}")
        raise

@task
def transform_data(input_data: Dict) -> Dict:
    """Transform data with automatic XCom handling.

    Args:
        input_data: Dictionary from upstream task (automatically passed via XCom)

    Returns:
        Dictionary with transformed data and metadata
    """
    logger = logging.getLogger(__name__)

    try:
        # Validate input
        if not input_data or "records" not in input_data:
            raise ValueError("Invalid input data: missing 'records' field")

        # Transform data
        transformed_records = [x * 2 for x in input_data["records"]]
        result = {
            "status": "success",
            "records": transformed_records,
            "source": input_data["source"],
            "timestamp": pendulum.now().isoformat()
        }

        logger.info(f"✅ Transformed {len(result['records'])} records")
        return result

    except Exception as e:
        logger.error(f"❌ Data transformation failed: {e}")
        raise

@task
def save_to_storage(data: Dict) -> ObjectStoragePath:
    """Save data to object storage using ObjectStoragePath."""
    logger = logging.getLogger(__name__)

    try:
        # Use ObjectStoragePath for cloud-agnostic storage
        base_path = ObjectStoragePath("s3://aws_default@my-bucket/data/")
        file_path = base_path / f"processed_data_{pendulum.now().format('YYYYMMDD')}.json"

        # Ensure directory exists
        base_path.mkdir(exist_ok=True)

        # Write data
        import json
        with file_path.open("w") as f:
            json.dump(data, f)

        logger.info(f"✅ Saved data to {file_path}")
        return file_path

    except Exception as e:
        logger.error(f"❌ Failed to save data: {e}")
        raise
```

### 2. Handle Conflicting Dependencies with Virtual Environments

For tasks requiring unique dependencies, use PythonVirtualenvOperator as recommended by official Airflow best practices:

```python
@task.virtualenv(
    requirements=["pandas==2.0.0", "scikit-learn==1.3.0"],
    system_site_packages=False
)
def ml_processing_task(data: Dict) -> Dict:
    """Task with specific ML dependencies in isolated environment."""
    # Import inside task to avoid top-level dependency conflicts
    import pandas as pd
    from sklearn.preprocessing import StandardScaler

    # Process data with specific library versions
    df = pd.DataFrame(data["records"])
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(df)

    return {"processed_data": scaled_data.tolist()}

@task.external_python(python="/path/to/specific/python")
def specialized_task(data: Dict) -> Dict:
    """Task using pre-installed Python environment."""
    # Use pre-configured environment with specific dependencies
    import specialized_library

    result = specialized_library.process(data)
    return result
```

### 3. Lazy Loading for Performance

Use lazy loading functions to avoid expensive operations during DAG parsing:

```python
def _get_config():
    """Lazy load configuration to avoid top-level execution."""
    import importlib.util
    import os

    config_path = os.path.join(os.path.dirname(__file__), "config.py")
    spec = importlib.util.spec_from_file_location("config", config_path)
    config = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config)
    return config

def _get_notification_config():
    """Get notification configuration. Moved to function to avoid top-level object creation."""
    return NotificationConfig(
        slack_on_failure=True,
        slack_on_success=False,
        email_on_failure=True,
        team_owner="data-engineering",
    )

@etdag(
    # ... other parameters
    notification_config=_get_notification_config(),  # Lazy loaded
)
def my_dag():
    # Lazy load heavy dependencies inside DAG function
    config = _get_config()
    env_config = _get_environment_config()

    # Use loaded configuration
    tables = config.TABLES
    conn_id = env_config.connections.starburst_conn_id
```

### 4. Robust Error Handling and Task Design

Implement comprehensive error handling following Airflow best practices:

```python
@task(retries=3, retry_delay=timedelta(minutes=2))
def robust_task(input_data: Dict) -> Dict:
    """Task with robust error handling and idempotent design."""
    logger = logging.getLogger(__name__)

    try:
        # Validate inputs first
        if not input_data:
            raise ValueError("Input data cannot be empty")

        # Use data_interval_start for partitioning (Airflow best practice)
        from airflow.sdk import get_current_context
        context = get_current_context()
        partition_date = context["data_interval_start"].strftime("%Y-%m-%d")

        # Main task logic with progress logging
        logger.info(f"🔄 Processing partition {partition_date} for {input_data.get('source', 'unknown')}")

        # Idempotent operation - use UPSERT instead of INSERT
        result = perform_upsert_operation(input_data, partition_date)

        # Validate results
        if not result or "status" not in result:
            raise ValueError("Invalid result format returned from operation")

        if result["status"] != "success":
            raise RuntimeError(f"Operation failed: {result.get('error', 'Unknown error')}")

        logger.info(f"✅ Task completed successfully: {result}")
        return result

    except ConnectionError as e:
        logger.error(f"🔌 Connection error - will retry: {e}")
        raise  # Let Airflow handle retries
    except ValueError as e:
        logger.error(f"📊 Data validation error - no retry: {e}")
        raise  # Don't retry validation errors
    except TimeoutError as e:
        logger.error(f"⏰ Operation timeout - will retry: {e}")
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        logger.error(f"📍 Error type: {type(e).__name__}")
        raise

def perform_upsert_operation(data: Dict, partition: str) -> Dict:
    """Idempotent operation that produces same result on retry."""
    # Use UPSERT/MERGE instead of INSERT to handle retries
    # Read from specific partition, not latest data
    # Write to specific partition based on data_interval_start
    pass
```

### 5. Cloud-Native Storage with ObjectStoragePath

Use ObjectStoragePath for portable, cloud-agnostic file operations:

```python
@task
def save_data_to_cloud(data: Dict) -> ObjectStoragePath:
    """Save data using ObjectStoragePath for cloud portability."""
    from airflow.sdk import ObjectStoragePath
    import json

    # Cloud-agnostic path - works with S3, GCS, Azure Blob
    base_path = ObjectStoragePath("s3://aws_default@my-bucket/data/")

    # Use data_interval_start for partitioning
    from airflow.sdk import get_current_context
    context = get_current_context()
    date_partition = context["data_interval_start"].strftime("%Y/%m/%d")

    file_path = base_path / date_partition / "processed_data.parquet"

    # Ensure directory exists
    file_path.parent.mkdir(parents=True, exist_ok=True)

    # Save as Parquet for efficient storage and querying
    import pandas as pd
    df = pd.DataFrame(data["records"])

    with file_path.open("wb") as f:
        df.to_parquet(f)

    return file_path

@task
def analyze_with_duckdb(file_path: ObjectStoragePath) -> Dict:
    """Analyze data using DuckDB with ObjectStoragePath."""
    import duckdb

    # Register filesystem for DuckDB
    conn = duckdb.connect(":memory:")
    conn.register_filesystem(file_path.fs)

    # Query Parquet file directly
    query = f"SELECT COUNT(*) as record_count FROM read_parquet('{file_path}')"
    result = conn.execute(query).fetchone()

    return {"analysis": {"record_count": result[0]}}
```

### 6. Environment-Aware Task Logic

Tasks should adapt their behavior based on the environment:

```python
@task
def environment_aware_task() -> Dict:
    """Task that adapts to different environments."""
    from config import get_environment_config

    env_config = get_environment_config()
    logger = logging.getLogger(__name__)

    logger.info(f"🌍 Running in {env_config.name} environment")

    if env_config.name == "local":
        # Use mock data or simplified logic for local development
        logger.info("🏠 Using LocalStack and mock data")
        return {
            "status": "success",
            "environment": "local",
            "data_source": "mock",
            "records_processed": 100
        }
    elif env_config.name == "dev":
        # Use development-specific logic with reduced data volumes
        logger.info("🔧 Processing development data with reduced volume")
        return process_dev_data(
            connection_id=env_config.connections.starburst_conn_id,
            limit=1000  # Reduced for dev
        )
    else:
        # Full production logic
        logger.info("🚀 Processing full production data")
        return process_prod_data(
            connection_id=env_config.connections.starburst_conn_id,
            enable_performance_monitoring=True
        )
```

## Notification and Alerting

### 1. Configure Notifications Appropriately

Use the NotificationConfig class for consistent alerting. The `@etdag` decorator automatically handles environment-aware routing:

```python
def _get_notification_config():
    """Get notification configuration. Use function to avoid top-level object creation."""
    return NotificationConfig(
        # Failure notifications (recommended for all DAGs)
        slack_on_failure=True,
        email_on_failure=True,

        # Success notifications (use sparingly)
        slack_on_success=False,  # Usually not needed
        email_on_success=False,

        # Team-based routing (automatically maps to environment-specific channels)
        team_owner="data-engineering",  # Maps to team channels

        # Override defaults if needed
        slack_channel_override="#critical-alerts",  # For critical DAGs only
        email_destination_override=["<EMAIL>"],  # For specific routing

        # OpsGenie (automatically enabled only in production)
        opsgenie_enabled=True,
        opsgenie_team="data-engineering",
    )

# Use in DAG definition
@etdag(
    # ... other parameters
    notification_config=_get_notification_config(),
)
def my_dag():
    pass
```

**Environment Behavior:**

- **Local/Dev**: Notifications go to development channels
- **Production**: Notifications go to production channels + OpsGenie (if enabled)
- **Team routing**: Automatically maps `team_owner` to appropriate channels

### 2. Use Structured Logging

Use consistent, structured logging with emojis for easy scanning:

```python
# Success indicators
logger.info("✅ Task completed successfully")
logger.info("🔍 Starting data validation")
logger.info("📊 Processed 1000 records")

# Warning indicators
logger.warning("⚠️ Performance threshold exceeded")
logger.warning("🐌 Query took longer than expected")

# Error indicators
logger.error("❌ Task failed with error")
logger.error("🚨 Critical system unavailable")

# Info indicators
logger.info("ℹ️ Skipping task in development environment")
logger.info("🔄 Retrying operation")
```

## Testing Standards

### 1. DAG Loader Testing

Test that your DAG loads without errors (official Airflow best practice):

```python
# Simple DAG loading test
def test_dag_loads():
    """Test that DAG loads without import errors."""
    import subprocess
    import sys

    # Test DAG file can be imported without errors
    result = subprocess.run([
        sys.executable, "dags/my_dag/my_dag.py"
    ], capture_output=True, text=True)

    assert result.returncode == 0, f"DAG failed to load: {result.stderr}"

# Using pytest and DagBag
import pytest
from airflow.models import DagBag

@pytest.fixture()
def dagbag():
    return DagBag()

def test_dag_loaded(dagbag):
    """Test DAG is loaded correctly."""
    dag = dagbag.get_dag(dag_id="my_dag")
    assert dagbag.import_errors == {}
    assert dag is not None
    assert len(dag.tasks) > 0
```

### 2. Unit Testing TaskFlow Functions

Test task logic independently from Airflow:

````python
"""
Unit tests for [DAG_NAME].

These tests focus on the core logic and configuration of the DAG without
excessive coupling to Airflow internals. Tests are organized to validate:

1. DAG structure and configuration
2. Task function logic (independent of Airflow)
3. Environment-aware behavior
4. Error handling
"""

class TestMyDAGTasks:
    def test_extract_data_function(self):
        """Test extract_data task logic independently."""
        # Import the actual function from your DAG
        from dags.my_dag.my_dag import extract_data

        # Test the function directly
        result = extract_data.python_callable()

        assert result is not None
        assert "records" in result
        assert len(result["records"]) > 0

    def test_transform_data_function(self):
        """Test transform_data task logic."""
        from dags.my_dag.my_dag import transform_data

        # Test with mock input
        mock_input = {"records": [1, 2, 3], "source": "test"}
        result = transform_data.python_callable(mock_input)

        assert result["status"] == "success"
        assert len(result["records"]) == 3

### 3. Mock External Dependencies
Keep tests isolated by mocking external dependencies:

```python
import pytest
from unittest.mock import patch, MagicMock

@pytest.fixture
def mock_environment_config():
    """Mock environment configuration."""
    with patch('config.get_environment_config') as mock_config:
        mock_env = MagicMock()
        mock_env.name = "test"
        mock_env.connections.starburst_conn_id = "test_conn"
        mock_config.return_value = mock_env
        yield mock_env

@pytest.fixture
def mock_object_storage():
    """Mock ObjectStoragePath for testing."""
    with patch('airflow.sdk.ObjectStoragePath') as mock_path:
        mock_instance = MagicMock()
        mock_path.return_value = mock_instance
        yield mock_instance

# Mock Variables and Connections for testing
@pytest.fixture
def mock_airflow_variables():
    """Mock Airflow Variables using environment variables."""
    import os
    with patch.dict(os.environ, {
        'AIRFLOW_VAR_TEST_KEY': 'test_value',
        'AIRFLOW_CONN_TEST_CONN': 'postgresql://user:pass@localhost:5432/db'
    }):
        yield
````

## Code Quality and Linting

### 1. Use Ruff for Airflow-Specific Linting

Install and use Ruff with Airflow-specific rules:

```bash
pip install "ruff>=0.11.6"

# Run Airflow-specific linting
ruff check dags/ --select AIR3 --preview
```

### 2. Common Airflow Anti-Patterns to Avoid

Ruff will help identify these issues:

```python
# ❌ BAD: Missing explicit schedule
@dag()
def my_dag():
    pass

# ✅ GOOD: Explicit schedule
@dag(schedule="@daily")
def my_dag():
    pass

# ❌ BAD: Using removed Airflow 3.0 features
from airflow.datasets import Dataset  # Removed in 3.0

# ✅ GOOD: Use current APIs
from airflow.sdk import Asset  # Airflow 3.0+
```

### 3. Pre-commit Hooks

Set up pre-commit hooks for code quality:

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.6
    hooks:
      - id: ruff
        args: [--fix, --select, AIR3, --preview]
      - id: ruff-format
```

## Performance and Monitoring

### 1. Set Appropriate Timeouts and Retries

Configure timeouts and retries based on task characteristics:

```python
# For quick data validation tasks
@task(retries=2, retry_delay=timedelta(minutes=1))
def quick_validation():
    pass

# For long-running data processing
@task(retries=3, retry_delay=timedelta(minutes=5), timeout=timedelta(hours=2))
def long_processing():
    pass
```

### 2. Monitor Performance

Include performance monitoring in your tasks:

```python
@task
def monitored_task():
    """Task with performance monitoring."""
    start_time = pendulum.now()

    try:
        # Task logic
        result = perform_operation()

        # Log performance metrics
        duration = (pendulum.now() - start_time).total_seconds()
        logger.info(f"📈 Task completed in {duration:.2f} seconds")

        if duration > PERFORMANCE_THRESHOLD:
            logger.warning(f"⚠️ Task exceeded performance threshold: {duration}s > {PERFORMANCE_THRESHOLD}s")

        return result
    except Exception as e:
        duration = (pendulum.now() - start_time).total_seconds()
        logger.error(f"❌ Task failed after {duration:.2f} seconds: {e}")
        raise
```

## Security and Safety

### 1. Validate Inputs

Always validate inputs and configurations:

```python
@task
def secure_task(config: Dict) -> Dict:
    """Task with input validation."""
    # Validate required fields
    required_fields = ["source", "destination", "format"]
    for field in required_fields:
        if field not in config:
            raise ValueError(f"Missing required field: {field}")

    # Validate field values
    if config["format"] not in ["csv", "parquet", "json"]:
        raise ValueError(f"Unsupported format: {config['format']}")

    # Proceed with validated config
    return process_with_config(config)
```

### 2. Use Secure Connection Management

Use Airflow connections for all external systems:

```python
# Good: Use Airflow connections
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
s3_hook = S3Hook(aws_conn_id=env_config.connections.s3_conn_id)

# Bad: Hardcode credentials
# aws_access_key = "AKIAIOSFODNN7EXAMPLE"  # DON'T DO THIS
```

## Migration from Legacy DAGs

### 1. Migration to @etdag Decorator

When migrating legacy DAGs to the modern `@etdag` decorator pattern:

**Step 1: Convert DAG Structure**

```python
# OLD: Legacy ETDAG context manager
with ETDAG(
    dag_id="my_dag",
    owner="team-name",
    description="My DAG description",
    tags=["legacy"],
) as dag:
    # tasks defined here

# NEW: Modern @etdag decorator
@etdag(
    dag_id="my_dag",
    owner="team-name",
    description="My DAG description",
    business_purpose="Clear business justification",
    data_sources=["source1"],
    downstream_systems=["system1"],
    tags=["modern", "team-name"],
    start_date=pendulum.today("UTC").add(days=-2),
    schedule="@daily",
    notification_config=_get_notification_config(),
    doc_md=__doc__,
)
def my_dag():
    # tasks defined here using TaskFlow API
    return None
```

**Step 2: Fix Import Statements**

```python
# OLD: Can cause import conflicts
from etdag import etdag

# NEW: Explicit import path
from etdag.decorators import etdag
```

**Step 3: Add Required Fields**
The `@etdag` decorator requires additional metadata:

- `business_purpose`: Why this DAG exists
- `data_sources`: What data sources it uses
- `downstream_systems`: What systems consume the output

### 2. Common Anti-Patterns to Fix

**Replace Variable.get() calls:**

```python
# OLD: Top-level Variable.get() calls
env = Variable.get("environment")
bucket = Variable.get("s3_bucket")

# NEW: Environment configuration with lazy loading
def _get_environment_config():
    from config import get_environment_config
    return get_environment_config()

@task
def my_task():
    env_config = _get_environment_config()
    bucket = env_config.s3.bucket
```

**Replace hardcoded connections:**

```python
# OLD: Hardcoded connection IDs
conn = BaseHook.get_connection("hardcoded_starburst_conn")

# NEW: Environment-aware connections
@task
def my_task():
    env_config = _get_environment_config()
    conn_id = env_config.connections.starburst_conn_id
    hook = SqlHook(conn_id=conn_id)
```

**Replace manual notification logic:**

```python
# OLD: Manual environment-based notifications
if Variable.get("environment") == "prod":
    send_slack_alert("#prod-alerts")
else:
    send_slack_alert("#dev-alerts")

# NEW: Automatic environment-aware notifications via @etdag
@etdag(
    notification_config=NotificationConfig(
        slack_on_failure=True,
        team_owner="data-engineering"  # Auto-routes to correct channels
    )
)
```

## Code Review Checklist

Before submitting a DAG for review, ensure compliance with official Airflow best practices and ETDAG standards:

### Required for All New DAGs:

- [ ] Uses `@etdag` decorator with TaskFlow API (not legacy patterns)
- [ ] Imports etdag correctly: `from etdag.decorators import etdag`
- [ ] Includes all required fields: `business_purpose`, `data_sources`, `downstream_systems`
- [ ] Has comprehensive docstring with business purpose and data flow
- [ ] Uses `doc_md=__doc__` parameter for UI documentation

### Airflow Best Practices Compliance:

- [ ] **No top-level expensive code** - all heavy operations inside tasks
- [ ] **No top-level Variable.get()** calls - use lazy loading
- [ ] **No top-level heavy imports** - import inside tasks when needed
- [ ] **Absolute imports only** - no relative imports (critical for Kubernetes compatibility)
- [ ] **Idempotent tasks** - use UPSERT instead of INSERT, handle retries gracefully
- [ ] **Proper partitioning** - use `data_interval_start` for time-based partitioning
- [ ] **TaskFlow API** - all new tasks use `@task` decorator with type hints

### Dependency Management:

- [ ] Uses `PythonVirtualenvOperator` or `@task.virtualenv` for conflicting dependencies
- [ ] Uses `@task.external_python` for pre-installed environments when appropriate
- [ ] No conflicting dependencies in main Airflow environment

### Cloud-Native Patterns:

- [ ] Uses `ObjectStoragePath` for cloud storage operations
- [ ] Implements cloud-agnostic file handling
- [ ] Uses proper connection management via Airflow connections

### Configuration and Environment:

- [ ] Uses environment configuration for all environment-specific settings
- [ ] Implements lazy loading for expensive operations (avoid top-level execution)
- [ ] Uses `_get_environment_config()` pattern inside tasks
- [ ] Handles environment-specific feature availability (UDFs, connections)

### Task Development:

- [ ] Uses TaskFlow API (`@task` decorator) for new tasks
- [ ] Includes proper type hints and return types
- [ ] Has appropriate error handling with specific exception types
- [ ] Includes structured logging with emojis for easy scanning
- [ ] Validates inputs and outputs appropriately
- [ ] Tasks are idempotent and handle retries correctly

### Testing and Quality:

- [ ] Includes DAG loader test (`python dag_file.py` succeeds)
- [ ] Includes comprehensive unit tests following testing standards
- [ ] Tests task functions independently from Airflow
- [ ] Tests environment-aware behavior
- [ ] Mocks external dependencies appropriately
- [ ] Has appropriate timeouts and retries configured

### Performance and Monitoring:

- [ ] DAG parsing time is optimized (no expensive top-level code)
- [ ] Uses appropriate retry strategies and timeouts
- [ ] Includes performance monitoring where appropriate
- [ ] Uses secure connection management (no hardcoded credentials)

### Notifications and Alerting:

- [ ] Uses NotificationConfig with team-based routing
- [ ] Follows notification best practices (failure alerts, minimal success alerts)
- [ ] Environment-aware notification routing configured

### Migration Specific (for legacy DAG updates):

- [ ] Migrated from `with ETDAG()` to `@etdag` decorator
- [ ] Migrated from traditional operators to TaskFlow API
- [ ] Removed top-level Variable.get() calls
- [ ] Converted to lazy loading patterns
- [ ] Updated import statements to avoid conflicts
- [ ] **Converted all relative imports to absolute imports** (critical for K8s)
- [ ] Implemented ObjectStoragePath for file operations

## Additional Resources

### Official Airflow Documentation

- [Airflow Best Practices](https://airflow.apache.org/docs/apache-airflow/stable/best-practices.html) - Official Airflow best practices
- [TaskFlow API Tutorial](https://airflow.apache.org/docs/apache-airflow/stable/tutorial/taskflow.html) - Official TaskFlow API guide
- [Object Storage Tutorial](https://airflow.apache.org/docs/apache-airflow/stable/tutorial/objectstorage.html) - Cloud-native storage patterns

### ETDAG Framework Documentation

- [ETDAG Decorator Documentation](../etdag/decorators.py)
- [Environment Configuration Guide](../config/environments/)
- [Testing Standards](./TESTING_STANDARDS.md)
- [Notification System Guide](./NOTIFICATION_SYSTEM_GUIDE.md)

### Example DAGs

- [Sage to Starburst Example](../dags/sage_to_starburst/) - Modern @etdag pattern with TaskFlow API
- [UDF Health Check Example](../dags/starburst_udf_healthcheck/) - Environment-aware processing
- [Official TaskFlow Examples](https://github.com/apache/airflow/tree/main/airflow/example_dags) - Airflow example DAGs

### Tools and Linting

- [Ruff for Airflow](https://docs.astral.sh/ruff/rules/#airflow-air) - Airflow-specific linting rules
- [Airflow Code Quality Guide](https://airflow.apache.org/docs/apache-airflow/stable/best-practices.html#code-quality-and-linting)