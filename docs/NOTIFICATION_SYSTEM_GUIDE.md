# ETDAG Notification System

## Overview

The ETDAG notification system provides configurable alerting capabilities across different environments, with support for Slack, email, and OpsGenie alerts.

## Features

### 1. Notification Configuration

The `NotificationConfig` class in `config/environments.py` serves as the central configuration point for all notification types in the ETDAG system.

#### Available Configuration Parameters

```python
NotificationConfig(
    # Slack configuration
    slack_on_success: bool = False,        # Send Slack notifications on success
    slack_on_failure: bool = True,         # Send Slack notifications on failure
    slack_channel_override: str = None,    # Override default Slack channel
    
    # Email configuration
    email_on_success: bool = False,        # Send email notifications on success
    email_on_failure: bool = False,        # Send email notifications on failure
    email_destination_override: Union[str, List[str]] = None,  # Override default email destinations
    
    # OpsGenie configuration
    opsgenie_enabled: bool = False,        # Enable OpsGenie alerts (production only)
    opsgenie_team: str = None,             # Specify OpsGenie team directly
    team_owner: str = None                 # Team owner for OpsGenie routing
)
```

### 2. Slack Notifications

- Default configuration: Slack notifications are enabled by default for failures (`slack_on_failure=True`) but disabled for successes (`slack_on_success=False`).
- Environment-specific channels: The system uses environment-specific default channels based on the current environment.
- Simple override: You can specify a custom Slack channel using `slack_channel_override`.

### 3. Email Notifications

- By default, email notifications are disabled (`email_on_success=False`, `email_on_failure=False`)
- Environment-specific defaults: Each environment has a default email destination.
- Simple override: You can specify custom email addresses using `email_destination_override`.

### 4. OpsGenie Integration

- Basic OpsGenie integration for production-only alerts.
- OpsGenie alerts are only sent in production environments.
- Team-based routing: Alerts are routed to team-specific OpsGenie teams based on:
  1. The `opsgenie_team` parameter if specified directly
  2. The `team_owner` parameter mapped to an OpsGenie team if available

### 5. Environment-Based Defaults

- Environment-specific Slack channels based on current environment (local, dev, prod)
- Environment-specific email destinations based on current environment
- Team-based OpsGenie routing via `get_opsgenie_team()` in environments.py

## Usage Examples

### Basic DAG with Default Notifications

```python
from etdag import ETDAG

with ETDAG(
    dag_id="my_dag",
    owner="data-engineering",
    description="My DAG description",
    tags=["my", "tags"],
    # No explicit notification config - uses defaults
):
    # DAG tasks
```

### Custom Notification Configuration

```python
from config.environments import NotificationConfig
from etdag import ETDAG

notification_config = NotificationConfig(
    slack_on_success=True,  # Override default (False)
    slack_on_failure=True,  # Keep default (True)
    slack_channel_override="#my-custom-channel",  # Direct channel override
    email_on_failure=True,  # Override default (False)
    email_destination_override=["<EMAIL>"],  # Direct email override
    opsgenie_enabled=True,  # Will only be active in production
    team_owner="analytics-engineering"  # For OpsGenie team routing
)

with ETDAG(
    dag_id="my_dag",
    owner="analytics-engineering",
    description="My DAG description",
    tags=["my", "tags"],
    notification_config=notification_config,
):
    # DAG tasks
```

## Advanced Configuration

### Environment-Specific Settings

The notification system adapts automatically based on the current environment:

```python
# In development environments:
- Slack alerts use #dev-airflow-notifications-dev channel by default
- Email notifications <NAME_EMAIL> if enabled
- OpsGenie alerts are disabled

# In production environments:
- Slack alerts use #dev-airflow-notifications-prod channel by default
- Email notifications <NAME_EMAIL> if enabled
- OpsGenie alerts are enabled if configured
```

### Available Properties

The NotificationConfig class provides the following properties that can be used in your code:

```python
# Get the appropriate Slack channel (either override or default)
notification_config.slack_channel

# Get the default Slack channel based on environment
notification_config.default_slack_channel

# Get the email destination list (either override or default)
notification_config.email_destination
```

### Adding OpsGenie Team Mappings

To add new team mappings for OpsGenie, update the mapping dictionary in `config/environments.py`:

```python
OPSGENIE_TEAM_MAPPING = {
    "data-engineering": "data-engineering",
    "analytics-engineering": "analytics-engineering",
    "business-intelligence": "business-intelligence",
    # Add new team mappings here
}
```

### Modifying Environment Defaults

To change environment default settings for email or Slack, modify the properties in NotificationConfig:

```python
# Customize default Slack channels
@property
def default_slack_channel(self) -> str:
    env = get_current_environment()
    if env == "local":
        return "#dev-airflow-notifications-local"
    elif env == "dev":
        return "#dev-airflow-notifications-dev"
    else:
        return "#dev-airflow-notifications-prod"

# Customize default email destinations
@property
def email_destination(self) -> List[str]:
    # Check for override first...
    if self.email_destination_override:
        # ...existing code...
    
    # Environment-based defaults
    env = get_current_environment()
    if env == "local":
        return []  # No emails in local environment
    elif env == "dev":
        return ["<EMAIL>"]
    else:  # prod
        return ["<EMAIL>"]
```