""" A base DAG to import instead of airflow.models.dag.DAG to set custom defaults.

The ETDAG class can be used as a Context Mangaer to create a DAG in Airflow.
Requires all the same arguments as a DAG, but changing a few of the default values.
Mainly supporting slack messaging for successes and failures.

Typical usage example:

    with ETDAG("test_dag"):
    with ETDAG("test_dag", start_date=datetime(2022, 10, 1)):

"""

from airflow.models.dag import DAG
from airflow.models import Variable
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
import logging
import pendulum

def log_success(context):
    logging.info(
        f"{context.get('task_instance').dag_id} has completed successfully at {context.get('execution_date')}"
    )
    return None

def log_failure(context):
    logging.info(
        f"{context.get('task_instance').dag_id} has failed at {context.get('execution_date')}"
    )
    return None


def task_slack_success_alert(context):
    """Sends a success message to Slack
    Args:
        context: Airflow will pass in the context of the dag

    Returns:
        Slack SDK WebhookResponse
    """
    log_success(context)

    slack_msg = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f":large_green_circle: Success: {context.get('task_instance').dag_id}",
                "emoji": True,
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Execution Time*: {context.get('execution_date')}",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Dag URL*: https://airflow.k8s.eltoro.com/dags/{context.get('task_instance').dag_id}/grid",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "*Airflow Runbook*: https://eltorocorp.atlassian.net/wiki/spaces/DSG/pages/2001076256/Airflow",
            },
        },
        
    ]
    success_alert = SlackWebhookOperator(
        task_id="slack_success_alert",
        slack_webhook_conn_id=f"slack_alert_conn_{Variable.get('environment')}",
        blocks=slack_msg,
    )
    return_msg = success_alert.execute(context)
    return return_msg

def task_slack_error_alert(context):
    """Sends an error message to Slack
    Args:
        context: Airflow will pass in the context of the dag

    Returns:
        Slack SDK WebhookResponse
    """
    logging.error(
        f"{context.get('task_instance').dag_id} has errored at {context.get('execution_date')}"
    )
    logging.error(f"Errored Task: {context.get('task_instance').task_id}")
    logging.error(f"Error: {context.get('exception') or context.get('reason')}")

    slack_msg = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f":red_circle: Error: {context.get('task_instance').dag_id} Dag Failed",
                "emoji": True,
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Error*: {context.get('exception') or context.get('reason')}",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Task*: {context.get('task_instance').task_id}",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Execution Time*: {context.get('execution_date')}",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Dag URL*: https://airflow.k8s.eltoro.com/dags/{context.get('task_instance').dag_id}/grid",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "*Airflow Runbook*: https://eltorocorp.atlassian.net/wiki/spaces/DSG/pages/2001076256/Airflow",
            },
        },
    ]
    failed_alert = SlackWebhookOperator(
        task_id="slack_failed_alert",
        slack_webhook_conn_id=f"slack_alert_conn_{Variable.get('environment')}",
        blocks=slack_msg,
    )
    return_msg = failed_alert.execute(context)
    return return_msg


class ETDAG(DAG):
    """A new DAG class designed to automate some settings, such as default start_date and some default callback functions

    Uses and requires the same arguments as airflow.models.dag : https://airflow.apache.org/docs/apache-airflow/stable/_api/airflow/models/dag/index.html#airflow.models.dag.DAG

    Args:
        dag_id (str) – The id of the DAG; must consist exclusively of alphanumeric characters, dashes, dots and underscores (all ASCII)
        et_success_msg ( bool | False) - Boolean on if we should include a success message to the appropriate slack channel
        description (str | None) – The description for the DAG to e.g. be shown on the webserver
        schedule (ScheduleArg) – Defines the rules according to which DAG runs are scheduled. Can accept cron string, timedelta object, Timetable, or list of Dataset objects. See also Customizing DAG Scheduling with Timetables.
        start_date (datetime | pendulum.yesterday()) – The timestamp from which the scheduler will attempt to backfill
        end_date (datetime | None) – A date beyond which your DAG won’t run, leave to None for open ended scheduling
        template_searchpath (str | Iterable[str] | None) – This list of folders (non relative) defines where jinja will look for your templates. Order matters. Note that jinja/airflow includes the path of your DAG file by default
        template_undefined (type[jinja2.StrictUndefined]) – Template undefined type.
        user_defined_macros (dict | None) – a dictionary of macros that will be exposed in your jinja templates. For example, passing dict(foo='bar') to this argument allows you to {{ foo }} in all jinja templates related to this DAG. Note that you can pass any type of object here.
        user_defined_filters (dict | None) – a dictionary of filters that will be exposed in your jinja templates. For example, passing dict(hello=lambda name: 'Hello %s' % name) to this argument allows you to {{ 'world' | hello }} in all jinja templates related to this DAG.
        default_args (dict | None) – A dictionary of default parameters to be used as constructor keyword parameters when initialising operators. Note that operators have the same hook, and precede those defined here, meaning that if your dict contains ‘depends_on_past’: True here and ‘depends_on_past’: False in the operator’s call default_args, the actual value will be False.
        params (dict | None) – a dictionary of DAG level parameters that are made accessible in templates, namespaced under params. These params can be overridden at the task level.
        max_active_tasks (int) – the number of task instances allowed to run concurrently
        max_active_runs (int) – maximum number of active DAG runs, beyond this number of DAG runs in a running state, the scheduler won’t create new active DAG runs
        dagrun_timeout (timedelta | None) – specify how long a DagRun should be up before timing out / failing, so that new DagRuns can be created. The timeout is only enforced for scheduled DagRuns.
        sla_miss_callback (SLAMissCallback | None) – specify a function to call when reporting SLA timeouts. See sla_miss_callback for more information about the function signature and parameters that are passed to the callback.
        default_view (str) – Specify DAG default view (grid, graph, duration, gantt, landing_times), default grid
        orientation (str) – Specify DAG orientation in graph view (LR, TB, RL, BT), default LR
        catchup (bool) – Perform scheduler catchup (or only run latest)? Defaults to True
        on_failure_callback (DagStateChangeCallback | SlackAlert ) – A function to be called when a DagRun of this dag fails. A context dictionary is passed as a single parameter to this function. Defaults to a failure message to the #dev-bigdata-notifications channel in Slack
        on_success_callback (DagStateChangeCallback | SlackAlert ) – Much like the on_failure_callback except that it is executed when the dag succeeds. Defaults to a success message to the #dev-bigdata-notifications channel in Slack
        access_control (dict | None) – Specify optional DAG-level actions, e.g., “{‘role1’: {‘can_read’}, ‘role2’: {‘can_read’, ‘can_edit’, ‘can_delete’}}”
        is_paused_upon_creation (bool | None) – Specifies if the dag is paused when created for the first time. If the dag exists already, this flag will be ignored. If this optional parameter is not specified, the global config setting will be used.
        jinja_environment_kwargs (dict | None) – additional configuration options to be passed to Jinja Environment for template rendering
        render_template_as_native_obj (bool) – If True, uses a Jinja NativeEnvironment to render templates as native Python types. If False, a Jinja Environment is used to render templates as string values.
        tags (list[str] | None) – List of tags to help filtering DAGs in the UI.
        owner_links (dict[str, str] | None) – Dict of owners and their links, that will be clickable on the DAGs view UI. Can be used as an HTTP link (for example the link to your Slack channel), or a mailto link. e.g: {“dag_owner”: “https://airflow.apache.org/”}
        auto_register (bool) – Automatically register this DAG when it is used in a with block


    """

    def __init__(
        self,
        dag_id,
        start_date=pendulum.yesterday(),
        on_success_callback=log_success,
        on_failure_callback=log_failure,
        et_success_msg=False,
        et_failure_msg=True,
        extra_slack_conn_ids_to_message:list|None=None,
        **kwargs,
    ) -> None:
        """Initiates an Airflow DAG with the custom ElToro Defaults"""
        self.extra_slack_conn_ids_to_message=extra_slack_conn_ids_to_message
        if et_success_msg:
            on_success_callback = task_slack_success_alert
        if et_failure_msg:
            on_failure_callback = self.task_slack_error_alert
        kwargs["dag_id"] = dag_id
        kwargs["start_date"] = start_date
        kwargs["on_success_callback"] = on_success_callback
        kwargs["on_failure_callback"] = on_failure_callback
        super().__init__(**kwargs)
        pass


    def task_slack_error_alert(self, context):
        """Sends an error message to Slack
        Args:
            context: Airflow will pass in the context of the dag

        Returns:
            Slack SDK WebhookResponse
        """
        logging.error(
            f"{context.get('task_instance').dag_id} has errored at {context.get('execution_date')}"
        )
        logging.error(f"Errored Task: {context.get('task_instance').task_id}")
        logging.error(f"Error: {context.get('exception') or context.get('reason')}")

        slack_msg = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f":red_circle: Error: {context.get('task_instance').dag_id} Dag Failed",
                    "emoji": True,
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Error*: {context.get('exception') or context.get('reason')}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Task*: {context.get('task_instance').task_id}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Execution Time*: {context.get('execution_date')}",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Dag URL*: https://airflow.k8s.eltoro.com/dags/{context.get('task_instance').dag_id}/grid",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Airflow Runbook*: https://eltorocorp.atlassian.net/wiki/spaces/DSG/pages/2001076256/Airflow",
                },
            },
        ]
        return_msg = SlackWebhookOperator(
            task_id="slack_failed_alert",
            slack_webhook_conn_id=f"slack_alert_conn_{Variable.get('environment')}",
            blocks=slack_msg,
        ).execute(context)
        for conn_id in self.extra_slack_conn_ids_to_message:
            return_msg = SlackWebhookOperator(
            task_id="slack_failed_alert",
            slack_webhook_conn_id=conn_id,
            blocks=slack_msg,
            ).execute(context)
        return return_msg
